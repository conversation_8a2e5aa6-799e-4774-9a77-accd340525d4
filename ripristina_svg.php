<?php
// ripristina_svg.php (Versione 3.1 - Finale Corretta)

$fileElenco = 'elenco_file.txt';
$backupDir = 'svg_backups';

if (!file_exists($fileElenco)) {
    echo "Errore: Il file di configurazione '$fileElenco' non è stato trovato.\n";
    exit(1);
}

if (!is_dir($backupDir)) {
    echo "i Cartella di backup '$backupDir' non trovata. Probabilmente non c'è nulla da ripristinare.\n";
    exit(0);
}

$percorsi = file($fileElenco, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
$fileRipristinati = 0;
$svgTotali = 0;

foreach ($percorsi as $file) {
    if (!file_exists($file)) {
        echo "Attenzione: File non trovato '$file'. Saltato.\n";
        continue;
    }

    $contenutoDaRipristinare = file_get_contents($file);
    $svgRipristinatiInFile = 0;

    $regex = '/SVG_PLACEHOLDER_(svg_.*)/';

    $contenutoNuovo = preg_replace_callback($regex, function ($matches) use ($backupDir, &$svgRipristinatiInFile) {
        $uniqueId = $matches[1];
        $backupFile = $backupDir . '/' . $uniqueId . '.svgbak';

        if (file_exists($backupFile)) {
            $svgRipristinatiInFile++;
            $svgCode = file_get_contents($backupFile);
            unlink($backupFile);
            return $svgCode;
        } else {
            return $matches[0]; 
        }
    }, $contenutoDaRipristinare);

    if ($svgRipristinatiInFile > 0) {
        file_put_contents($file, $contenutoNuovo);
        echo "✓ Ripristinato $svgRipristinatiInFile SVG in '$file'.\n";
        $fileRipristinati++;
        $svgTotali += $svgRipristinatiInFile;
    } else {
        echo "i Nessun segnaposto SVG da ripristinare trovato in '$file'.\n";
    }
}

if (count(scandir($backupDir)) == 2) { // . e ..
    rmdir($backupDir);
    echo "\nPulizia completata: la cartella '$backupDir' è stata rimossa.\n";
}

echo "\nOperazione completata: $svgTotali SVG ripristinati in $fileRipristinati file.\n";