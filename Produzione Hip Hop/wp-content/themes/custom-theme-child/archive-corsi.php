<?php
/**
 * The template for displaying all pages
 *
 * This is the template that displays all pages by default.
 * Please note that this is the WordPress construct of pages
 * and that other 'pages' on your WordPress site may use a
 * different template.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Custom_theme
 */

get_header();
?>





<?php get_template_part('templates/parts/nav'); ?>



<section class="page-header mb-140">

<div class="container-xl mx-auto h-460 flex items-center px-80 rounded-24 bg-brand-dark relative">

<div class="main max-w-800 relative z-60 flex flex-col items-start">
    <div class="pretitle uppercase font-tt-eb text-16 mb-28 spacing-12 text-brand-violet">
    I corsi di Produzione Hip Hop
    </div>
    <div class="title font-din-bc text-60 text-white">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sapien libero
    </div>
</div>

<div class="background absolute top-0 right-0 w-60-100 bottom-0 z-10 rounded-24 overflow-hidden">
<?php load_img(class: 'img img-cover', high: 177, low: 176); ?>
</div>


<div class="gradients absolute inset-0 z-40 overflow-hidden rounded-24">

<div class="radial-gradient-violet animate-float-around animation-duration-8000 absolute z-20 h-800 w-800 left--400 top--300 opacity-60"></div>
<div class="radial-gradient-blue absolute z-20 h-800 w-800 right--400 bottom--300"></div>
</div>



<div class="shapes z-50 absolute inset-0">

<div class="shape-1 animate-float-updown-light animation-duration-5000 absolute left--40 top-40">
<svg class="h-80 shadow-violet" width="132" height="140" viewBox="0 0 132 140" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_596_641)">
<path d="M126.867 77.9576L14.6966 3.41429C8.44291 -0.741649 0.286323 4.73807 1.76969 12.0988L26.0817 132.74C27.1921 138.25 33.2824 141.149 38.2596 138.538L126.118 92.4404C131.789 89.4651 132.201 81.502 126.867 77.9576Z" fill="#A973FF"/>
</g>
<defs>
<clipPath id="clip0_596_641">
<rect width="131" height="140" fill="white" transform="translate(0.9375)"/>
</clipPath>
</defs>
</svg>


    </div>

    <div class="shape-2 animate-float-updown-light animation-duration-8000 absolute right-200 bottom--40">
    <svg class="h-88 shadow-blue" width="73" height="90" viewBox="0 0 73 90" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_610_1553)">
<path d="M60.9958 81.1441L72.0798 12.6398C73.5036 3.8403 63.5728 -2.28396 56.3491 2.93878L4.91055 40.1292C-0.495635 44.0379 -0.631414 52.0436 4.63912 56.1334L44.9937 87.4474C50.9911 92.1012 59.7833 88.638 60.9958 81.1441Z" fill="#1289ED"/>
</g>
<defs>
<clipPath id="clip0_610_1553">
<rect width="72" height="90" fill="white" transform="translate(0.773438)"/>
</clipPath>
</defs>
</svg>

    </div>

</div>

</div>

</section>







<section class="corsi-listing mb-140">

<div class="container-l mx-auto"> 

<div class="body">


<div class="items grid grid-cols-3 gap-x-60 gap-y-120">


<?php
if ( have_posts() ) :
	while ( have_posts() ) :
		the_post();

		$corso_cover_id    = null;
		$corso_icona_id    = null;
		$corso_durata      = '';
		$corso_descrizione = '';
		$color_class_name  = 'violet';
		$rwmb_get_value_func = 'rwmb_get_value';

		if ( function_exists( $rwmb_get_value_func ) ) {
			$corso_cover_data = $rwmb_get_value_func( 'corso_cover', [ 'size' => 'large' ] );
			$corso_cover_id   = $corso_cover_data['ID'] ?? null;

			$corso_icona_data = $rwmb_get_value_func( 'corso_icona' );
			$corso_icona_id   = $corso_icona_data['ID'] ?? null;

			$corso_durata      = $rwmb_get_value_func( 'corso_durata' );
			$corso_descrizione = $rwmb_get_value_func( 'corso_descrizione' );
			$db_color          = $rwmb_get_value_func( 'corso_colore' );
			if ( ! empty( $db_color ) ) {
				$color_class_name = $db_color;
			}
		}

		?>

	<div class="item rounded-24 bg-white"> 
		<div class="head relative mb-40">
			<div class="cover h-200 relative rounded-24 overflow-hidden">
				<?php if ( $corso_cover_id ) : ?>
					<?php load_img( class: 'img img-cover', high: $corso_cover_id ); ?>
				<?php endif; ?>
			</div>
			<div class="badge absolute bottom--40 right-40 animate-float-updown-very-light animation-duration-5000">
			
				<?php if ( $corso_icona_id ) : ?>
					<?php load_img( class: 'img h-120 shadow-' . $color_class_name, high: $corso_icona_id ); ?>
				<?php endif; ?>

			</div>

		</div>
		<div class="body">
			<div class="pretitle text-brand-<?php echo esc_attr( $color_class_name ); ?> font-tt-eb text-16 uppercase spacing-12 mb-28">
				Corso
			</div>
			<div class="title font-din-bc text-48 mb-20">
				<?php the_title(); ?>
			</div>
			<div class="text text-gray font-ave-m leading-15 text-20 mb-40 max-w-90-100">
				<?php echo esc_html( $corso_descrizione ); ?>
			</div>
			<div class="info flex gap-28 mb-40">
				<div class="time flex gap-20 items-center">
					<div class="icon flex">
					<svg class="h-28" width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="13" cy="13" r="11.75" stroke="#121D23" stroke-width="2.5"/>
<path d="M13 6.5V13L9 15.3094" stroke="#121D23" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

					</div>
					<div class="text font-ave-d text-20">
						<?php echo esc_html( $corso_durata ); ?>
					</div>
				</div>
			</div>
			<a href="<?php the_permalink(); ?>" class="button mt-auto h-72 w-80-100 rounded-16 text-center border-brand-<?php echo esc_attr( $color_class_name ); ?> border-4 border-solid rounded-12 flex place-center z-20 relative hover-shadow-<?php echo esc_attr( $color_class_name ); ?> hover-up-2">
				<div class="text font-ave-b text-20">
				Maggiori informazioni
				</div>
			</a>

		</div>
	</div> <!-- item -->

		<?php
	endwhile;
else :
	?>
	<p><?php _e( 'Nessun corso trovato.', 'custom-theme' ); ?></p>
	<?php
endif;
?>

</div>

</div>


</section>



<section class="corsi-steps mb-140">
    <div class="container-l mx-auto grid grid-cols-2 gap-100 pb-100 relative">
        
        <div class="box bg-brand-pink-light rounded-24 px-100 py-100 relative">
            <div class="content flex flex-col items-center relative z-40">
            <div class="head mb-60 text-center flex flex-col items-center">
                <div class="number bg-brand-pink-medium rounded-12 h-120 w-120 place-center flex mb-48">
                    <div class="text relative top-6 font-din-bc text-60 text-brand-pink">
1
                    </div>

                </div>
                <div class="pretitle font-tt-eb text-16 uppercase spacing-12 text-brand-pink mb-28">
                Iscriviti
                </div>
                <div class="title font-din-bc text-60">
                Scegli il corso <br>e iscriviti
                </div>

            </div>
            <div class="body">
                <div class="items flex flex-col gap-20">

                    <div class="item flex items-center gap-40">
                        <div class="icon bg-brand-pink-medium rounded-12 h-80 w-80 place-center flex bg-opacity-60 shrink-0">
                        <svg class="h-50-100" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1.33978" y="1.33978" width="33.0479" height="33.0479" rx="7.59208" stroke="#192329" stroke-width="2.67956"/>
<path d="M11.168 19.6494L15.5225 22.999C16.3181 23.611 17.4617 23.4475 18.0539 22.6371L25.4589 12.5039" stroke="#E04E9D" stroke-width="3.57274" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

                        </div>
                        <div class="text font-ave-d text-22 leading-15">
                        Corsi completi che ti preparano al 100%
                        </div>
                    </div> <!-- item -->


                    <div class="item flex items-center gap-40">
                        <div class="icon bg-brand-pink-medium rounded-12 h-80 w-80 place-center flex bg-opacity-60 shrink-0">
                        <svg class="h-50-100" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1.33978" y="1.33978" width="33.0479" height="33.0479" rx="7.59208" stroke="#192329" stroke-width="2.67956"/>
<path d="M11.168 19.6494L15.5225 22.999C16.3181 23.611 17.4617 23.4475 18.0539 22.6371L25.4589 12.5039" stroke="#E04E9D" stroke-width="3.57274" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

                        </div>
                        <div class="text font-ave-d text-22 leading-15">
                        Accesso illimitato h24 al corso
                        </div>
                    </div> <!-- item -->


                    <div class="item flex items-center gap-40">
                        <div class="icon bg-brand-pink-medium rounded-12 h-80 w-80 place-center flex bg-opacity-60 shrink-0">
                        <svg class="h-50-100" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1.33978" y="1.33978" width="33.0479" height="33.0479" rx="7.59208" stroke="#192329" stroke-width="2.67956"/>
<path d="M11.168 19.6494L15.5225 22.999C16.3181 23.611 17.4617 23.4475 18.0539 22.6371L25.4589 12.5039" stroke="#E04E9D" stroke-width="3.57274" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

                        </div>
                        <div class="text font-ave-d text-22 leading-15">
                        Aggiornati nel tempo
                        </div>
                    </div> <!-- item -->



                    <div class="item flex items-center gap-40">
                        <div class="icon bg-brand-pink-medium rounded-12 h-80 w-80 place-center flex bg-opacity-60 shrink-0">
                        <svg class="h-50-100" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1.33978" y="1.33978" width="33.0479" height="33.0479" rx="7.59208" stroke="#192329" stroke-width="2.67956"/>
<path d="M11.168 19.6494L15.5225 22.999C16.3181 23.611 17.4617 23.4475 18.0539 22.6371L25.4589 12.5039" stroke="#E04E9D" stroke-width="3.57274" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

                        </div>
                        <div class="text font-ave-d text-22 leading-15">
                        Lezioni su richiesta <svg class="h-20" width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.9">
<circle cx="10.9414" cy="10.9414" r="9.94141" stroke="#798B97" stroke-width="1.87261"></circle>
<path d="M10.0211 12.8289V12.3094C10.0211 11.8447 10.1031 11.4756 10.2671 11.2022C10.4312 10.9197 10.741 10.5734 11.1967 10.1633C11.6523 9.74412 11.9166 9.44339 11.9895 9.26113C12.0715 9.06976 12.1125 8.93762 12.1125 8.86472C12.1125 8.5731 12.0214 8.34072 11.8391 8.16758C11.666 7.99443 11.4245 7.90786 11.1146 7.90786C10.7592 7.90786 10.4767 8.03088 10.2671 8.27693C10.0666 8.52298 9.95729 8.82371 9.93907 9.17912L7.76562 8.94674C7.86587 7.98988 8.23039 7.25628 8.85918 6.74596C9.49709 6.23563 10.2762 5.98047 11.1967 5.98047C12.1079 5.98047 12.8734 6.22196 13.4931 6.70495C14.1128 7.17882 14.4226 7.84863 14.4226 8.71436C14.4226 9.0971 14.3543 9.45251 14.2176 9.78057C14.09 10.0995 13.7665 10.4777 13.2471 10.9151C12.7367 11.3526 12.4315 11.6396 12.3312 11.7763C12.231 11.913 12.1763 12.0588 12.1672 12.2137C12.1581 12.3687 12.1535 12.4552 12.1535 12.4734V12.8289H10.0211ZM11.101 16.0548C10.7364 16.0548 10.4221 15.9318 10.1578 15.6858C9.8935 15.4306 9.76136 15.1253 9.76136 14.7699C9.76136 14.4145 9.8935 14.1092 10.1578 13.8541C10.4221 13.5989 10.7364 13.4713 11.101 13.4713C11.4655 13.4713 11.7753 13.5989 12.0305 13.8541C12.2948 14.1001 12.4269 14.4008 12.4269 14.7562C12.4269 15.1208 12.2993 15.4306 12.0442 15.6858C11.789 15.9318 11.4746 16.0548 11.101 16.0548Z" fill="#798B97"></path>
</g>
</svg>
                        </div>
                    </div> <!-- item -->



                </div>
            </div>
</div>



            <div class="shapes inset-0 h-100-100 z-20">
        <div class="shape-1 animate-float-updown-light animation-duration-6000 absolute left--80 bottom-200">

        <svg class="h-140 shadow-pink" width="131" height="140" viewBox="0 0 131 140" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_625_2509)">
<path d="M126.867 77.9576L14.6966 3.41429C8.44291 -0.741649 0.286323 4.73807 1.76969 12.0988L26.0817 132.74C27.1921 138.25 33.2824 141.149 38.2596 138.538L126.118 92.4404C131.789 89.4651 132.201 81.502 126.867 77.9576Z" fill="#E04E9D"/>
</g>
<defs>
<clipPath id="clip0_625_2509">
<rect width="130" height="140" fill="white" transform="translate(0.9375)"/>
</clipPath>
</defs>
</svg>

        </div>

    </div>

        </div> <!-- box -->

<div class="visual relative top-100 w-100-100 h-100-100 rounded-24 overflow-hidden">
<?php load_img(class: 'img img-cover', high: 110, low: 109); ?>
</div>


<div class="visual relative w-100-100 h-100-100 rounded-24 overflow-hidden">
<?php load_img(class: 'img img-cover', high: 110, low: 109); ?>
</div>


<div class="box bg-brand-violet-light rounded-24 px-100 py-100 relative top-100">
    <div class="content flex flex-col items-center relative z-40">
            <div class="head mb-60 text-center flex flex-col items-center">
                <div class="number bg-brand-violet-medium rounded-12 h-120 w-120 place-center flex mb-48">
                    <div class="text relative top-6 font-din-bc text-60 text-brand-violet">
2
                    </div>

                </div>
                <div class="pretitle font-tt-eb text-16 uppercase spacing-12 text-brand-violet mb-28">
                Online
                </div>
                <div class="title font-din-bc text-60">
                Segui il corso online dove e quando vuoi tu
                </div>

            </div>
            <div class="body">
                <div class="items flex flex-col gap-20">

                    <div class="item flex items-center gap-40">
                        <div class="icon bg-brand-violet-medium rounded-12 h-80 w-80 place-center flex bg-opacity-60 shrink-0">
                        <svg class="h-50-100" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1.33978" y="1.33978" width="33.0479" height="33.0479" rx="7.59208" stroke="#192329" stroke-width="2.67956"/>
<path d="M11.168 19.6494L15.5225 22.999C16.3181 23.611 17.4617 23.4475 18.0539 22.6371L25.4589 12.5039" stroke="#A973FF" stroke-width="3.57274" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

                        </div>
                        <div class="text font-ave-d text-22 leading-15">
                        Completa il corso con i tuoi tempi
                        </div>
                    </div> <!-- item -->


                    <div class="item flex items-center gap-40">
                        <div class="icon bg-brand-violet-medium rounded-12 h-80 w-80 place-center flex bg-opacity-60 shrink-0">
                        <svg class="h-50-100" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1.33978" y="1.33978" width="33.0479" height="33.0479" rx="7.59208" stroke="#192329" stroke-width="2.67956"/>
<path d="M11.168 19.6494L15.5225 22.999C16.3181 23.611 17.4617 23.4475 18.0539 22.6371L25.4589 12.5039" stroke="#A973FF" stroke-width="3.57274" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

                        </div>
                        <div class="text font-ave-d text-22 leading-15">
                        Non serve attrezzatura costosa basta un computer
                        </div>
                    </div> <!-- item -->


                    <div class="item flex items-center gap-40">
                        <div class="icon bg-brand-violet-medium rounded-12 h-80 w-80 place-center flex bg-opacity-60 shrink-0">
                        <svg class="h-50-100" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1.33978" y="1.33978" width="33.0479" height="33.0479" rx="7.59208" stroke="#192329" stroke-width="2.67956"/>
<path d="M11.168 19.6494L15.5225 22.999C16.3181 23.611 17.4617 23.4475 18.0539 22.6371L25.4589 12.5039" stroke="#A973FF" stroke-width="3.57274" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

                        </div>
                        <div class="text font-ave-d text-22 leading-15">
                        Ti forniamo noi le risorse software necessarie
                        </div>
                    </div> <!-- item -->



                    <div class="item flex items-center gap-40">
                        <div class="icon bg-brand-violet-medium rounded-12 h-80 w-80 place-center flex bg-opacity-60 shrink-0">
                        <svg class="h-50-100" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1.33978" y="1.33978" width="33.0479" height="33.0479" rx="7.59208" stroke="#192329" stroke-width="2.67956"/>
<path d="M11.168 19.6494L15.5225 22.999C16.3181 23.611 17.4617 23.4475 18.0539 22.6371L25.4589 12.5039" stroke="#A973FF" stroke-width="3.57274" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

                        </div>
                        <div class="text font-ave-d text-22 leading-15">
                        Non sono richieste competenze pregresse
                        </div>
                    </div> <!-- item -->



                </div>
            </div>
</div>


<div class="shapes inset-0 h-100-100 z-20">
        <div class="shape-1 animate-float-updown-light animation-duration-10000 absolute right--40 top-140">
        <svg class="h-88 shadow-violet" width="92" height="90" viewBox="0 0 92 90" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_625_2511)">
<path d="M90.4601 80.0943L90.4626 8.47041C90.4628 2.89418 85.145 -1.14514 79.7732 0.350904L7.02467 20.6114C0.180148 22.5176 -1.37944 31.5175 4.42463 35.6156L77.1706 86.9789C82.7534 90.9207 90.4598 86.9283 90.4601 80.0943Z" fill="#A973FF"/>
</g>
<defs>
<clipPath id="clip0_625_2511">
<rect width="91" height="90" fill="white" transform="translate(0.105469)"/>
</clipPath>
</defs>
</svg>


        </div>

    </div>

        </div> <!-- box -->




        <div class="box bg-brand-ocean-light rounded-24 px-100 py-100 relative">
            <div class="content flex flex-col items-center relative z-40">
            <div class="head mb-60 text-center flex flex-col items-center">
                <div class="number bg-brand-ocean-medium rounded-12 h-120 w-120 place-center flex mb-48">
                    <div class="text relative top-6 font-din-bc text-60 text-brand-ocean">
3
                    </div>

                </div>
                <div class="pretitle font-tt-eb text-16 uppercase spacing-12 text-brand-ocean mb-28">
                Supporto
                </div>
                <div class="title font-din-bc text-60">
                Ricevi feedback e supporto costante
                </div>

            </div>
            <div class="body">
                <div class="items flex flex-col gap-20">

                    <div class="item flex items-center gap-40">
                        <div class="icon bg-brand-ocean-medium rounded-12 h-80 w-80 place-center flex bg-opacity-60 shrink-0">
                        <svg class="h-50-100" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1.33978" y="1.33978" width="33.0479" height="33.0479" rx="7.59208" stroke="#192329" stroke-width="2.67956"/>
<path d="M11.168 19.6494L15.5225 22.999C16.3181 23.611 17.4617 23.4475 18.0539 22.6371L25.4589 12.5039" stroke="#194DD4" stroke-width="3.57274" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

                        </div>
                        <div class="text font-ave-d text-22 leading-15">
                        Feedback sulle tue produzioni e mix
                        </div>
                    </div> <!-- item -->


                    <div class="item flex items-center gap-40">
                        <div class="icon bg-brand-ocean-medium rounded-12 h-80 w-80 place-center flex bg-opacity-60 shrink-0">
                        <svg class="h-50-100" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1.33978" y="1.33978" width="33.0479" height="33.0479" rx="7.59208" stroke="#192329" stroke-width="2.67956"/>
<path d="M11.168 19.6494L15.5225 22.999C16.3181 23.611 17.4617 23.4475 18.0539 22.6371L25.4589 12.5039" stroke="#194DD4" stroke-width="3.57274" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

                        </div>
                        <div class="text font-ave-d text-22 leading-15">
                        Supporto per domande sulle lezioni
                        </div>
                    </div> <!-- item -->


                    <div class="item flex items-center gap-40">
                        <div class="icon bg-brand-ocean-medium rounded-12 h-80 w-80 place-center flex bg-opacity-60 shrink-0">
                        <svg class="h-50-100" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1.33978" y="1.33978" width="33.0479" height="33.0479" rx="7.59208" stroke="#192329" stroke-width="2.67956"/>
<path d="M11.168 19.6494L15.5225 22.999C16.3181 23.611 17.4617 23.4475 18.0539 22.6371L25.4589 12.5039" stroke="#194DD4" stroke-width="3.57274" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

                        </div>
                        <div class="text font-ave-d text-22 leading-15">
                        Contatto WhatsApp docente
                        </div>
                    </div> <!-- item -->



                    <div class="item flex items-center gap-40">
                        <div class="icon bg-brand-ocean-medium rounded-12 h-80 w-80 place-center flex bg-opacity-60 shrink-0">
                        <svg class="h-50-100" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect x="1.33978" y="1.33978" width="33.0479" height="33.0479" rx="7.59208" stroke="#192329" stroke-width="2.67956"/>
<path d="M11.168 19.6494L15.5225 22.999C16.3181 23.611 17.4617 23.4475 18.0539 22.6371L25.4589 12.5039" stroke="#194DD4" stroke-width="3.57274" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

                        </div>
                        <div class="text font-ave-d text-22 leading-15">
                        Gruppo condiviso con gli altri studenti del corso
                        </div>
                    </div> <!-- item -->



                </div>
            </div>
</div>
        </div> <!-- box -->

<div class="visual relative top-100 w-100-100 h-100-100 rounded-24 overflow-hidden">
<?php load_img(class: 'img img-cover', high: 110, low: 109); ?>
</div>




<div class="gradients absolute inset-0 z--10">

<div class="radial-gradient-pink absolute z-40 h-1000 w-1000 right--300 top-100 opacity-40"></div>

        </div>


    </div>
</section>

















<?php get_template_part('templates/parts/footer-cta'); ?>



<?php get_template_part('templates/parts/footer-classic'); ?>




<?php
get_sidebar();
get_footer();
