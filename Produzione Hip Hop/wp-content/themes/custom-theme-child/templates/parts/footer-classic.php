



<section class="footer-classic mb-140">
 

<div class="container-l mx-auto flex justify-between gap-200 relative">


<div class="main flex flex-col items-start">

<a href="https://app-656e0683c1ac188b30f693df.closte.com/" class="logo mb-48 relative">
<svg class="h-140 z-30 relative" width="60" height="142" viewBox="0 0 60 142" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.25 45.8135L59.7723 0V62.4516L32.5889 83.3696V117.113L24.8557 123.136V89.3304L20.2861 92.8689V126.651L12.5528 132.58V98.7736L7.98322 102.289V136.095L0.25 142V108.499V97.7198V64.2191L7.98322 58.2435V92.0487L12.5528 88.5336V54.7284L20.2861 48.7527V82.6048L24.8557 79.0897V45.2376L32.5889 39.2619V73.0587L52.0391 57.9996V16.2866L0.25 56.1244V45.8135Z" fill="#192329"/>
</svg>

<svg class="h-120 absolute top--40 left-40 z-20 animate-float-updown-light animation-duration-8000 " width="161" height="125" viewBox="0 0 161 125" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_605_709)">
<path d="M85.8687 0.121675C85.6515 0.183366 85.0496 0.210335 84.5143 0.186065C83.979 0.161796 83.3313 0.197777 83.0532 0.266573C82.7878 0.351931 81.9982 0.444403 81.3191 0.491301C79.129 0.657138 77.9713 1.16569 76.0824 2.82122C74.6144 4.11233 73.4183 5.49839 68.9628 11.16C61.9516 20.051 61.0748 21.1641 55.7212 28.2203C52.8539 32.008 49.7076 36.1035 48.7549 37.3415C45.707 41.2553 43.3535 44.4215 41.9264 46.5553L40.5399 48.6048L39.8281 47.6304C39.4348 47.086 38.2052 45.3719 37.0874 43.8365C34.3576 40.0738 32.9021 37.9115 29.6756 32.7834C24.5775 24.6614 23.0836 22.7934 20.2157 20.9122C19.0047 20.0979 18.6374 19.9307 17.9588 19.8579C16.9438 19.7268 15.1314 19.7471 14.7242 19.8889C14.5657 19.9581 13.9876 20.0332 13.4447 20.0677C11.2456 20.1875 10.2726 21.0041 8.78306 23.9675C6.24918 28.9949 4.89808 36.7921 3.91146 52.1898C3.45257 59.5641 3.43586 71.7366 3.90859 78.9621C4.13309 82.5507 4.92497 87.7681 5.56014 89.9142C5.73803 90.5056 5.75177 90.7466 5.61367 90.7737C5.50686 90.7899 5.05223 90.8358 4.59757 90.8819C3.50796 90.9804 2.66225 91.275 1.88533 91.8477C0.516077 92.8375 0.386438 93.4938 1.11038 95.5316C1.77264 97.3522 2.7714 98.7675 4.81847 100.752C6.51909 102.407 7.34476 102.963 8.85133 103.457C11.8078 104.422 12.7474 104.558 41.7564 108.32C69.8843 111.953 75.9634 112.827 92.5002 115.532C100.907 116.917 115.863 119.701 126.678 121.89C133.559 123.273 135.796 123.322 138.161 122.147C139.439 121.519 140.32 120.601 141.935 118.178C144.487 114.394 145.922 110.93 148.738 101.632C151.75 91.7452 152.783 87.4517 155.065 75.2276C156.37 68.2467 157.124 62.0623 158.109 49.925C158.751 42.1706 158.903 39.6029 158.894 37.3582C158.895 33.0657 158.054 30.893 156.237 30.3742C155.876 30.2676 155.502 30.0398 155.378 29.8442C155.037 29.3516 153.844 28.6293 153.226 28.5495C152.932 28.5116 152.36 28.5422 151.948 28.6087C151.535 28.6749 150.875 28.6945 150.463 28.6413C150.065 28.59 149.221 28.6452 148.578 28.7568C147.95 28.8701 146.884 29.0165 146.235 29.0672C143.481 29.31 139.613 31.3528 132.97 36.1187C129.491 38.6157 128.314 39.3911 125.529 41.0357C121.546 43.3929 119.253 44.8167 117.336 46.1095C112.467 49.4144 109.978 50.9627 109.83 50.8386C109.667 50.7129 109.119 49.2812 107.809 45.6418C105.629 39.5872 104.024 35.461 101.582 29.5818C100.999 28.1903 99.5962 24.8085 98.4685 22.0752C97.3409 19.342 95.9637 15.9935 95.3914 14.6332C94.8341 13.2748 93.7381 10.4113 92.9687 8.2628C91.0095 2.81979 90.6096 2.09508 89.3306 1.69056C89.0292 1.59182 88.5614 1.27713 88.3289 0.992851C87.6108 0.182179 86.7658 -0.106437 85.8687 0.121675ZM85.4022 16.1249C85.9821 17.4262 87.1923 20.215 88.1182 22.3089C91.4764 29.8918 93.0371 34.2515 98.1547 50.2132C98.4328 51.0716 99.0168 52.6878 99.483 53.8249C99.9348 54.9601 100.499 56.3791 100.734 56.9928C101.734 59.5597 103.465 62.3709 104.618 63.2827C104.89 63.4972 105.355 63.946 105.639 64.2968C106.496 65.3049 106.944 65.5422 108.014 65.5907C108.682 65.6321 109.249 65.5259 110.08 65.2294C111.646 64.684 115.322 62.8553 117.412 61.6147C119.31 60.4689 119.423 60.4086 123.01 57.8804C126.233 55.6197 127.775 54.682 131.86 52.4724C133.659 51.5085 136.125 50.1367 137.338 49.4261C143.65 45.7242 146.541 44.1831 146.72 44.4158C146.771 44.4821 146.407 47.995 145.923 52.21C144.952 60.535 144.419 63.9661 142.579 73.4652C140.28 85.3581 139.449 88.9001 136.259 100.394C134.687 106.084 133.905 108.436 133.343 109.081L133.041 109.446L126.109 108.461C122.305 107.924 118.112 107.263 116.783 107.017C113.34 106.348 103.426 104.708 93.7927 103.21C89.2326 102.501 84.6156 101.77 83.5326 101.585C82.4499 101.401 78.5279 100.73 74.827 100.087C65.7848 98.5304 60.9068 97.7358 50.7315 96.1523C46.0094 95.423 40.1459 94.501 37.6858 94.0935C20.9097 91.3883 12.4693 90.2681 9.78799 90.4154C9.01869 90.4658 8.30307 90.448 8.20578 90.3905C7.84229 90.1941 7.72235 85.3326 7.97015 80.9822C8.30588 75.3719 10.1605 59.6228 11.345 52.5369C11.7055 50.3248 12.3547 46.3406 12.7869 43.6891C13.221 41.0232 13.8046 37.7782 14.1099 36.4564C14.6365 34.1165 15.7864 30.077 16.0156 29.8076C16.1765 29.6041 16.9149 29.9086 17.8469 30.5675C19.355 31.6299 21.6328 34.7209 27.4657 43.6407C30.276 47.9374 32.338 51.1948 34.224 54.3101C36.2491 57.6229 38.7406 60.6817 40.0495 61.4341C41.2039 62.1068 43.0791 61.6011 44.8052 60.1638C45.3756 59.6842 46.5175 58.4856 47.3459 57.5157C48.1761 56.5311 51.5422 52.7032 54.8471 49.002C60.9687 42.1348 63.5737 39.1059 69.4444 31.9819C73.8618 26.6147 75.2747 24.8229 77.7098 21.4881C82.683 14.7117 83.9461 13.1549 84.2241 13.5498C84.2991 13.6641 84.8392 14.8107 85.4022 16.1249Z" fill="#FFBB44"/>
</g>
<defs>
<clipPath id="clip0_605_709">
<rect width="160" height="125" fill="white" transform="translate(0.125)"/>
</clipPath>
</defs>
</svg>


</a>

<div class="title font-ave-d text-24 leading-15 mb-140 max-w-540 relative">

Produzione Hip Hop offre percorsi formativi che in pochi mesi ti permettono di acquisire competenze reali, anche mentre studi o lavori.

<!--

Produzione Hip Hop offre percorsi formativi che, in pochi mesi anche se studi o lavori, ti danno le competenze che ti servono per il tuo percorso musicale.



Produzione Hip Hop offre percorsi formativi che in pochi mesi, anche mentre studi o lavori, ti permettono di acquisire competenze reali

Produzione Hip Hop offre percorsi che permettono di acquisire competenze reali in modo rapido e completo

Il metodo Produzione Hip Hop permette di acquisire competenze utili in modo rapido e 

Produzione Hip Hop offre percorsi formativi che in pochi mesi ti permettono di acquisire competenze realmente utili 




forniscono le competenze 


formano in modo completo


 e completi che ti formano in modo completo


scuola
specializzata in ambito Hip Hop

Percorso rapido e completo

Formarsi
COn un metodo che permette di 
Acquisire le competenze reali

In parallelo



La nostra esperienza 

Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque in euismod leo, vitae mollis arcu.
-->

<svg class="absolute left-0 bottom--80 z--10" width="462" height="55" viewBox="0 0 462 55" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M26.2481 18.748C41.1181 18.748 57.2811 18.102 73.4441 17.455L130.338 12.929C151.673 10.99 167.19 9.04999 177.534 8.40399C213.093 5.81799 247.357 3.87799 280.331 3.23199H282.917C288.089 3.23199 300.373 3.232 315.243 1.939H330.113C340.457 1.293 352.741 0.645996 363.086 0.645996C384.421 -3.93391e-06 399.291 0 406.402 0H410.926C412.219 0 413.512 -3.93391e-06 414.159 0.645996C416.097 1.292 416.745 2.58499 417.39 4.52499C418.036 7.11099 417.39 9.05101 414.804 10.99L412.865 11.636C410.926 12.282 406.4 12.282 399.289 12.929L344.335 16.808C317.827 18.101 297.785 19.394 284.208 20.687C270.631 21.333 252.528 22.626 231.193 24.566C187.876 27.152 152.318 31.031 116.112 36.205L91.5441 39.436C105.768 38.79 114.172 38.143 116.112 38.143L116.758 37.497H117.404H118.697C119.343 37.497 119.99 37.497 121.283 36.851C123.869 36.851 127.748 36.851 132.274 36.205L182.056 33.619C193.047 32.973 210.503 32.973 233.131 32.326L284.206 30.387C328.815 28.448 386.355 27.801 456.181 27.801C463.939 27.801 463.939 39.439 456.181 39.439C384.417 39.439 327.523 40.085 284.206 42.025L235.069 43.963C213.089 44.609 196.924 44.609 186.581 45.256C185.935 45.256 185.935 45.256 185.288 45.902L183.995 47.195H182.702C181.409 47.841 170.418 47.841 149.73 48.488C140.032 49.134 132.274 49.134 127.748 49.134H122.576H121.93H121.283C118.051 49.134 109.646 49.78 95.4221 51.073L60.5101 53.011C44.3471 54.304 32.0631 54.951 23.6581 54.951H10.7281C8.78914 54.304 7.49513 54.304 6.84913 54.304C6.20314 53.658 4.91013 53.658 4.26313 53.658L1.03113 51.072C-0.908866 47.84 -0.261866 43.313 4.26313 41.375C5.55613 40.729 6.84913 40.729 8.78913 40.729H12.0211H13.3141C24.3051 38.79 34.0021 37.497 42.4071 35.557L97.3621 27.152C69.5621 29.091 45.6401 30.384 26.2451 30.384C18.4891 30.385 18.4891 18.748 26.2481 18.748Z" fill="#FFBB44"/>
</svg>

</div>


<div class="social flex gap-40 items-center">
    
    <a href="https://www.instagram.com/produzionehiphop/" target="_blank" class="item flex"> 
    <svg class="h-28" width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
   <g clip-path="url(#clip0_582_659)">
   <path d="M15.9287 0H6.07108C2.72349 0 0 2.72362 0 6.0712V15.9288C0 19.2766 2.72349 22 6.07108 22H15.9287C19.2766 22 22 19.2764 22 15.9288V6.0712C22.0001 2.72362 19.2766 0 15.9287 0ZM20.0482 15.9288C20.0482 18.2002 18.2002 20.048 15.9288 20.048H6.07108C3.79979 20.0482 1.95195 18.2002 1.95195 15.9288V6.0712C1.95195 3.79992 3.79979 1.95195 6.07108 1.95195H15.9287C18.2001 1.95195 20.048 3.79992 20.048 6.0712L20.0482 15.9288Z" fill="#192329"/>
   <path d="M10.997 5.33203C7.87112 5.33203 5.32812 7.87503 5.32812 11.0009C5.32812 14.1266 7.87112 16.6695 10.997 16.6695C14.1228 16.6695 16.6658 14.1266 16.6658 11.0009C16.6658 7.87503 14.1228 5.33203 10.997 5.33203ZM10.997 14.7174C8.94755 14.7174 7.28007 13.0502 7.28007 11.0008C7.28007 8.9512 8.94742 7.28385 10.997 7.28385C13.0465 7.28385 14.7138 8.9512 14.7138 11.0008C14.7138 13.0502 13.0464 14.7174 10.997 14.7174Z" fill="#192329"/>
   <path d="M16.9119 3.67578C16.5358 3.67578 16.1664 3.82804 15.9008 4.0948C15.634 4.36026 15.4805 4.72983 15.4805 5.10721C15.4805 5.48341 15.6341 5.85286 15.9008 6.11962C16.1663 6.38508 16.5358 6.53863 16.9119 6.53863C17.2893 6.53863 17.6576 6.38508 17.9243 6.11962C18.1911 5.85286 18.3433 5.48329 18.3433 5.10721C18.3433 4.72983 18.1911 4.36026 17.9243 4.0948C17.6588 3.82804 17.2893 3.67578 16.9119 3.67578Z" fill="#192329"/>
   </g>
   <defs>
   <clipPath id="clip0_582_659">
   <rect width="22" height="22" fill="white"/>
   </clipPath>
   </defs>
   </svg>
   
   </a>
   
   <a href="https://www.tiktok.com/@produzionehiphop" target="_blank" class="item flex">   
   <svg class="h-28"    width="17" height="22" viewBox="0 0 17 22" fill="none" xmlns="http://www.w3.org/2000/svg">
   <path d="M17 9.20169C15.3289 9.2058 13.6988 8.66934 12.3399 7.66809V14.6511C12.3395 15.9444 11.9555 17.2068 11.2392 18.2694C10.523 19.332 9.50867 20.1442 8.33186 20.5974C7.15509 21.0505 5.87197 21.1231 4.65406 20.8053C3.43615 20.4876 2.3415 19.7946 1.5165 18.8192C0.691494 17.8437 0.175453 16.6323 0.0373795 15.3468C-0.100703 14.0613 0.145765 12.763 0.743827 11.6256C1.34188 10.4882 2.26302 9.56583 3.38408 8.98185C4.50512 8.39788 5.77266 8.18013 7.01718 8.35772V11.8699C6.44768 11.6855 5.83614 11.6911 5.2699 11.8858C4.70365 12.0805 4.21165 12.4544 3.86417 12.9541C3.51668 13.4539 3.33147 14.0538 3.33499 14.6684C3.3385 15.2829 3.53056 15.8806 3.88375 16.376C4.23693 16.8715 4.73317 17.2394 5.30161 17.4272C5.87005 17.6151 6.4816 17.6132 7.04895 17.4219C7.61629 17.2306 8.11041 16.8597 8.46074 16.3621C8.81106 15.8645 8.99972 15.2657 8.99972 14.6511V1H12.3399C12.3377 1.29036 12.3613 1.58033 12.4105 1.86624C12.5266 2.50447 12.7679 3.11163 13.1198 3.65057C13.4716 4.1895 13.9266 4.64889 14.4568 5.00062C15.2111 5.51407 16.0956 5.78774 17 5.78754V9.20169Z" fill="#192329"/>
   </svg>
   
   </a>
   
   <a href="https://www.youtube.com/@produzionehiphop/" target="_blank" class="item flex">   
   <svg class="h-28" width="26" height="22" viewBox="0 0 26 22" fill="none" xmlns="http://www.w3.org/2000/svg">
   <path d="M25.8548 5.24706C25.8548 5.02354 25.5147 3.2353 24.7209 2.45294C23.7003 1.3353 22.5663 1.22353 21.9992 1.22353H21.8859C18.3706 1 13.1542 1 13.0408 1C13.0408 1 7.71109 1 4.19574 1.22353H4.08235C3.51535 1.22353 2.38137 1.3353 1.36079 2.45294C0.566992 3.34706 0.226797 5.1353 0.226797 5.35883C0.226797 5.47059 0 7.48237 0 9.6059V11.5059C0 13.6294 0.226797 15.6412 0.226797 15.753C0.226797 15.9765 0.566992 17.7647 1.36079 18.547C2.26797 19.553 3.40196 19.6647 4.08235 19.7765C4.19574 19.7765 4.30913 19.7765 4.42253 19.7765C6.46371 20 12.7006 20 12.9274 20C12.9274 20 18.2572 20 21.7725 19.7765H21.8859C22.4529 19.6647 23.5869 19.553 24.6074 18.547C25.4012 17.653 25.7414 15.8647 25.7414 15.6412C25.7414 15.5294 25.9683 13.5177 25.9683 11.3942V9.49414C26.0816 7.48237 25.8548 5.35883 25.8548 5.24706ZM17.4634 10.7236L10.6595 14.3C10.5461 14.3 10.5461 14.4119 10.4327 14.4119C10.3193 14.4119 10.2059 14.4119 10.2059 14.3C10.0925 14.1883 9.97906 14.0765 9.97906 13.853V6.58825C9.97906 6.36472 10.0925 6.25295 10.2059 6.14119C10.3193 6.02942 10.5461 6.02942 10.7728 6.14119L17.5768 9.71767C17.8035 9.82943 17.917 9.9412 17.917 10.1647C17.917 10.3883 17.6901 10.6117 17.4634 10.7236Z" fill="#192329"/>
   </svg>
   
   </a>
   
   <a href="https://www.facebook.com/produzionehiphop" target="_blank" class="item flex">   
   <svg class="h-28" width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
   <g clip-path="url(#clip0_582_687)">
   <path d="M22.0378 11.0404C22.0378 4.94301 17.0948 0 10.9974 0C4.90004 0 -0.0429688 4.94301 -0.0429688 11.0404C-0.0429688 16.2179 3.52176 20.5625 8.33052 21.7558V14.4144H6.05399V11.0404H8.33052V9.58661C8.33052 5.82889 10.0312 4.08716 13.7204 4.08716C14.42 4.08716 15.6269 4.2245 16.1206 4.3614V7.4196C15.8601 7.39221 15.4074 7.37853 14.8452 7.37853C13.0351 7.37853 12.3355 8.06435 12.3355 9.84716V11.0404H15.9418L15.3222 14.4144H12.3355V22C17.8023 21.3398 22.0383 16.6851 22.0383 11.0404H22.0378Z" fill="#192329"/>
   </g>
   <defs>
   <clipPath id="clip0_582_687">
   <rect width="22" height="22" fill="white"/>
   </clipPath>
   </defs>
   </svg>
   
   
   </a>
   
   
    </div>






</div>


<div class="side shrink-0 grow-1">

<div class="cols grid grid-cols-2 gap-140">
    <div class="col">

    <div class="group">
    <a href="https://app-656e0683c1ac188b30f693df.closte.com/corsi/" class="title uppercase font-tt-eb text-16 mb-20 spacing-12 px-28 block">
Corsi
</a>

    <div class="items flex flex-col w-100-100">

<?php
$args = array(
    'post_type' => 'corsi',
    'posts_per_page' => -1,
    'orderby' => 'menu_order',
    'order' => 'ASC'
);

$corsi_query = new WP_Query($args);

if ($corsi_query->have_posts()) :
    while ($corsi_query->have_posts()) : $corsi_query->the_post();

        $corso_icona_id    = null;
        $color_class_name  = 'violet';
        $rwmb_get_value_func = 'rwmb_get_value';

        if (function_exists($rwmb_get_value_func)) {
			$corso_icona_data = $rwmb_get_value_func( 'corso_icona' );
			$corso_icona_id   = $corso_icona_data['ID'] ?? null;
            $db_color          = $rwmb_get_value_func('corso_colore');
            if (!empty($db_color)) {
                $color_class_name = $db_color;
            }
        }
        ?>

        <a href="<?php the_permalink(); ?>" class="item relative bg-brand-<?php echo esc_attr( $color_class_name ); ?>-light bg-opacity-0 px-28 py-14 rounded-8 w-100-100">
            <div class="text font-ave-d text-gray text-20">
            <?php the_title(); ?>
            </div>

            <div class="badge absolute bottom--10 right-28 animate-float-updown-very-light animation-duration-5000 opacity-0">
    
            <?php if ( $corso_icona_id ) : ?>
                <?php load_img( class: 'img h-60 shadow-' . $color_class_name, high: $corso_icona_id ); ?>
            <?php endif; ?>

            </div>
        </a> <!-- item -->

    <?php
    endwhile;
    wp_reset_postdata();
endif;
?>


</div>



    </div>


</div> <!-- col -->



<div class="col flex flex-col gap-60">

<div class="group">
    <a href="https://app-656e0683c1ac188b30f693df.closte.com/" class="title uppercase font-tt-eb text-16 mb-20 spacing-12 block">
Produzione Hip Hop
</a>

    <div class="items flex flex-col">
        <a href="https://app-656e0683c1ac188b30f693df.closte.com/" class="item py-14">
            <div class="text font-ave-d text-gray text-20">
            Homepage
            </div>
</a> <!-- item -->

<a href="https://app-656e0683c1ac188b30f693df.closte.com/scuola/" class="item py-14">
            <div class="text font-ave-d text-gray text-20">
            La scuola
            </div>
</a> <!-- item -->


<a href="https://app-656e0683c1ac188b30f693df.closte.com/contatti/" class="item py-14">
            <div class="text font-ave-d text-gray text-20">
            Contatti
            </div>
</a> <!-- item -->


<a href="https://app-656e0683c1ac188b30f693df.closte.com/blog/" class="item py-14">
            <div class="text font-ave-d text-gray text-20">
            Blog
            </div>
</a> <!-- item -->



    </div>
</div> <!-- group -->



<div class="group">
    <a href="https://app-656e0683c1ac188b30f693df.closte.com/contatti/" class="title uppercase font-tt-eb text-16 mb-20 spacing-12 block">
Contatti
</a>

    <div class="items flex flex-col">
        <a href="https://wa.me/393517778431" class="item py-14 flex items-center gap-24">
            <div class="icon">
            <svg class="h-28" width="21" height="21" viewBox="0 0 21 21" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_582_643)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M17.1064 2.92389C15.224 1.03937 12.7208 0.00109271 10.0537 0C4.55839 0 0.0857577 4.47228 0.0836128 9.9693C0.082844 11.7266 0.541943 13.4417 1.41445 14.9536L0 20.12L5.28529 18.7337C6.74149 19.5279 8.38109 19.9466 10.0498 19.9471H10.0538C10.0541 19.9471 10.0535 19.9471 10.0538 19.9471C15.5486 19.9471 20.0216 15.4744 20.0239 9.97729C20.0249 7.31327 18.9887 4.80836 17.1064 2.92389ZM10.0537 18.2634H10.0504C8.56352 18.2628 7.10506 17.8632 5.83274 17.1083L5.53018 16.9287L2.39381 17.7514L3.23099 14.6935L3.03393 14.3799C2.20444 13.0606 1.76634 11.5357 1.767 9.96991C1.76878 5.40097 5.48627 1.68387 10.0571 1.68387C12.2705 1.68456 14.3511 2.54768 15.9157 4.11404C17.4802 5.68038 18.3413 7.76237 18.3405 9.97661C18.3386 14.5459 14.6212 18.2634 10.0537 18.2634Z" fill="#636C74"></path>
<path fill-rule="evenodd" clip-rule="evenodd" d="M14.5958 12.0552C14.3468 11.9304 13.1219 11.3278 12.8936 11.2447C12.6653 11.1615 12.4992 11.1199 12.3331 11.3694C12.1671 11.6187 11.6896 12.1798 11.5443 12.3462C11.399 12.5123 11.2536 12.5333 11.0046 12.4084C10.7554 12.2837 9.95278 12.0207 9.00129 11.172C8.26063 10.5114 7.76073 9.69569 7.61532 9.4462C7.47003 9.19689 7.5999 9.06209 7.72464 8.93783C7.8367 8.8262 7.97376 8.64688 8.09832 8.50141C8.22282 8.356 8.26434 8.25198 8.34745 8.08584C8.43049 7.91951 8.38897 7.77404 8.32666 7.64942C8.26434 7.52473 7.76616 6.29851 7.55862 5.79964C7.35632 5.31395 7.15097 5.37976 6.99806 5.37202C6.85295 5.36478 6.68663 5.36328 6.52061 5.36328C6.35453 5.36328 6.08461 5.42565 5.85632 5.67499C5.62798 5.92441 4.98438 6.52715 4.98438 7.75319C4.98438 8.97941 5.87704 10.164 6.00161 10.3302C6.12613 10.4966 7.75823 13.0127 10.2573 14.0918C10.8516 14.3485 11.3156 14.5018 11.6774 14.6165C12.2742 14.8062 12.8173 14.7794 13.2465 14.7152C13.7252 14.6438 14.7204 14.1126 14.928 13.5308C15.1356 12.9488 15.1356 12.4499 15.0733 12.3461C15.011 12.2422 14.8449 12.1798 14.5958 12.0552Z" fill="#636C74"></path>
</g>
<defs>
<clipPath id="clip0_582_643">
<rect width="20.0239" height="20.12" fill="white"></rect>
</clipPath>
</defs>
</svg>
            </div>
            <div class="text font-ave-d text-gray text-20">
            +39 340 615 0433
            </div>
</a> <!-- item -->

<a href="mailto:<EMAIL>" class="item py-14 flex items-center gap-24">
    <div class="icon">
    <svg class="h-28" width="22" height="20" viewBox="0 0 22 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M18.7806 1.02899C20.2069 1.05538 21.5083 2.26296 21.5803 3.71468C21.5822 3.74957 21.5831 3.78444 21.5841 3.81932C21.6078 7.59948 21.5841 11.3796 21.5841 15.1598C21.5755 16.5851 20.3746 17.9058 18.8857 17.9794C18.8507 17.9812 18.8157 17.9822 18.7806 17.9822C12.4586 18.0218 9.16824 17.9831 2.84621 17.9831C1.41132 17.9737 0.0881878 16.7793 0.0143122 15.2974C0.012418 15.2625 0.0114707 15.2277 0.0105236 15.1918C-0.0131545 11.4117 0.0105236 7.63155 0.0105236 3.85137C0.0190477 2.42698 1.22 1.10534 2.70888 1.03182C2.74392 1.03087 2.77896 1.02993 2.81401 1.02899C9.14647 0.990338 12.4481 0.990338 18.7806 1.02899ZM2.85189 2.91246C2.35939 2.91529 1.91235 3.33384 1.90288 3.84289C1.8792 7.61831 1.8792 11.3938 1.90288 15.1692C1.91235 15.6538 2.32624 16.0893 2.83768 16.0987C9.15405 16.1383 12.4406 16.1383 18.7569 16.0987C19.2456 16.0902 19.6822 15.6698 19.6918 15.1692C19.7154 11.3938 19.7154 7.61831 19.6918 3.84289C19.6822 3.35835 19.2684 2.92189 18.7569 2.91246C12.4453 2.87381 9.16448 2.91246 2.85189 2.91246ZM16.9129 4.79783C17.1856 4.82611 17.2633 4.87419 17.3968 4.96751C17.8392 5.27765 17.9215 5.99408 17.5313 6.39381C17.4859 6.4409 17.4717 6.45037 17.4206 6.49181L11.3646 10.9366C11.0265 11.1694 10.5738 11.1741 10.229 10.9366L4.17408 6.49181C3.98654 6.33913 3.95245 6.27499 3.89278 6.15342C3.63516 5.63115 3.99128 4.89398 4.60788 4.80631C4.85318 4.77143 5.10414 4.84308 5.30966 4.98448L10.7973 9.00598L16.285 4.98448C16.285 4.98448 16.5994 4.79123 16.9129 4.79783Z" fill="#636C74"></path>
</svg>
    </div>
            <div class="text font-ave-d text-gray text-20">
            <EMAIL>
            </div>
</a> <!-- item -->





    </div>
</div> <!-- group -->

</div> <!-- col -->
    </div>

</div>




        <div class="shapes absolute inset-0 z--10">
        <div class="shape animate-float-updown-light animation-duration-5000 absolute right--140 top-240">
        <svg class="h-80 shadow-blue" width="84" height="81" viewBox="0 0 84 81" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_585_722)">
<path d="M61.0714 73.3806L82.4799 15.6238C85.5403 7.36736 77.2074 -0.528202 69.1278 2.97239L7.62662 29.6185C0.650762 32.6409 -0.534643 42.0341 5.47163 46.6948L45.5643 77.8054C50.9011 81.9466 58.7236 79.7145 61.0714 73.3806Z" fill="#1289ED"/>
</g>
<defs>
<clipPath id="clip0_585_722">
<rect width="83" height="81" fill="white" transform="translate(0.824219)"/>
</clipPath>
</defs>
</svg>


    </div>
        </div>



</div>


</section>


<section class="footer-loghi mb-60">

<div class="container-l mx-auto">


<div class="body">

<div class="items grid grid-cols-8 gap-40 items-center">

<div class="item">
        <?php load_img(class: 'img max-w-80-100', high: 88); ?>
</div>

<div class="item">
        <?php load_img(class: 'img max-w-80-100', high: 89); ?>
</div>

<div class="item">
        <?php load_img(class: 'img max-w-80-100', high: 90); ?>
</div>

<div class="item">
        <?php load_img(class: 'img max-w-80-100', high: 91); ?>
</div>


<div class="item">
        <?php load_img(class: 'img max-w-80-100', high: 92); ?>
</div>

<div class="item">
        <?php load_img(class: 'img max-w-80-100', high: 93); ?>
</div>

<div class="item">
        <?php load_img(class: 'img max-w-80-100', high: 94); ?>
</div>

<div class="item">
        <?php load_img(class: 'img max-w-80-100', high: 95); ?>
</div>



</div>

</div>



</div>



</section>



<section class="footer-bottom relative">

<div class="container-l mx-auto flex justify-between items-center gap-200 py-40 border-top-2 border-solid border-gray">

<div class="main">
    <div class="legal mb-16 font-ave-m text-18 text-gray leading-15">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Pellentesque in euismod leo, vitae mollis arcu.
    </div>
    <div class="signature font-ave-m text-gray text-18">
    Lorem ipsum dolor sit amet
    </div>
</div>

<div class="side">
    <div class="items flex gap-32">


        <div class="item">
        <svg class="h-40" width="73" height="40" viewBox="0 0 73 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_605_730)">
<path d="M70.0461 24.4876C68.9124 24.4876 67.9936 25.4148 67.9936 26.5587C67.9936 27.7024 68.9124 28.6299 70.0461 28.6299C71.1796 28.6299 72.0988 27.7025 72.0988 26.5587C72.0988 25.4147 71.1796 24.4876 70.0461 24.4876ZM63.2931 22.8863C63.2931 21.3202 61.9665 20.0509 60.3301 20.0509C58.6935 20.0509 57.3669 21.3203 57.3669 22.8863C57.3669 24.4524 58.6933 25.722 60.3301 25.722C61.9667 25.722 63.2931 24.4524 63.2931 22.8863ZM63.3041 17.3754H66.5742V28.3968H63.3041V27.6924C62.3803 28.3282 61.2651 28.702 60.0623 28.702C56.8794 28.702 54.2989 26.0982 54.2989 22.8862C54.2989 19.6743 56.8794 17.0707 60.0623 17.0707C61.2651 17.0707 62.3803 17.4444 63.3041 18.0804V17.3754ZM37.1338 18.811V17.3755H33.7857V28.3968H37.1413V23.2511C37.1413 21.515 39.0061 20.5819 40.3 20.5819C40.3133 20.5819 40.3256 20.5832 40.3389 20.5834V17.3764C39.0107 17.3764 37.7894 17.95 37.1338 18.811ZM28.7937 22.8863C28.7937 21.3203 27.4669 20.051 25.8304 20.051C24.194 20.051 22.8672 21.3203 22.8672 22.8863C22.8672 24.4524 24.1938 25.7221 25.8304 25.7221C27.4671 25.7221 28.7937 24.4524 28.7937 22.8863ZM28.8044 17.3754H32.0748V28.3969H28.8044V27.6925C27.8806 28.3282 26.7654 28.702 25.5628 28.702C22.3797 28.702 19.7993 26.0982 19.7993 22.8863C19.7993 19.6743 22.3797 17.0708 25.5628 17.0708C26.7654 17.0708 27.8806 17.4445 28.8044 18.0804V17.3754ZM48.488 17.079C47.1816 17.079 45.9453 17.4882 45.1187 18.6173V17.3761H41.8626V28.3969H45.1588V22.6051C45.1588 20.9291 46.2726 20.1084 47.6137 20.1084C49.051 20.1084 49.8772 20.9748 49.8772 22.5824V28.3969H53.1435V21.3883C53.1435 18.8234 51.1225 17.079 48.488 17.079ZM15.0358 28.3969H18.4586V12.4639H15.0358V28.3969ZM0 28.4013H3.62441V12.461H0V28.4013ZM12.677 12.461C12.677 15.9119 11.3442 19.1222 8.96937 21.5083L13.9793 28.4019H9.50271L4.05801 20.9101L5.4632 19.8482C7.79353 18.0867 9.13016 15.3943 9.13016 12.4609L12.677 12.461Z" fill="#121212"/>
</g>
<defs>
<clipPath id="clip0_605_730">
<rect width="73" height="17" fill="white" transform="translate(0 12)"/>
</clipPath>
</defs>
</svg>

        </div> <!-- item -->


        <div class="item">
        <svg class="h-40" width="96" height="40" viewBox="0 0 96 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_605_716)">
<path fill-rule="evenodd" clip-rule="evenodd" d="M74.3112 18.0907C74.0244 19.9746 72.5856 19.9746 71.1939 19.9746H70.4021L70.9576 16.4572C70.9911 16.2447 71.1744 16.0881 71.3897 16.0881H71.7527C72.6997 16.0881 73.5946 16.0881 74.056 16.6278C74.332 16.9507 74.4155 17.4293 74.3112 18.0907ZM73.7055 13.1758H68.4571C68.0984 13.1758 67.7929 13.4369 67.737 13.7914L65.6148 27.2486C65.5729 27.514 65.7781 27.7541 66.047 27.7541H68.7397C68.9909 27.7541 69.2048 27.5713 69.2437 27.3232L69.8457 23.5082C69.9016 23.1536 70.207 22.8925 70.5658 22.8925H72.2266C75.6835 22.8925 77.6785 21.2195 78.2 17.9038C78.4347 16.4537 78.2095 15.3142 77.5304 14.5165C76.7841 13.6395 75.4613 13.1758 73.7055 13.1758Z" fill="#2790C3"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M36.8815 18.0907C36.5947 19.9746 35.1558 19.9746 33.7644 19.9746H32.9726L33.5278 16.4572C33.5616 16.2447 33.7448 16.0881 33.96 16.0881H34.323C35.2702 16.0881 36.1651 16.0881 36.6263 16.6278C36.9025 16.9507 36.986 17.4293 36.8815 18.0907ZM36.276 13.1758H31.0276C30.6688 13.1758 30.3634 13.4369 30.3075 13.7914L28.1851 27.2486C28.1433 27.514 28.3486 27.7541 28.6172 27.7541H31.1231C31.4819 27.7541 31.7876 27.493 31.8435 27.1387L32.4162 23.5082C32.4718 23.1536 32.7775 22.8925 33.1363 22.8925H34.7971C38.2538 22.8925 40.249 21.2195 40.7705 17.9038C41.0052 16.4537 40.78 15.3142 40.1009 14.5165C39.3544 13.6395 38.0316 13.1758 36.276 13.1758Z" fill="#121212"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M48.4606 22.9247C48.2179 24.3607 47.0779 25.3247 45.624 25.3247C44.8952 25.3247 44.3116 25.0903 43.9366 24.6463C43.5649 24.2062 43.425 23.5793 43.5427 22.8811C43.7691 21.4581 44.9273 20.4629 46.359 20.4629C47.0727 20.4629 47.6521 20.6997 48.0346 21.1476C48.4196 21.5989 48.5708 22.2301 48.4606 22.9247ZM51.9629 18.0334H49.4499C49.2345 18.0334 49.0512 18.1898 49.0178 18.4028L48.9071 19.1055L48.7314 18.8507C48.1872 18.0608 46.9744 17.7969 45.7632 17.7969C42.9867 17.7969 40.615 19.9008 40.1534 22.8518C39.9131 24.3239 40.2542 25.7306 41.0889 26.7126C41.8557 27.6148 42.9502 27.9903 44.2541 27.9903C46.492 27.9903 47.7332 26.5527 47.7332 26.5527L47.6209 27.2511C47.5789 27.5167 47.7841 27.757 48.053 27.757H50.3162C50.675 27.757 50.9804 27.4961 51.0363 27.1416L52.395 18.5389C52.437 18.2733 52.2317 18.0334 51.9629 18.0334Z" fill="#121212"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M85.898 22.9247C85.6553 24.3607 84.5156 25.3247 83.0616 25.3247C82.3326 25.3247 81.7492 25.0903 81.374 24.6463C81.0022 24.2062 80.8624 23.5793 80.9803 22.8811C81.2065 21.4581 82.3646 20.4629 83.7966 20.4629C84.5101 20.4629 85.0897 20.6997 85.472 21.1476C85.857 21.5989 86.0082 22.2301 85.898 22.9247ZM89.4002 18.0334H86.8872C86.6719 18.0334 86.4886 18.1898 86.4551 18.4028L86.3445 19.1055L86.169 18.8507C85.6246 18.0608 84.4118 17.7969 83.2006 17.7969C80.4243 17.7969 78.0524 19.9008 77.5908 22.8518C77.3507 24.3239 77.6918 25.7306 78.5263 26.7126C79.2933 27.6148 80.3876 27.9903 81.6915 27.9903C83.9294 27.9903 85.1706 26.5527 85.1706 26.5527L85.0585 27.2511C85.0163 27.5167 85.2215 27.757 85.4906 27.757H87.7536C88.1124 27.757 88.4178 27.4961 88.474 27.1416L89.8324 18.5389C89.8743 18.2733 89.6691 18.0334 89.4002 18.0334Z" fill="#2790C3"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M65.3502 18.0312H62.8242C62.5827 18.0312 62.3567 18.1511 62.2211 18.3509L58.7368 23.4827L57.26 18.5513C57.1678 18.2428 56.8838 18.0312 56.5616 18.0312H54.0785C53.7787 18.0312 53.5676 18.326 53.6643 18.6098L56.4457 26.7739L53.8297 30.4652C53.6242 30.7548 53.8316 31.1557 54.1866 31.1557H56.7102C56.9493 31.1557 57.1732 31.0385 57.3095 30.8417L65.7099 18.7182C65.9108 18.428 65.7033 18.0312 65.3502 18.0312Z" fill="#121212"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M92.3547 13.5493L90.2007 27.2525C90.159 27.5181 90.3642 27.758 90.6328 27.758H92.7993C93.1581 27.758 93.4635 27.4969 93.5194 27.1426L95.6432 13.6854C95.6852 13.4198 95.48 13.1797 95.2111 13.1797H92.7868C92.5715 13.1797 92.3882 13.3365 92.3547 13.5493Z" fill="#2790C3"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.1507 30.2011L10.8562 25.7275L10.9017 25.4796C10.9357 25.264 11.0461 25.0668 11.2124 24.9249C11.3786 24.7829 11.5907 24.7045 11.8093 24.7045H12.3695C13.3034 24.7045 14.1612 24.6048 14.9196 24.4081C15.7286 24.1984 16.4482 23.8717 17.0582 23.4379C17.705 22.978 18.2482 22.3833 18.673 21.671C19.1211 20.9195 19.4502 20.0147 19.6512 18.9821C19.8288 18.0702 19.862 17.2538 19.7505 16.5554C19.6323 15.8176 19.3481 15.188 18.9058 14.6837C18.6376 14.3783 18.2944 14.1136 17.8859 13.8971L17.8762 13.8919L17.876 13.8801C18.0187 12.9699 18.0133 12.2106 17.8597 11.557C17.7054 10.902 17.3931 10.3125 16.9042 9.75542C15.8911 8.60106 14.0485 8.01562 11.4277 8.01562H4.23006C3.98947 8.01562 3.75596 8.10172 3.57292 8.2581C3.38989 8.41449 3.26865 8.63172 3.23091 8.86948L0.233915 27.8744C0.206554 28.0478 0.256323 28.224 0.370485 28.357C0.484647 28.4908 0.650701 28.5674 0.82619 28.5674H5.29149L5.28748 28.5887L4.98084 30.5332C4.95726 30.6844 5.00066 30.8377 5.09996 30.9537C5.19926 31.07 5.34385 31.137 5.4967 31.137H9.24188C9.45204 31.137 9.6556 31.0618 9.81528 30.9252C9.97497 30.7886 10.0811 30.5992 10.1139 30.3919L10.1507 30.2011Z" fill="#121212"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.58752 13.9074C7.63493 13.6062 7.82811 13.3597 8.08828 13.235C8.20669 13.1783 8.33877 13.1467 8.47723 13.1467H14.119C14.7875 13.1467 15.4107 13.1906 15.9806 13.2824C16.1433 13.3085 16.3016 13.3387 16.4556 13.3729C16.6096 13.4071 16.7589 13.4451 16.9035 13.4876C16.9759 13.5086 17.0469 13.5307 17.117 13.5541C17.3967 13.647 17.6571 13.7565 17.8968 13.8834C18.1794 12.082 17.8949 10.8562 16.9205 9.74593C15.8478 8.52364 13.9098 8 11.4303 8H4.23271C3.72606 8 3.29465 8.36867 3.21563 8.86919L0.218634 27.8741C0.15943 28.2492 0.449317 28.5881 0.828835 28.5881H5.2722L7.58752 13.9074Z" fill="#121212"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M17.891 13.8828C17.8696 14.0203 17.8458 14.1602 17.8175 14.3043C16.8685 19.1777 13.6211 20.8625 9.47348 20.8625H7.36148C6.85482 20.8625 6.42695 21.2314 6.34793 21.732L4.95983 30.5326C4.90817 30.8604 5.16173 31.1572 5.49361 31.1572H9.23902C9.68222 31.1572 10.0596 30.8347 10.1287 30.3967L10.1655 30.2064L10.871 25.7323L10.9165 25.4852C10.9857 25.0471 11.363 24.7247 11.8063 24.7247H12.3665C15.9949 24.7247 18.836 23.2505 19.6662 18.9873C20.013 17.206 19.8337 15.7191 18.9164 14.6737C18.639 14.3576 18.2937 14.096 17.891 13.8828Z" fill="#2790C3"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M16.9014 13.4893C16.7568 13.4468 16.6075 13.4088 16.4535 13.3746C16.2995 13.3404 16.1412 13.3102 15.9784 13.2841C15.4086 13.1923 14.7854 13.1484 14.1169 13.1484H8.47511C8.33666 13.1484 8.20457 13.18 8.08616 13.2367C7.82599 13.3614 7.63281 13.6079 7.5854 13.9091L6.38623 21.5123L6.35156 21.7342C6.43057 21.2337 6.85845 20.8648 7.3651 20.8648H9.4771C13.6247 20.8648 16.8722 19.18 17.8211 14.3066C17.8494 14.1625 17.8732 14.0226 17.8947 13.8851C17.655 13.7582 17.3946 13.6487 17.1149 13.5558C17.0448 13.5324 16.9738 13.5103 16.9014 13.4893Z" fill="#121212"/>
</g>
<defs>
<clipPath id="clip0_605_716">
<rect width="96" height="24" fill="white" transform="translate(0 8)"/>
</clipPath>
</defs>
</svg>


        </div> <!-- item -->


        <div class="item">
        <svg class="h-40" width="61" height="40" viewBox="0 0 61 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_605_711)">
<path d="M60.8 4.42188H0V34.8219H60.8V4.42188Z" fill="#F6F6F6"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M56.24 1C53.2 1 21.0143 0.999999 3.04 1C1.52 1 0 2.52 0 4.04V8.6H60.8C60.8 8.6 60.8 15.6573 60.8 5.56C60.8 2.52 59.28 1 56.24 1Z" fill="#0059A4"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 31.3984C0 31.3984 3.67222e-07 31.3984 0 34.4384C-1.75879e-07 37.4784 1.52 38.9984 4.56 38.9984C21.5168 38.9984 53.2 38.9984 56.24 38.9984C59.28 38.9984 60.8 37.4784 60.8 34.4384V31.3984H0Z" fill="#FFA100"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M42.4137 12.3178C41.5616 11.9912 40.2264 11.6406 38.5589 11.6406C34.309 11.6406 31.3154 13.8276 31.2899 16.9617C31.2659 19.2785 33.427 20.5709 35.0585 21.3423C36.7327 22.1325 37.2955 22.6366 37.2874 23.3423C37.2769 24.4231 35.9505 24.9169 34.7143 24.9169C32.9928 24.9169 32.0783 24.6725 30.6657 24.0705L30.1115 23.8143L29.5078 27.4237C30.5123 27.8738 32.3701 28.2638 34.2989 28.2839C38.82 28.2839 41.755 26.1221 41.7884 22.7752C41.8046 20.941 40.6586 19.5451 38.1773 18.3944C36.6739 17.6485 35.7533 17.1508 35.763 16.3955C35.763 15.7253 36.5423 15.0087 38.2261 15.0087C39.6326 14.9864 40.6514 15.2998 41.4452 15.6264L41.8306 15.8125L42.4137 12.3178Z" fill="#0059A4"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M53.4399 11.9336H50.1164C49.0869 11.9336 48.3164 12.2207 47.8643 13.2707L41.4766 28.0471H45.993C45.993 28.0471 46.7314 26.0603 46.8984 25.6241C47.392 25.6241 51.7795 25.6311 52.4067 25.6311C52.5355 26.1955 52.93 28.0471 52.93 28.0471H56.921L53.4399 11.9336ZM48.1362 22.3262C48.492 21.3972 49.8498 17.8189 49.8498 17.8189C49.8244 17.8618 50.2029 16.8853 50.4201 16.28L50.7108 17.6701C50.7108 17.6701 51.5343 21.5189 51.7065 22.326L48.1362 22.3262Z" fill="#0059A4"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M22.5117 28.0457L25.2008 11.9219H29.5019L26.8109 28.0457H22.5117Z" fill="#0059A4"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M18.9053 11.9336L14.6944 22.9288L14.2458 20.6944C13.4619 18.1186 11.0195 15.3278 8.28906 13.9307L12.1394 28.0317L16.69 28.0265L23.4612 11.9336H18.9053Z" fill="#0059A4"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M10.7872 11.9219H3.85177L3.79688 12.2573C9.19256 13.592 12.7628 16.8173 14.2449 20.6926L12.7368 13.2831C12.4765 12.2621 11.7213 11.9575 10.7872 11.9219Z" fill="#FFA100"/>
</g>
<defs>
<clipPath id="clip0_605_711">
<rect width="60.8" height="38" fill="white" transform="translate(0 1)"/>
</clipPath>
</defs>
</svg>


        </div> <!-- item -->

        <div class="item">
        <svg class="h-40" width="61" height="40" viewBox="0 0 61 40" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 3.85C0 2.2825 1.2825 1 2.85 1H57.95C59.5175 1 60.8 2.2825 60.8 3.85V36.15C60.8 37.7175 59.5175 39 57.95 39H2.85C1.2825 39 0 37.7175 0 36.15V3.85Z" fill="black"/>
<mask id="mask0_605_712" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="0" y="1" width="61" height="38">
<path d="M0 3.85C0 2.2825 1.2825 1 2.85 1H57.95C59.5175 1 60.8 2.2825 60.8 3.85V36.15C60.8 37.7175 59.5175 39 57.95 39H2.85C1.2825 39 0 37.7175 0 36.15V3.85Z" fill="white"/>
</mask>
<g mask="url(#mask0_605_712)">
<path d="M0 3.85C0 2.2825 1.2825 1 2.85 1H57.95C59.5175 1 60.8 2.2825 60.8 3.85V36.15C60.8 37.7175 59.5175 39 57.95 39H2.85C1.2825 39 0 37.7175 0 36.15V3.85Z" fill="black" stroke="black" stroke-width="0.95" stroke-miterlimit="10"/>
</g>
<mask id="mask1_605_712" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="15" y="29" width="33" height="5">
<path d="M19.6134 33.2009V31.3959C19.6134 30.7309 19.2334 30.3034 18.5209 30.2559C18.1409 30.2559 17.8084 30.3509 17.5234 30.7784C17.3334 30.4459 17.0009 30.2559 16.5734 30.2559C16.2884 30.2559 15.9559 30.3509 15.7184 30.6834V30.3509H15.1484V33.2009H15.7184V31.5859C15.7184 31.1109 16.0034 30.8259 16.4309 30.8259C16.8584 30.8259 17.0484 31.1109 17.0484 31.5859V33.2009H17.6184V31.5859C17.6184 31.1109 17.9034 30.8259 18.3309 30.8259C18.7584 30.8259 18.9959 31.1109 18.9959 31.5859V33.2009H19.6134ZM28.8284 30.3509H27.7834V29.4959H27.2134V30.3509H26.5959V30.8734H27.2134V32.2034C27.2134 32.8684 27.4509 33.2484 28.1634 33.2484C28.4484 33.2484 28.7334 33.1534 28.9234 33.0109L28.7334 32.4884C28.5434 32.5834 28.3534 32.6309 28.2109 32.6309C27.9259 32.6309 27.7834 32.4409 27.7834 32.1559V30.8259H28.8284V30.3509V30.3509ZM34.1009 30.3034C33.7684 30.3034 33.4834 30.4459 33.3409 30.7309V30.3984H32.7709V33.2484H33.3409V31.6334C33.3409 31.1584 33.5784 30.8734 33.9584 30.8734C34.1009 30.8734 34.1959 30.8734 34.3384 30.9684L34.5284 30.4459C34.3859 30.3034 34.1959 30.3034 34.1009 30.3034ZM26.1209 30.5884C25.8359 30.3984 25.4084 30.3034 24.9809 30.3034C24.2684 30.3034 23.8409 30.6359 23.8409 31.2059C23.8409 31.6809 24.1734 31.9659 24.7909 32.0134L25.0759 32.0609C25.4084 32.1084 25.5984 32.2034 25.5984 32.3934C25.5984 32.6309 25.3609 32.7734 24.9334 32.7734C24.5059 32.7734 24.2209 32.6309 23.9834 32.4884L23.6984 32.9159C24.1259 33.2009 24.6009 33.2959 24.9334 33.2959C25.7409 33.2959 26.2159 32.9159 26.2159 32.3934C26.2159 31.9184 25.8834 31.6334 25.2659 31.5384L24.9809 31.4909C24.6959 31.4434 24.4584 31.3959 24.4584 31.2059C24.4584 31.0159 24.6959 30.8734 25.0284 30.8734C25.4084 30.8734 25.7409 31.0159 25.9309 31.1109L26.1209 30.5884ZM34.7184 31.7759C34.7184 32.6309 35.2884 33.2484 36.1909 33.2484C36.6184 33.2484 36.9034 33.1534 37.1884 32.9159L36.9034 32.4884C36.6659 32.6309 36.4284 32.7259 36.1434 32.7259C35.6684 32.7259 35.2884 32.3459 35.2884 31.7759C35.2884 31.2059 35.6684 30.8259 36.1434 30.8259C36.4284 30.8259 36.6659 30.9209 36.9034 31.0634L37.1884 30.6359C36.9034 30.3984 36.6184 30.3034 36.1909 30.3034C35.2884 30.3034 34.7184 30.9209 34.7184 31.7759ZM30.7284 30.3034C29.9209 30.3034 29.3509 30.9209 29.3509 31.7759C29.3509 32.6784 29.9209 33.2484 30.7759 33.2484C31.2034 33.2484 31.5834 33.1534 31.9159 32.8684L31.6309 32.4409C31.3934 32.6309 31.1084 32.7259 30.8234 32.7259C30.4434 32.7259 30.0159 32.4884 29.9684 32.0134H32.1059C32.1059 31.9184 32.1059 31.8709 32.1059 31.7759C32.0584 30.8734 31.5359 30.3034 30.7284 30.3034ZM30.7284 30.8259C31.1559 30.8259 31.4409 31.1109 31.4884 31.5384H29.9684C30.0159 31.1109 30.3009 30.8259 30.7284 30.8259ZM23.0809 31.7759V30.3509H22.5109V30.6834C22.3209 30.4459 21.9884 30.2559 21.6084 30.2559C20.8009 30.2559 20.2309 30.8734 20.2309 31.7284C20.2309 32.5834 20.8484 33.2009 21.6084 33.2009C21.9884 33.2009 22.3209 33.0584 22.5109 32.7734V33.1059H23.0809V31.7759ZM20.7534 31.7759C20.7534 31.2534 21.0859 30.8259 21.6559 30.8259C22.1784 30.8259 22.5109 31.2534 22.5109 31.7759C22.5109 32.3459 22.1784 32.7259 21.6559 32.7259C21.1334 32.7259 20.7534 32.2984 20.7534 31.7759ZM42.6509 30.3034C42.3184 30.3034 42.0334 30.4459 41.8909 30.7309V30.3984H41.3209V33.2484H41.8909V31.6334C41.8909 31.1584 42.1284 30.8734 42.5084 30.8734C42.6509 30.8734 42.7459 30.8734 42.8884 30.9684L43.0784 30.4459C42.9359 30.3034 42.7459 30.3034 42.6509 30.3034ZM47.2109 32.7734C47.2584 32.7734 47.3059 32.7734 47.3059 32.7734C47.3534 32.7734 47.3534 32.8209 47.4009 32.8209C47.4484 32.8684 47.4484 32.8684 47.4484 32.9159C47.4484 32.9634 47.4484 32.9634 47.4484 33.0109C47.4484 33.0584 47.4484 33.1059 47.4484 33.1059C47.4484 33.1534 47.4009 33.1534 47.4009 33.2009C47.3534 33.2484 47.3534 33.2484 47.3059 33.2484C47.2584 33.2484 47.2109 33.2484 47.2109 33.2484C47.1634 33.2484 47.1159 33.2484 47.1159 33.2484C47.0684 33.2484 47.0684 33.2009 47.0209 33.2009C46.9734 33.1534 46.9734 33.1534 46.9734 33.1059C46.9734 33.0584 46.9734 33.0584 46.9734 33.0109C46.9734 32.9634 46.9734 32.9159 46.9734 32.9159C46.9734 32.8684 47.0209 32.8684 47.0209 32.8209C47.0684 32.7734 47.0684 32.7734 47.1159 32.7734C47.1159 32.7734 47.1634 32.7734 47.2109 32.7734ZM47.2109 33.2959C47.2584 33.2959 47.2584 33.2959 47.3059 33.2959C47.3534 33.2959 47.3534 33.2484 47.3534 33.2484L47.4009 33.2009C47.4009 33.1534 47.4009 33.1534 47.4009 33.1059C47.4009 33.0584 47.4009 33.0584 47.4009 33.0109C47.4009 32.9634 47.3534 32.9634 47.3534 32.9634L47.3059 32.9159C47.2584 32.9159 47.2584 32.9159 47.2109 32.9159C47.1634 32.9159 47.1634 32.9159 47.1159 32.9159C47.0684 32.9159 47.0684 32.9634 47.0684 32.9634L47.0209 33.0109C47.0209 33.0584 47.0209 33.0584 47.0209 33.1059C47.0209 33.1534 47.0209 33.1534 47.0209 33.2009C47.0209 33.2484 47.0684 33.2484 47.0684 33.2484L47.1159 33.2959C47.1634 33.2959 47.1634 33.2959 47.2109 33.2959ZM47.2109 32.9159C47.2584 32.9159 47.2584 32.9159 47.3059 32.9159L47.3534 32.9634V33.0109C47.3534 33.0109 47.3059 33.0109 47.3059 33.0584L47.4009 33.1534H47.3534L47.2584 33.0584H47.2109V33.1534H47.1634V32.8684H47.2109V32.9159V32.9159ZM47.1634 32.9634L47.2584 33.0109C47.2584 33.0109 47.2584 33.0109 47.2584 32.9634C47.2584 32.9634 47.2584 32.9634 47.2584 32.9159C47.2584 32.9159 47.2584 32.9159 47.1634 32.9634ZM40.4659 31.7759V30.3509H39.8959V30.6834C39.7059 30.4459 39.3734 30.2559 38.9934 30.2559C38.1859 30.2559 37.6159 30.8734 37.6159 31.7284C37.6159 32.5834 38.2334 33.2009 38.9934 33.2009C39.3734 33.2009 39.7059 33.0584 39.8959 32.7734V33.1059H40.4659V31.7759ZM38.1859 31.7759C38.1859 31.2534 38.5184 30.8259 39.0884 30.8259C39.6109 30.8259 39.9434 31.2534 39.9434 31.7759C39.9434 32.3459 39.6109 32.7259 39.0884 32.7259C38.5184 32.7259 38.1859 32.2984 38.1859 31.7759ZM46.1659 31.7759V29.2109H45.5959V30.6834C45.4059 30.4459 45.0734 30.2559 44.6934 30.2559C43.8859 30.2559 43.3159 30.8734 43.3159 31.7284C43.3159 32.5834 43.9334 33.2009 44.6934 33.2009C45.0734 33.2009 45.4059 33.0584 45.5959 32.7734V33.1059H46.1659V31.7759ZM43.8859 31.7759C43.8859 31.2534 44.2184 30.8259 44.7884 30.8259C45.3109 30.8259 45.6434 31.2534 45.6434 31.7759C45.6434 32.3459 45.3109 32.7259 44.7884 32.7259C44.2184 32.7259 43.8859 32.2984 43.8859 31.7759Z" fill="white"/>
</mask>
<g mask="url(#mask1_605_712)">
<path d="M49.8709 26.8359H12.7734V35.7184H49.8709V26.8359Z" fill="white"/>
</g>
<mask id="mask2_605_712" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="25" y="8" width="11" height="18">
<path d="M35.7209 8.83594H25.4609V25.6034H35.7209V8.83594Z" fill="white"/>
</mask>
<g mask="url(#mask2_605_712)">
<path d="M38.0959 6.46094H23.0859V27.9784H38.0959V6.46094Z" fill="#FF5F00"/>
</g>
<mask id="mask3_605_712" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="13" y="6" width="18" height="22">
<path d="M26.5442 17.1908C26.5442 13.7708 28.1592 10.7783 30.6292 8.83078C28.8242 7.40578 26.5442 6.55078 24.0267 6.55078C18.1367 6.55078 13.3867 11.3008 13.3867 17.1908C13.3867 23.0808 18.1367 27.8308 24.0267 27.8308C26.4967 27.8308 28.7767 26.9758 30.6292 25.5508C28.1117 23.6033 26.5442 20.5633 26.5442 17.1908Z" fill="white"/>
</mask>
<g mask="url(#mask3_605_712)">
<path d="M32.9613 4.17578H10.9688V30.2533H32.9613V4.17578Z" fill="#EB001B"/>
</g>
<mask id="mask4_605_712" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="46" y="23" width="2" height="1">
<path d="M46.8267 23.8025V23.47H46.9692V23.4225H46.6367V23.47H46.7792L46.8267 23.8025ZM47.4917 23.8025V23.375H47.3967L47.2542 23.66L47.1117 23.375H47.0167V23.8025H47.1117V23.47L47.2067 23.755H47.3017L47.3967 23.47V23.8025H47.4917Z" fill="white"/>
</mask>
<g mask="url(#mask4_605_712)">
<path d="M49.8713 21H44.2188V26.1775H49.8713V21Z" fill="#F79E1B"/>
</g>
<mask id="mask5_605_712" style="mask-type:luminance" maskUnits="userSpaceOnUse" x="30" y="6" width="18" height="22">
<path d="M47.8284 17.1908C47.8284 23.0808 43.0784 27.8308 37.1884 27.8308C34.7184 27.8308 32.4384 26.9758 30.5859 25.5508C33.0559 23.6033 34.6709 20.5633 34.6709 17.1908C34.6709 13.7708 33.0559 10.7783 30.5859 8.83078C32.3909 7.40578 34.6709 6.55078 37.1884 6.55078C43.0784 6.55078 47.8284 11.3008 47.8284 17.1908Z" fill="white"/>
</mask>
<g mask="url(#mask5_605_712)">
<path d="M50.2034 4.17578H28.2109V30.2533H50.2034V4.17578Z" fill="#F79E1B"/>
</g>
</svg>


        </div> <!-- item -->




    </div>
</div>


</div>



</section>





<div class="gradients absolute inset-0 rounded-24 pointer-none overflow-hidden">

<div class="radial-gradient-blue absolute z-40 h-800 w-800 right--600 bottom-100 opacity-20"></div>
        </div>