<?php
/**
 * The template for displaying all pages
 *
 * This is the template that displays all pages by default.
 * Please note that this is the WordPress construct of pages
 * and that other 'pages' on your WordPress site may use a
 * different template.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Custom_theme
 */

get_header();
?>

 
 


<?php get_template_part('templates/parts/nav'); ?>




<section class="page-header mb-140">

<div class="container-xl mx-auto h-460 flex items-center px-80 rounded-24 bg-brand-dark relative">

<div class="main max-w-800 relative z-60 flex flex-col items-start">
    <div class="pretitle uppercase font-tt-eb text-16 mb-36 spacing-12 text-brand-violet">
    Contatti
    </div>
    <div class="title font-din-bc text-60 text-white">
     Mettiti in contatto per informazioni, supporto o per parlare di te e del tuo percorso formativo
    </div>
</div>

<div class="background absolute top-0 right-0 w-60-100 bottom-0 z-10 rounded-24 overflow-hidden">
<?php load_img(class: 'img img-cover', high: 179, low: 178); ?>
</div>


<div class="gradients absolute inset-0 z-40 overflow-hidden rounded-24">

<div class="radial-gradient-violet animate-float-around animation-duration-8000 absolute z-20 h-800 w-800 left--400 top--300 opacity-60"></div>
<div class="radial-gradient-blue absolute z-20 h-800 w-800 right--400 bottom--300"></div>
</div>



<div class="shapes z-50 absolute inset-0">

<div class="shape-1 animate-float-updown-light animation-duration-5000 absolute left--40 top-40">
SVG_PLACEHOLDER_svg_687f54a19d8da0.72738111


    </div>

    <div class="shape-2 animate-float-updown-light animation-duration-8000 absolute right-200 bottom--40">
    SVG_PLACEHOLDER_svg_687f54a19df144.30603359

    </div>

</div>

</div>

</section>


 
<section class="contatti-intro mb-100">

<div class="container-l mx-auto flex items-center justify-between gap-140">

<div class="main max-w-700">

    <div class="pretitle uppercase font-tt-eb text-16 mb-36 spacing-12 text-brand-violet">
    Modalità di contatto
    </div>

    <div class="title font-din-bc text-60 mb-28">
    Scegli la modalità che preferisci per entrare in contatto con noi
    </div>

    <div class="text font-ave-m text-20 leading-15 text-gray max-w-600">
    Qualsiasi canale tu scelga, ci assicureremo di rispondere il prima possibile alla tua richiesta.
    </div>

</div>
<div class="side">
SVG_PLACEHOLDER_svg_687f54a19e0090.09958443

</div>

</div>


</section>


<section class="contatti-options mb-40">

<div class="container-xl mx-auto grid gap-40">


    <div class="box bg-brand-whatsapp-light rounded-24 p-80">
        <div class="head mb-48">
        <div class="icon h-100 w-100 rounded-12 place-center flex mb-48 bg-brand-whatsapp-medium">
        SVG_PLACEHOLDER_svg_687f54a19e0bb8.35106757

        </div>
        <div class="pretitle uppercase font-tt-eb text-16 mb-36 spacing-12 text-brand-whatsapp">
        WhatsApp
        </div>
        <div class="title font-din-bc text-60 mb-20">
        Scrivici su <br>WhatsApp
        </div>
        <div class="text font-ave-m leading-15 text-20 text-gray">
       Un canale diretto per entrare in contatto e parlare con noi, scrivici e ti risponderemo rapidamente!
        </div>
</div>
        <div class="content">
            <a href="" class="link font-ave-m text-24">
            +39 340 615 0438
</a>
        </div>

    </div> <!-- box -->

    



    <div class="box bg-brand-violet-light rounded-24 p-80 flex flex-col">
        <div class="head mb-48">
        <div class="icon h-100 w-100 rounded-12 place-center flex mb-48 bg-brand-violet-medium">
        SVG_PLACEHOLDER_svg_687f54a19e15b2.53913810

        </div>
        <div class="pretitle uppercase font-tt-eb text-16 mb-36 spacing-12 text-brand-violet">
        Form di contatto
        </div>
        <div class="title font-din-bc text-60 mb-20">
        Compila il form <br>di contatto
        </div>
        <div class="text font-ave-m leading-15 text-20 text-gray">
        Lascia i tuoi dati nel modulo sottostante assieme ad un messaggio per essere ricontattato
        </div>
</div>
        <div class="content grow-1">


        <form class="footer_contact_form h-100-100 flex flex-col" method="post">

<div class="flex flex-col gap-10 mb-40 grow-1">
  <!-- Nome e Cognome -->
  <div class="field text border-solid border-2 border-gray rounded-12 h-72 min-h-60 w-100-100 relative bg-white" field="full_name">
    <input
      class="font-ave-d text-18 px-28 s:pt-16 w-100-100 h-100-100 bg-transparent"
      type="text" name="full_name" autocomplete="name"
      placeholder=" " required>
    <label
      class="font-ave-d text-gray text-18 absolute left-28 top-50-100 translate-y--50-100 pointer-none transition-all"
      for="full_name">Nome e Cognome</label>
  </div>

  <!-- E-mail -->
  <div class="field text border-solid border-2 border-gray rounded-12 h-72 min-h-60 w-100-100 relative bg-white" field="email">
    <input
      class="font-ave-d text-18 px-28 s:pt-16 w-100-100 h-100-100 bg-transparent"
      type="email" name="user_email" autocomplete="email" placeholder=" " required>
    <label
      class="font-ave-d text-gray text-18 absolute left-28 top-50-100 translate-y--50-100 pointer-none transition-all"
      for="user_email">E-mail</label>
  </div>

  <!-- Telefono -->
  <div class="field phone border-solid border-2 border-gray rounded-12 h-72 min-h-60 w-100-100 pl-20 bg-white" field="phone">
    <div class="inner h-100-100 w-100-100 relative">
      <input
        class="field-input font-ave-d text-18 pr-28 s:pl-100 s:pt-16 h-100-100 w-100-100 bg-transparent"
        type="tel" name="phone" autocomplete="tel" placeholder=" " required>
      <label
        class="font-ave-d text-gray text-18 absolute s:left-100 top-50-100 translate-y--50-100 pointer-none transition-all"
        for="phone">Telefono</label>
    </div>
  </div>

  <!-- Textarea -->
  <div class="field textarea border-solid border-2 border-gray rounded-12 w-100-100 relative bg-white grow-1" field="message">
    <textarea
      class="font-ave-d text-18 px-28 py-36 w-100-100 h-100-100 bg-transparent resize-none"
      name="message" placeholder=" " required></textarea>
    <label
      class="font-ave-d text-gray text-18 absolute left-28 top-28 pointer-none transition-all z-50"
      for="message">Messaggio</label>
  </div>
</div>

<!-- Privacy Checkbox -->
<div class="group mb-40">
  <div class="option checkbox inline-flex gap-16" option="privacy_footer">
    <input
      class="appearance-none relative s:w-24 s:h-24 border-solid border-gray border-2 rounded-6 top--4 shrink-0 bg-white"
      type="checkbox"
      name="privacy_footer"
      value="yes"
      id="privacy_footer"
      required
    >
    <label class="font-ave-d text-16 text-gray leading-13" for="privacy_footer">
      Ho letto e compreso l'informativa sul trattamento dei miei dati personali.
    </label>
  </div>
</div>

<button
  class="submit button h-72 w-100-100 rounded-16 text-center hover-border-animated-violet-light overflow-hidden hover-up-2 hover-shadow-violet cursor-pointer"
  type="submit">
  <div class="inner flex place-center flex place-center h-100-100 w-100-100">
<div class="text font-ave-b text-20">
Invia
</div>
</div>
</button>
<input type="hidden" name="action" value="footer_contact_form_action">
<input type="hidden" name="security"
  value="<?php echo esc_attr( wp_create_nonce( 'footer-contact-nonce' ) ); ?>">
<div
  class="errors hidden bg-red border-l-3 bg-opacity-10 border-solid border-red px-40 py-20 text-16 font-ave-d text-red leading-14 mt-20">
</div>
</form>

         
        </div>

    </div> <!-- box -->





    <div class="box bg-brand-yellow-light rounded-24 p-80">
        <div class="head mb-48">
        <div class="icon h-100 w-100 rounded-12 place-center flex mb-48 bg-brand-yellow-medium">
        SVG_PLACEHOLDER_svg_687f54a19e1df5.06179877


        </div>
        <div class="pretitle uppercase font-tt-eb text-16 mb-36 spacing-12 text-brand-yellow">
        E-mail
        </div>
        <div class="title font-din-bc text-60 mb-20">
        Contattaci <br>via e-mail
        </div>
        <div class="text font-ave-m leading-15 text-20 text-gray">
        Se preferisci un canale più formale o hai bisogno di inviarci un brano o altri allegati, questo è il modo migliore.
        </div>
</div>
        <div class="content">
            <a href="" class="link font-ave-m text-24">
           <EMAIL>
</a>
        </div>

    </div> <!-- box -->




</div>


</section>






<section class="contatti-call mb-140">
    <div class="container-xl mx-auto rounded-24 bg-brand-blue-light items-center p-80 flex gap-140 justify-between">
        <div class="main mb-60">
        <div class="head mb-48">
        <div class="icon h-100 w-100 rounded-12 place-center flex mb-48 bg-brand-blue-medium">
        SVG_PLACEHOLDER_svg_687f54a19e27b0.91538544



        </div>
        <div class="pretitle uppercase font-tt-eb text-16 mb-36 spacing-12 text-brand-blue">
        Chiamata gratuita
        </div>
        <div class="title font-din-bc text-60 mb-20">
        Hai domande sui corsi? prenota una chiamata gratuita e Parliamo di te e dei tuoi obiettivi
        </div>
        <div class="text font-ave-m leading-15 text-20 text-gray">
        Risponderemo alle tue domande, definiremo il percorso formativo più adatto a te sulla base del tuo background e dei tuoi obiettivi.
        </div>
</div>
            <div class="info flex flex-col gap-36">


                <div class="item flex items-center gap-24">
                    <div class="icon w-40 justify-center flex">
                    SVG_PLACEHOLDER_svg_687f54a19e2ff4.73611055

                    </div>
                    <div class="text font-ave-d text-24">
                    30 minuti
                    </div>
                </div> <!-- item -->


                <div class="item flex items-center gap-24">
                    <div class="icon w-40 justify-center flex">
                    SVG_PLACEHOLDER_svg_687f54a19e3960.75197422


                    </div>
                    <div class="text font-ave-d text-24">
                    Gratuita
                    </div>
                </div> <!-- item -->


            </div>
        </div>

<div class="side shrink-0 max-w-600">
    <div class="box bg-white rounded-24 shadow-blue border-animated-blue-light">
        <div class="inner p-60">
            <div class="title font-ave-b text-36 mb-20 text-center">
            Prenota una <br>chiamata gratuita
            </div>
            <div class="text font-ave-m text-18 text-gray mb-40 leading-15 text-center">
            Scegli un giorno e un orario
            </div>
            <div class="form">
            <?php load_img(class: 'img w-100-100', high: 228); ?>





            </div>
        </div>

</div>
        </div>

    </div>
</section>





<section class="contatti-faq mb-140">

<div class="container-l mx-auto">
    <div class="intro mb-60">
        <div class="pretitle uppercase font-tt-eb text-16 mb-36 spacing-12 text-brand-violet">
FAQ
        </div>
        <div class="title font-din-bc text-60 mb-20">
        Domande frequenti
        </div>
        <div class="text font-ave-m leading-15 text-20 text-gray max-w-800">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sapien libero, ultrices sed elit id, rhoncus sagittis augue.
        </div>
    </div> 

    <div class="content">
        <div class="rows divide-y-2 divide-gray divide-solid">


            <div class="row">
                <div class="head flex justify-between items-center gap-80 py-40 cursor-pointer">
                    <div class="text font-ave-d text-24 leading-15">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit?
                    </div>
                    <div class="icon flex shrink-0">
                    SVG_PLACEHOLDER_svg_687f54a19e41b6.38141550

                    </div>
                </div>
                <div class="body pb-40 hidden">
                    <div class="text font-ave-m leading-15 text-20 text-gray">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sapien libero, ultrices sed elit id, rhoncus sagittis augue. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sapien libero, ultrices sed elit id, rhoncus sagittis augue.
                    </div>
                </div>

            </div> <!-- row -->



            <div class="row">
                <div class="head flex justify-between items-center gap-80 py-40 cursor-pointer">
                    <div class="text font-ave-d text-24 leading-15">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit?
                    </div>
                    <div class="icon flex shrink-0">
                    SVG_PLACEHOLDER_svg_687f54a19e4962.77593609

                    </div>
                </div>
                <div class="body pb-40 hidden">
                    <div class="text font-ave-m leading-15 text-20 text-gray">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sapien libero, ultrices sed elit id, rhoncus sagittis augue. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sapien libero, ultrices sed elit id, rhoncus sagittis augue.
                    </div>
                </div>

            </div> <!-- row -->




            <div class="row">
                <div class="head flex justify-between items-center gap-80 py-40 cursor-pointer">
                    <div class="text font-ave-d text-24 leading-15">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit?
                    </div>
                    <div class="icon flex shrink-0">
                    SVG_PLACEHOLDER_svg_687f54a19e5146.62503173

                    </div>
                </div>
                <div class="body pb-40 hidden">
                    <div class="text font-ave-m leading-15 text-20 text-gray">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sapien libero, ultrices sed elit id, rhoncus sagittis augue. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sapien libero, ultrices sed elit id, rhoncus sagittis augue.
                    </div>
                </div>

            </div> <!-- row -->



            <div class="row">
                <div class="head flex justify-between items-center gap-80 py-40 cursor-pointer">
                    <div class="text font-ave-d text-24 leading-15">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit?
                    </div>
                    <div class="icon flex shrink-0">
                    SVG_PLACEHOLDER_svg_687f54a19e5a08.26745201

                    </div>
                </div>
                <div class="body pb-40 hidden">
                    <div class="text font-ave-m leading-15 text-20 text-gray">
                    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sapien libero, ultrices sed elit id, rhoncus sagittis augue. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sapien libero, ultrices sed elit id, rhoncus sagittis augue.
                    </div>
                </div>

            </div> <!-- row -->



        </div>
</div>


</div>

</section>



<section class="home-gallery mb-140">

<div class="container max-w-2000 mx-auto">
    <div class="items grid grid-cols-3 gap-20">
        <div class="item ratio-3-4 relative">
            <?php load_img(class: 'img img-cover', high: 244, low: 243); ?>
        </div>
        <div class="item ratio-3-4 relative">
            <?php load_img(class: 'img img-cover', high: 246, low: 245); ?>
        </div>
        <div class="item ratio-3-4 relative">
            <?php load_img(class: 'img img-cover', high: 248, low: 247); ?>
        </div>
    </div>
</div>

</section>





<section class="contatti-location mb-140">

<div class="intro container-l mx-auto flex gap-140 items-center justify-between mb-100">
    <div class="main">
        <div class="pretitle uppercase font-tt-eb text-16 mb-28 spacing-12 text-brand-yellow">
        Dove siamo
        </div>
        <div class="title font-din-bc text-60 mb-20">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sapien libero
        </div>
        <div class="text font-ave-m leading-15 text-20 text-gray">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sapien libero, ultrices sed elit id, rhoncus sagittis augue.
        </div>
    </div> 
    <div class="side">


    <div class="box flex flex-col rounded-24 relative">
        <div class="inner z-40 p-60 rounded-24 flex gap-20">
        <div class="icon flex">
            SVG_PLACEHOLDER_svg_687f54a19e6521.09283622

            </div>
            <div class="title font-ave-d text-22 leading-15 no-wrap">
            Via Carlo Serassi, 13 E, 24125 Bergamo BG, Italia
            </div>

        </div>
        <div class="gradients absolute inset-0 z-10 overflow-hidden rounded-24">

<div class="radial-gradient-red animate-float-around animate-opacity-70-100 animation-duration-10000 absolute z-40 h-540 w-540 left--340 bottom--340"></div>

<div class="radial-gradient-yellow animate-float-around animate-opacity-70-100 animation-duration-10000 absolute z-40 h-540 w-540 right--140 top--140"></div>
        </div>


        <div class="shapes absolute inset-0 z-60">
        <div class="shape-1 animate-float-updown-light animation-duration-5000 absolute right-80 top--40">
        SVG_PLACEHOLDER_svg_687f54a19e6fa0.76937376


    </div>

</div>
        </div>



    </div>
</div>

<div class="content container-xl mx-auto">

<div class="map h-600 relative rounded-24 overflow-hidden">
<?php load_img(class: 'img img-cover', high: 181, low: 180); ?>


SVG_PLACEHOLDER_svg_687f54a19e78b4.51529725


</div>


</div>
    
</section>


 













<?php get_template_part('templates/parts/footer-classic'); ?>




<?php
get_sidebar();
get_footer();
