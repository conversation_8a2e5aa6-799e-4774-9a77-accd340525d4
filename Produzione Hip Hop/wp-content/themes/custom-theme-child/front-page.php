<?php
/**
 * The template for displaying all pages
 *
 * This is the template that displays all pages by default.
 * Please note that this is the WordPress construct of pages
 * and that other 'pages' on your WordPress site may use a
 * different template.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Custom_theme
 */

get_header();
?>


  


<?php get_template_part('templates/parts/nav'); ?>


<section class="home-header">

<div class="container-xl mx-auto rounded-24 bg-brand-dark relative">

<div class="container-l mx-auto relative z-50">

<div class="content py-140 flex flex-col items-start">
    <div class="pretitle mb-36 uppercase font-tt-eb text-16 spacing-12 text-gradient-pink-violet">
    PRODUZIONE HIP HOP
    </div>
    <div class="title mb-28 font-din-bc text-72 text-white">
   La scuola per produttori<br>
e tecnici del suono <br>
della scena hip hop
    </div>
    <div class="text max-w-500 mb-60 font-ave-d text-20 leading-15 text-white opacity-80">
    Dal 2014 formiamo la nuova generazione di beatmaker e fonici, fornendo tutto il necessario per trasformare la loro passione in professione.
    </div>
    <a href="https://app-656e0683c1ac188b30f693df.closte.com/corsi/" class="button h-72 w-400 rounded-16 text-center mb-36 border-animated-violet overflow-hidden hover-up-2 hover-dark-shadow">
        <div class="inner flex place-center flex place-center h-100-100 w-100-100">
            <div class="text font-ave-b text-white text-20">
        Scopri i nostri corsi
</div>
        </div>
</a>
    <div class="trust flex gap-20 items-center">
        <div class="photo flex items-center">
            
        <div class="item mr--6 h-36 w-36 overflow-hidden rounded-full relative">
            <?php load_img(class: 'img img-cover', high: 229); ?>
            </div>
            <div class="item mr--10 h-36 w-36 overflow-hidden rounded-full relative">
            <?php load_img(class: 'img img-cover', high: 230); ?>
            </div>
            <div class="item h-36 w-36 overflow-hidden rounded-full relative">
            <?php load_img(class: 'img img-cover', high: 231); ?>
            </div>

        </div>
        <div class="stars">
        <svg class="h-20" width="95" height="19" viewBox="0 0 95 19" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9.4391 0L11.5583 6.52226L18.4162 6.52226L12.8681 10.5532L14.9873 17.0755L9.4391 13.0445L3.89094 17.0755L6.01015 10.5532L0.461983 6.52226L7.31989 6.52226L9.4391 0Z" fill="#FFBB44"/>
<path d="M28.318 0L30.4372 6.52226L37.2951 6.52226L31.747 10.5532L33.8662 17.0755L28.318 13.0445L22.7698 17.0755L24.8891 10.5532L19.3409 6.52226L26.1988 6.52226L28.318 0Z" fill="#FFBB44"/>
<path d="M47.193 0L49.3122 6.52226L56.1701 6.52226L50.622 10.5532L52.7412 17.0755L47.193 13.0445L41.6448 17.0755L43.7641 10.5532L38.2159 6.52226L45.0738 6.52226L47.193 0Z" fill="#FFBB44"/>
<path d="M66.0719 0L68.1911 6.52226L75.049 6.52226L69.5009 10.5532L71.6201 17.0755L66.0719 13.0445L60.5237 17.0755L62.643 10.5532L57.0948 6.52226L63.9527 6.52226L66.0719 0Z" fill="#FFBB44"/>
<path d="M85.5602 0L87.6794 6.52226L94.5373 6.52226L88.9892 10.5532L91.1084 17.0755L85.5602 13.0445L80.012 17.0755L82.1312 10.5532L76.5831 6.52226L83.441 6.52226L85.5602 0Z" fill="#FFBB44"/>
</svg> 

        </div>
        <div class="text font-ave-d text-18 text-white opacity-80">
        500+ Studenti
        </div>
    </div>
</div>
 

</div>

<div class="background absolute top-0 right-0 bottom-0 w-60-100 z-10 rounded-24 overflow-hidden">
<?php load_img(class: 'img img-cover', high: 34); ?>
</div>


<div class="gradients absolute inset-0 z-20 overflow-hidden rounded-24">

<div class="radial-gradient-pink animate-float-around animate-opacity-70-100 animation-duration-10000 absolute z-20 h-1200 w-1200"></div>
<div class="radial-gradient-violet animate-float-around animate-opacity-70-100 animation-duration-10000 absolute z-20 h-1200 w-1200"></div>

</div>

<div class="shapes z-30 absolute inset-0">
    <div class="shape-1 animate-float-updown-light animation-duration-5000 absolute left--80 bottom-40">
    <svg class="h-140 shadow-pink" width="134" height="139" viewBox="0 0 134 139" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_584_701)">
<path d="M129.867 76.7037L17.6966 2.16038C11.4429 -1.99556 3.28632 3.48416 4.76969 10.8449L29.0817 131.486C30.1921 136.996 36.2824 139.895 41.2596 137.284L129.118 91.1865C134.789 88.2112 135.201 80.2481 129.867 76.7037Z" fill="#E04E9D"/>
</g>
<defs>
<clipPath id="clip0_584_701">
<rect width="133" height="139" fill="white" transform="translate(0.9375)"/>
</clipPath>
</defs>
</svg>

    </div>

    <div class="shape-2 animate-float-updown-light animation-duration-3000 absolute right--20 top-80">
    <svg class="h-88 shadow-violet" width="73" height="90" viewBox="0 0 73 90" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_584_704)">
<path d="M60.9958 81.1441L72.0798 12.6398C73.5036 3.8403 63.5728 -2.28396 56.3491 2.93878L4.91055 40.1292C-0.495635 44.0379 -0.631414 52.0436 4.63912 56.1334L44.9937 87.4474C50.9911 92.1012 59.7833 88.638 60.9958 81.1441Z" fill="#AB77FF"/>
</g>
<defs>
<clipPath id="clip0_584_704">
<rect width="72" height="90" fill="white" transform="translate(0.773438)"/>
</clipPath>
</defs>
</svg>
</div>

</div>


</div>

</section>



<section class="home-obj">
<div class="container-l mx-auto relative py-140">
<div class="main grow-1 flex flex-col items-start w-40-100">
    <div class="pretitle mb-36 uppercase font-tt-eb text-16 spacing-12 text-gradient-pink-violet">
    Produzione Hip Hop
    </div>
    <div class="title mb-20 font-din-bc text-60">
   Ti affianchiamo in Un percorso costruito su misura per le tue ambizioni musicali
    </div>
    <div class="text mb-60 font-ave-d text-20 leading-15 text-gray">
   Che tu voglia vendere beat, mixare per altri o pubblicare i tuoi dischi, il nostro percorso si adatta per darti solo le competenze che ti servono davvero.
    </div>
    <div class="cta relative flex gap-40 items-center">
    <a href="https://wa.me/393517778431" class="button h-72 w-400 rounded-16 text-center border-brand-whatsapp border-4 border-solid rounded-12 flex gap-20 place-center hover-shadow-whatsapp hover-up-2">
        <div class="icon flex">
        <svg class="h-32" width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M28.9074 4.94096C25.7265 1.75639 21.4963 0.00184653 16.9894 0C7.70303 0 0.144918 7.55752 0.141294 16.8467C0.139994 19.8162 0.915807 22.7146 2.39022 25.2695L0 34L8.93139 31.6572C11.3922 32.9994 14.1629 33.7069 16.9827 33.7078H16.9895C16.989 33.7078 16.99 33.7078 16.9895 33.7078C26.2749 33.7078 33.8336 26.1496 33.8375 16.8602C33.8392 12.3584 32.0883 8.12543 28.9074 4.94096ZM16.9894 30.8625H16.9837C14.4711 30.8615 12.0065 30.1863 9.8565 28.9106L9.34521 28.6071L4.0452 29.9974L5.45991 24.8299L5.12691 24.3C3.72519 22.0705 2.98487 19.4936 2.98597 16.8477C2.98898 9.12687 9.27101 2.8455 16.9951 2.8455C20.7354 2.84667 24.2514 4.30521 26.8952 6.95213C29.539 9.59903 30.9942 13.1173 30.9928 16.8591C30.9896 24.5805 24.7078 30.8625 16.9894 30.8625Z" fill="#25D366"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M24.6717 20.3825C24.2508 20.1717 22.181 19.1535 21.7951 19.013C21.4093 18.8724 21.1286 18.8022 20.848 19.2237C20.5674 19.6449 19.7606 20.5932 19.515 20.8743C19.2695 21.1551 19.0238 21.1905 18.603 20.9795C18.182 20.7688 16.8256 20.3243 15.2177 18.8901C13.9661 17.7738 13.1213 16.3954 12.8756 15.9738C12.6301 15.5525 12.8495 15.3247 13.0603 15.1147C13.2497 14.9261 13.4813 14.623 13.6918 14.3772C13.9022 14.1315 13.9724 13.9557 14.1128 13.675C14.2531 13.3939 14.183 13.1481 14.0777 12.9375C13.9724 12.7268 13.1305 10.6546 12.7798 9.8116C12.4379 8.99086 12.0909 9.10206 11.8325 9.08899C11.5873 9.07675 11.3063 9.07422 11.0257 9.07422C10.745 9.07422 10.2889 9.17961 9.90315 9.60096C9.5173 10.0224 8.42969 11.041 8.42969 13.1128C8.42969 15.185 9.93817 17.1867 10.1487 17.4676C10.3591 17.7488 13.1171 22.0006 17.3401 23.8242C18.3445 24.2579 19.1286 24.5169 19.7399 24.7108C20.7485 25.0314 21.6662 24.9861 22.3915 24.8776C23.2004 24.7569 24.8821 23.8594 25.233 22.8761C25.5838 21.8927 25.5838 21.0497 25.4785 20.8742C25.3733 20.6986 25.0926 20.5932 24.6717 20.3825Z" fill="#25D366"/>
</svg>

        </div>
        <div class="text font-ave-b text-20">
        Parlaci dei tuoi obiettivi
        </div>
</a>

    <div class="signature">
    <svg class="h-88" width="193" height="111" viewBox="0 0 193 111" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_584_706)">
<path d="M50.1131 17.7205C50.1853 17.6146 50.1684 17.5255 50.1515 17.4365C50.1178 17.2584 50.1009 17.1693 50.1562 16.9744C50.1393 16.8853 49.9997 16.635 50.0887 16.6182C49.9828 16.546 49.949 16.3679 49.9321 16.2788L49.7925 16.0286C49.6865 15.9564 49.6697 15.8674 49.6697 15.8674C49.6697 15.8674 49.6697 15.8674 49.5806 15.8842C49.5637 15.7952 49.4578 15.723 49.4578 15.723C49.4578 15.723 49.4578 15.723 49.3519 15.6508C49.3519 15.6508 49.246 15.5787 49.1569 15.5956L49.051 15.5234C49.0679 15.6124 48.767 15.485 48.6779 15.5018C48.6779 15.5018 48.3048 15.4803 48.3939 15.4634C48.2158 15.4972 48.0377 15.5309 47.8596 15.5647C47.5034 15.6322 47.0582 15.7166 46.7188 15.8731C46.7188 15.8731 46.3627 15.9406 46.2736 15.9575C46.1124 16.0803 45.9343 16.1141 45.7731 16.2369C45.0944 16.55 44.3268 16.88 43.665 17.2821C42.4137 17.9805 41.1239 18.963 39.851 20.0344C38.6841 21.1781 37.6783 22.1989 36.6342 23.5038C34.5627 26.2025 33.0041 29.1731 32.2421 32.4539C32.0378 33.3229 31.8335 34.1918 31.7352 35.1329C31.6199 35.985 31.56 36.6421 31.6228 37.4604C31.5704 39.1308 31.6961 40.7674 31.9999 42.3703C32.1349 43.0827 32.359 43.7782 32.5999 44.5628C32.8408 45.3473 33.0818 46.1319 33.3949 46.8105C33.9658 48.3627 34.7149 49.8812 35.358 51.3276C36.8392 54.2755 38.3035 57.1344 39.3563 60.2557C39.5803 60.9512 39.6985 61.5746 39.8335 62.287L42.1965 60.6399L52.4162 53.7216C52.4162 53.7216 53.3649 54.8333 52.7369 55.4136C52.6816 55.6085 51.7695 56.1504 50.7301 56.9931L40.3567 65.0475C40.5161 66.8622 40.5144 68.7997 40.3176 70.6819C39.6028 78.1049 36.2427 85.1991 32.0736 91.4318C29.9975 94.5927 27.6037 97.5371 25.1039 100.409C22.6762 103.176 19.9815 105.992 16.8677 108.059C14.0041 109.985 10.5772 111.372 7.2964 110.611C5.75348 110.257 4.42708 109.586 3.37249 108.402C2.22885 107.235 1.64104 105.594 1.33725 103.991C0.806507 100.217 2.40827 96.5007 4.57341 93.3229C6.73855 90.1452 9.5055 87.2223 12.234 84.5835C17.9966 78.9712 24.1445 73.9317 30.4321 69.1424C32.7782 67.4063 35.1244 65.6701 37.4705 63.934C37.3355 63.2216 37.2005 62.5092 36.9764 61.8137C35.1379 55.5204 31.1023 50.2892 29.5862 43.7502C28.8727 40.4723 28.7488 36.8981 29.3711 33.3671C30.0823 29.8191 31.5566 26.4033 33.5774 23.4374C35.5261 20.5774 37.9153 18.0951 40.6898 16.1856C42.0686 15.1863 43.4811 14.3651 44.9274 13.722C46.5519 13.0451 48.2991 12.5295 49.9479 12.955C53.4406 13.8612 52.9871 18.2828 52.0686 21.2243C50.9628 25.1237 49.556 28.8958 47.8483 32.5403C47.1557 34.2398 44.8544 33.2922 45.5639 31.6818C46.3287 29.8764 47.0766 27.982 47.7524 26.1934C48.1179 25.2017 48.4112 24.3159 48.7767 23.3241C48.9595 22.8282 49.1423 22.3324 49.325 21.8365C49.4187 21.3575 49.474 21.1625 49.5846 20.7726C49.8058 19.9927 50.027 19.2128 50.1422 18.3607C50.1085 18.1826 50.1638 17.9876 50.13 17.8095L50.1131 17.7205ZM37.6077 71.4723C37.872 69.9462 37.9413 68.3649 37.8325 66.8173C34.3578 69.4131 30.8831 72.0088 27.4253 74.6936C24.379 77.1159 21.2774 79.7331 18.3539 82.3167C15.4304 84.9002 12.6129 87.5559 10.0409 90.534C7.66857 93.1053 5.34687 95.9438 4.31782 99.2752C4.22411 99.7542 4.04135 100.25 4.03668 100.712C3.99827 100.996 3.94297 101.191 3.88768 101.386C3.90455 101.475 3.93831 101.653 3.86614 101.759L3.88301 101.848C3.87835 102.31 3.87369 102.773 3.9412 103.129C3.99183 103.396 4.02558 103.574 4.05934 103.752C4.05934 103.752 4.07621 103.841 4.09309 103.93C4.10997 104.019 4.12684 104.108 4.12684 104.108C4.21123 104.553 4.36779 104.893 4.52434 105.232C4.66402 105.482 4.69778 105.66 4.94338 105.983C5.06618 106.144 5.18899 106.305 5.31179 106.467C5.31179 106.467 5.41771 106.539 5.43459 106.628C5.66332 106.861 5.9811 107.078 6.19295 107.222C6.29887 107.294 6.4048 107.366 6.49385 107.35C6.51073 107.439 6.51073 107.439 6.51073 107.439C6.61665 107.511 6.81162 107.566 6.91755 107.638C7.21845 107.766 7.51935 107.893 7.80337 107.932C7.89242 107.915 7.9093 108.004 7.9093 108.004L7.99835 107.987C8.0874 107.97 8.19332 108.042 8.28237 108.025C8.47734 108.081 8.56639 108.064 8.76137 108.119C9.13444 108.141 9.41846 108.179 9.77465 108.111C9.77465 108.111 9.8637 108.095 9.95275 108.078C10.0418 108.061 10.1308 108.044 10.2368 108.116C10.4149 108.082 10.593 108.049 10.7711 108.015C11.1273 107.947 11.4666 107.791 11.9118 107.706C11.9118 107.706 12.0009 107.69 12.0899 107.673C12.179 107.656 12.1621 107.567 12.2511 107.55C12.4292 107.516 12.6964 107.465 12.8576 107.343C13.1969 107.186 13.5362 107.03 13.9646 106.856C16.8956 105.286 19.7132 102.63 22.0856 100.059C27.0467 94.5985 31.5336 88.5823 34.5159 81.9287C36.0793 78.496 37.0914 75.0755 37.6077 71.4723ZM71.3283 52.7202C69.4781 54.6391 63.1765 60.8147 61.2925 62.5555C59.0139 64.6478 56.6462 66.757 54.2954 68.9552C53.5062 69.6583 52.7338 70.4504 51.9446 71.1535C51.3166 71.7337 50.4768 72.1697 49.5525 72.1603C47.9881 72.1801 47.0609 70.6954 46.8968 69.3428C46.5762 67.6508 47.0738 65.8961 47.5162 64.3363C47.8095 63.4505 48.1029 62.5646 48.3962 61.6788C48.3962 61.6788 48.4684 61.5729 48.4515 61.4838C48.8508 60.6702 49.161 59.8734 49.5603 59.0598C49.6325 58.9538 49.7768 58.742 49.8321 58.547C50.5753 57.1147 51.6242 55.3477 52.6683 54.0428C52.8127 53.831 53.2073 53.4795 53.6188 53.217C53.7078 53.2001 53.6909 53.1111 53.6909 53.1111L53.78 53.0942C54.3696 52.7979 55.1373 52.4679 56.3118 52.3376C56.6848 52.3591 57.0579 52.3807 57.4478 52.4913C57.7319 52.5297 58.3337 52.7846 58.5362 53.8532C58.794 54.7268 58.2166 55.5742 57.822 55.9257C57.2831 56.4891 56.6382 56.9803 55.8705 57.3103C55.2088 57.7125 54.412 57.4023 54.0989 56.7236C54.0098 56.7405 54.0267 56.8295 53.9545 56.9355C52.9273 58.3294 52.1118 59.8676 51.3854 61.389C50.7481 62.8935 50.1998 64.3811 49.8634 66.0131C49.6254 66.7039 49.4595 67.2889 49.4164 68.035C49.4333 68.1241 49.467 68.3022 49.3949 68.4081C49.3949 68.4081 49.3949 68.4081 49.4117 68.4971C49.4117 68.4971 49.4117 68.4971 49.4286 68.5862C49.4624 68.7643 49.513 69.0314 49.5467 69.2095C49.5636 69.2986 49.5636 69.2986 49.5805 69.3876C49.6695 69.3707 49.6864 69.4598 49.6864 69.4598C49.7755 69.4429 49.7755 69.4429 49.7755 69.4429C49.8645 69.426 49.9367 69.3201 49.9367 69.3201C50.6706 68.812 51.2817 68.1427 51.9098 67.5625C52.6099 66.8763 53.327 66.2791 53.9381 65.6098C56.7894 63.1322 59.5348 60.5825 62.2802 58.0327C63.6975 56.7494 65.1147 55.4661 66.4429 54.1996C67.0709 53.6194 67.7879 53.0222 68.399 52.3529C68.4881 52.336 68.4881 52.336 68.4881 52.336C68.8827 51.9845 69.1883 51.6498 69.5829 51.2983C69.655 51.1924 69.8331 51.1586 69.9053 51.0527L69.9944 51.0358L70.2999 50.7012C70.3721 50.5953 70.3721 50.5953 70.3721 50.5953L70.7667 50.2437C70.7667 50.2437 72.2066 52.0003 71.3283 52.7202ZM49.6143 69.5657C49.4362 69.5995 49.5252 69.5826 49.6143 69.5657V69.5657ZM49.8092 69.621C49.8092 69.621 49.8092 69.621 49.7924 69.532L49.7033 69.5488C49.8092 69.621 49.8092 69.621 49.8092 69.621ZM89.8435 46.6282C89.8435 46.6282 90.9196 47.4389 90.1811 48.4091L74.0799 65.6668L74.0078 65.7727C73.63 66.2133 72.0685 67.7085 70.9324 67.5548C70.5593 67.5333 70.3475 67.3889 70.2247 67.2277C69.6613 66.6887 69.6368 65.5864 69.7306 65.1074C69.8121 64.0772 70.1607 62.9964 70.4372 62.0216C70.7643 61.3138 71.0914 60.6061 71.4185 59.8984C71.7287 59.1016 74.4777 52.6768 75.1319 51.2614C70.733 53.8478 71.5886 50.088 71.5886 50.088C71.5886 50.088 72.6694 50.4366 74.5993 49.4251C74.5993 49.4251 75.1889 49.1289 75.8123 49.0107C77.7882 48.7285 78.0414 50.0642 78.0152 50.8994C78.0152 50.8994 77.9936 51.2725 77.7387 51.8743C77.7387 51.8743 77.4116 52.582 77.2288 53.0779L76.3919 54.9892C76.2091 55.4851 75.9542 56.0868 75.6824 56.5996C75.4443 57.2904 75.1894 57.8922 74.8454 58.5109C74.5905 59.1127 74.4077 59.6086 74.1528 60.2104C73.9531 60.6172 73.7535 61.024 73.6429 61.414C73.3711 61.9267 73.1883 62.4226 72.9165 62.9354C72.8612 63.1303 72.8059 63.3253 72.6785 63.6262C72.6232 63.8212 72.5679 64.0162 72.6907 64.1774L88.7656 47.7549L89.3767 47.0856C89.3767 47.0856 89.4658 47.0687 89.4489 46.9797L89.8435 46.6282ZM104.807 43.7921C104.807 43.7921 106.468 44.7687 105.297 46.3745L97.8865 54.9742L95.8704 57.478C95.5648 57.8127 94.4147 59.0454 94.2535 59.1682C93.8758 59.6088 93.5702 59.9434 93.1756 60.295C92.3864 60.998 91.5803 61.612 90.4226 61.8314C89.5321 62.0002 88.2901 61.7744 87.3706 61.3029C86.2222 60.5981 85.5576 59.5248 85.5332 58.4225C85.5332 58.4225 85.5332 58.4225 85.5163 58.3334C85.5041 57.7823 85.8207 54.5859 90.9758 45.7678C91.048 45.6619 91.2092 45.5391 91.2813 45.4332C91.765 45.0648 92.4943 45.0188 93.1298 45.4518C93.6595 45.8127 94.0063 46.6694 93.7176 47.0931C92.6182 48.593 91.6247 50.165 90.843 51.8813C90.0997 53.3136 89.4455 54.7291 89.0032 56.2889C88.7482 56.8907 88.438 57.6875 88.3059 58.4505C88.2675 58.7345 88.2675 58.7345 88.3181 59.0016C88.3012 58.9126 88.3012 58.9126 88.3181 59.0016C88.3181 59.0016 88.424 59.0738 88.335 59.0907C88.335 59.0907 88.335 59.0907 88.424 59.0738C88.53 59.146 88.7418 59.2903 88.6359 59.2182C88.7418 59.2903 88.8309 59.2734 88.9368 59.3456L89.0258 59.3287C89.1149 59.3119 89.2039 59.295 89.293 59.2781C89.6492 59.2106 89.7382 59.1937 90.1497 58.9312C90.7393 58.635 91.3504 57.9657 91.9784 57.3854C92.1949 57.0677 92.5174 56.8221 92.8229 56.4874L104.268 44.3555L104.574 44.0208L104.807 43.7921ZM95.2468 34.7189C95.8701 34.6008 96.6116 35.106 96.7466 35.8184C96.8816 36.5308 96.3764 37.2722 95.7531 37.3904C95.0407 37.5254 94.2992 37.0202 94.1642 36.3078C94.0292 35.5954 94.5344 34.8539 95.2468 34.7189ZM119.34 35.2261C119.925 35.392 120.222 35.9816 120.234 36.5328C120.212 36.9058 120.191 37.2789 120.152 37.5629C120.181 38.2031 119.715 38.6606 119.091 38.7787L119.019 38.8847C118.819 39.2915 118.531 39.7152 118.331 40.122C117.898 40.7576 117.482 41.4822 117.066 42.2068C116.505 43.1432 115.839 44.0075 115.278 44.944C114.663 46.0754 114.03 47.1178 113.308 48.177C112.603 49.3253 111.899 50.4736 111.194 51.6219C110.489 52.7702 109.873 53.9017 109.168 55.05C108.536 56.0924 107.814 57.1516 107.181 58.194C106.693 59.0245 106.115 59.8719 105.644 60.7915C105.211 61.4271 104.867 62.0457 104.434 62.6813C103.729 63.8296 103.024 64.9779 102.091 65.8928C101.463 66.4731 100.601 67.2821 99.6987 66.8997C99.6096 66.9165 99.4147 66.8612 99.3978 66.7722C99.3087 66.7891 99.2919 66.7 99.2919 66.7C99.2919 66.7 99.2028 66.7169 99.1859 66.6278C98.9572 66.3945 98.6947 65.983 98.8053 65.593C98.8821 65.025 98.959 64.4569 99.1249 63.872C99.2907 63.2871 99.4566 62.7022 99.7116 62.1004L105.936 42.3789C106.086 41.7049 107.031 41.3412 107.633 41.5961C108.324 41.8342 108.637 42.5128 108.416 43.2927C108.416 43.2927 108.433 43.3817 108.361 43.4877L103.653 59.0472L117.771 35.708C118.043 35.1953 118.845 35.0434 119.34 35.2261ZM136.579 37.7704C136.579 37.7704 138.24 38.7471 137.069 40.3529L129.659 48.9526L127.642 51.4564C127.337 51.791 126.187 53.0237 126.026 53.1465C125.648 53.5871 125.342 53.9218 124.948 54.2733C124.158 54.9764 123.352 55.5904 122.195 55.8098C121.304 55.9785 120.062 55.7527 119.143 55.2813C117.994 54.5764 117.33 53.5032 117.305 52.4008C117.305 52.4008 117.305 52.4008 117.288 52.3118C117.276 51.7606 117.593 48.5642 122.748 39.7462C122.82 39.6402 122.981 39.5174 123.053 39.4115C123.537 39.0431 124.266 38.9971 124.902 39.4302C125.431 39.791 125.778 40.6478 125.49 41.0715C124.39 42.5713 123.397 44.1433 122.615 45.8597C121.872 47.292 121.218 48.7074 120.775 50.2672C120.52 50.869 120.21 51.6658 120.078 52.4288C120.04 52.7128 120.04 52.7128 120.09 52.98C120.073 52.8909 120.073 52.8909 120.09 52.98C120.09 52.98 120.196 53.0522 120.107 53.069C120.107 53.069 120.107 53.069 120.196 53.0522C120.302 53.1243 120.514 53.2687 120.408 53.1965C120.514 53.2687 120.603 53.2518 120.709 53.324L120.798 53.3071C120.887 53.2902 120.976 53.2733 121.065 53.2565C121.421 53.189 121.51 53.1721 121.922 52.9096C122.511 52.6134 123.122 51.944 123.75 51.3638C123.967 51.046 124.289 50.8004 124.595 50.4658L136.04 38.3338L136.346 37.9992L136.579 37.7704ZM127.019 28.6973C127.642 28.5791 128.384 29.0843 128.519 29.7967C128.654 30.5091 128.148 31.2506 127.525 31.3687C126.813 31.5037 126.071 30.9985 125.936 30.2862C125.801 29.5738 126.306 28.8323 127.019 28.6973ZM155.498 36.7678C153.648 38.6867 147.346 44.8624 145.462 46.6031C143.183 48.6954 140.816 50.8046 138.465 53.0029C137.676 53.7059 136.903 54.498 136.114 55.2011C135.486 55.7814 134.646 56.2173 133.722 56.208C132.158 56.2277 131.23 54.743 131.066 53.3904C130.746 51.6985 131.243 49.9437 131.686 48.3839C131.979 47.4981 132.272 46.6123 132.566 45.7264C132.566 45.7264 132.638 45.6205 132.621 45.5315C133.02 44.7178 133.331 43.921 133.73 43.1074C133.802 43.0015 133.946 42.7896 134.002 42.5946C134.745 41.1623 135.794 39.3953 136.838 38.0905C136.982 37.8786 137.377 37.5271 137.788 37.2646C137.877 37.2477 137.86 37.1587 137.86 37.1587L137.949 37.1418C138.539 36.8456 139.307 36.5156 140.481 36.3852C140.854 36.4068 141.227 36.4283 141.617 36.5389C141.901 36.5773 142.503 36.8322 142.706 37.9008C142.964 38.7744 142.386 39.6218 141.992 39.9734C141.453 40.5367 140.808 41.028 140.04 41.3579C139.378 41.7601 138.581 41.4499 138.268 40.7712C138.179 40.7881 138.196 40.8772 138.124 40.9831C137.097 42.377 136.281 43.9153 135.555 45.4366C134.918 46.9411 134.369 48.4288 134.033 50.0607C133.795 50.7516 133.629 51.3365 133.586 52.0826C133.603 52.1717 133.637 52.3498 133.564 52.4557C133.564 52.4557 133.564 52.4557 133.581 52.5447C133.581 52.5447 133.581 52.5447 133.598 52.6338C133.632 52.8119 133.682 53.079 133.716 53.2571C133.733 53.3462 133.733 53.3462 133.75 53.4352C133.839 53.4184 133.856 53.5074 133.856 53.5074C133.945 53.4905 133.945 53.4905 133.945 53.4905C134.034 53.4737 134.106 53.3677 134.106 53.3677C134.84 52.8596 135.451 52.1903 136.079 51.6101C136.779 50.9239 137.496 50.3267 138.108 49.6574C140.959 47.1798 143.704 44.6301 146.45 42.0803C147.867 40.797 149.284 39.5137 150.612 38.2472C151.24 37.667 151.957 37.0698 152.569 36.4005C152.658 36.3837 152.658 36.3837 152.658 36.3837C153.052 36.0321 153.358 35.6975 153.752 35.3459C153.825 35.24 154.003 35.2063 154.075 35.1003L154.164 35.0835L154.469 34.7488C154.542 34.6429 154.542 34.6429 154.542 34.6429L154.936 34.2913C154.936 34.2913 156.376 36.0479 155.498 36.7678ZM133.784 53.6133C133.606 53.6471 133.695 53.6302 133.784 53.6133V53.6133ZM133.979 53.6686C133.979 53.6686 133.979 53.6686 133.962 53.5796L133.873 53.5965C133.979 53.6686 133.979 53.6686 133.979 53.6686ZM169.917 31.4521C169.917 31.4521 171.578 32.4288 170.406 34.0345L162.996 42.6343L160.98 45.1381C160.674 45.4727 159.524 46.7054 159.363 46.8282C158.985 47.2688 158.68 47.6034 158.285 47.955C157.496 48.658 156.69 49.272 155.532 49.4915C154.642 49.6602 153.4 49.4344 152.48 48.9629C151.332 48.2581 150.667 47.1849 150.643 46.0825C150.643 46.0825 150.643 46.0825 150.626 45.9935C150.613 45.4423 150.93 42.2459 156.085 33.4279C156.157 33.3219 156.319 33.1991 156.391 33.0932C156.874 32.7248 157.604 32.6788 158.239 33.1119C158.769 33.4727 159.116 34.3294 158.827 34.7531C157.728 36.253 156.734 37.825 155.952 39.5413C155.209 40.9737 154.555 42.3891 154.113 43.9489C153.858 44.5507 153.547 45.3475 153.415 46.1105C153.377 46.3945 153.377 46.3945 153.428 46.6617C153.411 46.5726 153.411 46.5726 153.428 46.6617C153.428 46.6617 153.533 46.7338 153.444 46.7507C153.444 46.7507 153.444 46.7507 153.533 46.7338C153.639 46.806 153.851 46.9503 153.745 46.8782C153.851 46.9503 153.94 46.9335 154.046 47.0056L154.135 46.9888C154.224 46.9719 154.313 46.955 154.402 46.9381C154.759 46.8706 154.848 46.8537 155.259 46.5913C155.849 46.295 156.46 45.6257 157.088 45.0455C157.304 44.7277 157.627 44.4821 157.932 44.1474L169.378 32.0155L169.683 31.6809L169.917 31.4521ZM160.356 22.379C160.98 22.2608 161.721 22.766 161.856 23.4784C161.991 24.1908 161.486 24.9323 160.862 25.0504C160.15 25.1854 159.409 24.6802 159.274 23.9678C159.139 23.2554 159.644 22.514 160.356 22.379ZM191.41 0.719064C191.978 0.795901 192.398 1.54671 192.07 2.25443C190.38 5.98803 188.761 9.6157 187.07 13.3493C184.177 19.986 181.373 26.6058 178.657 33.2087C175.836 39.7394 173.21 46.3255 170.601 53.0006C170.274 53.7083 169.557 54.3054 168.9 54.2455C168.243 54.1855 167.913 53.4178 168.24 52.7101C169.646 48.9381 171.159 45.2382 172.672 41.5384C175.387 34.9355 178.102 28.3326 180.996 21.6959C183.8 15.0761 186.71 8.52847 189.62 1.98084C190.036 1.25624 190.664 0.675986 191.41 0.719064ZM166.805 59.2548C167.517 59.1198 168.259 59.625 168.394 60.3374C168.512 60.9607 168.007 61.7022 167.294 61.8372C166.582 61.9722 165.946 61.5392 165.811 60.8268C165.676 60.1144 166.182 59.373 166.805 59.2548Z" fill="#192329"/>
</g>
<defs>
<clipPath id="clip0_584_706">
<rect width="192" height="111" fill="white" transform="translate(0.726562)"/>
</clipPath>
</defs>
</svg>


    </div>
</div>
    


</div>
<div class="side w-40-100 absolute top-0 right-0 bottom-0">

<div class="items font-ave-m text-24 flex flex-col overflow-hidden h-100-100 w-100-100 relative">
    
<div class="item py-20">
    Produrre <span class="bg-brand-violet-medium">musica per artisti</span> famosi
    </div>
    <div class="item py-20">
    Aprire il tuo <span class="bg-brand-pink-medium">studio di registrazione</span>
    </div>
    <div class="item py-20">
    Vivere guadagnando con la musica
    </div>
    <div class="item py-20">
    Vendere i tuoi <span class="bg-brand-yellow-medium">beat online</span>
    </div>
    <div class="item py-20">
    Creare un sound <span class="bg-brand-green-medium">unico e riconoscibile</span>
    </div>
    <div class="item py-20">
    <span class="bg-brand-red-medium">Autoprodurre</span> i tuoi brani da zero
    </div>
    <div class="item py-20">
    Diventare un produttore affermato
    </div>
    <div class="item py-20">
    Trasformare la <span class="bg-brand-ocean-medium">passione</span> in un lavoro
    </div>
    <div class="item py-20">
    Collaborare con artisti internazionali
    </div>
    <div class="item py-20">
    Dedicare il <span class="bg-brand-blue-medium">100% del tempo</span> alla musica
    </div>
    <div class="item py-20">
    Diventare musicalmente <span class="bg-brand-sky-medium">indipendente</span>
    </div>
    <div class="item py-20">
    Finalizzare ogni brano che inizi
    </div>
    <div class="item py-20">
    Produrre per i tuoi amici
    </div>
    <div class="item py-20">
    Gestire <span class="bg-brand-brown-medium">produzione, mix e master</span>
    </div>
    <div class="item py-20">
    Imparare a usare i programmi
    </div>
    <div class="item py-20">
    Avere una <span class="bg-brand-violet-medium">libreria suoni</span> e effetti vasta
    </div>
    <div class="item py-20">
    Realizzare un brano completo
    </div> 
<div class="item py-20">
    Produrre <span class="bg-brand-violet-medium">musica per artisti</span> famosi
    </div>
    <div class="item py-20">
    Aprire il tuo <span class="bg-brand-pink-medium">studio di registrazione</span>
    </div>
    <div class="item py-20">
    Vivere guadagnando con la musica
    </div>
    <div class="item py-20">
    Vendere i tuoi <span class="bg-brand-yellow-medium">beat online</span>
    </div>
    <div class="item py-20">
    Creare un sound <span class="bg-brand-green-medium">unico e riconoscibile</span>
    </div>
    <div class="item py-20">
    <span class="bg-brand-red-medium">Autoprodurre</span> i tuoi brani da zero
    </div>
    <div class="item py-20">
    Diventare un produttore affermato
    </div>
    <div class="item py-20">
    Trasformare la <span class="bg-brand-ocean-medium">passione</span> in un lavoro
    </div>
    <div class="item py-20">
    Collaborare con artisti internazionali
    </div>
    <div class="item py-20">
    Dedicare il <span class="bg-brand-blue-medium">100% del tempo</span> alla musica
    </div>
    <div class="item py-20">
    Diventare musicalmente <span class="bg-brand-sky-medium">indipendente</span>
    </div>
    <div class="item py-20">
    Finalizzare ogni brano che inizi
    </div>
    <div class="item py-20">
    Produrre per i tuoi amici
    </div>
    <div class="item py-20">
    Gestire <span class="bg-brand-brown-medium">produzione, mix e master</span>
    </div>
    <div class="item py-20">
    Imparare a usare i programmi
    </div>
    <div class="item py-20">
    Avere una <span class="bg-brand-violet-medium">libreria suoni</span> e effetti vasta
    </div>
    <div class="item py-20">
    Realizzare un brano completo
    </div>
</div>

</div>
</div>
</section>




<section class="home-target mb-140 relative z-60">

<div class="container-l mx-auto">

<div class="grid grid-cols-2 gap-40">


    <div class="box flex items-center h-500 relative bg-brand-dark rounded-24 pl-80">
        <div class="content relative z-80">
            <div class="pretitle text-brand-yellow font-tt-eb text-16 uppercase spacing-12 mb-28">
                Sei un principiante?
            </div>
            <div class="title text-white font-din-bc text-60 mb-40">
            Acquisisci da zero <br>le competenze <br>necessarie
            </div>
            <div class="cta flex gap-20 items-center">
                <div class="text font-ave-b text-20 text-white">
Maggiori dettagli
                </div>
                <div class="icon flex">
                <svg class="h-20" width="11" height="20" viewBox="0 0 11 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.5824 10.5826L2.43421 18.7308C1.87735 19.2877 0.974503 19.2877 0.417644 18.7308C-0.139215 18.1739 -0.139215 17.2711 0.417644 16.7142L7.55751 9.57436L0.417643 2.4345C-0.139215 1.87764 -0.139215 0.974792 0.417643 0.417934C0.974502 -0.138924 1.87735 -0.138924 2.43421 0.417934L10.5824 8.56608C11.1392 9.12294 11.1392 10.0258 10.5824 10.5826Z" fill="white"/>
</svg>

                </div>
            </div> 
        </div>
        <div class="background overflow-hidden absolute top-0 right-0 bottom-0 z-20 rounded-24">
           <?php load_img(class: 'img z-30 relative', high: 38, low: 37); ?>


        </div>
        <div class="subject absolute bottom-0 right-0 z-40 rounded-bottom-right-24 overflow-hidden">
        <?php load_img(class: 'img', high: 39); ?>
        </div>

        <div class="gradients absolute inset-0 overflow-hidden rounded-24 z-60">

<div class="radial-gradient-yellow animate-float-around animate-opacity-70-100 animation-duration-10000 absolute z-40 h-540 w-540 left--340 bottom--340"></div>
        </div>

        <div class="shapes absolute inset-0 z-60">
        <div class="shape animate-float-updown-light animation-duration-5000 absolute left--40 top-40">
        <svg class="h-80 shadow-yellow" width="84" height="81" viewBox="0 0 84 81" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_585_722)">
<path d="M61.0714 73.3806L82.4799 15.6238C85.5403 7.36736 77.2074 -0.528202 69.1278 2.97239L7.62662 29.6185C0.650762 32.6409 -0.534643 42.0341 5.47163 46.6948L45.5643 77.8054C50.9011 81.9466 58.7236 79.7145 61.0714 73.3806Z" fill="#FFBB44"/>
</g>
<defs>
<clipPath id="clip0_585_722">
<rect width="83" height="81" fill="white" transform="translate(0.824219)"/>
</clipPath>
</defs>
</svg>


    </div>
        </div>


    </div> <!-- box -->


    <div class="box flex items-center h-500 relative bg-brand-dark rounded-24 pl-80">
        <div class="content relative z-80">
            <div class="pretitle text-brand-pink font-tt-eb text-16 uppercase spacing-12 mb-28">
            Sei un produttore?
            </div>
            <div class="title text-white font-din-bc text-60 mb-40">
            Migliora con il <br>supporto di <br>Produzione Hip Hop
            </div>
            <div class="cta flex gap-20 items-center">
                <div class="text font-ave-b text-20 text-white">
Maggiori dettagli
                </div>
                <div class="icon flex">
                <svg class="h-20" width="11" height="20" viewBox="0 0 11 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.5824 10.5826L2.43421 18.7308C1.87735 19.2877 0.974503 19.2877 0.417644 18.7308C-0.139215 18.1739 -0.139215 17.2711 0.417644 16.7142L7.55751 9.57436L0.417643 2.4345C-0.139215 1.87764 -0.139215 0.974792 0.417643 0.417934C0.974502 -0.138924 1.87735 -0.138924 2.43421 0.417934L10.5824 8.56608C11.1392 9.12294 11.1392 10.0258 10.5824 10.5826Z" fill="white"/>
</svg>

                </div>
            </div> 
        </div>
        <div class="background overflow-hidden absolute top-0 right-0 bottom-0 z-20 rounded-24">
           <?php load_img(class: 'img z-30 relative', high: 40, low: 41); ?>


        </div>
        <div class="subject absolute bottom-0 right-0 z-40 rounded-bottom-right-24 overflow-hidden">
        <?php load_img(class: 'img', high: 42); ?>
        </div>

        <div class="gradients absolute inset-0 overflow-hidden rounded-24 z-60">

<div class="radial-gradient-pink animate-float-around animate-opacity-70-100 animation-duration-6000 absolute z-40 h-540 w-540 left--240 top--240"></div>
        </div>

        <div class="shapes absolute inset-0 z-60">
        <div class="shape animate-float-updown-light animation-duration-5000 absolute right--40 bottom-40">
        <svg class="h-84 shadow-pink" width="73" height="85" viewBox="0 0 73 85" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_589_667)">
<path d="M67.0395 74.8437L71.7773 11.0321C72.3886 2.79846 63.323 -2.58705 56.3847 1.88789L5.52556 34.6898C-0.511803 38.5836 -0.595878 47.3837 5.36599 51.3922L51.4874 82.402C57.8544 86.6828 66.4715 82.495 67.0395 74.8437Z" fill="#E04E9D"/>
</g>
<defs>
<clipPath id="clip0_589_667">
<rect width="72" height="85" fill="white" transform="translate(0.941406)"/>
</clipPath>
</defs>
</svg>




    </div>
        </div>


    </div> <!-- box -->


    <div class="box flex items-center h-500 relative bg-brand-dark rounded-24 pl-80 left--80">
        <div class="content relative z-80">
            <div class="pretitle text-brand-blue font-tt-eb text-16 uppercase spacing-12 mb-28">
            Sei un fonico?
            </div>
            <div class="title text-white font-din-bc text-60 mb-40">
            Perfeziona le <br>tecniche di mix e <br>mastering hip hop
            </div>
            <div class="cta flex gap-20 items-center">
                <div class="text font-ave-b text-20 text-white">
Maggiori dettagli
                </div>
                <div class="icon flex">
                <svg class="h-20" width="11" height="20" viewBox="0 0 11 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.5824 10.5826L2.43421 18.7308C1.87735 19.2877 0.974503 19.2877 0.417644 18.7308C-0.139215 18.1739 -0.139215 17.2711 0.417644 16.7142L7.55751 9.57436L0.417643 2.4345C-0.139215 1.87764 -0.139215 0.974792 0.417643 0.417934C0.974502 -0.138924 1.87735 -0.138924 2.43421 0.417934L10.5824 8.56608C11.1392 9.12294 11.1392 10.0258 10.5824 10.5826Z" fill="white"/>
</svg>

                </div>
            </div> 
        </div>
        <div class="background overflow-hidden absolute top-0 right-0 bottom-0 z-20 rounded-24">
           <?php load_img(class: 'img z-30 relative', high: 43, low: 44); ?>


        </div>
        <div class="subject absolute bottom-0 right-0 z-40 rounded-bottom-right-24 overflow-hidden">
        <?php load_img(class: 'img', high: 45); ?>
        </div>

        <div class="gradients absolute inset-0 overflow-hidden rounded-24 z-60">

<div class="radial-gradient-blue animate-float-around animate-opacity-70-100 animation-duration-8000 absolute z-40 h-540 w-540 bottom--500 right-200"></div>
        </div>

        <div class="shapes absolute inset-0 z-60">
        <div class="shape animate-float-updown-light animation-duration-8000 absolute right--40 top-60">
        <svg class="h-80 shadow-blue" width="76" height="80" viewBox="0 0 76 80" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_589_720)">
<path d="M73.5918 44.712L9.27885 1.97265C5.69327 -0.410151 1.0167 2.73165 1.86719 6.95193L15.8065 76.1212C16.4431 79.2804 19.935 80.943 22.7887 79.4457L73.1623 53.0157C76.4136 51.3098 76.6497 46.7442 73.5918 44.712Z" fill="#1289ED"/>
</g>
<defs>
<clipPath id="clip0_589_720">
<rect width="75" height="80" fill="white" transform="translate(0.890625)"/>
</clipPath>
</defs>
</svg>



    </div>
        </div>


    </div> <!-- box -->

    <div class="box left--80 relative">
        <div class="inner flex items-center h-500 relative bg-brand-dark rounded-24 pl-80 z-20">
        <div class="content relative z-80">
            <div class="pretitle text-brand-green font-tt-eb text-16 uppercase spacing-12 mb-28">
                Sei un cantante?
            </div>
            <div class="title text-white font-din-bc text-60 mb-40">
            Produci i tuoi <br>brani in <br>autonomia
            </div>
            <div class="cta flex gap-20 items-center">
                <div class="text font-ave-b text-20 text-white">
Maggiori dettagli
                </div>
                <div class="icon flex">
                <svg class="h-20" width="11" height="20" viewBox="0 0 11 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.5824 10.5826L2.43421 18.7308C1.87735 19.2877 0.974503 19.2877 0.417644 18.7308C-0.139215 18.1739 -0.139215 17.2711 0.417644 16.7142L7.55751 9.57436L0.417643 2.4345C-0.139215 1.87764 -0.139215 0.974792 0.417643 0.417934C0.974502 -0.138924 1.87735 -0.138924 2.43421 0.417934L10.5824 8.56608C11.1392 9.12294 11.1392 10.0258 10.5824 10.5826Z" fill="white"/>
</svg>

                </div>
            </div> 
        </div>
        <div class="background overflow-hidden absolute top-0 right-0 bottom-0 z-20 rounded-24">
           <?php load_img(class: 'img z-30 relative', high: 48, low: 47); ?>


        </div>
        <div class="subject absolute bottom-0 right-0 z-40 rounded-bottom-right-24 overflow-hidden">
        <?php load_img(class: 'img', high: 46); ?>
        </div>

        <div class="gradients absolute inset-0 overflow-hidden rounded-24 z-60">

<div class="radial-gradient-green animate-float-around animate-opacity-70-100 animation-duration-5000 absolute z-40 h-540 w-540 top--300 left--300"></div>
        </div>


</div>


<div class="shapes absolute inset-0 z-10">
        <div class="shape animate-float-updown-light animation-duration-5000 absolute right--40 bottom-60">
        <svg class="h-80 shadow-green" width="73" height="85" viewBox="0 0 73 85" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_589_751)">
<path d="M67.02 74.8437L71.7578 11.0321C72.3691 2.79846 63.3035 -2.58705 56.3652 1.88789L5.50602 34.6898C-0.531334 38.5836 -0.615409 47.3837 5.34646 51.3922L51.4678 82.402C57.8349 86.6828 66.4519 82.495 67.02 74.8437Z" fill="#25D366"/>
</g>
<defs>
<clipPath id="clip0_589_751">
<rect width="72" height="85" fill="white" transform="translate(0.9375)"/>
</clipPath>
</defs>
</svg>



    </div>
        </div>
    </div> <!-- box -->







</div>

</div>


<div class="gradients absolute inset-0 z-10">

<div class="radial-gradient-sky absolute z-40 h-1200 w-1200 top--500 right--1000"></div>





</div>

</section>
 


<section class="home-corsi mb-140">

<div class="container-l mx-auto"> 


<div class="head flex items-center justify-between gap-140 mb-100">
    <div class="main max-w-900 flex flex-col items-start">
        <div class="pretitle text-gradient-ocean-violet font-tt-eb text-16 uppercase spacing-12 mb-28">
        I corsi di Produzione Hip Hop
        </div>
        <div class="title font-din-bc text-60 mb-20">
        La nostra offerta formativa comprende corsi completi nati da anni di esperienza
        </div>
        <div class="text text-gray font-ave-m leading-15 text-20 max-w-700">
       Abbiamo creato un'offerta formativa pensata per darti un metodo testato e tutte le competenze necessarie.
        </div>

    </div>
    <div class="side relative">
        <a href="https://app-656e0683c1ac188b30f693df.closte.com/corsi/" class="button h-72 w-400 rounded-16 text-center border-gradient-ocean-violet border-4 border-solid border-transparent rounded-12 flex gap-20 place-center z-20 relative equalizer-1-hover hover-shadow-violet hover-up-2">
            <div class="text font-ave-b text-20">
            Vai alla pagina dei corsi
            </div>
            <div class="icon flex">
            <svg class="h-20" width="11" height="20" viewBox="0 0 11 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.5824 10.5826L2.43421 18.7308C1.87735 19.2877 0.974503 19.2877 0.417644 18.7308C-0.139215 18.1739 -0.139215 17.2711 0.417644 16.7142L7.55751 9.57436L0.417643 2.4345C-0.139215 1.87764 -0.139215 0.974792 0.417643 0.417934C0.974502 -0.138924 1.87735 -0.138924 2.43421 0.417934L10.5824 8.56608C11.1392 9.12294 11.1392 10.0258 10.5824 10.5826Z" fill="#192329"/>
</svg>



        </div>
</a>

    <div id="equalizer-container-1" class="absolute-center-y right-300 z-10"></div>
<!--
    <div class="pattern-container w-300 h-200 absolute-center-y right-300 z-10">

</div>  -->
</div>

</div>
<div class="body">

<div class="items grid grid-cols-3 gap-40">

<?php
$args = array(
    'post_type' => 'corsi',
    'posts_per_page' => 3
);

$corsi_query = new WP_Query($args);

if ($corsi_query->have_posts()) :
    while ($corsi_query->have_posts()) : $corsi_query->the_post();

        $corso_cover_id    = null;
        $corso_icona_id    = null;
        $corso_durata      = '';
        $corso_descrizione = '';
        $color_class_name  = 'violet';
        $rwmb_get_value_func = 'rwmb_get_value';

        if (function_exists($rwmb_get_value_func)) {
            $corso_cover_data = $rwmb_get_value_func('corso_cover', ['size' => 'large']);
            $corso_cover_id   = $corso_cover_data['ID'] ?? null;

            $corso_icona_data = $rwmb_get_value_func('corso_icona');
            $corso_icona_id   = $corso_icona_data['ID'] ?? null;

            $corso_durata      = $rwmb_get_value_func('corso_durata');
            $corso_descrizione = $rwmb_get_value_func('corso_descrizione');
            $db_color          = $rwmb_get_value_func('corso_colore');
            if (!empty($db_color)) {
                $color_class_name = $db_color;
            }
        }
        ?>

        <div class="item rounded-24 bg-white"> 
            <div class="head relative mb-40">
                <div class="cover h-200 relative rounded-24 overflow-hidden">
                    <?php if ($corso_cover_id) : ?>
                        <?php load_img(class: 'img img-cover', high: $corso_cover_id); ?>
                    <?php endif; ?>
                </div>
                <div class="badge absolute bottom--40 right-40 animate-float-updown-very-light animation-duration-5000">
                    <?php if ($corso_icona_id) : ?>
                        <?php load_img(class: 'img h-120 shadow-' . $color_class_name, high: $corso_icona_id); ?>
                    <?php endif; ?>
                </div>
            </div>
            <div class="body">
                <div class="pretitle text-brand-<?php echo esc_attr($color_class_name); ?> font-tt-eb text-16 uppercase spacing-12 mb-28">
                    Corso
                </div>
                <div class="title font-din-bc text-48 mb-20">
                    <?php the_title(); ?>
                </div>
                <div class="text text-gray font-ave-m leading-15 text-20 mb-40 max-w-90-100">
                    <?php echo esc_html($corso_descrizione); ?>
                </div>
                <div class="info flex gap-28 mb-40">
                    <div class="time flex gap-20 items-center">
                        <div class="icon flex">
                            <svg class="h-28" width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <circle cx="13" cy="13" r="11.75" stroke="#121D23" stroke-width="2.5"/>
                                <path d="M13 6.5V13L9 15.3094" stroke="#121D23" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <div class="text font-ave-d text-20">
                            <?php echo esc_html($corso_durata); ?>
                        </div>
                    </div>
                </div>
                <a href="<?php the_permalink(); ?>" class="button mt-auto h-72 w-80-100 rounded-16 text-center border-brand-<?php echo esc_attr($color_class_name); ?> border-4 border-solid rounded-12 flex place-center z-20 relative hover-shadow-<?php echo esc_attr($color_class_name); ?> hover-up-2">
                    <div class="text font-ave-b text-20">
                        Maggiori informazioni
                    </div>
                </a>
            </div>
        </div> <!-- item -->

    <?php
    endwhile;
    wp_reset_postdata();
else :
    ?>
    <p><?php _e('Nessun corso trovato.', 'custom-theme'); ?></p>
    <?php
endif;
?>

</div>

</div>


</section>



<section class="home-mockup mb-80 relative">

<div class="main absolute-center z-50">
    <div class="mockup relative z-40">
        <div class="computer relative"> 
    <?php load_img(class: 'img h-600 shadow-dark', high: 87); ?>
</div>
        <div class="screen absolute-center-x top-20 z-30 w-700 h-440 overflow-hidden rounded-4">
        <?php load_img(class: 'img img-cover', high: 34); ?>
        </div>
    </div>

    <div class="shapes absolute inset-0 z-60">    
    <div class="shape-1 animate-float-updown-light animation-duration-5000 absolute right-40 top-240">
        <svg class="h-80 shadow-ocean" width="84" height="79" viewBox="0 0 84 79" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_593_1151)">
<g filter="url(#filter0_d_593_1151)">
<path d="M60.8956 71.7204L82.3041 13.9636C85.3645 5.7072 77.0316 -2.18836 68.952 1.31223L7.45084 27.9584C0.474981 30.9807 -0.710424 40.3739 5.29585 45.0346L45.3885 76.1453C50.7253 80.2865 58.5478 78.0543 60.8956 71.7204Z" fill="#2758D6"/>
</g>
</g>
<defs>
<filter id="filter0_d_593_1151" x="-26.5742" y="-19.5391" width="137.527" height="133.789" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
<feOffset dy="8"/>
<feGaussianBlur stdDeviation="14"/>
<feComposite in2="hardAlpha" operator="out"/>
<feColorMatrix type="matrix" values="0 0 0 0 0.156863 0 0 0 0 0.235294 0 0 0 0 0.313726 0 0 0 0.15 0"/>
<feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_593_1151"/>
<feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_593_1151" result="shape"/>
</filter>
<clipPath id="clip0_593_1151">
<rect width="83" height="79" fill="white" transform="translate(0.824219)"/>
</clipPath>
</defs>
</svg>


        </div>

    </div>

</div>

<div class="rows flex flex-col gap-20 relative">



    <div class="row h-220">
        <div class="items absolute-center-x flex gap-20">

        <div class="item w-300 h-220 bg-brand-dark relative flex place-center text-center rounded-12 overflow-hidden px-40">
            <div class="cover absolute inset-0 z-40">
                <?php load_img(class: 'img img-cover', high: 50, low: 49); ?>
            </div>
            <div class="text font-din-bc text-36 text-white relative z-80">
Organizzazione del progetto
</div>
<div class="gradients absolute inset-0 z-60">
    <div class="radial-gradient-violet absolute z-40 h-400 w-400 top--240 right--240"></div>
    <div class="radial-gradient-green absolute z-40 h-400 w-400 bottom--240 left--240"></div>
</div>
        </div> <!-- item -->

        <div class="item w-300 h-220 bg-brand-dark relative flex place-center text-center rounded-12 overflow-hidden px-40">
            <div class="cover absolute inset-0 z-40">
                <?php load_img(class: 'img img-cover', high: 52, low: 51); ?>
            </div>
            <div class="text font-din-bc text-36 text-white relative z-80">
Mastering
</div>
<div class="gradients absolute inset-0 z-60">
    <div class="radial-gradient-yellow absolute z-40 h-400 w-400 top--240 right--240"></div>
    <div class="radial-gradient-red absolute z-40 h-400 w-400 bottom--240 left--240"></div>
</div>
        </div> <!-- item -->

        <div class="item w-300 h-220 bg-brand-dark relative flex place-center text-center rounded-12 overflow-hidden px-40">
            <div class="cover absolute inset-0 z-40">
                <?php load_img(class: 'img img-cover', high: 54, low: 53); ?>
            </div>
            <div class="text font-din-bc text-36 text-white relative z-80">
Compressione
</div>
<div class="gradients absolute inset-0 z-60">
    <div class="radial-gradient-blue absolute z-40 h-400 w-400 top--240 right--240"></div>
    <div class="radial-gradient-violet absolute z-40 h-400 w-400 bottom--240 left--240"></div>
</div>
        </div> <!-- item -->

        <div class="item w-300 h-220 bg-brand-dark relative flex place-center text-center rounded-12 overflow-hidden px-40">
            <div class="cover absolute inset-0 z-40">
                <?php load_img(class: 'img img-cover', high: 56, low: 55); ?>
            </div>
            <div class="text font-din-bc text-36 text-white relative z-80">
Mix voci
</div>
<div class="gradients absolute inset-0 z-60">
    <div class="radial-gradient-yellow absolute z-40 h-400 w-400 top--240 right--240"></div>
    <div class="radial-gradient-red absolute z-40 h-400 w-400 bottom--240 left--240"></div>
</div>
        </div> <!-- item -->

        <div class="item w-300 h-220 bg-brand-dark relative flex place-center text-center rounded-12 overflow-hidden px-40">
            <div class="cover absolute inset-0 z-40">
                <?php load_img(class: 'img img-cover', high: 58, low: 57); ?>
            </div>
            <div class="text font-din-bc text-36 text-white relative z-80">
Allineamento volumi
</div>
<div class="gradients absolute inset-0 z-60">
    <div class="radial-gradient-blue absolute z-40 h-400 w-400 top--240 right--240"></div>
    <div class="radial-gradient-violet absolute z-40 h-400 w-400 bottom--240 left--240"></div>
</div>
        </div> <!-- item -->

        <div class="item w-300 h-220 bg-brand-dark relative flex place-center text-center rounded-12 overflow-hidden px-40">
            <div class="cover absolute inset-0 z-40">
                <?php load_img(class: 'img img-cover', high: 60, low: 59); ?>
            </div>
            <div class="text font-din-bc text-36 text-white relative z-80">
Le fondamenta del beatmaking
</div>
<div class="gradients absolute inset-0 z-60">
    <div class="radial-gradient-red absolute z-40 h-400 w-400 top--240 right--240"></div>
    <div class="radial-gradient-brown absolute z-40 h-400 w-400 bottom--240 left--240"></div>
</div>
        </div> <!-- item -->

        <div class="item w-300 h-220 bg-brand-dark relative flex place-center text-center rounded-12 overflow-hidden px-40">
            <div class="cover absolute inset-0 z-40">
                <?php load_img(class: 'img img-cover', high: 62, low: 61); ?>
            </div>
            <div class="text font-din-bc text-36 text-white relative z-80">
I parametri del campionamento
</div>
<div class="gradients absolute inset-0 z-60">
    <div class="radial-gradient-ocean absolute z-40 h-400 w-400 top--240 right--240"></div>
    <div class="radial-gradient-yellow absolute z-40 h-400 w-400 bottom--240 left--240"></div>
</div>
        </div> <!-- item -->

        <div class="item w-300 h-220 bg-brand-dark relative flex place-center text-center rounded-12 overflow-hidden px-40">
            <div class="cover absolute inset-0 z-40">
                <?php load_img(class: 'img img-cover', high: 64, low: 63); ?>
            </div>
            <div class="text font-din-bc text-36 text-white relative z-80">
Trattamento acustico
</div>
<div class="gradients absolute inset-0 z-60">
    <div class="radial-gradient-sky absolute z-40 h-400 w-400 top--240 right--240"></div>
    <div class="radial-gradient-yellow absolute z-40 h-400 w-400 bottom--240 left--240"></div>
</div>
        </div> <!-- item -->


</div>


    </div> <!-- row -->



    <div class="row h-220">
        <div class="items absolute-center-x flex gap-20">

        <div class="item w-300 h-220 bg-brand-dark relative flex place-center text-center rounded-12 overflow-hidden px-40">
            <div class="cover absolute inset-0 z-40">
                <?php load_img(class: 'img img-cover', high: 66, low: 65); ?>
            </div>
            <div class="text font-din-bc text-36 text-white relative z-80">
Batterie e percussioni
</div>
<div class="gradients absolute inset-0 z-60">
    <div class="radial-gradient-green absolute z-40 h-400 w-400 top--240 right--240"></div>
    <div class="radial-gradient-yellow absolute z-40 h-400 w-400 bottom--240 left--240"></div>
</div>
        </div> <!-- item -->

        <div class="item w-300 h-220 bg-brand-dark relative flex place-center text-center rounded-12 overflow-hidden px-40">
            <div class="cover absolute inset-0 z-40">
                <?php load_img(class: 'img img-cover', high: 68, low: 67); ?>
            </div>
            <div class="text font-din-bc text-36 text-white relative z-80">
Panning & stereo
</div>
<div class="gradients absolute inset-0 z-60">
    <div class="radial-gradient-sky absolute z-40 h-400 w-400 top--240 right--240"></div>
    <div class="radial-gradient-violet absolute z-40 h-400 w-400 bottom--240 left--240"></div>
</div>
        </div> <!-- item -->

        <div class="item w-300 h-220 bg-brand-dark relative flex place-center text-center rounded-12 overflow-hidden px-40">
            <div class="cover absolute inset-0 z-40">
                <?php load_img(class: 'img img-cover', high: 70, low: 69); ?>
            </div>
            <div class="text font-din-bc text-36 text-white relative z-80">
Equalizzazione
</div>
<div class="gradients absolute inset-0 z-60">
    <div class="radial-gradient-violet absolute z-40 h-400 w-400 top--240 left--240"></div>
    <div class="radial-gradient-blue absolute z-40 h-400 w-400 bottom--240 right--240"></div>
</div>
        </div> <!-- item -->



        <div class="empty w-900 h-220">


        </div>





        <div class="item w-300 h-220 bg-brand-dark relative flex place-center text-center rounded-12 overflow-hidden px-40">
            <div class="cover absolute inset-0 z-40">
                <?php load_img(class: 'img img-cover', high: 72, low: 71); ?>
            </div>
            <div class="text font-din-bc text-36 text-white relative z-80">
Riverberi e delay
</div>
<div class="gradients absolute inset-0 z-60">
    <div class="radial-gradient-blue absolute z-40 h-400 w-400 top--240 right--240"></div>
    <div class="radial-gradient-sky absolute z-40 h-400 w-400 bottom--240 left--240"></div>
</div>
        </div> <!-- item -->

        <div class="item w-300 h-220 bg-brand-dark relative flex place-center text-center rounded-12 overflow-hidden px-40">
            <div class="cover absolute inset-0 z-40">
                <?php load_img(class: 'img img-cover', high: 74, low: 73); ?>
            </div>
            <div class="text font-din-bc text-36 text-white relative z-80">
Stereofonia
</div>
<div class="gradients absolute inset-0 z-60">
    <div class="radial-gradient-yellow absolute z-40 h-400 w-400 top--240 left--240"></div>
    <div class="radial-gradient-sky absolute z-40 h-400 w-400 bottom--240 right--240"></div>
</div>
        </div> <!-- item -->

        <div class="item w-300 h-220 bg-brand-dark relative flex place-center text-center rounded-12 overflow-hidden px-40">
            <div class="cover absolute inset-0 z-40">
                <?php load_img(class: 'img img-cover', high: 76, low: 75); ?>
            </div>
            <div class="text font-din-bc text-36 text-white relative z-80">
Armonie ed accordi
</div>
<div class="gradients absolute inset-0 z-60">
    <div class="radial-gradient-yellow absolute z-40 h-400 w-400 top--240 right--240"></div>
    
</div>
        </div> <!-- item -->


</div>


    </div> <!-- row -->





    <div class="row h-220">
        <div class="items absolute-center-x flex gap-20">

        <div class="item w-300 h-220 bg-brand-dark relative flex place-center text-center rounded-12 overflow-hidden px-40">
            <div class="cover absolute inset-0 z-40">
                <?php load_img(class: 'img img-cover', high: 78, low: 77); ?>
            </div>
            <div class="text font-din-bc text-36 text-white relative z-80">
Scale maggiori
</div>
<div class="gradients absolute inset-0 z-60">
    <div class="radial-gradient-yellow absolute z-40 h-400 w-400 top--240 right--240"></div>
    <div class="radial-gradient-violet absolute z-40 h-400 w-400 bottom--240 left--240"></div>
</div>
        </div> <!-- item -->

        <div class="item w-300 h-220 bg-brand-dark relative flex place-center text-center rounded-12 overflow-hidden px-40">
            <div class="cover absolute inset-0 z-40">
                <?php load_img(class: 'img img-cover', high: 80, low: 79); ?>
            </div>
            <div class="text font-din-bc text-36 text-white relative z-80">
Mixer audio
</div>
<div class="gradients absolute inset-0 z-60">
    <div class="radial-gradient-sky absolute z-40 h-400 w-400 bottom--240 right--240"></div>
</div>
        </div> <!-- item -->



        <div class="empty w-1200 h-220">


        </div>





        <div class="item w-300 h-220 bg-brand-dark relative flex place-center text-center rounded-12 overflow-hidden px-40">
            <div class="cover absolute inset-0 z-40">
                <?php load_img(class: 'img img-cover', high: 82, low: 81); ?>
            </div>
            <div class="text font-din-bc text-36 text-white relative z-80">
Saturazione
</div>
<div class="gradients absolute inset-0 z-60">
    <div class="radial-gradient-blue absolute z-40 h-400 w-400 top--240 right--240"></div>
    <div class="radial-gradient-sky absolute z-40 h-400 w-400 bottom--240 left--240"></div>
</div>
        </div> <!-- item -->

        <div class="item w-300 h-220 bg-brand-dark relative flex place-center text-center rounded-12 overflow-hidden px-40">
            <div class="cover absolute inset-0 z-40">
                <?php load_img(class: 'img img-cover', high: 84, low: 83); ?>
            </div>
            <div class="text font-din-bc text-36 text-white relative z-80">
Processing parallelo
</div>
<div class="gradients absolute inset-0 z-60">
    <div class="radial-gradient-yellow absolute z-40 h-400 w-400 top--240 right--240"></div>
    <div class="radial-gradient-violet absolute z-40 h-400 w-400 bottom--240 left--240"></div>
</div>
        </div> <!-- item -->


</div>


    </div> <!-- row -->


    <div class="shapes absolute-center-x h-100-100 z-40">
        <div class="shape-1 animate-float-updown-light animation-duration-8000 absolute right-540 bottom-100">
        <svg class="h-120 shadow-sky" width="107" height="116" viewBox="0 0 107 116" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_593_1149)">
<path d="M102.744 63.0023L29.9301 2.98856C23.8337 -2.03613 14.5923 1.66005 13.6425 9.50298L2.35452 102.708C1.44345 110.23 8.90437 115.993 15.953 113.212L100.055 80.0209C107.141 77.2243 108.623 67.8476 102.744 63.0023Z" fill="#56CCF2"/>
</g>
<defs>
<clipPath id="clip0_593_1149">
<rect width="106" height="116" fill="white" transform="translate(0.976562)"/>
</clipPath>
</defs>
</svg>

        </div>

    </div>
</div>


</section>


<section class="home-brands mb-140">

<div class="container-l mx-auto">

<div class="head mb-60">

<div class="title font-ave-d text-24 text-center">
    Ci affidiamo ai seguenti brand
</div>

</div>
<div class="body">

<div class="items grid grid-cols-8 gap-40 items-center">

<div class="item">
        <?php load_img(class: 'img max-w-80-100', high: 88); ?>
</div>

<div class="item">
        <?php load_img(class: 'img max-w-80-100', high: 89); ?>
</div>

<div class="item">
        <?php load_img(class: 'img max-w-80-100', high: 90); ?>
</div>

<div class="item">
        <?php load_img(class: 'img max-w-80-100', high: 91); ?>
</div>


<div class="item">
        <?php load_img(class: 'img max-w-80-100', high: 92); ?>
</div>

<div class="item">
        <?php load_img(class: 'img max-w-80-100', high: 93); ?>
</div>

<div class="item">
        <?php load_img(class: 'img max-w-80-100', high: 94); ?>
</div>

<div class="item">
        <?php load_img(class: 'img max-w-80-100', high: 95); ?>
</div>



</div>

</div>

</div>


</section>



<section class="home-key mb-140">

<div class="container-l mx-auto">

<div class="boxes grid grid-cols-12 gap-40">

<div class="box bg-brand-yellow bg-opacity-10 relative rounded-24 col-span-5 flex flex-col">

<div class="head p-80 mb-10">
    <div class="icon mb-48">

    <svg class="h-100" width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="100" height="100" rx="15" fill="#FFE9C2"/>
<path d="M54.8792 55.4523C55.1211 55.5664 55.3864 55.6223 55.6538 55.6154C55.9221 55.6169 56.188 55.5655 56.4364 55.4641C56.6847 55.3626 56.9106 55.2132 57.1011 55.0242L64.6434 47.4615H73.0011C74.3456 47.4562 75.6335 46.9197 76.5843 45.969C77.535 45.0182 78.0715 43.7303 78.0768 42.3858V28.1166C78.0795 27.4473 77.9504 26.7841 77.6967 26.1648C77.4431 25.5455 77.07 24.9822 76.5986 24.5071C76.1273 24.032 75.567 23.6543 74.9497 23.3958C74.3325 23.1372 73.6703 23.0027 73.0011 23H52.6165C51.9473 22.9974 51.284 23.1265 50.6647 23.3802C50.0454 23.6338 49.4821 24.0069 49.007 24.4782C48.5319 24.9496 48.1542 25.5099 47.8957 26.1271C47.6371 26.7444 47.5026 27.4066 47.5 28.0758V37.2693H29.745C28.5086 37.2746 27.3244 37.7682 26.4501 38.6424C25.5758 39.5167 25.0823 40.7009 25.0769 41.9373V65.8077H23.0385C22.4978 65.8077 21.9793 66.0224 21.5971 66.4047C21.2148 66.787 21 67.3055 21 67.8461V69.7419C21 71.4016 21.6593 72.9934 22.8329 74.167C24.0066 75.3406 25.5983 76 27.2581 76H33.2308C33.7714 76 34.2899 75.7852 34.6722 75.4029C35.0544 75.0206 35.2692 74.5021 35.2692 73.9615C35.2692 73.4209 35.0544 72.9024 34.6722 72.5201C34.2899 72.1378 33.7714 71.923 33.2308 71.923H27.2581C26.7035 71.9242 26.1693 71.7141 25.7641 71.3355C25.3589 70.9568 25.1132 70.438 25.0769 69.8846H73.9999C73.9636 70.438 73.7179 70.9568 73.3127 71.3355C72.9076 71.7141 72.3734 71.9242 71.8188 71.923H41.3846C40.844 71.923 40.3255 72.1378 39.9432 72.5201C39.5609 72.9024 39.3461 73.4209 39.3461 73.9615C39.3461 74.5021 39.5609 75.0206 39.9432 75.4029C40.3255 75.7852 40.844 76 41.3846 76H71.8188C72.6413 76.0027 73.4563 75.8426 74.2168 75.5291C74.9773 75.2155 75.6682 74.7547 76.2499 74.173C76.8316 73.5914 77.2924 72.9004 77.606 72.1399C77.9195 71.3795 78.0795 70.5645 78.0768 69.7419V67.8461C78.0768 67.3055 77.8621 66.787 77.4798 66.4047C77.0975 66.0224 76.579 65.8077 76.0384 65.8077H73.9999V53.5769C73.9999 53.0363 73.7852 52.5178 73.4029 52.1355C73.0206 51.7532 72.5021 51.5385 71.9615 51.5385C71.4208 51.5385 70.9023 51.7532 70.5201 52.1355C70.1378 52.5178 69.923 53.0363 69.923 53.5769V65.8077H47.5V62.75C47.4974 61.7994 47.305 60.859 46.9339 59.9838C46.5629 59.1087 46.0207 58.3165 45.3392 57.6538C46.3569 56.6642 47.0563 55.3934 47.3479 54.0041C47.6394 52.6147 47.5099 51.17 46.9758 49.8547C46.4417 48.5394 45.5274 47.4133 44.3499 46.6205C43.1723 45.8277 41.785 45.4042 40.3654 45.4042C38.9458 45.4042 37.5584 45.8277 36.3809 46.6205C35.2033 47.4133 34.289 48.5394 33.7549 49.8547C33.2208 51.17 33.0913 52.6147 33.3828 54.0041C33.6744 55.3934 34.3738 56.6642 35.3915 57.6538C34.71 58.3165 34.1678 59.1087 33.7968 59.9838C33.4257 60.859 33.2333 61.7994 33.2308 62.75V65.8077H29.1538V41.9373C29.1538 41.7805 29.2161 41.6302 29.327 41.5193C29.4378 41.4084 29.5882 41.3462 29.745 41.3462H47.5V42.3858C47.5053 43.7303 48.0418 45.0182 48.9925 45.969C49.9433 46.9197 51.2312 47.4562 52.5757 47.4615H53.6153V53.5769C53.6173 53.9794 53.7384 54.3722 53.9633 54.7059C54.1883 55.0397 54.5069 55.2994 54.8792 55.4523ZM37.3077 52.5577C37.3077 51.9529 37.487 51.3618 37.823 50.8589C38.159 50.3561 38.6365 49.9642 39.1952 49.7328C39.754 49.5013 40.3687 49.4408 40.9619 49.5588C41.555 49.6767 42.0998 49.968 42.5275 50.3956C42.9551 50.8232 43.2463 51.368 43.3643 51.9612C43.4823 52.5543 43.4217 53.1691 43.1903 53.7278C42.9589 54.2865 42.5669 54.7641 42.0641 55.1001C41.5613 55.436 40.9701 55.6154 40.3654 55.6154C39.5544 55.6154 38.7767 55.2932 38.2032 54.7198C37.6298 54.1464 37.3077 53.3686 37.3077 52.5577ZM37.3077 62.75C37.3077 61.939 37.6298 61.1613 38.2032 60.5879C38.7767 60.0144 39.5544 59.6923 40.3654 59.6923C41.1763 59.6923 41.954 60.0144 42.5275 60.5879C43.1009 61.1613 43.423 61.939 43.423 62.75V65.8077H37.3077V62.75ZM52.5757 43.3846C52.4446 43.3846 52.3147 43.3588 52.1935 43.3086C52.0723 43.2584 51.9622 43.1848 51.8694 43.0921C51.7767 42.9993 51.7031 42.8892 51.6529 42.768C51.6027 42.6468 51.5769 42.517 51.5769 42.3858V28.1166C51.5714 27.982 51.5932 27.8477 51.6409 27.7218C51.6886 27.5959 51.7613 27.4809 51.8546 27.3838C51.9479 27.2867 52.0599 27.2094 52.1838 27.1567C52.3077 27.104 52.4411 27.0768 52.5757 27.077H72.9603C73.2252 27.077 73.4793 27.1822 73.6666 27.3695C73.8539 27.5568 73.9592 27.8109 73.9592 28.0758V42.345C73.9592 42.6099 73.8539 42.864 73.6666 43.0513C73.4793 43.2386 73.2252 43.3439 72.9603 43.3439H63.8076C63.5394 43.3423 63.2734 43.3937 63.0251 43.4952C62.7767 43.5966 62.5508 43.7461 62.3603 43.935L57.6923 48.6642V45.4231C57.6923 44.8825 57.4775 44.364 57.0952 43.9817C56.7129 43.5994 56.1944 43.3846 55.6538 43.3846H52.5757Z" fill="#192329"/>
<path d="M57.6908 33.1902H67.8831C68.4237 33.1902 68.9422 32.9754 69.3245 32.5931C69.7068 32.2109 69.9216 31.6924 69.9216 31.1517C69.9216 30.6111 69.7068 30.0926 69.3245 29.7103C68.9422 29.328 68.4237 29.1133 67.8831 29.1133H57.6908C57.1502 29.1133 56.6317 29.328 56.2494 29.7103C55.8671 30.0926 55.6523 30.6111 55.6523 31.1517C55.6523 31.6924 55.8671 32.2109 56.2494 32.5931C56.6317 32.9754 57.1502 33.1902 57.6908 33.1902Z" fill="#FFBB44"/>
<path d="M57.6908 41.3464H67.8831C68.4237 41.3464 68.9422 41.1317 69.3245 40.7494C69.7068 40.3671 69.9216 39.8486 69.9216 39.308C69.9216 38.7674 69.7068 38.2489 69.3245 37.8666C68.9422 37.4843 68.4237 37.2695 67.8831 37.2695H57.6908C57.1502 37.2695 56.6317 37.4843 56.2494 37.8666C55.8671 38.2489 55.6523 38.7674 55.6523 39.308C55.6523 39.8486 55.8671 40.3671 56.2494 40.7494C56.6317 41.1317 57.1502 41.3464 57.6908 41.3464Z" fill="#FFBB44"/>
</svg>

    </div>
    <div class="pretitle uppercase font-tt-eb text-16 mb-28 spacing-12 text-brand-yellow">
Metodo
    </div>
    <div class="title font-din-bc text-60 mb-20">
    Metodo formativo efficace che bilancia teoria e pratica
    </div>
    <div class="text font-ave-m leading-15 text-20 text-gray">
    Niente lezioni noiose. Ti diamo solo la teoria che serve, applicata subito in esercizi pratici per risultati concreti.
    </div>

</div>
<div class="body mt-auto rounded-bottom-24  overflow-hidden relative h-400">

<?php load_img(class: 'background img-cover z-20 object-right-bottom', high: 97, low: 96); ?>
<?php load_img(class: 'subject img-cover z-50 object-right-bottom', high: 98); ?>


</div>

<div class="shapes absolute inset-0 z-40">

    <div class="shape-1 animate-float-updown-light animation-duration-5000 absolute right-80 top--40">
    <svg class="h-80 shadow-yellow" width="97" height="77" viewBox="0 0 97 77" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_594_1161)">
<path d="M57.9785 73.4115L93.9976 28.5635C97.7901 23.8414 95.1635 16.7811 89.2069 15.6859L10.4579 1.2065C3.36968 -0.0967964 -1.69236 7.90195 2.51967 13.7501L45.2496 73.0775C48.3396 77.3679 54.6677 77.5339 57.9785 73.4115Z" fill="#FFBB44"/>
</g>
<defs>
<clipPath id="clip0_594_1161">
<rect width="96" height="77" fill="white" transform="translate(0.148438)"/>
</clipPath>
</defs>
</svg>


    </div>
</div>

</div> <!-- box -->








<div class="box bg-brand-violet bg-opacity-10 relative rounded-24 col-span-7 flex flex-col">

<div class="head p-80 mb-10">
    <div class="icon mb-48">
    <svg class="h-100" width="100" height="100" viewBox="0 0 100 100" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="100" height="100" rx="15" fill="#E3D1FF"/>
<path d="M72.3029 39.1016H70.4029C69.3522 39.1016 68.5029 39.9509 68.5029 41.0015C68.5029 42.0522 69.3522 42.9015 70.4029 42.9015H72.3029C73.3517 42.9015 74.2028 43.7527 74.2028 44.8015V63.8014C74.2028 64.8502 73.3517 65.7014 72.3029 65.7014H70.4029C69.3522 65.7014 68.5029 66.5507 68.5029 67.6014V72.5147L62.2462 66.2581C61.8909 65.9009 61.4083 65.7014 60.9029 65.7014H41.9031C40.8543 65.7014 40.0031 64.8502 40.0031 63.8014C40.0031 62.7507 39.1538 61.9014 38.1031 61.9014C37.0524 61.9014 36.2031 62.7507 36.2031 63.8014C36.2031 66.944 38.7605 69.5013 41.9031 69.5013H60.1163L69.0596 78.4446C69.4225 78.8075 69.9089 79.0013 70.4029 79.0013C70.648 79.0013 70.895 78.9557 71.1306 78.8569C71.8412 78.5643 72.3029 77.8689 72.3029 77.1013V69.5013C75.4454 69.5013 78.0028 66.944 78.0028 63.8014V44.8015C78.0028 41.6589 75.4454 39.1016 72.3029 39.1016Z" fill="#192329"/>
<path d="M60.8997 58.0997C64.0423 58.0997 66.5997 55.5424 66.5997 52.3998V27.7C66.5997 24.5574 64.0423 22 60.8997 22H26.7C23.5574 22 21 24.5574 21 27.7V52.3998C21 55.5424 23.5574 58.0997 26.7 58.0997V65.6997C26.7 66.4673 27.1617 67.1627 27.8723 67.4553C28.1078 67.5541 28.3548 67.5997 28.5999 67.5997C29.0939 67.5997 29.5803 67.4059 29.9432 67.043L38.8865 58.0997H60.8997ZM36.7566 54.8565L30.4999 61.1131V56.1997C30.4999 55.1491 29.6506 54.2998 28.5999 54.2998H26.7C25.6512 54.2998 24.8 53.4486 24.8 52.3998V27.7C24.8 26.6512 25.6512 25.8 26.7 25.8H60.8997C61.9485 25.8 62.7997 26.6512 62.7997 27.7V52.3998C62.7997 53.4486 61.9485 54.2998 60.8997 54.2998H38.0999C37.5945 54.2998 37.1119 54.4993 36.7566 54.8565Z" fill="#192329"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M35.0618 37.4844H38.9013V48.683H35.0618C34.7084 48.683 34.4219 48.2973 34.4219 47.8216V38.3458C34.4219 37.8701 34.7084 37.4844 35.0618 37.4844Z" stroke="#A973FF" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M38.9023 38.3267L42.5975 31.1673C43.1434 30.1095 44.4435 29.6946 45.5012 30.2405C45.6988 30.3425 45.8795 30.4745 46.0367 30.6317C46.6668 31.2618 47.0207 32.1163 47.0207 33.0073V36.3669H52.901C53.3828 36.3669 53.8414 36.5738 54.1603 36.935C54.4791 37.2962 54.6275 37.777 54.5678 38.2551L53.3079 47.214C53.2029 48.0546 52.4883 48.6854 51.6411 48.6854H38.9023" stroke="#A973FF" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
</svg>


    </div>
    <div class="pretitle uppercase font-tt-eb text-16 mb-28 spacing-12 text-brand-violet">
    Tutoring
    </div>
    <div class="title font-din-bc text-60 mb-20">
    Affiancamento durante il percorso di studio per feedback e supporto
    </div>
    <div class="text font-ave-m leading-15 text-20 text-gray">
   Non sarai mai solo. Il docente analizza i tuoi progressi, risponde ai tuoi dubbi e ti guida con feedback mirati e personalizzati.
    </div>
 
</div>
<div class="body mt-auto rounded-bottom-24  overflow-hidden relative h-400">

<?php load_img(class: 'background img-cover z-20', high: 100, low: 99); ?>
<?php load_img(class: 'subject img-cover z-50', high: 101); ?>


</div>

<div class="shapes absolute inset-0 z-80">

    <div class="shape-1 animate-float-updown-light animation-duration-7000 absolute right--60 bottom-200">
    <svg class="h-120 shadow-violet" width="107" height="116" viewBox="0 0 107 116" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_594_1183)">
<path d="M102.768 64.0023L29.9536 3.98856C23.8571 -1.03613 14.6158 2.66005 13.6659 10.503L2.37795 103.708C1.46689 111.23 8.92781 116.993 15.9764 114.212L100.078 81.0209C107.165 78.2243 108.646 68.8476 102.768 64.0023Z" fill="#AB77FF"/>
</g>
<defs>
<clipPath id="clip0_594_1183">
<rect width="106" height="116" fill="white" transform="translate(0.976562)"/>
</clipPath>
</defs>
</svg>


    </div>
</div>

</div> <!-- box -->








<div class="box bg-brand-blue bg-opacity-10 relative rounded-24 col-span-7 flex flex-col">

<div class="head p-80 mb-10">
    <div class="icon mb-48">
    <svg class="h-100" width="100" height="101" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect y="0.402344" width="100" height="100" rx="15" fill="#B2D9F9"/>
<path d="M36.5 29.9023H32C28.6863 29.9023 26 32.5886 26 35.9023V43.4023M36.5 29.9023V25.4023V34.9023M36.5 29.9023H63M63 29.9023H67.5C70.8137 29.9023 73.5 32.5886 73.5 35.9023V43.4023M63 29.9023V25.4023V34.9023M26 43.4023V68.9023C26 72.2161 28.6863 74.9023 32 74.9023H67.5C70.8137 74.9023 73.5 72.2161 73.5 68.9023V43.4023M26 43.4023H73.5" stroke="#192329" stroke-width="4"/>
<rect x="36" y="51" width="6" height="6" rx="1.5" fill="#1289ED"/>
<rect x="36" y="61" width="6" height="6" rx="1.5" fill="#1289ED"/>
<rect x="47" y="51" width="6" height="6" rx="1.5" fill="#1289ED"/>
<rect x="47" y="61" width="6" height="6" rx="1.5" fill="#1289ED"/>
<rect x="58" y="51" width="6" height="6" rx="1.5" fill="#1289ED"/>
</svg>



    </div>
    <div class="pretitle uppercase font-tt-eb text-16 mb-28 spacing-12 text-brand-blue">
    Tempi
    </div>
    <div class="title font-din-bc text-60 mb-20">
    Completa i corsi con flessibilità, studia con i tuoi tempi, dove e quando vuoi
    </div>
    <div class="text font-ave-m leading-15 text-20 text-gray">
    Il nostro metodo è pensato per chi studia o lavora, permettendoti di imparare al tuo passo. Accedi quando vuoi, da qualsiasi dispositivo, e rivedi i passaggi tutte le volte che ti serve.
    </div>

</div>
<div class="body mt-auto rounded-bottom-24  overflow-hidden text-center">

<?php load_img(class: 'img h-320', high: 102); ?>


</div>


</div> <!-- box -->










<div class="box bg-brand-green bg-opacity-10 relative rounded-24 col-span-5 flex flex-col">

<div class="head p-80 mb-10">
    <div class="icon mb-48">
    <svg class="h-100" width="100" height="101" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect y="0.402344" width="100" height="100" rx="15" fill="#92E9B2"/>
<path d="M66.125 34.9422L72.775 28.2922C73.25 27.8172 73.25 27.1047 72.775 26.6297C72.3 26.1547 71.5875 26.1547 71.1125 26.6297L63.275 34.4672C60.6625 33.5172 57.575 33.2797 54.25 33.2797C41.1875 33.2797 30.5 38.5047 30.5 45.1547V66.5297C30.5 73.1797 41.1875 78.4047 54.25 78.4047C67.3125 78.4047 78 73.1797 78 66.5297V45.1547C78 40.8797 73.25 37.0797 66.125 34.9422ZM35.25 66.5297V52.2797C39.525 55.1297 46.4125 57.0297 54.25 57.0297C56.625 57.0297 59.2375 56.7922 61.375 56.5547V73.1797C59.2375 73.6547 56.8625 73.8922 54.25 73.8922C42.375 73.6547 35.25 69.1422 35.25 66.5297ZM66.125 55.3672V71.7547C65.4125 71.9922 64.7 72.2297 63.75 72.4672V55.8422C64.4625 55.8422 65.4125 55.6047 66.125 55.3672ZM73.25 66.5297C73.25 67.7172 71.5875 69.3797 68.5 70.8047V54.6547C70.4 53.9422 72.0625 53.2297 73.25 52.2797V66.5297ZM54.25 52.2797C42.375 52.2797 35.25 47.7672 35.25 45.1547C35.25 42.5422 42.375 38.0297 54.25 38.0297C56.15 38.0297 57.8125 38.2672 59.475 38.2672L57.1 40.6422C55.675 39.9297 54.25 40.1672 53.0625 41.3547C51.6375 42.7797 51.6375 44.9172 53.0625 46.3422C54.4875 47.7672 56.625 47.7672 58.05 46.3422C59.2375 45.1547 59.2375 43.7297 58.7625 42.3047L62.325 38.7422C69.2125 40.1672 73.25 43.2547 73.25 45.1547C73.25 47.7672 66.125 52.2797 54.25 52.2797Z" fill="#192329"/>
<path d="M45.9375 28.5273C47.8375 28.5273 49.5 26.8648 49.5 24.9648C49.5 23.0648 47.8375 21.4023 45.9375 21.4023C44.275 21.4023 43.0875 22.3523 42.6125 23.7773H22.1875C21.475 23.7773 21 24.2523 21 24.9648C21 25.6773 21.475 26.1523 22.1875 26.1523H42.6125C43.0875 27.5773 44.275 28.5273 45.9375 28.5273Z" fill="#25D366"/>
</svg>


    </div>
    <div class="pretitle uppercase font-tt-eb text-16 mb-28 spacing-12 text-brand-green">
    Risorse
    </div>
    <div class="title font-din-bc text-60 mb-20">
    Risorse utili e tutto il necessario per iniziare
    </div>
    <div class="text font-ave-m leading-15 text-20 text-gray">
    Il tuo unico pensiero sarà creare. A fornirti le dispense, i suoni e i sintetizzatori per iniziare, ci pensiamo noi.
    </div>

</div>
<div class="body mt-auto rounded-bottom-24  overflow-hidden text-center">

<?php load_img(class: 'img w-100-100 max-w-500', high: 104); ?>


</div>


</div> <!-- box -->




</div>



</div>

</section>




<section class="home-reviews mb-140">

<div class="container-l mx-auto">

<div class="intro flex gap-140 items-center justify-between mb-100">

<div class="main flex flex-col items-start">
    <div class="pretitle uppercase font-tt-eb text-16 mb-28 spacing-12 text-gradient-red-yellow">
    Testimonianze corsisti
    </div>
    <div class="title font-din-bc text-60 mb-20">
    Le parole di chi ha scelto Produzione Hip Hop
    </div>  
    <div class="text font-ave-m leading-15 text-20 text-gray">
    Non siamo noi a dirlo, ma la community che ha seguito i nostri corsi. Queste sono le loro esperienze dirette.
    </div>


</div>
<div class="side">

<div class="items">


    <div class="item flex flex-col rounded-24 relative">
        <div class="inner z-40 p-60 rounded-24">
        <div class="stars mb-40">
        <svg class="h-20"width="111" height="20" viewBox="0 0 111 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9.82471 16.0737L15.7202 20L14.1557 12.6L19.3643 7.62105L12.5053 6.97895L9.82471 0L7.14409 6.97895L0.285156 7.62105L5.49375 12.6L3.92927 20L9.82471 16.0737Z" fill="#FFBB44"/>
<path d="M32.7192 16.0737L38.6147 20L37.0502 12.6L42.2588 7.62105L35.3999 6.97895L32.7192 0L30.0386 6.97895L23.1797 7.62105L28.3883 12.6L26.8238 20L32.7192 16.0737Z" fill="#FFBB44"/>
<path d="M55.6138 16.0737L61.5092 20L59.9447 12.6L65.1533 7.62105L58.2944 6.97895L55.6138 0L52.9332 6.97895L46.0742 7.62105L51.2828 12.6L49.7183 20L55.6138 16.0737Z" fill="#FFBB44"/>
<path d="M78.5083 16.0737L84.4037 20L82.8393 12.6L88.0479 7.62105L81.1889 6.97895L78.5083 0L75.8277 6.97895L68.9688 7.62105L74.1773 12.6L72.6129 20L78.5083 16.0737Z" fill="#FFBB44"/>
<path d="M101.403 16.0737L107.298 20L105.734 12.6L110.942 7.62105L104.083 6.97895L101.403 0L98.7222 6.97895L91.8633 7.62105L97.0719 12.6L95.5074 20L101.403 16.0737Z" fill="#FFBB44"/>
</svg>

        </div>
        <div class="text mb-40 font-ave-m text-20 leading-15 text-gray">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam quis posuere nunc.Lorem ipsum dolor sit amet. Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam quis posuere nunc.Lorem ipsum dolor sit amet.
        </div>
        <div class="author mt-auto flex gap-28 items-center">
            <div class="photo w-80 h-80 rounded-full overflow-hidden relative">
                <?php load_img(class: 'img img-cover', high: 34); ?>

            </div>
            <div class="info">
                <div class="name font-tt-eb text-16 uppercase spacing-12 mb-12">
                    Mario
                </div>
                <div class="role font-ave-d text-18">
                    Produttore musicale
                </div>
            </div>
        </div>

        </div>
        <div class="gradients absolute inset-0 z-10 overflow-hidden rounded-24">

<div class="radial-gradient-red animate-float-around animate-opacity-70-100 animation-duration-10000 absolute z-40 h-540 w-540 left--340 bottom--340"></div>

<div class="radial-gradient-yellow animate-float-around animate-opacity-70-100 animation-duration-10000 absolute z-40 h-540 w-540 right--140 top--140"></div>
        </div>


        <div class="shapes absolute inset-0 z-60">
        <div class="shape-1 animate-float-updown-light animation-duration-5000 absolute right-80 top--40">
        <svg class="h-88 shadow-yellow" width="74" height="91" viewBox="0 0 74 91" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_595_638)">
<path d="M61.7693 82.1441L72.8533 13.6398C74.277 4.8403 64.3462 -1.28396 57.1226 3.93878L5.68398 41.1292C0.277802 45.0379 0.142024 53.0436 5.41256 57.1334L45.7671 88.4474C51.7646 93.1012 60.5568 89.638 61.7693 82.1441Z" fill="#FFBB44"/>
</g>
<defs>
<clipPath id="clip0_595_638">
<rect width="73" height="91" fill="white" transform="translate(0.773438)"/>
</clipPath>
</defs>
</svg>


    </div>

</div>
        </div>
</div> <!-- item -->


</div>


</div>

<div class="content relative">

<div class="items flex items-center gap-40 relative z-30">


    <div class="item px-28 pt-28 pb-40 rounded-24 bg-white shadow-48 shadow-opacity-10 mt-40">
        <div class="head">
            <div class="clip relative rounded-12 overflow-hidden">
                <div class="play h-120 absolute-center flex place-center z-40 rounded-full w-120">
                    <div class="icon">
                    <svg class="h-50-100" width="51" height="60" viewBox="0 0 51 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M48.0174 27.6454L17.8589 3.836C15.8916 2.28285 13 3.68412 13 6.19065V53.8094C13 56.3159 15.8916 57.7172 17.8589 56.164L48.0174 32.3546C49.5389 31.1535 49.5389 28.8465 48.0174 27.6454Z" fill="white"/>
</svg>

                    </div>
                </div>
                <div class="video ratio-4-5 relative rounded-12 overflow-hidden">
                <?php load_img(class: 'img img-cover', high: 249); ?>
                </div>
            </div>

        </div>

<div class="body relative z-40 pt-40">

<div class="photo w-80 h-80 rounded-full overflow-hidden relative absolute top--60 right-28 z-40">
                <?php load_img(class: 'img img-cover', high: 254); ?>
            </div>
        <div class="author">
            <div class="name font-tt-eb text-16 uppercase spacing-12 mb-16">
                Mario
            </div>
            <div class="text font-ave-m text-gray leading-14 text-18">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam quis posuere nunc.Lorem ipsum dolor sit amet.
            </div>
        </div>

        </div>
    </div> <!-- item -->

    <div class="item px-28 pt-28 pb-40 rounded-24 bg-white shadow-48 shadow-opacity-10 mt--80">
        <div class="head">
            <div class="clip relative rounded-12 overflow-hidden">
                <div class="play h-120 absolute-center flex place-center z-40 rounded-full w-120">
                    <div class="icon">
                    <svg class="h-50-100" width="51" height="60" viewBox="0 0 51 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M48.0174 27.6454L17.8589 3.836C15.8916 2.28285 13 3.68412 13 6.19065V53.8094C13 56.3159 15.8916 57.7172 17.8589 56.164L48.0174 32.3546C49.5389 31.1535 49.5389 28.8465 48.0174 27.6454Z" fill="white"/>
</svg>
 
                    </div>
                </div>
                <div class="video ratio-4-5 relative rounded-12 overflow-hidden">
                <?php load_img(class: 'img img-cover', high: 250); ?>
                </div>
            </div>

        </div>
        <div class="body relative z-40 pt-40">

        <div class="photo w-80 h-80 rounded-full overflow-hidden relative absolute top--60 right-28 z-40">
                        <?php load_img(class: 'img img-cover', high: 252); ?>
                    </div>

        <div class="author">
            <div class="name font-tt-eb text-16 uppercase spacing-12 mb-16">
                Mario
            </div>
            <div class="text font-ave-m text-gray leading-14 text-18">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam quis posuere nunc.Lorem ipsum dolor sit amet.
            </div>
        </div>

        </div>
    </div> <!-- item -->



    <div class="item px-28 pt-28 pb-40 rounded-24 bg-white shadow-48 shadow-opacity-10 mt-120">
        <div class="head">
            <div class="clip relative rounded-12 overflow-hidden">
                <div class="play h-120 absolute-center flex place-center z-40 rounded-full w-120">
                    <div class="icon">
                    <svg class="h-50-100" width="51" height="60" viewBox="0 0 51 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M48.0174 27.6454L17.8589 3.836C15.8916 2.28285 13 3.68412 13 6.19065V53.8094C13 56.3159 15.8916 57.7172 17.8589 56.164L48.0174 32.3546C49.5389 31.1535 49.5389 28.8465 48.0174 27.6454Z" fill="white"/>
</svg>

                    </div>
                </div>
                <div class="video ratio-4-5 relative rounded-12 overflow-hidden">
                <?php load_img(class: 'img img-cover', high: 251); ?>
                </div>
            </div>

        </div>

        <div class="body relative z-40 pt-40">

        <div class="photo w-80 h-80 rounded-full overflow-hidden relative absolute top--60 right-28 z-40">
                        <?php load_img(class: 'img img-cover', high: 253); ?>
                    </div>

        <div class="author">
            <div class="name font-tt-eb text-16 uppercase spacing-12 mb-16">
                Mario
            </div>
            <div class="text font-ave-m text-gray leading-14 text-18">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Aliquam quis posuere nunc.Lorem ipsum dolor sit amet.
            </div>
        </div>

        </div>
    </div> <!-- item -->






</div>


<div class="gradients absolute inset-0 z-10">

<div class="radial-gradient-red animate-float-around absolute z-20 h-800 w-800 left--400 top-300 opacity-20"></div>
</div>

<div class="shapes">

<div class="shape-1 animate-float-updown-light animation-duration-5000 absolute left--80 top-80">
<svg class="h-140 shadow-red" width="132" height="140" viewBox="0 0 132 140" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_596_641)">
<path d="M126.867 77.9576L14.6966 3.41429C8.44291 -0.741649 0.286323 4.73807 1.76969 12.0988L26.0817 132.74C27.1921 138.25 33.2824 141.149 38.2596 138.538L126.118 92.4404C131.789 89.4651 132.201 81.502 126.867 77.9576Z" fill="#FF4444"/>
</g>
<defs>
<clipPath id="clip0_596_641">
<rect width="131" height="140" fill="white" transform="translate(0.9375)"/>
</clipPath>
</defs>
</svg>


    </div>

</div>


</div>

</div>


</section>


<section class="home-about mb-140 relative z-60">

<div class="container-l mx-auto flex gap-140 items-center">

<div class="main flex flex-col items-start">
    <div class="intro relative mb-100 w-100-100">
        <div class="logo absolute-center-y left-0 z-80">
        <svg class="h-260" width="110" height="261" viewBox="0 0 110 261" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0.960938 84.2199L109.963 0.03125V114.795L60.1827 153.235V215.243L46.0209 226.31V164.188L37.6527 170.691V232.77L23.4909 243.665V181.542L15.1227 188.002V250.124L0.960938 260.976V199.414V179.605V118.043L15.1227 107.062V169.184L23.4909 162.724V100.602L37.6527 89.6212V151.829L46.0209 145.37V83.1617L60.1827 72.1806V134.287L95.8015 106.614V29.9602L0.960938 103.168V84.2199Z" fill="#192329"/>
</svg>

        </div>
        <div class="composition flex flex-col items-center gap-20 w-100-100">
            <div class="row w-50-100">
                <div class="item h-60 grow-1 bg-gradient-white-gray rounded-full">
                    
                </div>
            </div>
            <div class="row flex gap-20 w-80-100 relative">
                <div class="item h-60 grow-5 bg-gradient-white-gray rounded-full">
                    
                </div>
                <div class="item h-60 grow-8 bg-gradient-violet-white bg-opacity-50 rounded-full flex place-center text-center">
                    <div class="text font-ave-b text-22">
                        Hip Hop
                    </div>
                </div>

                <div class="shapes absolute inset-0 z-50">
                    <div class="shape-1 animate-float-updown-light animation-duration-5000 absolute right-36 top--60">
                <svg class="h-72 shadow-violet" width="59" height="72" viewBox="0 0 59 72" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_596_645)">
<path d="M48.9873 64.848L57.9368 9.5359C59.0863 2.43098 51.0679 -2.51389 45.2354 1.70308L3.70263 31.7315C-0.662459 34.8875 -0.77208 41.3515 3.48348 44.6537L36.0667 69.9374C40.9092 73.695 48.0082 70.8987 48.9873 64.848Z" fill="#AB77FF"/>
</g>
<defs>
<clipPath id="clip0_596_645">
<rect width="59" height="72" fill="white"/>
</clipPath>
</defs>
</svg>
</div>
                </div>
            </div>
            <div class="row flex gap-20 w-100-100">
                <div class="item h-60 grow-5 bg-gradient-white-gray rounded-full">
                    
                </div>
                <div class="item h-60 grow-7 bg-gradient-white-yellow bg-opacity-50 rounded-full flex place-center text-center">
                    <div class="text font-ave-b text-22">
                        Rap
                    </div>
                </div>
            </div>

            <div class="row flex gap-20 w-60-100">
                <div class="item h-60 grow-3 bg-gradient-white-gray rounded-full">
                    
                </div>
                <div class="item h-60 grow-7 bg-gradient-red-white bg-opacity-50 rounded-full flex place-center text-center">
                    <div class="text font-ave-b text-22">
                       Trap
                    </div>
                </div>
            </div>

            <div class="row flex w-40-100">
                <div class="item h-60 grow-7 bg-gradient-white-sky bg-opacity-50 rounded-full flex place-center text-center">
                    <div class="text font-ave-b text-22">
                       Drill
                    </div>
                </div>
            </div>

            
        </div>
    </div>
    <div class="header mb-80 flex flex-col items-start">
    <div class="pretitle uppercase font-tt-eb text-16 mb-28 spacing-12 text-gradient-pink-violet">
    Produzione Hip Hop
    </div>
    <div class="title font-din-bc text-60 mb-20 max-w-800">
    Siamo la prima realtà in Italia specializzata in Produzione Hip Hop
    </div>
    <div class="text font-ave-m leading-15 text-20 text-gray max-w-600">
    Non siamo una scuola di musica generica, siamo producer e fonici che vivono questa cultura e insegnano un metodo testato sul campo.
    </div>
    </div>

    

    <div class="content relative w-100-100">
        <div class="title uppercase font-tt-eb text-16 mb-28 spacing-12">
        Produzioni
        </div>
        <div class="text font-ave-m leading-15 text-20 text-gray mb-60 max-w-600">
        Vacca, Jamil, Vegas Jones, Laïoung, Amill Leonardo. I nostri studenti hanno lavorato con Baby Gang, Gucci Mane, NBA Never Broke Again.
        </div>
        <div class="items flex flex-col gap-20 mb-80">

            <a href="" class="item flex items-center gap-20">
            <div class="value uppercase font-tt-eb text-16 spacing-12 w-72">
                1.7M+
            </div>
            <div class="text font-ave-m text-20 text-gray">
            Jamill Feat Lauoung Animali
            </div>
            <div class="icon flex">
            <svg class="h-20" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10 0C4.46875 0 0 4.48031 0 10C0 15.5312 4.48031 20 10 20C15.5312 20 20 15.5197 20 10C20 4.48031 15.5197 0 10 0ZM14.5875 14.4325C14.4084 14.7312 14.0259 14.8147 13.7272 14.6356C11.3734 13.2019 8.4225 12.8794 4.93344 13.6678C4.59906 13.7397 4.26469 13.5366 4.19281 13.2022C4.12094 12.8678 4.32406 12.5334 4.65844 12.4616C8.46969 11.5897 11.7428 11.9597 14.3716 13.5728C14.4424 13.615 14.5041 13.6708 14.5533 13.7369C14.6025 13.8031 14.6381 13.8783 14.6581 13.9582C14.678 14.0382 14.682 14.1213 14.6697 14.2028C14.6575 14.2843 14.6292 14.3626 14.5866 14.4331L14.5875 14.4325ZM15.8063 11.7084C15.5794 12.0787 15.1012 12.1866 14.7312 11.9709C12.0431 10.3222 7.945 9.84438 4.76719 10.8C4.34906 10.9194 3.91906 10.6925 3.79938 10.2863C3.68 9.86812 3.90688 9.43812 4.325 9.31844C7.95687 8.21937 12.4731 8.745 15.5675 10.6447C15.9259 10.8597 16.0331 11.3375 15.8066 11.7081L15.8063 11.7084ZM15.9137 8.86469C12.6878 6.95312 7.37125 6.77406 4.28875 7.70594C3.79875 7.86125 3.27313 7.57469 3.11781 7.08469C2.9625 6.59469 3.24906 6.06906 3.73906 5.91375C7.27563 4.83875 13.1538 5.05344 16.8578 7.25187C17.3 7.51437 17.4431 8.08813 17.1803 8.53C16.9412 8.98406 16.3559 9.1275 15.9137 8.86438V8.86469Z" fill="#1ED760"/>
</svg>

            </div>
</a> <!-- item -->

<a href="" class="item flex items-center gap-20">
            <div class="value uppercase font-tt-eb text-16 spacing-12 w-72">
                1.2M+
            </div>
            <div class="text font-ave-m text-20 text-gray">
            Letale
            </div>
            <div class="icon flex">
            <svg class="h-20" width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10 0C4.46875 0 0 4.48031 0 10C0 15.5312 4.48031 20 10 20C15.5312 20 20 15.5197 20 10C20 4.48031 15.5197 0 10 0ZM14.5875 14.4325C14.4084 14.7312 14.0259 14.8147 13.7272 14.6356C11.3734 13.2019 8.4225 12.8794 4.93344 13.6678C4.59906 13.7397 4.26469 13.5366 4.19281 13.2022C4.12094 12.8678 4.32406 12.5334 4.65844 12.4616C8.46969 11.5897 11.7428 11.9597 14.3716 13.5728C14.4424 13.615 14.5041 13.6708 14.5533 13.7369C14.6025 13.8031 14.6381 13.8783 14.6581 13.9582C14.678 14.0382 14.682 14.1213 14.6697 14.2028C14.6575 14.2843 14.6292 14.3626 14.5866 14.4331L14.5875 14.4325ZM15.8063 11.7084C15.5794 12.0787 15.1012 12.1866 14.7312 11.9709C12.0431 10.3222 7.945 9.84438 4.76719 10.8C4.34906 10.9194 3.91906 10.6925 3.79938 10.2863C3.68 9.86812 3.90688 9.43812 4.325 9.31844C7.95687 8.21937 12.4731 8.745 15.5675 10.6447C15.9259 10.8597 16.0331 11.3375 15.8066 11.7081L15.8063 11.7084ZM15.9137 8.86469C12.6878 6.95312 7.37125 6.77406 4.28875 7.70594C3.79875 7.86125 3.27313 7.57469 3.11781 7.08469C2.9625 6.59469 3.24906 6.06906 3.73906 5.91375C7.27563 4.83875 13.1538 5.05344 16.8578 7.25187C17.3 7.51437 17.4431 8.08813 17.1803 8.53C16.9412 8.98406 16.3559 9.1275 15.9137 8.86438V8.86469Z" fill="#1ED760"/>
</svg>

            </div>
</a> <!-- item -->


<a href="" class="item flex items-center gap-20">
            <div class="value uppercase font-tt-eb text-16 spacing-12 w-72">
                1.1M+
            </div>
            <div class="text font-ave-m text-20 text-gray">
            Jamil Feat Mboss
            </div>
            <div class="icon flex">
            <svg class="h-16" width="24" height="16" viewBox="0 0 24 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M23.4761 2.50593C23.3381 2.02179 23.0692 1.58044 22.6963 1.22573C22.3233 0.871027 21.8593 0.615311 21.3503 0.483995C19.4865 1.27553e-07 11.985 0 11.985 0C11.985 0 4.48313 0.0142667 2.619 0.498261C2.10997 0.629578 1.64594 0.885294 1.27299 1.24C0.900053 1.59471 0.631192 2.03606 0.493125 2.5202C0 4.29318 0 8 0 8C0 8 0 11.7068 0.5085 13.4941C0.646567 13.9782 0.915428 14.4196 1.28837 14.7743C1.66131 15.129 2.12535 15.3847 2.63438 15.516C4.49813 16 12 16 12 16C12 16 19.5019 16 21.366 15.5164C21.875 15.385 22.3391 15.1293 22.712 14.7746C23.0849 14.4199 23.3538 13.9786 23.4919 13.4944C24 11.7214 24 8 24 8C24 8 23.985 4.29318 23.4761 2.50593Z" fill="#FF0000"/>
<path d="M9.33634 12L16.0026 8L9.33594 4L9.33634 12Z" fill="white"/>
</svg>

            </div>
</a> <!-- item -->


<a href="" class="item flex items-center gap-20">
            <div class="value uppercase font-tt-eb text-16 spacing-12 w-72">
               900K+
            </div>
            <div class="text font-ave-m text-20 text-gray">
            Vegas Jones feat Mboss
            </div>
            <div class="icon flex">
            <svg class="h-16" width="24" height="16" viewBox="0 0 24 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M23.4761 2.50593C23.3381 2.02179 23.0692 1.58044 22.6963 1.22573C22.3233 0.871027 21.8593 0.615311 21.3503 0.483995C19.4865 1.27553e-07 11.985 0 11.985 0C11.985 0 4.48313 0.0142667 2.619 0.498261C2.10997 0.629578 1.64594 0.885294 1.27299 1.24C0.900053 1.59471 0.631192 2.03606 0.493125 2.5202C0 4.29318 0 8 0 8C0 8 0 11.7068 0.5085 13.4941C0.646567 13.9782 0.915428 14.4196 1.28837 14.7743C1.66131 15.129 2.12535 15.3847 2.63438 15.516C4.49813 16 12 16 12 16C12 16 19.5019 16 21.366 15.5164C21.875 15.385 22.3391 15.1293 22.712 14.7746C23.0849 14.4199 23.3538 13.9786 23.4919 13.4944C24 11.7214 24 8 24 8C24 8 23.985 4.29318 23.4761 2.50593Z" fill="#FF0000"/>
<path d="M9.33634 12L16.0026 8L9.33594 4L9.33634 12Z" fill="white"/>
</svg>

            </div>
</a> <!-- item -->


<a href="" class="item flex items-center gap-20">
            <div class="value uppercase font-tt-eb text-16 spacing-12 w-72">
                800K+
            </div>
            <div class="text font-ave-m text-20 text-gray">
            Vacca Feat Mboss
            </div>
            <div class="icon flex">
            <svg class="h-16" width="24" height="16" viewBox="0 0 24 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M23.4761 2.50593C23.3381 2.02179 23.0692 1.58044 22.6963 1.22573C22.3233 0.871027 21.8593 0.615311 21.3503 0.483995C19.4865 1.27553e-07 11.985 0 11.985 0C11.985 0 4.48313 0.0142667 2.619 0.498261C2.10997 0.629578 1.64594 0.885294 1.27299 1.24C0.900053 1.59471 0.631192 2.03606 0.493125 2.5202C0 4.29318 0 8 0 8C0 8 0 11.7068 0.5085 13.4941C0.646567 13.9782 0.915428 14.4196 1.28837 14.7743C1.66131 15.129 2.12535 15.3847 2.63438 15.516C4.49813 16 12 16 12 16C12 16 19.5019 16 21.366 15.5164C21.875 15.385 22.3391 15.1293 22.712 14.7746C23.0849 14.4199 23.3538 13.9786 23.4919 13.4944C24 11.7214 24 8 24 8C24 8 23.985 4.29318 23.4761 2.50593Z" fill="#FF0000"/>
<path d="M9.33634 12L16.0026 8L9.33594 4L9.33634 12Z" fill="white"/>
</svg>

            </div>
</a> <!-- item -->


<a href="" class="item flex items-center gap-20">
            <div class="value uppercase font-tt-eb text-16 spacing-12 w-72">
                200K+
            </div>
            <div class="text font-ave-m text-20 text-gray">
            Il primo esse magazine freestyle
            </div>
            <div class="icon flex">
            <svg class="h-16" width="24" height="16" viewBox="0 0 24 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M23.4761 2.50593C23.3381 2.02179 23.0692 1.58044 22.6963 1.22573C22.3233 0.871027 21.8593 0.615311 21.3503 0.483995C19.4865 1.27553e-07 11.985 0 11.985 0C11.985 0 4.48313 0.0142667 2.619 0.498261C2.10997 0.629578 1.64594 0.885294 1.27299 1.24C0.900053 1.59471 0.631192 2.03606 0.493125 2.5202C0 4.29318 0 8 0 8C0 8 0 11.7068 0.5085 13.4941C0.646567 13.9782 0.915428 14.4196 1.28837 14.7743C1.66131 15.129 2.12535 15.3847 2.63438 15.516C4.49813 16 12 16 12 16C12 16 19.5019 16 21.366 15.5164C21.875 15.385 22.3391 15.1293 22.712 14.7746C23.0849 14.4199 23.3538 13.9786 23.4919 13.4944C24 11.7214 24 8 24 8C24 8 23.985 4.29318 23.4761 2.50593Z" fill="#FF0000"/>
<path d="M9.33634 12L16.0026 8L9.33594 4L9.33634 12Z" fill="white"/>
</svg>

            </div>
</a> <!-- item -->




        </div>
        <a href="https://app-656e0683c1ac188b30f693df.closte.com/scuola/" class="button h-72 w-400 rounded-16 text-center border-gradient-pink-violet border-4 border-solid border-transparent rounded-12 flex gap-20 place-center hover-shadow-pink hover-up-2">
        <div class="text font-ave-b text-20">
           Scopri la scuola
            </div>
            <div class="icon flex">
            <svg class="h-20" width="11" height="20" viewBox="0 0 11 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.5824 10.5826L2.43421 18.7308C1.87735 19.2877 0.974503 19.2877 0.417644 18.7308C-0.139215 18.1739 -0.139215 17.2711 0.417644 16.7142L7.55751 9.57436L0.417643 2.4345C-0.139215 1.87764 -0.139215 0.974792 0.417643 0.417934C0.974502 -0.138924 1.87735 -0.138924 2.43421 0.417934L10.5824 8.56608C11.1392 9.12294 11.1392 10.0258 10.5824 10.5826Z" fill="#192329"/>
</svg>



        </div>
</a>
        <div class="visual absolute right-0 bottom-40 z-50">
            <div class="photo absolute w-120 h-180 rounded-24 overflow-hidden z-10 bottom-100 right-80">
                <?php load_img(class: 'img img-cover', high: 106, low: 105); ?>
            </div>
            <div class="photo absolute w-120 h-180 rounded-24 overflow-hidden z-20 bottom-0 right-0">
                <?php load_img(class: 'img img-cover', high: 108, low: 107); ?>
            </div>
            
        </div>
    </div>


</div>
<div class="side max-w-700 relative">
    <div class="photo mb-40 rounded-24 relative ratio-3-4 overflow-hidden">
    <?php load_img(class: 'img img-cover', high: 110, low: 109); ?>
    </div>
    <div class="simone relative rounded-24 overflow-hidden">
        <div class="inner p-60 z-60 relative">
        <div class="author flex items-center gap-28 mb-28">
            <div class="photo w-80 h-80 rounded-full overflow-hidden relative">
                <?php load_img(class: 'img img-cover', high: 34); ?>
            </div>
            <div class="info">
            <div class="name font-tt-eb text-16 uppercase spacing-12 mb-12">
                    Simone Beretta
                </div>
                <div class="role font-ave-d text-18">
                Fondatore e docente
                </div>
            </div>
</div>
            <div class="text font-ave-d leading-15 text-18 text-gray">
            Dal 2014 è il punto di riferimento per centinaia di studenti, molti dei quali oggi lavorano nel settore musicale grazie al suo affiancamento diretto.
            </div>
        </div>


        <div class="gradients absolute inset-0 z-10 overflow-hidden rounded-24">

<div class="radial-gradient-red animate-float-around animation-duration-10000 absolute z-40 h-540 w-540 left--140 bottom--140"></div>

<div class="radial-gradient-yellow animate-float-around animation-duration-10000 absolute z-40 h-540 w-540 right--80 top--80"></div>
        </div>
            
    </div>

    <div class="shapes">
        <div class="shape-1 animate-float-updown-light animation-duration-4000 absolute left--60 top-400">
        <svg class="h-140 shadow-pink"width="132" height="140" viewBox="0 0 132 140" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_596_647)">
<path d="M127.742 77.2115L15.5716 2.66819C9.31791 -1.48774 1.16132 3.99197 2.64469 11.3527L26.9567 131.994C28.0671 137.503 34.1574 140.403 39.1346 137.792L126.993 91.6943C132.664 88.719 133.076 80.7559 127.742 77.2115Z" fill="#E04E9D"/>
</g>
<defs>
<clipPath id="clip0_596_647">
<rect width="131" height="140" fill="white" transform="translate(0.9375)"/>
</clipPath>
</defs>
</svg>

        </div>
    </div>


</div>

</div>


</section>


<section class="home-gallery mb-140">

<div class="container max-w-2000 mx-auto">
    <div class="items grid grid-cols-3 gap-20">
        <div class="item ratio-3-4 relative">
            <?php load_img(class: 'img img-cover', high: 238, low: 237); ?>
        </div>
        <div class="item ratio-3-4 relative">
            <?php load_img(class: 'img img-cover', high: 240, low: 239); ?>
        </div>
        <div class="item ratio-3-4 relative">
            <?php load_img(class: 'img img-cover', high: 242, low: 241); ?>
        </div>
    </div>
</div>

</section>



<section class="home-social mb-140">
    <div class="container-l mx-auto">
    <div class="intro flex flex-col text-center items-center mb-100">
    <div class="pretitle mb-36 uppercase font-tt-eb text-16 spacing-12 text-gradient-pink-violet">
    Seguici sui social
    </div>
    <div class="title mb-20 font-din-bc text-60 max-w-1000">
    Unisciti alla nostra community sui social per non perderti nessun contenuto ed essere sempre un passo avanti
    </div>
    <div class="text font-ave-d text-20 leading-15 text-gray max-w-800">
    La tua fonte di ispirazione quotidiana. Tra tutorial veloci, giveaway e analisi di hit, c'è sempre qualcosa di nuovo da imparare.
    </div>

    </div>
    
    <div class="cta mb-120">

    <div class="items grid grid-cols-4 gap-20">


    <a href="" target="_blank" class="button h-260 w-100-100 rounded-24 bg-white text-center border-4 border-solid border-gray border-animated-instagram overflow-hidden hover-up-2 hover-scale hover-shadow-instagram">
        <div class="inner flex flex-col place-center h-100-100 w-100-100">
            <div class="icon mb-28">
            <svg class="h-28 width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_597_1062)">
<path d="M15.9287 0H6.07108C2.72349 0 0 2.72362 0 6.0712V15.9288C0 19.2766 2.72349 22 6.07108 22H15.9287C19.2766 22 22 19.2764 22 15.9288V6.0712C22.0001 2.72362 19.2766 0 15.9287 0ZM20.0482 15.9288C20.0482 18.2002 18.2002 20.048 15.9288 20.048H6.07108C3.79979 20.0482 1.95195 18.2002 1.95195 15.9288V6.0712C1.95195 3.79992 3.79979 1.95195 6.07108 1.95195H15.9287C18.2001 1.95195 20.048 3.79992 20.048 6.0712L20.0482 15.9288Z" fill="#192329"/>
<path d="M10.997 5.33203C7.87112 5.33203 5.32812 7.87503 5.32812 11.0009C5.32812 14.1266 7.87112 16.6695 10.997 16.6695C14.1228 16.6695 16.6658 14.1266 16.6658 11.0009C16.6658 7.87503 14.1228 5.33203 10.997 5.33203ZM10.997 14.7174C8.94755 14.7174 7.28007 13.0502 7.28007 11.0008C7.28007 8.9512 8.94742 7.28385 10.997 7.28385C13.0465 7.28385 14.7138 8.9512 14.7138 11.0008C14.7138 13.0502 13.0464 14.7174 10.997 14.7174Z" fill="#192329"/>
<path d="M16.9119 3.67578C16.5358 3.67578 16.1664 3.82804 15.9008 4.0948C15.634 4.36026 15.4805 4.72983 15.4805 5.10721C15.4805 5.48341 15.6341 5.85286 15.9008 6.11962C16.1663 6.38508 16.5358 6.53863 16.9119 6.53863C17.2893 6.53863 17.6576 6.38508 17.9243 6.11962C18.1911 5.85286 18.3433 5.48329 18.3433 5.10721C18.3433 4.72983 18.1911 4.36026 17.9243 4.0948C17.6588 3.82804 17.2893 3.67578 16.9119 3.67578Z" fill="#192329"/>
</g>
<defs>
<clipPath id="clip0_597_1062">
<rect width="22" height="22" fill="white"/>
</clipPath>
</defs>
</svg>

            </div>
            <div class="text font-ave-d text-22 mb-20">
            Instagram
</div>
<div class="handle font-ave-d text-gray text-18">
    @produzionehiphop
</div>
        </div>
</a>


<a href="" target="_blank" class="button h-260 w-100-100 rounded-24 bg-white text-center border-4 border-solid border-gray border-animated-tiktok overflow-hidden hover-up-2 hover-shadow-tiktok">
        <div class="inner flex flex-col place-center h-100-100 w-100-100">
            <div class="icon mb-28">
            <svg class="h-28" width="17" height="22" viewBox="0 0 17 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M17 9.20169C15.3289 9.2058 13.6988 8.66934 12.3399 7.66809V14.6511C12.3395 15.9444 11.9555 17.2068 11.2392 18.2694C10.523 19.332 9.50867 20.1442 8.33186 20.5974C7.15509 21.0505 5.87197 21.1231 4.65406 20.8053C3.43615 20.4876 2.3415 19.7946 1.5165 18.8192C0.691494 17.8437 0.175453 16.6323 0.0373795 15.3468C-0.100703 14.0613 0.145765 12.763 0.743827 11.6256C1.34188 10.4882 2.26302 9.56583 3.38408 8.98185C4.50512 8.39788 5.77266 8.18013 7.01718 8.35772V11.8699C6.44768 11.6855 5.83614 11.6911 5.2699 11.8858C4.70365 12.0805 4.21165 12.4544 3.86417 12.9541C3.51668 13.4539 3.33147 14.0538 3.33499 14.6684C3.3385 15.2829 3.53056 15.8806 3.88375 16.376C4.23693 16.8715 4.73317 17.2394 5.30161 17.4272C5.87005 17.6151 6.4816 17.6132 7.04895 17.4219C7.61629 17.2306 8.11041 16.8597 8.46074 16.3621C8.81106 15.8645 8.99972 15.2657 8.99972 14.6511V1H12.3399C12.3377 1.29036 12.3613 1.58033 12.4105 1.86624C12.5266 2.50447 12.7679 3.11163 13.1198 3.65057C13.4716 4.1895 13.9266 4.64889 14.4568 5.00062C15.2111 5.51407 16.0956 5.78774 17 5.78754V9.20169Z" fill="#192329"/>
</svg>

            </div>
            <div class="text font-ave-d text-22 mb-20">
            TikTok
</div>
<div class="handle font-ave-d text-gray text-18">
    @produzionehiphop
</div>
        </div>
</a>


<a href="" target="_blank" class="button h-260 w-100-100 rounded-24 bg-white text-center border-4 border-solid border-gray border-animated-youtube overflow-hidden hover-up-2 hover-shadow-youtube">
        <div class="inner flex flex-col place-center h-100-100 w-100-100">
            <div class="icon mb-28">
            <svg class="h-28" width="26" height="22" viewBox="0 0 26 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M25.8548 5.24706C25.8548 5.02354 25.5147 3.2353 24.7209 2.45294C23.7003 1.3353 22.5663 1.22353 21.9992 1.22353H21.8859C18.3706 1 13.1542 1 13.0408 1C13.0408 1 7.71109 1 4.19574 1.22353H4.08235C3.51535 1.22353 2.38137 1.3353 1.36079 2.45294C0.566992 3.34706 0.226797 5.1353 0.226797 5.35883C0.226797 5.47059 0 7.48237 0 9.6059V11.5059C0 13.6294 0.226797 15.6412 0.226797 15.753C0.226797 15.9765 0.566992 17.7647 1.36079 18.547C2.26797 19.553 3.40196 19.6647 4.08235 19.7765C4.19574 19.7765 4.30913 19.7765 4.42253 19.7765C6.46371 20 12.7006 20 12.9274 20C12.9274 20 18.2572 20 21.7725 19.7765H21.8859C22.4529 19.6647 23.5869 19.553 24.6074 18.547C25.4012 17.653 25.7414 15.8647 25.7414 15.6412C25.7414 15.5294 25.9683 13.5177 25.9683 11.3942V9.49414C26.0816 7.48237 25.8548 5.35883 25.8548 5.24706ZM17.4634 10.7236L10.6595 14.3C10.5461 14.3 10.5461 14.4119 10.4327 14.4119C10.3193 14.4119 10.2059 14.4119 10.2059 14.3C10.0925 14.1883 9.97906 14.0765 9.97906 13.853V6.58825C9.97906 6.36472 10.0925 6.25295 10.2059 6.14119C10.3193 6.02942 10.5461 6.02942 10.7728 6.14119L17.5768 9.71767C17.8035 9.82943 17.917 9.9412 17.917 10.1647C17.917 10.3883 17.6901 10.6117 17.4634 10.7236Z" fill="#192329"/>
</svg>

            </div>
            <div class="text font-ave-d text-22 mb-20">
            YouTube
</div>
<div class="handle font-ave-d text-gray text-18">
    @produzionehiphop
</div>
        </div>
</a>



<a href="" target="_blank" class="button h-260 w-100-100 rounded-24 bg-white text-center border-4 border-solid border-gray border-animated-facebook overflow-hidden hover-up-2 hover-shadow-facebook">
        <div class="inner flex flex-col place-center h-100-100 w-100-100">
            <div class="icon mb-28">
            <svg class="h-28" width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_597_1068)">
<path d="M22.0378 11.0404C22.0378 4.94301 17.0948 0 10.9974 0C4.90004 0 -0.0429688 4.94301 -0.0429688 11.0404C-0.0429688 16.2179 3.52176 20.5625 8.33052 21.7558V14.4144H6.05399V11.0404H8.33052V9.58661C8.33052 5.82889 10.0312 4.08716 13.7204 4.08716C14.42 4.08716 15.6269 4.2245 16.1206 4.3614V7.4196C15.8601 7.39221 15.4074 7.37853 14.8452 7.37853C13.0351 7.37853 12.3355 8.06435 12.3355 9.84716V11.0404H15.9418L15.3222 14.4144H12.3355V22C17.8023 21.3398 22.0383 16.6851 22.0383 11.0404H22.0378Z" fill="#192329"/>
</g>
<defs>
<clipPath id="clip0_597_1068">
<rect width="22" height="22" fill="white"/>
</clipPath>
</defs>
</svg>


            </div>
            <div class="text font-ave-d text-22 mb-20">
            Facebook
</div>
<div class="handle font-ave-d text-gray text-18">
    @produzionehiphop
</div>
        </div>
</a>

    </div>

    </div>
    <div class="content mb--60 max-w-1200 mx-auto">

    <div class="items grid grid-cols-3 gap-40">


        <div class="item flex items-center gap-28">
            <div class="icon flex">
            <svg class="h-72" width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="70" height="70" rx="15" fill="#EEFAFE"/>
<path d="M50 40V46.6667C50 47.5507 49.6488 48.3986 49.0237 49.0237C48.3986 49.6488 47.5507 50 46.6667 50H23.3333C22.4493 50 21.6014 49.6488 20.9763 49.0237C20.3512 48.3986 20 47.5507 20 46.6667V40" stroke="black" stroke-width="3.33333" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M26.668 31.668L35.0013 40.0013L43.3346 31.668" stroke="black" stroke-width="3.33333" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M35 40V20" stroke="black" stroke-width="3.33333" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

            </div>
            <div class="text font-ave-d text-20">
            Risorse gratuite
            </div>
        </div> <!-- item -->

        <div class="item flex items-center gap-28">
            <div class="icon flex">
            <svg class="h-72" width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="70" height="70" rx="15" fill="#E8EDFB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M37.0746 46.0281H32.4912L28.605 52.0544C28.1116 52.8196 27.1046 53.063 26.3162 52.6078L26.2452 52.5668C25.4677 52.1179 25.2013 51.1237 25.6502 50.3462C25.6635 50.3232 25.6774 50.3004 25.6919 50.278L28.4325 46.0281H17.7075C16.7645 46.0281 16 45.2636 16 44.3206C16 43.3775 16.7645 42.613 17.7075 42.613V22.1226C17.7075 20.2365 19.2365 18.7075 21.1226 18.7075H26.391C26.7426 17.7127 27.6913 17 28.8065 17H40.7593C41.8745 17 42.8232 17.7127 43.1748 18.7075H48.4432C50.3293 18.7075 51.8582 20.2365 51.8582 22.1226V42.613C52.8013 42.613 53.5658 43.3775 53.5658 44.3206C53.5658 45.2636 52.8013 46.0281 51.8582 46.0281H41.1333L43.8739 50.278C43.8884 50.3004 43.9023 50.3232 43.9156 50.3462C44.3645 51.1237 44.0981 52.1179 43.3206 52.5668L43.2496 52.6078C42.4611 53.063 41.4542 52.8196 40.9608 52.0544L37.0746 46.0281ZM26.2452 22.1226H21.1226V42.613H48.4432V22.1226H43.3206V22.9764C43.3206 24.3909 42.1738 25.5377 40.7593 25.5377H28.8065C27.3919 25.5377 26.2452 24.3909 26.2452 22.9764V22.1226ZM28.0374 33.9928C27.5172 34.472 26.7071 34.4387 26.2279 33.9185C25.7487 33.3983 25.7819 32.5881 26.3021 32.1089C27.7039 30.8176 28.7759 30.0653 29.6674 29.8306C31.2144 29.4234 32.2153 30.4769 32.2153 32.0598C32.2153 33.6288 32.5637 33.9243 32.9549 33.5418C33.1868 33.315 33.4194 33.0553 33.8054 32.6009C34.8292 31.3959 35.0957 31.1086 35.6447 30.739C36.7518 29.9936 37.9999 30.1594 38.7821 31.3613C39.0056 31.7046 39.1702 32.0702 39.281 32.4548C39.4982 33.2085 39.5006 33.8399 39.3931 34.81C39.3843 34.89 39.3769 34.9572 39.3707 35.0143C39.561 34.9936 39.7848 34.7369 40.3271 34.057C41.2056 32.9557 41.537 32.1289 41.4498 31.6191C41.3304 30.9219 41.7988 30.26 42.496 30.1407C43.1931 30.0214 43.855 30.4898 43.9743 31.1869C44.2099 32.563 43.6187 34.0379 42.3294 35.6542C40.5653 37.8657 38.9201 38.0097 37.4372 36.8642C36.9577 36.4939 36.7944 36.004 36.7816 35.4366C36.7759 35.1871 36.7829 35.1101 36.8474 34.5279C36.9204 33.8692 36.919 33.5082 36.8199 33.1641C36.8128 33.1397 36.8053 33.1156 36.7972 33.0918C36.5877 33.2925 36.2989 33.6219 35.7574 34.2592C35.3228 34.7708 35.0499 35.0756 34.7455 35.3732C32.4411 37.6262 29.9592 35.7641 29.68 32.647C29.2615 32.9294 28.7032 33.3795 28.0374 33.9928ZM29.6603 20.4151V22.1226H39.9055V20.4151H29.6603Z" fill="black"/>
</svg>


            </div>
            <div class="text font-ave-d text-20">
            Tutorial
            </div>
        </div> <!-- item -->

        <div class="item flex items-center gap-28">
            <div class="icon flex">
            <svg class="h-72" width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="70" height="70" rx="15" fill="#FFECEC"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.9589 32.2188H26.7124V48.9997H20.9589C20.4293 48.9997 20 48.4218 20 47.7089V33.5096C20 32.7967 20.4293 32.2188 20.9589 32.2188Z" stroke="black" stroke-width="3.3562" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M26.7109 33.4774L32.2481 22.7491C33.0661 21.164 35.0142 20.5423 36.5993 21.3604C36.8954 21.5132 37.1661 21.711 37.4017 21.9466C38.3458 22.8907 38.8762 24.1712 38.8762 25.5064V30.5407H47.6876C48.4096 30.5407 49.0969 30.8507 49.5747 31.392C50.0525 31.9333 50.2749 32.6536 50.1853 33.3701L48.2975 46.7949C48.14 48.0545 47.0692 48.9998 45.7998 48.9998H26.7109" stroke="black" stroke-width="3.3562" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

            </div>
            <div class="text font-ave-d text-20">
            Recensione prodotti
            </div>
        </div> <!-- item -->





        <div class="item flex items-center gap-28">
            <div class="icon flex">
            <svg class="h-72" width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="70" height="70" rx="15" fill="#FCEDF5"/>
<path d="M48.3432 25.574C49.8164 25.574 51.0107 26.7683 51.0107 28.2416V35.3551C51.0107 36.5165 50.2684 37.5046 49.2323 37.8708V48.6929C49.2323 50.6572 47.6399 52.2496 45.6756 52.2496H24.3351C22.3708 52.2496 20.7784 50.6572 20.7784 48.6929V37.8708C19.7423 37.5046 19 36.5165 19 35.3551V28.2416C19 26.7683 20.1943 25.574 21.6676 25.574H26.4942C24.8963 22.684 25.4688 19.1368 28.0697 17.6352C30.339 16.325 33.1849 17.1307 35.0054 19.2364C36.8258 17.1307 39.6717 16.325 41.941 17.6352C44.542 19.1368 45.1145 22.684 43.5165 25.574H48.3432ZM36.7837 48.6929H45.6756V38.0226H36.7837V48.6929ZM33.227 48.6929V38.0226H24.3351V48.6929H33.227ZM33.227 34.4659V29.1307H22.5567V34.4659H33.227ZM36.7837 34.4659H47.454V29.1307H36.7837V34.4659ZM32.5156 25.3357C33.2812 24.8938 33.4968 23.4783 32.722 22.1364C31.9472 20.7944 30.6136 20.2734 29.8481 20.7154C29.0825 21.1574 28.867 22.5728 29.6417 23.9148C30.4165 25.2567 31.7501 25.7777 32.5156 25.3357ZM37.4951 25.3357C38.2606 25.7777 39.5942 25.2567 40.369 23.9148C41.1438 22.5728 40.9282 21.1574 40.1626 20.7154C39.3971 20.2734 38.0635 20.7944 37.2887 22.1364C36.514 23.4783 36.7295 24.8938 37.4951 25.3357Z" fill="black"/>
</svg>


            </div>
            <div class="text font-ave-d text-20">
            Giveaway
            </div>
        </div> <!-- item -->

        <div class="item flex items-center gap-28">
            <div class="icon flex">
            <svg class="h-72"width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="70" height="70" rx="15" fill="#F6F1FF"/>
<path d="M52.2382 17.4763C52.0198 17.2864 51.7617 17.1478 51.4828 17.0705C51.2039 16.9932 50.9113 16.9793 50.6263 17.0296L31.2059 20.9137C30.7606 21.0045 30.3613 21.2486 30.0774 21.6034C29.7936 21.9583 29.6431 22.4014 29.6523 22.8558V39.3437C28.4513 38.723 27.12 38.3969 25.7682 38.3921C23.8394 38.2746 21.942 38.9223 20.4877 40.1948C19.0334 41.4673 18.1396 43.2619 18 45.1893C18.1396 47.1166 19.0334 48.9112 20.4877 50.1837C21.942 51.4562 23.8394 52.1039 25.7682 51.9864C27.697 52.1039 29.5944 51.4562 31.0486 50.1837C32.5029 48.9112 33.3968 47.1166 33.5363 45.1893V24.4482L49.0727 21.341V35.4596C47.8718 34.839 46.5404 34.5128 45.1886 34.508C43.2598 34.3905 41.3624 35.0383 39.9082 36.3107C38.4539 37.5832 37.56 39.3778 37.4204 41.3052C37.56 43.2325 38.4539 45.0271 39.9082 46.2996C41.3624 47.5721 43.2598 48.2198 45.1886 48.1023C47.1174 48.2198 49.0148 47.5721 50.4691 46.2996C51.9233 45.0271 52.8172 43.2325 52.9568 41.3052V18.9717C52.9549 18.6848 52.8895 18.4019 52.7652 18.1434C52.641 17.8848 52.461 17.657 52.2382 17.4763ZM25.7682 48.1023C23.6708 48.1023 21.8841 46.7623 21.8841 45.1893C21.8841 43.6162 23.6708 42.2762 25.7682 42.2762C27.8656 42.2762 29.6523 43.6162 29.6523 45.1893C29.6523 46.7623 27.8656 48.1023 25.7682 48.1023ZM45.1886 44.2182C43.0912 44.2182 41.3045 42.8782 41.3045 41.3052C41.3045 39.7321 43.0912 38.3921 45.1886 38.3921C47.286 38.3921 49.0727 39.7321 49.0727 41.3052C49.0727 42.8782 47.286 44.2182 45.1886 44.2182Z" fill="black"/>
</svg>


            </div>
            <div class="text font-ave-d text-20">
            Analisi brani famosi
            </div>
        </div> <!-- item -->

        <div class="item flex items-center gap-28">
            <div class="icon flex">
            <svg class="h-72" width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="70" height="70" rx="15" fill="#E7F3FD"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M19.0006 48.04C19.0006 45.8648 19.0003 30.4359 19.0001 22.9948C19 20.7856 20.7909 19 23.0001 19H48.0007C50.2098 19 52.0007 20.7856 52.0007 22.9947C52.0007 30.4358 52.0007 45.8648 52.0007 48.04C52.0007 50.227 50.139 52 47.8426 52H23.1586C20.8622 52 19.0006 50.227 19.0006 48.04Z" stroke="black" stroke-width="3.66667" stroke-linecap="round" stroke-linejoin="round"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M26.332 26.332V33.6654H44.6654V26.332H26.332Z" stroke="black" stroke-width="3.66667" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M26.332 40H37.332" stroke="black" stroke-width="3.66667" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M26 45H33" stroke="black" stroke-width="3.66667" stroke-linecap="round" stroke-linejoin="round"/>
</svg>


            </div>
            <div class="text font-ave-d text-20">
            Novità nel settore
            </div>
        </div> <!-- item -->






        <div class="item flex items-center gap-28">
            <div class="icon flex">
            <svg class="h-72" width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="70" height="70" rx="15" fill="#F5F2F1"/>
<path d="M51.1369 32.2135L47.8683 28.9449V24.305C47.8683 23.4593 47.5323 22.6482 46.9343 22.0502C46.3363 21.4521 45.5252 21.1162 44.6794 21.1162H40.0556L36.787 17.9273C36.1895 17.3334 35.3813 17 34.5388 17C33.6964 17 32.8881 17.3334 32.2907 17.9273L29.0221 21.1321H24.3822C23.5365 21.1321 22.7254 21.4681 22.1274 22.0661C21.5293 22.6641 21.1934 23.4752 21.1934 24.321V28.9449L18.0045 32.2135C17.6964 32.5029 17.4489 32.8507 17.2765 33.2367C17.1041 33.6226 17.0101 34.039 17 34.4616C16.9993 35.3037 17.3318 36.1119 17.9248 36.7098L21.2093 39.9784V44.6182C21.2093 45.4639 21.5453 46.275 22.1433 46.873C22.7413 47.4711 23.5524 47.807 24.3982 47.807H29.0221L32.2907 51.0756C32.8885 51.6686 33.6967 52.0011 34.5388 52.0004C35.3809 52.0011 36.1891 51.6686 36.787 51.0756L40.0556 47.7911H44.6954C45.5411 47.7911 46.3522 47.4551 46.9502 46.8571C47.5483 46.2591 47.8842 45.448 47.8842 44.6022V39.9784L51.1528 36.7098C51.7468 36.1123 52.0801 35.3041 52.0801 34.4616C52.0801 33.6192 51.7468 32.8109 51.1528 32.2135H51.1369ZM44.6794 38.6709V44.6022H38.7481L34.5388 48.8115L30.3295 44.6022H24.3982V38.6709L20.1889 34.4616L24.3982 30.2523V24.321H30.3295L34.5388 20.1117L38.7481 24.321H44.6794V30.2523L48.8887 34.4616L44.6794 38.6709Z" fill="black"/>
<path d="M38.1095 27.8433L28.5429 39.0044C28.4068 39.1635 28.3034 39.3479 28.2385 39.547C28.1737 39.7461 28.1487 39.956 28.165 40.1648C28.1979 40.5864 28.3969 40.9776 28.7183 41.2525C29.0397 41.5274 29.4571 41.6633 29.8787 41.6304C30.3003 41.5975 30.6916 41.3985 30.9664 41.0771L40.5331 29.9161C40.8079 29.5947 40.9439 29.1773 40.911 28.7557C40.8781 28.334 40.6791 27.9428 40.3577 27.6679C40.0363 27.393 39.6189 27.2571 39.1973 27.29C38.7757 27.3229 38.3844 27.5219 38.1095 27.8433Z" fill="black"/>
<path d="M31.2156 31.9901C31.6717 31.8584 32.0783 31.5938 32.3836 31.2302C32.6888 30.8666 32.879 30.4203 32.9297 29.9482C32.9805 29.4762 32.8897 28.9997 32.6687 28.5795C32.4478 28.1592 32.1068 27.8142 31.6891 27.5885C31.2714 27.3627 30.796 27.2664 30.3234 27.3117C29.8508 27.3571 29.4024 27.542 29.0352 27.8431C28.6681 28.1442 28.3989 28.5477 28.2619 29.0023C28.1249 29.4568 28.1263 29.9419 28.2659 30.3957C28.4538 30.9928 28.8669 31.4932 29.4176 31.7908C29.9682 32.0885 30.6131 32.16 31.2156 31.9901Z" fill="black"/>
<path d="M37.8607 36.9301C37.4046 37.0619 36.998 37.3265 36.6927 37.6901C36.3874 38.0537 36.1973 38.5 36.1465 38.972C36.0958 39.4441 36.1866 39.9206 36.4076 40.3408C36.6285 40.7611 36.9695 41.106 37.3872 41.3318C37.8049 41.5576 38.2803 41.6539 38.7529 41.6086C39.2255 41.5632 39.6739 41.3782 40.041 41.0772C40.4082 40.7761 40.6774 40.3726 40.8144 39.918C40.9514 39.4634 40.95 38.9784 40.8104 38.5246C40.6225 37.9275 40.2094 37.4271 39.6587 37.1295C39.1081 36.8318 38.4632 36.7603 37.8607 36.9301Z" fill="black"/>
</svg>


            </div>
            <div class="text font-ave-d text-20">
            Promozioni
            </div>
        </div> <!-- item -->

        <div class="item flex items-center gap-28">
            <div class="icon flex">
            <svg class="h-72" width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="70" height="70" rx="15" fill="#F6FBF0"/>
<path d="M40.2893 49.1592C40.2893 42.2339 43.5697 40.047 43.5697 40.047C48.1258 35.4908 48.3518 27.9714 43.7993 23.4153C41.612 21.2285 38.6457 20 35.5528 20C32.4598 20 29.4935 21.2285 27.3062 23.4153C22.7501 27.9714 22.9761 35.4908 27.5322 40.047C27.5322 40.047 30.8126 42.2339 30.8126 49.1592H40.2893Z" stroke="black" stroke-width="3.7" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M37.7332 54H33.3594" stroke="black" stroke-width="3.7" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M39.196 33.6719H31.9062" stroke="black" stroke-width="3.7" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M35.5508 40.9617L35.5508 33.6719" stroke="black" stroke-width="3.7" stroke-linecap="round" stroke-linejoin="round"/>
</svg>



            </div>
            <div class="text font-ave-d text-20">
            Tips & tricks
            </div>
        </div> <!-- item -->

        <div class="item flex items-center gap-28">
            <div class="icon flex">
            <svg class="h-72" width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="70" height="70" rx="15" fill="#FFF8EC"/>
<circle cx="25.5" cy="34.5" r="2.5" fill="black"/>
<circle cx="36.5" cy="34.5" r="2.5" fill="black"/>
<circle cx="47.5" cy="34.5" r="2.5" fill="black"/>
</svg>


            </div>
            <div class="text font-ave-d text-20">
            E molto altro, seguici!
            </div>
        </div> <!-- item -->



    </div>

    </div>
</div>
    <div class="visual overflow-hidden relative">
        <div class="cols flex gap-20 items-end absolute-center-x">


        <div class="col flex gap-20 flex-col w-280">
            <?php load_img(class: 'img w-100-100 rounded-8', high: 112, low: 112); ?>
            <?php load_img(class: 'img w-100-100 rounded-8', high: 112, low: 112); ?>
            </div>


            <div class="col flex gap-20 flex-col w-280">
                <?php load_img(class: 'img w-100-100 rounded-8', high: 111, low: 111); ?>
                <?php load_img(class: 'img w-100-100 rounded-8', high: 112, low: 112); ?>
            </div>

            <div class="col flex gap-20 flex-col w-280">
                <?php load_img(class: 'img w-100-100 rounded-8', high: 111, low: 111); ?>
            </div>

            <div class="col flex gap-20 flex-col w-280">
            <?php load_img(class: 'img w-100-100 rounded-8', high: 257, low: 257); ?>
            <?php load_img(class: 'img w-100-100 rounded-8', high: 112, low: 112); ?>
            </div>



            <div class="col flex gap-20 flex-col w-280">
                <?php load_img(class: 'img w-100-100 rounded-8', high: 255, low: 255); ?>
            </div>


            <div class="col flex gap-20 flex-col w-280">
            <?php load_img(class: 'img w-100-100 rounded-8', high: 112, low: 112); ?>
            <?php load_img(class: 'img w-100-100 rounded-8', high: 256, low: 256); ?>
            </div>



            <div class="col flex gap-20 flex-col w-280">
                <?php load_img(class: 'img w-100-100 rounded-8', high: 111, low: 111); ?>
            </div>

            <div class="col flex gap-20 flex-col w-280">
                <?php load_img(class: 'img w-100-100 rounded-8', high: 111, low: 111); ?>
                <?php load_img(class: 'img w-100-100 rounded-8', high: 112, low: 112); ?>
            </div>


            <div class="col flex gap-20 flex-col w-280">
            <?php load_img(class: 'img w-100-100 rounded-8', high: 112, low: 112); ?>
            <?php load_img(class: 'img w-100-100 rounded-8', high: 112, low: 112); ?>
            </div>



        </div>

        <div class="opacity-0 w-280">

        <?php load_img(class: 'img w-100-100', high: 111, low: 111); ?>

<?php load_img(class: 'img w-100-100', high: 112, low: 112); ?>
        </div>

    </div>
    
</section>  

 


<?php get_template_part('templates/parts/footer-cta'); ?>



<?php get_template_part('templates/parts/footer-classic'); ?>




<?php
get_sidebar();
get_footer();
