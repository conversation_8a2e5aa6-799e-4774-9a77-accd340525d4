<?php
/**
 * The template for displaying all pages
 *
 * This is the template that displays all pages by default.
 * Please note that this is the WordPress construct of pages
 * and that other 'pages' on your WordPress site may use a
 * different template.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Custom_theme
 */

get_header();
?>


 


<?php get_template_part('templates/parts/nav'); ?>




<section class="page-header mb-140">

<div class="container-xl mx-auto h-460 flex items-center px-80 rounded-24 bg-brand-dark relative">

<div class="main max-w-800 relative z-60 flex flex-col items-start">
    <div class="pretitle uppercase font-tt-eb text-16 mb-28 spacing-12 text-brand-violet">
    La scuola
    </div>
    <div class="title font-din-bc text-60 text-white">
    PRODUZIONE HIP HOP è LA PRIMA SCUOLA IN ITALIA A PROPORRE CORSI DI MUSICA HIP HOP
    </div>
</div>

<div class="background absolute top-0 right-0 w-60-100 bottom-0 z-10 rounded-24 overflow-hidden">
<?php load_img(class: 'img img-cover', high: 122, low: 121); ?>
</div>


<div class="gradients absolute inset-0 z-40 overflow-hidden rounded-24">

<div class="radial-gradient-violet animate-float-around animation-duration-8000 absolute z-20 h-800 w-800 left--400 top--300 opacity-60"></div>
<div class="radial-gradient-blue absolute z-20 h-800 w-800 right--400 bottom--300"></div>
</div>



<div class="shapes z-50 absolute inset-0">

<div class="shape-1 animate-float-updown-light animation-duration-5000 absolute left--40 top-40">
<svg class="h-80 shadow-violet" width="132" height="140" viewBox="0 0 132 140" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_596_641)">
<path d="M126.867 77.9576L14.6966 3.41429C8.44291 -0.741649 0.286323 4.73807 1.76969 12.0988L26.0817 132.74C27.1921 138.25 33.2824 141.149 38.2596 138.538L126.118 92.4404C131.789 89.4651 132.201 81.502 126.867 77.9576Z" fill="#A973FF"/>
</g>
<defs>
<clipPath id="clip0_596_641">
<rect width="131" height="140" fill="white" transform="translate(0.9375)"/>
</clipPath>
</defs>
</svg>


    </div>

    <div class="shape-2 animate-float-updown-light animation-duration-8000 absolute right-200 bottom--40">
    <svg class="h-88 shadow-blue" width="73" height="90" viewBox="0 0 73 90" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_610_1553)">
<path d="M60.9958 81.1441L72.0798 12.6398C73.5036 3.8403 63.5728 -2.28396 56.3491 2.93878L4.91055 40.1292C-0.495635 44.0379 -0.631414 52.0436 4.63912 56.1334L44.9937 87.4474C50.9911 92.1012 59.7833 88.638 60.9958 81.1441Z" fill="#1289ED"/>
</g>
<defs>
<clipPath id="clip0_610_1553">
<rect width="72" height="90" fill="white" transform="translate(0.773438)"/>
</clipPath>
</defs>
</svg>

    </div>

</div>

</div>

</section>




<section class="scuola-docente mb-140">

<div class="container-l mx-auto flex gap-140">

<div class="main w-740 shrink-0">
    <div class="head mb-60 flex flex-col items-start">
        <div class="pretitle uppercase font-tt-eb text-16 mb-28 spacing-12 text-gradient-pink-violet">
        Fondatore e docente
        </div>
        <div class="title font-din-bc text-60 mb-20 ">
        Una vita dedicata alla musica e all'insegnamento
        </div>
        <div class="text font-ave-m leading-15 text-20 text-gray">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sapien libero, ultrices sed elit id, rhoncus sagittis augue.
        </div>
    </div>

    <div class="body">
        <div class="timeline flex gap-40 relative">
            

        <div class="line w-36 relative overflow-hidden shrink-0">
          

<svg class="absolute-center-x top-0 w-4 h-auto" width="4" height="726" viewBox="0 0 4 726" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2 2L2.00003 724" stroke="#A973FF" stroke-width="4" stroke-linecap="round" stroke-dasharray="0.1 10"/>
</svg> 

        </div>

        <div class="items flex flex-col gap-48">

            <div class="item flex flex-col gap-20">
                <div class="circle border-2 border-solid bg-brand-violet-light border-brand-violet rounded-full h-36 w-36 absolute left-0">

                </div>
                <div class="text font-ave-m leading-15 text-20 text-gray">
                Ho studiato canto, pianoforte, arrangiamento e composizione, il mix e master, tecniche di produzione musicale, imparato ad utilizzare Cubase, Studio One, FL Studio, Logic Pro X e acquisito certificazioni come Waves audio, Apple e Sonnox.
                </div>
                <div class="certifications flex gap-10" trigger-modale="certifications">
                    <div class="item">
                    <?php load_img(class: 'img h-36', high: 131); ?>
                    </div>
                    <div class="item">
                    <?php load_img(class: 'img h-36', high: 130); ?>
                    </div>
                    <div class="item">
                    <?php load_img(class: 'img h-36', high: 129); ?>
                    </div>
                    <div class="item">
                    <?php load_img(class: 'img h-36', high: 128); ?>
                    </div>
                    <div class="item">
                    <?php load_img(class: 'img h-36', high: 127); ?>
                    </div>
                </div>
            </div> <!-- item -->


            <div class="item">

            <div class="circle border-2 border-solid bg-brand-violet-light border-brand-violet rounded-full h-36 w-36 absolute left-0">

</div>
                <div class="text font-ave-m leading-15 text-20 text-gray">
                Nel 2014 ho fondato <span class="font-ave-d text-black">la prima scuola in Italia di Produzione Hip Hop</span> e seguito personalmente piu' di 500 studenti.
                </div>
            </div> <!-- item -->

            <div class="item">

            <div class="circle border-2 border-solid bg-brand-violet-light border-brand-violet rounded-full h-36 w-36 absolute left-0">

</div>
                <div class="text font-ave-m leading-15 text-20 text-gray">
                <span class="font-ave-d text-black">Prodotto per numerosi artisti,</span> anche di fama internazionale, negli anni per un totale di 6+ milioni di ascolti e realizzato piu' di <span class="font-ave-d text-black">500 mixaggi e mastering.</span>
                </div>
            </div> <!-- item -->

            <div class="item">

            <div class="circle border-2 border-solid bg-brand-violet-light border-brand-violet rounded-full h-36 w-36 absolute left-0">

</div>
                <div class="text font-ave-m leading-15 text-20 text-gray">
                <span class="font-ave-d text-black">Aperto uno studio di registrazione</span> a Bergamo.
                </div>
            </div> <!-- item -->

</div>



        </div>
    </div>

</div>
<div class="side mt--80">
    <div class="visual flex flex-col items-center gap-60">
        <div class="photo relative">
            <?php load_img(class: 'img w-100-100', high: 124, low: 123); ?>
        </div>
        <div class="quote flex flex-col items-center gap-40 w-80-100 mt--140 z-30 relative">
            <?php load_img(class: 'img w-100-100', high: 138); ?>
            <div class="author">
                
            <svg class="h-120" width="377" height="128" viewBox="0 0 377 128" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_610_1801)">
<path d="M364.245 42.7616C364.245 42.7616 364.245 42.7616 356.209 51.1985C355.488 51.8911 354.754 52.4844 354.021 53.0778L353.922 53.0899C353.101 53.7946 352.343 54.1894 351.76 54.3614C351.462 54.3978 351.264 54.4221 350.867 54.4706C350.668 54.4949 350.557 54.4077 350.457 54.4199L349.826 54.1948C349.615 54.1198 349.38 53.8463 349.145 53.5728C348.28 52.268 349.229 50.1372 349.928 48.4399C350.603 46.544 351.489 44.7232 352.375 42.9023L353.336 40.8708L353.248 40.9822C353.062 41.1057 352.876 41.2292 352.701 41.452C352.142 41.8225 351.583 42.1931 351.012 42.4644C350.341 42.7478 349.46 42.9562 348.729 42.7433C347.898 42.5426 347.69 41.6613 347.605 40.9665C347.631 40.3588 347.82 39.4291 348.493 38.3394C349.478 36.5064 353.95 29.4116 357.124 29.8298L357.657 30.0669C358.091 30.3162 358.449 30.7761 358.51 31.2724L358.546 31.5702C358.633 31.4588 358.732 31.4467 358.72 31.3474C359.354 30.7662 360.149 30.6692 360.693 31.0056C361.25 31.4413 361.137 32.1604 360.725 32.9159C358.656 36.594 356.5 40.3834 354.653 44.2358C353.93 45.7346 353.293 47.1221 352.669 48.6087C352.594 48.8194 352.519 49.03 352.444 49.2407C352.132 49.984 351.544 51.7685 352.974 50.2841L353.335 49.9378C353.522 49.8143 353.696 49.5915 353.783 49.4801L373.101 29.4897C373.101 29.4897 374.489 30.1261 374.176 30.8694C373.765 31.6249 368.45 38.4197 364.245 42.7616ZM351.418 39.191L353.47 37.0261L356.145 34.1807C355.835 34.1178 355.612 33.9436 355.377 33.67L355.116 34.0042L354.605 34.7718C354.157 35.2295 353.821 35.7743 353.472 36.2199C352.688 37.2224 352.003 38.2127 351.418 39.191Z" fill="#192329"/>
<path d="M364.186 10.7312C364.968 10.5349 365.76 11.2441 365.845 11.939C365.954 12.8323 365.32 13.4135 364.538 13.6098C360.517 14.5041 356.607 15.4855 352.585 16.3798C350.637 16.9201 348.676 17.3612 346.715 17.8023L344.058 23.2649L337.521 36.5557C337.122 37.4104 336.623 38.2773 336.125 39.1442L324.836 62.1834L352.48 32.008C352.48 32.008 352.838 32.4679 352.899 32.9642C352.948 33.3612 352.897 33.7704 352.822 33.9811C352.858 34.2789 352.485 34.5259 335.138 54.9807L326.865 63.9503L326.255 64.73L325.807 65.1877L325.546 65.5219C325.446 65.534 325.359 65.6454 325.272 65.7568C324.103 66.9071 321.828 68.8977 320.329 68.1741C319.908 68.0241 319.648 67.5521 319.588 67.0558C319.503 66.3609 319.716 65.6297 319.941 64.9978C320.427 64.0316 320.827 63.1769 321.325 62.31C321.376 61.9009 321.637 61.5667 321.799 61.2447C322.037 60.712 322.361 60.0679 322.598 59.5352C322.76 59.2131 322.922 58.8911 323.096 58.6683C323.334 58.1356 323.583 57.7022 323.733 57.2809C324.219 56.3148 324.619 55.46 325.117 54.5932C325.604 53.6271 326.09 52.6609 326.576 51.6948C326.964 50.7408 327.45 49.7747 327.936 48.8086C328.423 47.8424 328.921 46.9756 329.309 46.0216C329.72 45.2661 330.219 44.3993 330.618 43.5445C330.942 42.9004 331.266 42.2564 331.591 41.6123C331.741 41.191 331.99 40.7575 332.239 40.3241C332.314 40.1135 332.401 40.0021 332.476 39.7914L311.989 60.9321C311.902 61.0435 311.815 61.1549 311.716 61.167C310.546 62.3173 308.283 64.4072 306.784 63.6837C305.496 63.0351 306.159 61.04 306.621 59.8753L307.045 59.2191L323.036 26.8407L324.982 22.9762C320.079 24.0789 315.189 25.2809 310.187 26.3957C309.505 26.5799 308.713 25.8707 308.616 25.0766C308.519 24.2825 309.153 23.7013 309.836 23.5171C313.845 22.5236 317.866 21.6293 321.875 20.6357C323.451 20.3424 325.015 19.9499 326.678 19.5452L327.639 17.5137L332.017 8.81854C331.98 8.52075 332.055 8.31011 332.031 8.11158C332.256 7.47963 332.692 6.92268 333.152 6.56425C334.369 5.81102 336.127 7.00664 335.379 8.30693C335.142 8.83962 334.905 9.37232 334.668 9.90501L332.497 14.4015L330.402 18.6873C334.609 17.6695 338.817 16.6517 342.937 15.7452C343.521 15.5732 344.104 15.4012 344.7 15.3284L348.891 6.75682C348.954 6.4469 348.93 6.24839 349.005 6.03773C349.131 5.41791 349.665 4.84883 350.125 4.4904C351.244 3.7493 353.002 4.94492 352.353 6.23308C352.116 6.76577 351.879 7.29847 351.642 7.83117L349.471 12.3276L348.411 14.3712C353.711 13.22 358.899 11.9817 364.186 10.7312ZM335.158 34.5274L342.991 18.6602C339.181 19.6295 335.259 20.5117 331.436 21.3817L328.693 22.0191L327.084 25.3387L320.547 38.6296C319.749 40.339 318.839 41.9614 318.04 43.6708L311.005 57.8285L315.147 53.7965L335.158 34.5274Z" fill="#192329"/>
<path d="M317.613 36.2683C317.613 36.2683 319.059 38.2073 318.052 39.0356C315.777 41.0262 308.392 47.3687 306.29 49.1366C303.63 51.275 300.871 53.4255 298.123 55.6753C297.203 56.3922 296.296 57.2083 295.376 57.9251C294.543 58.5306 293.686 58.9375 292.669 58.8603C290.945 58.7687 290.032 57.0669 289.85 55.5779C289.719 53.6798 290.394 51.784 290.994 50.0988C291.381 49.1448 291.769 48.1908 292.156 47.2368C292.156 47.2368 292.243 47.1254 292.231 47.0261C292.63 46.1714 293.129 45.3045 293.627 44.4377C293.714 44.3263 293.789 44.1156 293.964 43.8928C294.886 42.3698 296.069 40.5125 297.413 39.1395C297.5 39.0281 297.773 38.7932 297.96 38.6696C297.96 38.6696 300.395 37.1632 302.051 39.1772C303.015 40.4698 302.452 42.4528 302.202 42.8862C301.989 43.6174 301.566 44.2737 301.229 44.8185C300.37 46.0316 299.288 47.0705 298.293 47.998C297.846 48.4557 297.485 48.802 297.025 49.1604C296.018 49.9887 294.738 51.0518 293.67 51.3837C293.595 51.5944 293.52 51.805 293.457 52.115C293.283 52.3377 293.22 52.6477 293.157 52.9576C292.869 53.8994 292.717 55.127 293.012 55.8968C293.112 55.8847 293.112 55.8847 293.112 55.8847C293.211 55.8725 293.199 55.7733 293.298 55.7612C294.044 55.2671 294.864 54.5624 295.598 53.969C296.418 53.2643 297.251 52.6589 297.972 51.9663C301.291 49.4452 304.498 46.8369 307.706 44.2287C309.359 42.9185 310.913 41.6205 312.566 40.3103C313.3 39.7169 314.133 39.1115 314.854 38.4189C314.953 38.4068 314.953 38.4068 314.953 38.4068C315.413 38.0483 315.774 37.702 316.234 37.3436C316.321 37.2322 316.519 37.208 316.606 37.0966L317.066 36.7382C317.154 36.6268 317.154 36.6268 317.154 36.6268L317.613 36.2683ZM299.863 41.0564C297.14 43.5047 295.966 46.2675 295.966 46.2675C298.69 43.8191 299.863 41.0564 299.863 41.0564Z" fill="#192329"/>
<path d="M297.164 38.7648C297.164 38.7648 298.29 39.7354 297.406 40.75L278.429 58.5832L278.342 58.6946C277.894 59.1523 276.066 60.6852 274.827 60.4337C274.417 60.383 274.195 60.2087 274.071 60.0223C273.49 59.3881 273.543 58.1727 273.681 57.6522C273.845 56.5239 274.307 55.3592 274.682 54.306C275.094 53.5505 275.505 52.795 275.917 52.0396C276.316 51.1848 279.808 44.3106 280.631 42.7996C275.6 45.3284 276.815 41.251 276.815 41.251C276.815 41.251 277.98 41.7132 280.178 40.7394C280.178 40.7394 280.848 40.456 281.543 40.3711C283.739 40.2035 283.921 41.6925 283.831 42.6101C283.831 42.6101 283.781 43.0192 283.456 43.6633C283.456 43.6633 283.045 44.4188 282.808 44.9515L281.748 46.9951C281.511 47.5278 281.187 48.1719 280.85 48.7167C280.538 49.4601 280.214 50.1041 279.79 50.7604C279.466 51.4044 279.229 51.9371 278.904 52.5812C278.655 53.0146 278.406 53.4481 278.256 53.8694C277.919 54.4142 277.682 54.9469 277.346 55.4917C277.271 55.7024 277.196 55.913 277.034 56.2351C276.959 56.4457 276.884 56.6564 277.007 56.8428L295.895 39.9272L296.616 39.2346C296.616 39.2346 296.716 39.2225 296.704 39.1232L297.164 38.7648Z" fill="#192329"/>
<path d="M275.899 41.366C275.899 41.366 277.344 43.305 276.337 44.1332C274.062 46.1239 266.677 52.4664 264.576 54.2342C261.915 56.3726 259.156 58.5232 256.408 60.773C255.489 61.4898 254.581 62.3059 253.661 63.0228C252.828 63.6283 251.971 64.0352 250.954 63.958C249.231 63.8663 248.317 62.1645 248.135 60.6756C248.004 58.7775 248.679 56.8816 249.279 55.1964C249.667 54.2424 250.054 53.2884 250.441 52.3344C250.441 52.3344 250.528 52.223 250.516 52.1238C250.915 51.269 251.414 50.4022 251.912 49.5353C251.999 49.4239 252.074 49.2133 252.249 48.9905C253.171 47.4674 254.354 45.6102 255.698 44.2371C255.785 44.1257 256.058 43.8908 256.245 43.7673C256.245 43.7673 258.68 42.2608 260.336 44.2748C261.3 45.5675 260.737 47.5505 260.487 47.9839C260.275 48.7151 259.851 49.3713 259.515 49.9161C258.655 51.1293 257.573 52.1682 256.579 53.0957C256.131 53.5534 255.77 53.8997 255.31 54.2581C254.303 55.0863 253.023 56.1495 251.955 56.4814C251.88 56.6921 251.805 56.9027 251.742 57.2126C251.568 57.4354 251.505 57.7453 251.442 58.0552C251.154 58.9971 251.002 60.2246 251.297 60.9945C251.397 60.9823 251.397 60.9823 251.397 60.9823C251.496 60.9702 251.484 60.8709 251.583 60.8588C252.329 60.3647 253.149 59.66 253.883 59.0667C254.703 58.362 255.536 57.7565 256.257 57.0639C259.576 54.5428 262.784 51.9346 265.991 49.3264C267.644 48.0162 269.198 46.7181 270.852 45.4079C271.585 44.8146 272.418 44.2091 273.139 43.5166C273.238 43.5044 273.238 43.5044 273.238 43.5044C273.698 43.146 274.059 42.7997 274.519 42.4413C274.606 42.3299 274.804 42.3056 274.892 42.1942L275.352 41.8358C275.439 41.7244 275.439 41.7244 275.439 41.7244L275.899 41.366ZM258.148 46.1541C255.425 48.6024 254.251 51.3651 254.251 51.3651C256.975 48.9168 258.148 46.1541 258.148 46.1541Z" fill="#192329"/>
<path d="M255.556 43.8504C255.556 43.8504 256.593 45.7387 255.772 46.4434C254.952 47.1481 241.921 54.2811 241.921 54.2811C241.88 55.5958 241.541 56.9468 241.29 58.1865C239.609 65.041 235.809 71.0461 231.551 76.6035C226.921 82.4079 221.746 87.8758 216.101 92.7968C213.279 95.2572 210.444 97.6184 207.387 99.8054C204.802 101.733 201.398 103.559 198.086 103.662C196.585 103.744 195.122 103.319 193.897 102.36C192.573 101.414 191.497 100.034 190.807 98.5065C189.327 95.4636 189.2 91.953 189.916 88.7425C191.636 81.3796 197.356 76.248 203.296 72.0969C209.709 67.6867 216.492 63.8356 223.41 60.2702C228.627 57.618 233.856 55.065 239.185 52.4998C239.172 52.4006 239.172 52.4006 239.172 52.4006C239.087 51.7057 239.003 51.0109 238.93 50.4153C238.906 50.2168 238.893 50.1176 238.77 49.9312C238.77 49.9312 238.734 49.6334 238.721 49.5341C238.586 49.2484 238.462 49.0621 238.426 48.7643C238.315 48.6771 238.179 48.3915 238.179 48.3915C238.167 48.2922 238.055 48.2051 238.043 48.1058C237.92 47.9194 237.808 47.8323 237.697 47.7452C237.685 47.6459 237.561 47.4595 237.573 47.5588C237.45 47.3724 237.338 47.2852 237.128 47.2102L236.905 47.036C236.682 46.8617 236.372 46.7988 236.062 46.7359C235.331 46.523 234.724 46.4965 233.929 46.5936C232.143 46.8119 230.48 47.2166 228.916 47.6091C222.014 49.6613 216.136 53.5025 210.53 57.915C209.249 58.9781 207.307 57.0998 208.464 55.8503C211.562 52.3487 214.76 48.835 217.747 45.2462C223.21 38.8364 228.462 32.3515 233.256 25.4189C235.561 22.0143 237.753 18.5226 239.735 14.9559C240.732 13.2222 241.705 11.2899 242.69 9.45695C243.09 8.60222 243.477 7.64822 243.677 6.81774C243.752 6.60709 243.74 6.50783 243.716 6.3093C239.03 10.0049 234.59 14.0734 230.473 18.3039C226.269 22.6459 222.287 27.1621 218.528 31.8526C214.844 36.3325 211.184 41.0108 207.847 45.8513C207.249 46.7303 206.651 47.6093 206.041 48.389C205.966 48.5997 205.879 48.7111 205.792 48.8225C200.979 58.0745 195.967 67.3508 190.942 76.5279C190.531 77.2834 189.773 77.6782 189.03 77.366C188.287 77.0539 187.979 76.1848 188.192 75.4535C188.504 74.7102 188.729 74.0782 189.041 73.3349C191.301 67.9208 193.971 62.5575 196.974 57.4556C198.956 53.8889 201.248 50.3851 203.552 46.9805C206.071 42.0385 208.69 37.0844 211.197 32.0431C215.899 22.7039 220.589 13.2655 225.18 3.83913C225.492 3.09578 226.349 2.68885 227.092 3.001C227.736 3.32528 228.155 4.28152 227.831 4.9256C225.261 10.2768 222.691 15.628 220.01 20.892C218.163 24.7444 216.218 28.6089 214.272 32.4734C215.405 31.0253 216.537 29.5772 217.682 28.2284C221.54 23.5258 225.621 18.9975 229.937 14.7426C233.979 10.7227 238.157 6.98846 242.67 3.51561C243.217 3.04579 245.279 1.78636 246.216 3.6867C247.178 5.78557 246.564 8.17774 245.504 10.2214C244.456 12.3643 243.408 14.5072 242.15 16.5751C237.936 24.1419 232.955 31.1981 227.541 38.005C224.578 41.7922 221.504 45.4923 218.319 49.1053C219.822 48.2165 221.338 47.4268 222.964 46.7244C224.591 46.0219 226.329 45.4065 228.179 44.8783C229.929 44.3622 231.779 43.834 233.677 43.7028C235.563 43.4724 237.41 43.7504 238.97 44.9703C240.183 45.8295 241.048 47.1343 241.428 48.599C241.724 49.3688 241.833 50.2622 241.93 51.0562C242.687 50.6614 243.532 50.1552 244.302 49.8597C247.718 48.1327 251.034 46.4178 254.339 44.6037C254.712 44.3566 255.084 44.1096 255.556 43.8504ZM231.378 71.8897C234.815 67.037 237.695 61.7487 238.791 55.8725C233.09 58.6847 227.389 61.4968 221.799 64.3961C215.364 67.8016 209.066 71.4928 203.149 75.8424C197.793 79.8214 192.867 84.856 192.5 91.7513C192.387 92.4704 192.472 93.1653 192.581 94.0586C192.629 94.4557 192.666 94.7534 192.714 95.1505C192.726 95.2498 192.763 95.5475 192.763 95.5475C192.874 95.6347 192.886 95.7339 192.898 95.8332C193.07 96.4166 193.342 96.988 193.613 97.5593C193.625 97.6585 193.761 97.9442 193.761 97.9442C193.872 98.0313 193.884 98.1306 193.996 98.2177C194.131 98.5034 194.366 98.7769 194.49 98.9633C194.613 99.1497 194.725 99.2368 194.848 99.4232C194.947 99.4111 194.959 99.5104 194.959 99.5104C195.194 99.7839 195.417 99.9582 195.739 100.12C195.751 100.22 195.851 100.207 195.863 100.307C198.153 101.739 201.494 100.223 203.842 98.828C204.203 98.4817 204.576 98.2347 205.048 97.9755C206.527 96.8881 207.907 95.8129 209.373 94.6262C212.22 92.3643 214.955 90.0152 217.579 87.579C222.529 82.743 227.332 77.522 231.378 71.8897Z" fill="#192329"/>
<path d="M178.524 53.2605C178.524 53.2605 179.969 55.1995 178.962 56.0277C176.687 58.0184 169.302 64.3609 167.201 66.1287C164.54 68.2672 161.781 70.4177 159.033 72.6675C158.114 73.3844 157.206 74.2005 156.286 74.9173C155.453 75.5228 154.596 75.9297 153.579 75.8525C151.856 75.7609 150.942 74.059 150.76 72.5701C150.629 70.672 151.304 68.7762 151.904 67.0909C152.292 66.1369 152.679 65.1829 153.066 64.229C153.066 64.229 153.153 64.1176 153.141 64.0183C153.54 63.1636 154.039 62.2967 154.537 61.4298C154.624 61.3185 154.699 61.1078 154.874 60.885C155.796 59.362 156.979 57.5047 158.323 56.1317C158.41 56.0203 158.683 55.7854 158.87 55.6618C158.87 55.6618 161.305 54.1554 162.962 56.1693C163.925 57.462 163.362 59.445 163.112 59.8784C162.9 60.6096 162.476 61.2658 162.14 61.8107C161.28 63.0238 160.198 64.0627 159.204 64.9902C158.756 65.4479 158.395 65.7942 157.935 66.1526C156.928 66.9809 155.648 68.044 154.58 68.3759C154.505 68.5866 154.43 68.7972 154.367 69.1071C154.193 69.3299 154.13 69.6398 154.067 69.9498C153.779 70.8916 153.627 72.1192 153.923 72.889C154.022 72.8769 154.022 72.8769 154.022 72.8769C154.121 72.8647 154.109 72.7655 154.208 72.7533C154.954 72.2593 155.774 71.5546 156.508 70.9612C157.328 70.2565 158.161 69.651 158.882 68.9584C162.201 66.4373 165.409 63.8291 168.616 61.2209C170.269 59.9107 171.823 58.6126 173.477 57.3025C174.21 56.7091 175.043 56.1037 175.764 55.4111C175.863 55.399 175.863 55.399 175.863 55.399C176.323 55.0405 176.684 54.6942 177.144 54.3358C177.231 54.2244 177.429 54.2002 177.517 54.0888L177.977 53.7304C178.064 53.619 178.064 53.619 178.064 53.619L178.524 53.2605ZM160.773 58.0486C158.05 60.4969 156.876 63.2597 156.876 63.2597C159.6 60.8113 160.773 58.0486 160.773 58.0486Z" fill="#192329"/>
<path d="M158.061 55.7613C158.061 55.7613 159.064 56.5455 158.739 57.1896C158.502 57.7222 150.338 67.5852 146.071 72.2371L144.204 74.2784L143.931 74.5133C143.844 74.6247 143.844 74.6247 143.757 74.7361C142.5 75.9978 140.225 77.9884 138.823 78.059C138.724 78.0711 138.712 77.9718 138.613 77.984C138.005 77.9575 137.634 77.3983 137.487 77.0134C137.167 76.045 137.307 74.7182 137.483 73.6892C137.621 73.1686 137.672 72.7595 137.822 72.3382C137.81 72.2389 137.897 72.1275 137.897 72.1275C138.173 71.0864 138.548 70.0331 139.034 69.067L141.469 63.4302L127.657 79.0202C127.21 79.4779 126.837 79.725 126.75 79.8364C126.377 80.0834 125.992 80.2312 125.496 80.2918C125.508 80.3911 125.508 80.3911 125.508 80.3911C125.309 80.4153 125.099 80.3403 124.999 80.3524C124.999 80.3524 124.999 80.3524 124.9 80.3646C124.888 80.2653 124.789 80.2774 124.677 80.1903C124.343 79.9289 124.282 79.4326 124.234 79.0355C124.321 78.9241 124.285 78.6264 124.534 78.1929L127.453 72.3962L133.94 58.7083C133.94 58.7083 134.834 58.5992 135.354 58.7371C135.454 58.7249 135.454 58.7249 135.565 58.8121C136.197 59.0371 136.58 59.6955 135.868 61.2936L135.481 62.2476C135.394 62.359 135.307 62.4704 135.319 62.5696C135.145 62.7924 135.07 63.0031 134.907 63.3251C134.496 64.0806 134.196 64.9232 133.785 65.6787L132.175 68.9983L131.365 70.6085C131.365 70.6085 131.365 70.6085 131.464 70.5964L130.977 71.5625L140.969 60.1667C141.143 59.9439 141.504 59.5976 142.075 59.3263C142.56 59.1664 143.167 59.1929 143.811 59.5172C144.765 59.9043 144.749 61.4175 144.647 62.2359C144.231 64.6038 143.283 66.7345 142.235 68.8774C141.911 69.5215 141.598 70.2649 141.373 70.8968C141.211 71.2189 141.049 71.5409 140.899 71.9622C140.899 71.9622 140.824 72.1729 140.836 72.2721C140.749 72.3835 140.674 72.5942 140.674 72.5942C140.536 73.1147 140.498 73.6232 140.36 74.1437L143.768 70.705L148.868 65.4477L155.909 57.9383L156.892 56.9115L158.061 55.7613Z" fill="#192329"/>
<path d="M133.037 58.8191C133.037 58.8191 134.238 59.579 133.64 60.458C133.565 60.6687 133.391 60.8914 133.042 61.337C127.952 67.4998 122.958 70.3263 121.145 71.1523C121.157 71.2516 121.07 71.363 120.983 71.4744C120.833 71.8957 120.671 72.2177 120.409 72.5519C120.073 73.0967 119.998 73.3074 119.998 73.3074C119.749 73.7408 119.4 74.1864 119.151 74.6198C118.379 75.7216 117.507 76.8355 116.512 77.763C115.343 78.9132 114.087 80.1749 112.363 80.0833C110.937 79.9553 110.097 78.849 109.927 77.4594C109.682 76.2804 110.033 75.0286 110.495 73.8639C111.383 71.2369 112.68 68.6605 114.324 66.4449L114.411 66.3335C114.563 65.1059 114.914 63.8542 115.487 62.7767C116.136 61.4885 117.082 60.1639 118.67 59.9699C118.67 59.9699 118.77 59.9578 118.869 59.9456C121.089 59.9766 122.014 61.7777 122.322 62.6468C122.618 63.4166 122.727 64.31 122.725 65.1162C122.686 65.6247 122.734 66.0217 122.584 66.443C122.609 66.6415 122.534 66.8522 122.459 67.0628C122.471 67.1621 122.42 67.5713 122.321 67.5834C125.635 66.6747 133.037 58.8191 133.037 58.8191ZM112.83 77.3062C112.842 77.4055 112.83 77.3062 112.83 77.3062V77.3062ZM117.497 71.7996C116.666 71.5988 115.799 71.1003 115.305 70.3547C114.47 71.7664 113.747 73.2652 113.234 74.839C113.059 75.0618 112.997 75.3717 112.921 75.5824C112.934 75.6816 112.934 75.6816 112.934 75.6816C112.859 75.8923 112.871 75.9915 112.784 76.1029C112.808 76.3015 112.733 76.5121 112.769 76.8099C112.733 76.5121 112.794 77.0084 112.806 77.1077C112.905 77.0955 112.992 76.9842 113.091 76.972C114.844 75.6497 116.325 73.7561 117.497 71.7996ZM119.783 65.7778C119.822 65.2694 119.885 64.9595 119.836 64.5624C118.841 65.4899 118.045 66.3932 117.273 67.495C117.273 67.495 117.186 67.6063 117.198 67.7056C117.21 67.8049 117.321 67.892 117.333 67.9913L117.346 68.0905C117.358 68.1898 117.37 68.289 117.481 68.3762C117.481 68.3762 117.481 68.3762 117.493 68.4754C117.493 68.4754 117.605 68.5626 117.617 68.6618L117.728 68.749L117.84 68.8361C117.939 68.824 117.951 68.9232 118.05 68.9111C117.84 68.8361 118.261 68.9861 118.261 68.9861C118.36 68.974 118.459 68.9619 118.559 68.9497C118.67 69.0369 118.769 69.0247 118.869 69.0126L118.968 69.0005C119.343 67.9472 119.619 66.9061 119.783 65.7778Z" fill="#192329"/>
<path d="M116.884 63.5119C116.896 63.6111 111.21 69.8467 110.14 70.9849L101.01 80.3614C101.022 80.4606 100.923 80.4728 100.836 80.5842C100.574 80.9183 99.318 82.18 97.9769 82.7468C95.481 83.757 95.1392 81.7839 95.1535 81.0769C95.061 78.6704 95.8861 76.3532 96.9339 74.2103L99.5546 68.45L95.3017 72.3949C94.1204 73.4459 92.852 74.6083 91.6829 75.7586C90.6759 76.5868 89.7803 77.5022 88.7855 78.4297L82.9156 83.9826C81.2987 85.5905 80.1583 85.3269 79.6013 84.8912C79.1679 84.6419 79.008 84.1578 78.9716 83.86C78.9595 83.7607 78.9474 83.6614 78.9352 83.5622L89.1586 69.1157C89.2458 69.0043 89.3329 68.8929 89.42 68.7815C89.0594 69.1278 88.6866 69.3748 88.3259 69.7211C82.0347 75.124 76.2376 81.2724 71.1088 87.9437C69.964 89.2925 67.9832 87.9226 68.7068 86.4238L70.5653 82.6707C72.7603 78.3728 74.8438 73.9877 77.0388 69.6898C77.276 69.1571 77.6124 68.6123 77.8495 68.0796C77.9366 67.9682 77.9245 67.8689 78.0117 67.7575C78.0867 67.5469 78.2609 67.3241 78.3359 67.1134C78.4109 66.9028 78.5852 66.68 78.6602 66.4694L78.7352 66.2587L78.8974 65.9367C78.8974 65.9367 78.8974 65.9367 78.8852 65.8374L79.0595 65.6146L79.0474 65.5154L79.1345 65.404C79.1345 65.404 79.333 65.3797 79.5316 65.3555L80.5242 65.2342C80.7227 65.2099 81.219 65.1493 81.4539 65.4228C81.776 65.585 81.9237 65.9699 81.7108 66.7011L81.6116 66.7132L80.5637 68.8561C80.4887 69.0668 80.5008 69.166 80.4137 69.2774C80.3387 69.4881 80.1644 69.7109 80.0894 69.9215C79.6901 70.7762 79.2787 71.5317 78.8794 72.3865L76.3602 77.3285C81.1746 72.2068 86.2481 67.5571 91.7673 63.2561C92.1401 63.009 92.6121 62.7499 93.1205 62.7885C93.9146 62.6915 94.583 63.2143 94.5807 64.0205C94.7748 65.6087 94.1262 66.8968 93.267 68.11C93.1799 68.2214 93.0056 68.4442 92.9185 68.5556L92.3328 69.5338C92.3328 69.5338 92.3328 69.5338 92.2335 69.5459C92.0714 69.868 91.8971 70.0908 91.7228 70.3135C91.735 70.4128 91.6357 70.4249 91.6478 70.5242L90.4401 72.1829L88.9081 74.4857C89.6415 73.8924 90.2636 73.2119 90.9849 72.5193L91.0842 72.5072C92.4397 71.2334 93.7952 69.9596 95.1507 68.6858C95.6107 68.3274 95.9713 67.9811 96.332 67.6348L98.421 65.7676C98.6945 65.5327 98.8688 65.31 99.1423 65.0751C99.2416 65.0629 99.2294 64.9637 99.3287 64.9515C99.975 64.4696 100.832 64.0627 101.897 64.5369C102.864 65.0234 102.835 66.4373 102.646 67.367C102.218 69.6357 101.182 71.8778 100.135 74.0207C99.8103 74.6648 99.486 75.3089 99.1738 76.0523C99.111 76.3622 98.9488 76.6842 98.7988 77.1055C98.7988 77.1055 98.6245 77.3283 98.6367 77.4276C98.6488 77.5268 98.5738 77.7375 98.5738 77.7375C98.3366 78.2702 98.2859 78.6793 98.148 79.1999L101.556 75.7612L106.768 70.591L114.094 62.946L114.99 62.0306L115.089 62.0185L116.159 60.8803C116.159 60.8803 118.215 62.0396 117.244 63.1656C117.157 63.277 117.07 63.3884 116.884 63.5119Z" fill="#192329"/>
<path d="M78.3264 65.5044C78.3264 65.5044 80.0844 66.7001 78.6781 68.383L69.8964 77.314L67.4953 79.9245C67.1346 80.2708 65.7791 81.5446 65.5927 81.6681C65.1449 82.1258 64.7842 82.4721 64.3243 82.8305C63.4045 83.5473 62.4725 84.1649 61.1821 84.3226C60.1895 84.4438 58.8384 84.1052 57.8602 83.5195C56.647 82.6603 55.993 81.4305 56.046 80.2151C56.046 80.2151 56.046 80.2151 56.0339 80.1159C56.0604 79.5082 56.6407 76.012 62.9552 66.6773C63.0423 66.5659 63.2287 66.4424 63.3158 66.331C63.875 65.9604 64.6813 65.9627 65.3496 66.4855C65.9065 66.9212 66.2263 67.8895 65.8778 68.3351C64.5587 69.9067 63.3509 71.5654 62.366 73.3984C61.4439 74.9214 60.621 76.4324 60.021 78.1176C59.6967 78.7617 59.2974 79.6164 59.0966 80.4469C59.0338 80.7568 59.0338 80.7568 59.0702 81.0546C59.058 80.9553 59.058 80.9553 59.0702 81.0546C59.0702 81.0546 59.1815 81.1417 59.0823 81.1538C59.0823 81.1538 59.0823 81.1538 59.1815 81.1417C59.2929 81.2288 59.5157 81.4031 59.4043 81.316C59.5157 81.4031 59.615 81.391 59.7264 81.4781L59.8256 81.466C59.9249 81.4539 60.0241 81.4417 60.1234 81.4296C60.5205 81.3811 60.6197 81.369 61.0918 81.1098C61.7624 80.8264 62.4837 80.1338 63.2171 79.5405C63.4785 79.2063 63.8513 78.9593 64.212 78.613L77.6922 66.0856L78.0529 65.7393L78.3264 65.5044ZM68.4581 54.8224C69.1529 54.7375 69.9326 55.3474 70.0297 56.1415C70.1267 56.9356 69.5167 57.7154 68.8219 57.8003C68.0278 57.8973 67.2481 57.2873 67.1511 56.4932C67.054 55.6992 67.664 54.9194 68.4581 54.8224Z" fill="#192329"/>
<path d="M61.7176 27.7391C61.8048 27.6277 61.7926 27.5284 61.7805 27.4292C61.7562 27.2307 61.7441 27.1314 61.8191 26.9207C61.807 26.8215 61.6714 26.5358 61.7706 26.5237C61.6592 26.4366 61.635 26.238 61.6228 26.1388L61.4872 25.8531C61.3758 25.766 61.3637 25.6667 61.3637 25.6667C61.3637 25.6667 61.3637 25.6667 61.2644 25.6789C61.2523 25.5796 61.1409 25.4925 61.1409 25.4925C61.1409 25.4925 61.1409 25.4925 61.0295 25.4053C61.0295 25.4053 60.9181 25.3182 60.8189 25.3303L60.7075 25.2432C60.7196 25.3424 60.3976 25.1803 60.2983 25.1924C60.2983 25.1924 59.8891 25.1417 59.9884 25.1295C59.7899 25.1538 59.5913 25.1781 59.3928 25.2023C58.9958 25.2508 58.4995 25.3115 58.1145 25.4592C58.1145 25.4592 57.7175 25.5077 57.6182 25.5199C57.4318 25.6434 57.2333 25.6677 57.0469 25.7912C56.2771 26.0867 55.408 26.3944 54.6503 26.7892C53.222 27.4674 51.7308 28.4556 50.2518 29.543C48.8841 30.7175 47.7029 31.7685 46.4587 33.1294C43.9826 35.9506 42.0513 39.1081 40.9747 42.6649C40.6868 43.6068 40.3989 44.5487 40.2224 45.5777C40.0337 46.5074 39.9201 47.2265 39.93 48.132C39.7513 49.9672 39.771 51.7782 39.9893 53.5649C40.0864 54.359 40.2826 55.1409 40.4911 56.0222C40.6995 56.9034 40.9079 57.7846 41.2034 58.5545C41.7195 60.3048 42.4341 62.0309 43.0373 63.6698C44.4544 67.0227 45.8594 70.2763 46.7923 73.7891C46.9886 74.5711 47.0735 75.2659 47.1705 76.06L49.8914 74.4179L61.6443 67.5418C61.6443 67.5418 62.6082 68.8344 61.8747 69.4278C61.7997 69.6384 60.7563 70.1689 59.5508 71.0214L47.5465 79.1371C47.5905 81.1466 47.4481 83.2796 47.0951 85.3376C45.7702 93.4583 41.5568 101.025 36.5151 107.585C34.0004 110.915 31.1514 113.983 28.1911 116.964C25.3179 119.833 22.147 122.739 18.5691 124.788C15.2768 126.702 11.4034 127.981 7.84654 126.904C6.17346 126.404 4.76177 125.569 3.6865 124.189C2.51197 122.821 1.98375 120.972 1.76545 119.185C1.45461 114.992 3.48742 111.016 6.10145 107.674C8.71547 104.332 11.9736 101.315 15.1688 98.6075C21.9199 92.8463 29.0537 87.7434 36.3232 82.9262C39.032 81.1849 41.7408 79.4435 44.4496 77.7021C44.3526 76.908 44.2555 76.1139 44.0592 75.332C42.4912 68.27 38.4273 62.2182 37.232 54.9092C36.684 51.2486 36.8066 47.3047 37.7476 43.4622C38.7878 39.6076 40.6584 35.9537 43.0982 32.8348C45.4508 29.8273 48.2611 27.2676 51.4541 25.3663C53.0445 24.366 54.6592 23.5643 56.2982 22.9611C58.1356 22.3336 60.0966 21.8925 61.8811 22.4805C65.6607 23.7313 64.841 28.5664 63.6167 31.7383C62.1165 35.9513 60.2944 40.0022 58.1502 43.891C57.2645 45.7118 54.7995 44.5018 55.6974 42.7802C56.6702 40.848 57.6309 38.8165 58.5045 36.8964C58.9788 35.831 59.3659 34.877 59.8402 33.8116C60.0774 33.2789 60.3145 32.7462 60.5517 32.2135C60.6896 31.693 60.7646 31.4823 60.9146 31.061C61.2146 30.2184 61.5146 29.3758 61.7033 28.4461C61.679 28.2475 61.754 28.0369 61.7298 27.8384L61.7176 27.7391ZM44.0544 86.0114C44.4559 84.3504 44.6468 82.6145 44.6391 80.9028C40.6256 83.5088 36.612 86.1147 32.6105 88.82C29.0811 91.2661 25.4767 93.9228 22.0709 96.5553C18.665 99.1878 15.3705 101.907 12.323 105C9.52486 107.659 6.76307 110.616 5.3887 114.209C5.25082 114.729 5.01367 115.262 4.97504 115.771C4.91217 116.08 4.83716 116.291 4.76215 116.502C4.77428 116.601 4.79854 116.8 4.7114 116.911L4.72353 117.01C4.68491 117.519 4.64629 118.027 4.6948 118.424C4.73118 118.722 4.75544 118.92 4.77969 119.119C4.77969 119.119 4.79182 119.218 4.80395 119.317C4.81607 119.417 4.8282 119.516 4.8282 119.516C4.88884 120.012 5.03661 120.397 5.18438 120.782C5.32003 121.068 5.34429 121.266 5.59132 121.639C5.71484 121.825 5.83836 122.012 5.96187 122.198C5.96187 122.198 6.07326 122.285 6.08539 122.385C6.3203 122.658 6.65447 122.92 6.87725 123.094C6.98864 123.181 7.10002 123.268 7.19929 123.256C7.21142 123.355 7.21142 123.355 7.21142 123.355C7.32281 123.442 7.53346 123.517 7.64485 123.605C7.96689 123.767 8.28893 123.929 8.59884 123.992C8.6981 123.98 8.71023 124.079 8.71023 124.079L8.80949 124.067C8.90876 124.055 9.02014 124.142 9.11941 124.13C9.33006 124.205 9.42932 124.192 9.63997 124.267C10.0491 124.318 10.3591 124.381 10.7561 124.333C10.7561 124.333 10.8554 124.32 10.9546 124.308C11.0539 124.296 11.1532 124.284 11.2645 124.371C11.4631 124.347 11.6616 124.323 11.8601 124.298C12.2572 124.25 12.6421 124.102 13.1384 124.042C13.1384 124.042 13.2377 124.029 13.3369 124.017C13.4362 124.005 13.4241 123.906 13.5233 123.894C13.7218 123.87 14.0196 123.833 14.206 123.71C14.5909 123.562 14.9759 123.414 15.46 123.254C18.8008 121.738 22.0953 119.018 24.8935 116.359C30.7512 110.707 36.127 104.409 39.8927 97.2994C41.8626 93.6335 43.2248 89.941 44.0544 86.0114Z" fill="#192329"/>
</g>
<defs>
<clipPath id="clip0_610_1801">
<rect width="376" height="128" fill="white" transform="translate(0.875)"/>
</clipPath>
</defs>
</svg>
            </div>
        </div>
    </div>
</div>


</div>

</section>


<section class="scuola-whatsapp mb-140">

<div class="container-xl mx-auto bg-brand-whatsapp-light rounded-24 px-80 py-80 flex justify-between gap-200 items-center relative">

<div class="main">
    <div class="pretitle uppercase font-tt-eb text-16 mb-28 spacing-12 text-brand-whatsapp">
    Scrivimi su WhatsApp
    </div>
    <div class="title font-din-bc text-60 mb-20">
    IN COSA POSSO AIUTARTI?
    </div>
    <div class="text font-ave-m leading-15 text-20 text-gray max-w-700">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sapien libero, ultrices sed elit id, rhoncus sagittis augue.
    </div>

</div>  

<div class="side">
<a href="https://wa.me/393517778431" class="button h-72 w-400 rounded-16 text-center border-brand-whatsapp border-4 border-solid rounded-12 flex gap-20 place-center hover-shadow-whatsapp hover-up-2">
        <div class="icon flex">
        <svg class="h-32" width="34" height="34" viewBox="0 0 34 34" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M28.9074 4.94096C25.7265 1.75639 21.4963 0.00184653 16.9894 0C7.70303 0 0.144918 7.55752 0.141294 16.8467C0.139994 19.8162 0.915807 22.7146 2.39022 25.2695L0 34L8.93139 31.6572C11.3922 32.9994 14.1629 33.7069 16.9827 33.7078H16.9895C16.989 33.7078 16.99 33.7078 16.9895 33.7078C26.2749 33.7078 33.8336 26.1496 33.8375 16.8602C33.8392 12.3584 32.0883 8.12543 28.9074 4.94096ZM16.9894 30.8625H16.9837C14.4711 30.8615 12.0065 30.1863 9.8565 28.9106L9.34521 28.6071L4.0452 29.9974L5.45991 24.8299L5.12691 24.3C3.72519 22.0705 2.98487 19.4936 2.98597 16.8477C2.98898 9.12687 9.27101 2.8455 16.9951 2.8455C20.7354 2.84667 24.2514 4.30521 26.8952 6.95213C29.539 9.59903 30.9942 13.1173 30.9928 16.8591C30.9896 24.5805 24.7078 30.8625 16.9894 30.8625Z" fill="#25D366"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M24.6717 20.3825C24.2508 20.1717 22.181 19.1535 21.7951 19.013C21.4093 18.8724 21.1286 18.8022 20.848 19.2237C20.5674 19.6449 19.7606 20.5932 19.515 20.8743C19.2695 21.1551 19.0238 21.1905 18.603 20.9795C18.182 20.7688 16.8256 20.3243 15.2177 18.8901C13.9661 17.7738 13.1213 16.3954 12.8756 15.9738C12.6301 15.5525 12.8495 15.3247 13.0603 15.1147C13.2497 14.9261 13.4813 14.623 13.6918 14.3772C13.9022 14.1315 13.9724 13.9557 14.1128 13.675C14.2531 13.3939 14.183 13.1481 14.0777 12.9375C13.9724 12.7268 13.1305 10.6546 12.7798 9.8116C12.4379 8.99086 12.0909 9.10206 11.8325 9.08899C11.5873 9.07675 11.3063 9.07422 11.0257 9.07422C10.745 9.07422 10.2889 9.17961 9.90315 9.60096C9.5173 10.0224 8.42969 11.041 8.42969 13.1128C8.42969 15.185 9.93817 17.1867 10.1487 17.4676C10.3591 17.7488 13.1171 22.0006 17.3401 23.8242C18.3445 24.2579 19.1286 24.5169 19.7399 24.7108C20.7485 25.0314 21.6662 24.9861 22.3915 24.8776C23.2004 24.7569 24.8821 23.8594 25.233 22.8761C25.5838 21.8927 25.5838 21.0497 25.4785 20.8742C25.3733 20.6986 25.0926 20.5932 24.6717 20.3825Z" fill="#25D366"/>
</svg>

        </div>
        <div class="text font-ave-b text-20">
        Scrivimi ora
        </div>
</a>

</div>


<div class="shapes z-50 absolute inset-0">

<div class="shape-1 animate-float-updown-light animation-duration-5000 absolute right-600 bottom--20">

<svg class="h-120 shadow-whatsapp" width="131" height="140" viewBox="0 0 131 140" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_612_1882)">
<path d="M125.93 76.9576L13.7591 2.41429C7.50541 -1.74165 -0.651177 3.73807 0.83219 11.0988L25.1442 131.74C26.2546 137.25 32.3449 140.149 37.3221 137.538L125.181 91.4404C130.851 88.4651 131.263 80.502 125.93 76.9576Z" fill="#25D366" stroke="#25D366" stroke-width="0.842821"/>
</g>
<defs>
<clipPath id="clip0_612_1882">
<rect width="131" height="140" fill="white"/>
</clipPath>
</defs>
</svg>



    </div>


</div>


</div>

</section>





<section class="home-gallery mb-140">

<div class="container max-w-2000 mx-auto">
    <div class="items grid grid-cols-3 gap-20">
        <div class="item ratio-3-4 relative">
            <?php load_img(class: 'img img-cover', high: 244, low: 243); ?>
        </div>
        <div class="item ratio-3-4 relative">
            <?php load_img(class: 'img img-cover', high: 246, low: 245); ?>
        </div>
        <div class="item ratio-3-4 relative">
            <?php load_img(class: 'img img-cover', high: 248, low: 247); ?>
        </div>
    </div>
</div>

</section>




<section class="scuola-produzioni mb-140">
    <div class="container-l mx-auto">

    <div class="intro flex justify-between gap-140 items-center mb-100 relative">
        <div class="main flex flex-col items-start max-w-800">
            <div class="pretitle mb-36 uppercase font-tt-eb text-16 spacing-12 text-gradient-pink-violet">
                Produzioni
            </div>
            <div class="title mb-20 font-din-bc text-60">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sapien libero
            </div>
            <div class="text font-ave-d text-20 leading-15 text-gray">
            Ho lavorato con Vacca, Jamil, Vegas Jones, Laïoung, Amill Leonardo. I miei studenti hanno lavorato con Baby Gang, Gucci MaCone, Tony Boy, NBA Never Broke Again.
            </div>
        </div>
        <div class="side">
            <div class="stats flex flex-col items-center text-center relative">
                <div class="number flex items-center gap-20">
                    <div id="count-up-stats" class="value font-din-bc text-128">
                    6.000.000
                    </div>
                    <div class="plus font-din-bc text-96">
                        +
                    </div>
                </div>
                <div class="text font-ave-d text-gray text-28 py-16 bg-white">
                Visualizzazioni e ascolti
                </div>
                <svg class="absolute-center z--10" width="715" height="397" viewBox="0 0 715 397" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M2.89473 386.266C0.280092 387.981 -0.449218 391.491 1.26577 394.105C2.98077 396.72 6.49063 397.449 9.10527 395.734L2.89473 386.266ZM124.19 313.477L121.084 308.743L121.084 308.743L124.19 313.477ZM152.689 308.868L154.144 303.396L154.144 303.396L152.689 308.868ZM254.298 335.881L255.752 330.409L255.752 330.409L254.298 335.881ZM287.596 327.493L283.723 323.363L283.723 323.363L287.596 327.493ZM423.682 199.864L427.555 203.993L427.555 203.993L423.682 199.864ZM460.045 192.442L461.99 187.125L461.99 187.125L460.045 192.442ZM513.841 212.12L511.896 217.437L511.896 217.437L513.841 212.12ZM554.131 200.35L558.632 203.784L558.632 203.784L554.131 200.35ZM708.008 5.24525C707.591 2.14626 704.741 -0.0280675 701.642 0.388767L651.141 7.18147C648.042 7.59831 645.868 10.4485 646.285 13.5475C646.702 16.6464 649.552 18.8208 652.651 18.4039L697.541 12.366L703.579 57.2558C703.995 60.3548 706.845 62.5292 709.944 62.1123C713.043 61.6955 715.218 58.8454 714.801 55.7464L708.008 5.24525ZM6 391L9.10527 395.734L127.295 318.211L124.19 313.477L121.084 308.743L2.89473 386.266L6 391ZM152.689 308.868L151.234 314.34L252.843 341.352L254.298 335.881L255.752 330.409L154.144 303.396L152.689 308.868ZM287.596 327.493L291.469 331.623L427.555 203.993L423.682 199.864L419.808 195.734L283.723 323.363L287.596 327.493ZM460.045 192.442L458.1 197.759L511.896 217.437L513.841 212.12L515.786 206.803L461.99 187.125L460.045 192.442ZM554.131 200.35L558.632 203.784L706.898 9.43406L702.397 6L697.896 2.56594L549.629 196.916L554.131 200.35ZM513.841 212.12L511.896 217.437C528.783 223.615 547.725 218.081 558.632 203.784L554.131 200.35L549.629 196.916C541.731 207.269 528.014 211.276 515.786 206.803L513.841 212.12ZM423.682 199.864L427.555 203.993C435.742 196.314 447.558 193.903 458.1 197.759L460.045 192.442L461.99 187.125C447.432 181.799 431.115 185.13 419.808 195.734L423.682 199.864ZM254.298 335.881L252.843 341.352C266.535 344.992 281.136 341.314 291.469 331.623L287.596 327.493L283.723 323.363C276.24 330.381 265.667 333.045 255.752 330.409L254.298 335.881ZM124.19 313.477L127.295 318.211C134.361 313.576 143.067 312.168 151.234 314.34L152.689 308.868L154.144 303.396C142.865 300.398 130.843 302.342 121.084 308.743L124.19 313.477Z" fill="url(#paint0_linear_612_1883)"/>
<defs>
<linearGradient id="paint0_linear_612_1883" x1="93.0496" y1="198.5" x2="612.946" y2="220.357" gradientUnits="userSpaceOnUse">
<stop stop-color="#E04E9D"/>
<stop offset="1" stop-color="#A973FF"/>
</linearGradient>
</defs>
</svg>

            </div>
        

</div>


<div class="shapes absolute inset-0 z-40">

<div class="shape-1 animate-float-updown-light animation-duration-5000 absolute right--120 bottom--10">

<svg class="h-88 shadow-violet" width="74" height="90" viewBox="0 0 74 90" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_612_1903)">
<path d="M61.7693 81.1441L72.8533 12.6398C74.277 3.8403 64.3462 -2.28396 57.1226 2.93878L5.68398 40.1292C0.277802 44.0379 0.142024 52.0436 5.41256 56.1334L45.7671 87.4474C51.7646 92.1012 60.5568 88.638 61.7693 81.1441Z" fill="#AB77FF"/>
</g>
<defs>
<clipPath id="clip0_612_1903">
<rect width="73" height="90" fill="white" transform="translate(0.773438)"/>
</clipPath>
</defs>
</svg>



</div>
</div>


    </div>
    <div class="content relative">
        <div class="portfolio grid grid-cols-3 gap-y-100 gap-x-60">

            <div class="item flex flex-col gap-40">
                <div class="visual relative h-300 rounded-24 overflow-hidden">
                <?php load_img(class: 'img img-cover', high: 140, low: 139); ?>
                <svg class="absolute-center z-30 h-52" width="69" height="52" viewBox="0 0 69 52" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M68.6146 11.4358C68.6146 10.8339 67.7121 6.01883 65.6055 3.91223C62.8969 0.90283 59.8875 0.601887 58.3826 0.601887H58.0818C48.7526 -1.78814e-07 34.9092 0 34.6083 0C34.6083 0 20.464 -1.78814e-07 11.1348 0.601887H10.8339C9.32921 0.601887 6.31978 0.90283 3.61132 3.91223C1.50471 6.31978 0.601883 11.1348 0.601883 11.7367C0.601883 12.0377 0 17.4546 0 23.1725V28.2886C0 34.0063 0.601883 39.4235 0.601883 39.7243C0.601883 40.3264 1.50471 45.1412 3.61132 47.2477C6.01883 49.9563 9.02827 50.2572 10.8339 50.5584C11.1348 50.5584 11.4358 50.5584 11.7367 50.5584C17.1537 51.16 33.7055 51.16 34.3074 51.16C34.3074 51.16 48.4518 51.16 57.7809 50.5584H58.0818C59.5867 50.2572 62.5961 49.9563 65.3043 47.2477C67.4109 44.8403 68.3138 40.0252 68.3138 39.4235C68.3138 39.1223 68.9158 33.7055 68.9158 27.9878V22.8716C69.2166 17.4546 68.6146 11.7367 68.6146 11.4358Z" fill="white"/>
<path d="M68.6146 11.4358C68.6146 10.8339 67.7121 6.01883 65.6055 3.91223C62.8969 0.90283 59.8875 0.601887 58.3826 0.601887H58.0818C48.7526 -1.78814e-07 34.9092 0 34.6083 0C34.6083 0 20.464 -1.78814e-07 11.1348 0.601887H10.8339C9.32921 0.601887 6.31978 0.90283 3.61132 3.91223C1.50471 6.31978 0.601883 11.1348 0.601883 11.7367C0.601883 12.0377 0 17.4546 0 23.1725V28.2886C0 34.0063 0.601883 39.4235 0.601883 39.7243C0.601883 40.3264 1.50471 45.1412 3.61132 47.2477C6.01883 49.9563 9.02827 50.2572 10.8339 50.5584C11.1348 50.5584 11.4358 50.5584 11.7367 50.5584C17.1537 51.16 33.7055 51.16 34.3074 51.16C34.3074 51.16 48.4518 51.16 57.7809 50.5584H58.0818C59.5867 50.2572 62.5961 49.9563 65.3043 47.2477C67.4109 44.8403 68.3138 40.0252 68.3138 39.4235C68.3138 39.1223 68.9158 33.7055 68.9158 27.9878V22.8716C69.2166 17.4546 68.6146 11.7367 68.6146 11.4358ZM46.3452 26.182L28.2885 35.812C27.9876 35.812 27.9876 36.1132 27.6867 36.1132C27.3857 36.1132 27.0848 36.1132 27.0848 35.812C26.7838 35.5112 26.4829 35.2103 26.4829 34.6083V15.0471C26.4829 14.4452 26.7838 14.1443 27.0848 13.8433C27.3857 13.5424 27.9876 13.5424 28.5895 13.8433L46.6461 23.4735C47.2477 23.7744 47.5489 24.0754 47.5489 24.6772C47.5489 25.2792 46.9469 25.8809 46.3452 26.182Z" fill="#FF0000"/>
</svg>

                </div>
                <div class="info flex flex-col gap-20">
                    <div class="title uppercase font-tt-eb text-16 spacing-12">
                    MBOSS FEAT AMILL LEONARDO & VEGAS
                    </div>
                    <div class="author font-ave-d text-20 text-gray">
                    Pare (prod. Syler)
                    </div>
                    <div class="stats flex items-center gap-12">
                        <div class="icon flex">
                        <svg class="h-14" width="19" height="13" viewBox="0 0 19 13" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 3.33333C0 1.49238 1.49238 0 3.33333 0H15C16.8409 0 18.3333 1.49238 18.3333 3.33333V9.16667C18.3333 11.0076 16.8409 12.5 15 12.5H3.33333C1.49238 12.5 0 11.0076 0 9.16667V3.33333ZM3.33333 1.66667C2.41286 1.66667 1.66667 2.41286 1.66667 3.33333V9.16667C1.66667 10.0872 2.41286 10.8333 3.33333 10.8333H15C15.9205 10.8333 16.6667 10.0872 16.6667 9.16667V3.33333C16.6667 2.41286 15.9205 1.66667 15 1.66667H3.33333Z" fill="#798B97"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.11646 2.59418C7.39207 2.45069 7.72464 2.47245 7.97922 2.65064L12.1459 5.56733C12.3686 5.72325 12.5013 5.97808 12.5013 6.25C12.5013 6.52192 12.3686 6.77675 12.1459 6.93267L7.97922 9.84933C7.72464 10.0276 7.39207 10.0493 7.11646 9.90583C6.84085 9.76233 6.66797 9.47742 6.66797 9.16667V3.33333C6.66797 3.02261 6.84085 2.73768 7.11646 2.59418ZM8.33464 4.93392V7.56608L10.2148 6.25L8.33464 4.93392Z" fill="#798B97"/>
</svg>

                        </div>
                        <div class="number font-ave-d text-16 text-gray">
                        1M
                        </div>
                        <div class="unit font-ave-d text-16 text-gray">
                        Visualizzazioni
                        </div>
                    </div>
                </div>
            </div> <!-- item -->


            <div class="item flex flex-col gap-40">
                <div class="visual relative h-300 rounded-24 overflow-hidden">
                <?php load_img(class: 'img img-cover', high: 142, low: 141); ?>
                <svg class="absolute-center z-30 h-52" width="69" height="52" viewBox="0 0 69 52" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M68.6146 11.4358C68.6146 10.8339 67.7121 6.01883 65.6055 3.91223C62.8969 0.90283 59.8875 0.601887 58.3826 0.601887H58.0818C48.7526 -1.78814e-07 34.9092 0 34.6083 0C34.6083 0 20.464 -1.78814e-07 11.1348 0.601887H10.8339C9.32921 0.601887 6.31978 0.90283 3.61132 3.91223C1.50471 6.31978 0.601883 11.1348 0.601883 11.7367C0.601883 12.0377 0 17.4546 0 23.1725V28.2886C0 34.0063 0.601883 39.4235 0.601883 39.7243C0.601883 40.3264 1.50471 45.1412 3.61132 47.2477C6.01883 49.9563 9.02827 50.2572 10.8339 50.5584C11.1348 50.5584 11.4358 50.5584 11.7367 50.5584C17.1537 51.16 33.7055 51.16 34.3074 51.16C34.3074 51.16 48.4518 51.16 57.7809 50.5584H58.0818C59.5867 50.2572 62.5961 49.9563 65.3043 47.2477C67.4109 44.8403 68.3138 40.0252 68.3138 39.4235C68.3138 39.1223 68.9158 33.7055 68.9158 27.9878V22.8716C69.2166 17.4546 68.6146 11.7367 68.6146 11.4358Z" fill="white"/>
<path d="M68.6146 11.4358C68.6146 10.8339 67.7121 6.01883 65.6055 3.91223C62.8969 0.90283 59.8875 0.601887 58.3826 0.601887H58.0818C48.7526 -1.78814e-07 34.9092 0 34.6083 0C34.6083 0 20.464 -1.78814e-07 11.1348 0.601887H10.8339C9.32921 0.601887 6.31978 0.90283 3.61132 3.91223C1.50471 6.31978 0.601883 11.1348 0.601883 11.7367C0.601883 12.0377 0 17.4546 0 23.1725V28.2886C0 34.0063 0.601883 39.4235 0.601883 39.7243C0.601883 40.3264 1.50471 45.1412 3.61132 47.2477C6.01883 49.9563 9.02827 50.2572 10.8339 50.5584C11.1348 50.5584 11.4358 50.5584 11.7367 50.5584C17.1537 51.16 33.7055 51.16 34.3074 51.16C34.3074 51.16 48.4518 51.16 57.7809 50.5584H58.0818C59.5867 50.2572 62.5961 49.9563 65.3043 47.2477C67.4109 44.8403 68.3138 40.0252 68.3138 39.4235C68.3138 39.1223 68.9158 33.7055 68.9158 27.9878V22.8716C69.2166 17.4546 68.6146 11.7367 68.6146 11.4358ZM46.3452 26.182L28.2885 35.812C27.9876 35.812 27.9876 36.1132 27.6867 36.1132C27.3857 36.1132 27.0848 36.1132 27.0848 35.812C26.7838 35.5112 26.4829 35.2103 26.4829 34.6083V15.0471C26.4829 14.4452 26.7838 14.1443 27.0848 13.8433C27.3857 13.5424 27.9876 13.5424 28.5895 13.8433L46.6461 23.4735C47.2477 23.7744 47.5489 24.0754 47.5489 24.6772C47.5489 25.2792 46.9469 25.8809 46.3452 26.182Z" fill="#FF0000"/>
</svg>

                </div>
                <div class="info flex flex-col gap-20">
                    <div class="title uppercase font-tt-eb text-16 spacing-12">
                    VACCA feat mboss
                    </div>
                    <div class="author font-ave-d text-20 text-gray">
                    Asciugamano in testa (prod. Syler)
                    </div>
                    <div class="stats flex items-center gap-12">
                        <div class="icon flex">
                        <svg class="h-14" width="19" height="13" viewBox="0 0 19 13" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 3.33333C0 1.49238 1.49238 0 3.33333 0H15C16.8409 0 18.3333 1.49238 18.3333 3.33333V9.16667C18.3333 11.0076 16.8409 12.5 15 12.5H3.33333C1.49238 12.5 0 11.0076 0 9.16667V3.33333ZM3.33333 1.66667C2.41286 1.66667 1.66667 2.41286 1.66667 3.33333V9.16667C1.66667 10.0872 2.41286 10.8333 3.33333 10.8333H15C15.9205 10.8333 16.6667 10.0872 16.6667 9.16667V3.33333C16.6667 2.41286 15.9205 1.66667 15 1.66667H3.33333Z" fill="#798B97"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.11646 2.59418C7.39207 2.45069 7.72464 2.47245 7.97922 2.65064L12.1459 5.56733C12.3686 5.72325 12.5013 5.97808 12.5013 6.25C12.5013 6.52192 12.3686 6.77675 12.1459 6.93267L7.97922 9.84933C7.72464 10.0276 7.39207 10.0493 7.11646 9.90583C6.84085 9.76233 6.66797 9.47742 6.66797 9.16667V3.33333C6.66797 3.02261 6.84085 2.73768 7.11646 2.59418ZM8.33464 4.93392V7.56608L10.2148 6.25L8.33464 4.93392Z" fill="#798B97"/>
</svg>

                        </div>
                        <div class="number font-ave-d text-16 text-gray">
                        1M
                        </div>
                        <div class="unit font-ave-d text-16 text-gray">
                        Visualizzazioni
                        </div>
                    </div>
                </div>
            </div> <!-- item -->



            <div class="item flex flex-col gap-40">
                <div class="visual relative h-300 rounded-24 overflow-hidden">
                <?php load_img(class: 'img img-cover', high: 144, low: 143); ?>
                <svg class="absolute-center z-30 h-52" width="69" height="52" viewBox="0 0 69 52" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M68.6146 11.4358C68.6146 10.8339 67.7121 6.01883 65.6055 3.91223C62.8969 0.90283 59.8875 0.601887 58.3826 0.601887H58.0818C48.7526 -1.78814e-07 34.9092 0 34.6083 0C34.6083 0 20.464 -1.78814e-07 11.1348 0.601887H10.8339C9.32921 0.601887 6.31978 0.90283 3.61132 3.91223C1.50471 6.31978 0.601883 11.1348 0.601883 11.7367C0.601883 12.0377 0 17.4546 0 23.1725V28.2886C0 34.0063 0.601883 39.4235 0.601883 39.7243C0.601883 40.3264 1.50471 45.1412 3.61132 47.2477C6.01883 49.9563 9.02827 50.2572 10.8339 50.5584C11.1348 50.5584 11.4358 50.5584 11.7367 50.5584C17.1537 51.16 33.7055 51.16 34.3074 51.16C34.3074 51.16 48.4518 51.16 57.7809 50.5584H58.0818C59.5867 50.2572 62.5961 49.9563 65.3043 47.2477C67.4109 44.8403 68.3138 40.0252 68.3138 39.4235C68.3138 39.1223 68.9158 33.7055 68.9158 27.9878V22.8716C69.2166 17.4546 68.6146 11.7367 68.6146 11.4358Z" fill="white"/>
<path d="M68.6146 11.4358C68.6146 10.8339 67.7121 6.01883 65.6055 3.91223C62.8969 0.90283 59.8875 0.601887 58.3826 0.601887H58.0818C48.7526 -1.78814e-07 34.9092 0 34.6083 0C34.6083 0 20.464 -1.78814e-07 11.1348 0.601887H10.8339C9.32921 0.601887 6.31978 0.90283 3.61132 3.91223C1.50471 6.31978 0.601883 11.1348 0.601883 11.7367C0.601883 12.0377 0 17.4546 0 23.1725V28.2886C0 34.0063 0.601883 39.4235 0.601883 39.7243C0.601883 40.3264 1.50471 45.1412 3.61132 47.2477C6.01883 49.9563 9.02827 50.2572 10.8339 50.5584C11.1348 50.5584 11.4358 50.5584 11.7367 50.5584C17.1537 51.16 33.7055 51.16 34.3074 51.16C34.3074 51.16 48.4518 51.16 57.7809 50.5584H58.0818C59.5867 50.2572 62.5961 49.9563 65.3043 47.2477C67.4109 44.8403 68.3138 40.0252 68.3138 39.4235C68.3138 39.1223 68.9158 33.7055 68.9158 27.9878V22.8716C69.2166 17.4546 68.6146 11.7367 68.6146 11.4358ZM46.3452 26.182L28.2885 35.812C27.9876 35.812 27.9876 36.1132 27.6867 36.1132C27.3857 36.1132 27.0848 36.1132 27.0848 35.812C26.7838 35.5112 26.4829 35.2103 26.4829 34.6083V15.0471C26.4829 14.4452 26.7838 14.1443 27.0848 13.8433C27.3857 13.5424 27.9876 13.5424 28.5895 13.8433L46.6461 23.4735C47.2477 23.7744 47.5489 24.0754 47.5489 24.6772C47.5489 25.2792 46.9469 25.8809 46.3452 26.182Z" fill="#FF0000"/>
</svg>

                </div>
                <div class="info flex flex-col gap-20">
                    <div class="title uppercase font-tt-eb text-16 spacing-12">
                    YANK
                    </div>
                    <div class="author font-ave-d text-20 text-gray">
                    Esse Magazine Freestyle #1 (prod. Syler)
                    </div>
                    <div class="stats flex items-center gap-12">
                        <div class="icon flex">
                        <svg class="h-14" width="19" height="13" viewBox="0 0 19 13" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 3.33333C0 1.49238 1.49238 0 3.33333 0H15C16.8409 0 18.3333 1.49238 18.3333 3.33333V9.16667C18.3333 11.0076 16.8409 12.5 15 12.5H3.33333C1.49238 12.5 0 11.0076 0 9.16667V3.33333ZM3.33333 1.66667C2.41286 1.66667 1.66667 2.41286 1.66667 3.33333V9.16667C1.66667 10.0872 2.41286 10.8333 3.33333 10.8333H15C15.9205 10.8333 16.6667 10.0872 16.6667 9.16667V3.33333C16.6667 2.41286 15.9205 1.66667 15 1.66667H3.33333Z" fill="#798B97"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.11646 2.59418C7.39207 2.45069 7.72464 2.47245 7.97922 2.65064L12.1459 5.56733C12.3686 5.72325 12.5013 5.97808 12.5013 6.25C12.5013 6.52192 12.3686 6.77675 12.1459 6.93267L7.97922 9.84933C7.72464 10.0276 7.39207 10.0493 7.11646 9.90583C6.84085 9.76233 6.66797 9.47742 6.66797 9.16667V3.33333C6.66797 3.02261 6.84085 2.73768 7.11646 2.59418ZM8.33464 4.93392V7.56608L10.2148 6.25L8.33464 4.93392Z" fill="#798B97"/>
</svg>

                        </div>
                        <div class="number font-ave-d text-16 text-gray">
                        500k
                        </div>
                        <div class="unit font-ave-d text-16 text-gray">
                        Visualizzazioni
                        </div>
                    </div>
                </div>
            </div> <!-- item -->



            <div class="item flex flex-col gap-40">
                <div class="visual relative h-300 rounded-24 overflow-hidden">
                <?php load_img(class: 'img img-cover', high: 146, low: 145); ?>
                <svg class="absolute-center z-30 h-52" width="69" height="52" viewBox="0 0 69 52" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M68.6146 11.4358C68.6146 10.8339 67.7121 6.01883 65.6055 3.91223C62.8969 0.90283 59.8875 0.601887 58.3826 0.601887H58.0818C48.7526 -1.78814e-07 34.9092 0 34.6083 0C34.6083 0 20.464 -1.78814e-07 11.1348 0.601887H10.8339C9.32921 0.601887 6.31978 0.90283 3.61132 3.91223C1.50471 6.31978 0.601883 11.1348 0.601883 11.7367C0.601883 12.0377 0 17.4546 0 23.1725V28.2886C0 34.0063 0.601883 39.4235 0.601883 39.7243C0.601883 40.3264 1.50471 45.1412 3.61132 47.2477C6.01883 49.9563 9.02827 50.2572 10.8339 50.5584C11.1348 50.5584 11.4358 50.5584 11.7367 50.5584C17.1537 51.16 33.7055 51.16 34.3074 51.16C34.3074 51.16 48.4518 51.16 57.7809 50.5584H58.0818C59.5867 50.2572 62.5961 49.9563 65.3043 47.2477C67.4109 44.8403 68.3138 40.0252 68.3138 39.4235C68.3138 39.1223 68.9158 33.7055 68.9158 27.9878V22.8716C69.2166 17.4546 68.6146 11.7367 68.6146 11.4358Z" fill="white"/>
<path d="M68.6146 11.4358C68.6146 10.8339 67.7121 6.01883 65.6055 3.91223C62.8969 0.90283 59.8875 0.601887 58.3826 0.601887H58.0818C48.7526 -1.78814e-07 34.9092 0 34.6083 0C34.6083 0 20.464 -1.78814e-07 11.1348 0.601887H10.8339C9.32921 0.601887 6.31978 0.90283 3.61132 3.91223C1.50471 6.31978 0.601883 11.1348 0.601883 11.7367C0.601883 12.0377 0 17.4546 0 23.1725V28.2886C0 34.0063 0.601883 39.4235 0.601883 39.7243C0.601883 40.3264 1.50471 45.1412 3.61132 47.2477C6.01883 49.9563 9.02827 50.2572 10.8339 50.5584C11.1348 50.5584 11.4358 50.5584 11.7367 50.5584C17.1537 51.16 33.7055 51.16 34.3074 51.16C34.3074 51.16 48.4518 51.16 57.7809 50.5584H58.0818C59.5867 50.2572 62.5961 49.9563 65.3043 47.2477C67.4109 44.8403 68.3138 40.0252 68.3138 39.4235C68.3138 39.1223 68.9158 33.7055 68.9158 27.9878V22.8716C69.2166 17.4546 68.6146 11.7367 68.6146 11.4358ZM46.3452 26.182L28.2885 35.812C27.9876 35.812 27.9876 36.1132 27.6867 36.1132C27.3857 36.1132 27.0848 36.1132 27.0848 35.812C26.7838 35.5112 26.4829 35.2103 26.4829 34.6083V15.0471C26.4829 14.4452 26.7838 14.1443 27.0848 13.8433C27.3857 13.5424 27.9876 13.5424 28.5895 13.8433L46.6461 23.4735C47.2477 23.7744 47.5489 24.0754 47.5489 24.6772C47.5489 25.2792 46.9469 25.8809 46.3452 26.182Z" fill="#FF0000"/>
</svg>

                </div>
                <div class="info flex flex-col gap-20">
                    <div class="title uppercase font-tt-eb text-16 spacing-12">
                    VACCA feat mboss
                    </div>
                    <div class="author font-ave-d text-20 text-gray">
                    Asciugamano in testa (prod. Syler)
                    </div>
                    <div class="stats flex items-center gap-12">
                        <div class="icon flex">
                        <svg class="h-14" width="19" height="13" viewBox="0 0 19 13" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M0 3.33333C0 1.49238 1.49238 0 3.33333 0H15C16.8409 0 18.3333 1.49238 18.3333 3.33333V9.16667C18.3333 11.0076 16.8409 12.5 15 12.5H3.33333C1.49238 12.5 0 11.0076 0 9.16667V3.33333ZM3.33333 1.66667C2.41286 1.66667 1.66667 2.41286 1.66667 3.33333V9.16667C1.66667 10.0872 2.41286 10.8333 3.33333 10.8333H15C15.9205 10.8333 16.6667 10.0872 16.6667 9.16667V3.33333C16.6667 2.41286 15.9205 1.66667 15 1.66667H3.33333Z" fill="#798B97"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M7.11646 2.59418C7.39207 2.45069 7.72464 2.47245 7.97922 2.65064L12.1459 5.56733C12.3686 5.72325 12.5013 5.97808 12.5013 6.25C12.5013 6.52192 12.3686 6.77675 12.1459 6.93267L7.97922 9.84933C7.72464 10.0276 7.39207 10.0493 7.11646 9.90583C6.84085 9.76233 6.66797 9.47742 6.66797 9.16667V3.33333C6.66797 3.02261 6.84085 2.73768 7.11646 2.59418ZM8.33464 4.93392V7.56608L10.2148 6.25L8.33464 4.93392Z" fill="#798B97"/>
</svg>

                        </div>
                        <div class="number font-ave-d text-16 text-gray">
                        1M
                        </div>
                        <div class="unit font-ave-d text-16 text-gray">
                        Visualizzazioni
                        </div>
                    </div>
                </div>
            </div> <!-- item -->



            <div class="item flex flex-col gap-40">
                <div class="visual relative h-300 rounded-24 overflow-hidden">
                <?php load_img(class: 'img img-cover', high: 148, low: 147); ?>
             
                <svg class="absolute-center z-30 h-60" width="63" height="63" viewBox="0 0 63 63" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="31.5" cy="31.5" r="31.5" fill="white"/>
<path d="M31.5 0C14.0766 0 0 14.113 0 31.5C0 48.9234 14.113 63 31.5 63C48.9234 63 63 48.887 63 31.5C63 14.113 48.887 0 31.5 0ZM45.9506 45.4624C45.3866 46.4034 44.1817 46.6663 43.2406 46.1022C35.8263 41.5859 26.5309 40.57 15.5403 43.0536C14.487 43.28 13.4338 42.6402 13.2074 41.5869C12.981 40.5336 13.6208 39.4803 14.6741 39.2539C26.6795 36.5075 36.9899 37.673 45.2704 42.7544C45.4935 42.8872 45.688 43.0629 45.8429 43.2713C45.9978 43.4796 46.11 43.7166 46.1729 43.9684C46.2358 44.2203 46.2484 44.4822 46.2097 44.7389C46.171 44.9956 46.082 45.2422 45.9477 45.4643L45.9506 45.4624ZM49.7897 36.8816C49.075 38.0481 47.5689 38.3877 46.4034 37.7085C37.9358 32.5149 25.0268 31.0098 15.0166 34.02C13.6995 34.396 12.345 33.6814 11.968 32.4017C11.592 31.0846 12.3067 29.7301 13.6237 29.3531C25.0642 25.891 39.2903 27.5467 49.0376 33.5308C50.1667 34.208 50.5043 35.7131 49.7907 36.8806L49.7897 36.8816ZM50.1283 27.9238C39.9666 21.9023 23.2194 21.3383 13.5096 24.2737C11.9661 24.7629 10.3103 23.8603 9.82111 22.3168C9.33187 20.7733 10.2345 19.1175 11.778 18.6283C22.9182 15.2421 41.4343 15.9183 53.1021 22.8434C54.495 23.6703 54.9458 25.4776 54.118 26.8695C53.3649 28.2998 51.5212 28.7516 50.1283 27.9228V27.9238Z" fill="#1ED760"/>
</svg>



                </div>
                <div class="info flex flex-col gap-20">
                    <div class="title uppercase font-tt-eb text-16 spacing-12">
                    Jamil, Laïoung
                    </div>
                    <div class="author font-ave-d text-20 text-gray">
                    Animali
                    </div>
                    <div class="stats flex items-center gap-12">
                        <div class="icon flex">
                        <svg class="h-16" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="8" cy="8" r="7.15" stroke="#798B97" stroke-width="1.7"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.44849 4.09418C6.7241 3.95069 7.05667 3.97245 7.31125 4.15064L11.4779 7.06733C11.7006 7.22325 11.8333 7.47808 11.8333 7.75C11.8333 8.02192 11.7006 8.27675 11.4779 8.43267L7.31125 11.3493C7.05667 11.5276 6.7241 11.5493 6.44849 11.4058C6.17288 11.2623 6 10.9774 6 10.6667V4.83333C6 4.52261 6.17288 4.23768 6.44849 4.09418ZM7.66667 6.43392V9.06608L9.54683 7.75L7.66667 6.43392Z" fill="#798B97"/>
</svg>

                        </div>
                        <div class="number font-ave-d text-16 text-gray">
                        1.7M
                        </div>
                        <div class="unit font-ave-d text-16 text-gray">
                        Riproduzioni
                        </div>
                    </div>
                </div>
            </div> <!-- item -->



            <div class="item flex flex-col gap-40">
                <div class="visual relative h-300 rounded-24 overflow-hidden">
                <?php load_img(class: 'img img-cover', high: 150, low: 149); ?>

                <svg class="absolute-center z-30 h-60" width="63" height="63" viewBox="0 0 63 63" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="31.5" cy="31.5" r="31.5" fill="white"/>
<path d="M31.5 0C14.0766 0 0 14.113 0 31.5C0 48.9234 14.113 63 31.5 63C48.9234 63 63 48.887 63 31.5C63 14.113 48.887 0 31.5 0ZM45.9506 45.4624C45.3866 46.4034 44.1817 46.6663 43.2406 46.1022C35.8263 41.5859 26.5309 40.57 15.5403 43.0536C14.487 43.28 13.4338 42.6402 13.2074 41.5869C12.981 40.5336 13.6208 39.4803 14.6741 39.2539C26.6795 36.5075 36.9899 37.673 45.2704 42.7544C45.4935 42.8872 45.688 43.0629 45.8429 43.2713C45.9978 43.4796 46.11 43.7166 46.1729 43.9684C46.2358 44.2203 46.2484 44.4822 46.2097 44.7389C46.171 44.9956 46.082 45.2422 45.9477 45.4643L45.9506 45.4624ZM49.7897 36.8816C49.075 38.0481 47.5689 38.3877 46.4034 37.7085C37.9358 32.5149 25.0268 31.0098 15.0166 34.02C13.6995 34.396 12.345 33.6814 11.968 32.4017C11.592 31.0846 12.3067 29.7301 13.6237 29.3531C25.0642 25.891 39.2903 27.5467 49.0376 33.5308C50.1667 34.208 50.5043 35.7131 49.7907 36.8806L49.7897 36.8816ZM50.1283 27.9238C39.9666 21.9023 23.2194 21.3383 13.5096 24.2737C11.9661 24.7629 10.3103 23.8603 9.82111 22.3168C9.33187 20.7733 10.2345 19.1175 11.778 18.6283C22.9182 15.2421 41.4343 15.9183 53.1021 22.8434C54.495 23.6703 54.9458 25.4776 54.118 26.8695C53.3649 28.2998 51.5212 28.7516 50.1283 27.9228V27.9238Z" fill="#1ED760"/>
</svg>



                </div>
                <div class="info flex flex-col gap-20">
                    <div class="title uppercase font-tt-eb text-16 spacing-12">
                    Jamil, Laïoung
                    </div>
                    <div class="author font-ave-d text-20 text-gray">
                    Letale
                    </div>
                    <div class="stats flex items-center gap-12">
                        <div class="icon flex">
                        <svg class="h-16" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
<circle cx="8" cy="8" r="7.15" stroke="#798B97" stroke-width="1.7"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.44849 4.09418C6.7241 3.95069 7.05667 3.97245 7.31125 4.15064L11.4779 7.06733C11.7006 7.22325 11.8333 7.47808 11.8333 7.75C11.8333 8.02192 11.7006 8.27675 11.4779 8.43267L7.31125 11.3493C7.05667 11.5276 6.7241 11.5493 6.44849 11.4058C6.17288 11.2623 6 10.9774 6 10.6667V4.83333C6 4.52261 6.17288 4.23768 6.44849 4.09418ZM7.66667 6.43392V9.06608L9.54683 7.75L7.66667 6.43392Z" fill="#798B97"/>
</svg>


                        </div>
                        <div class="number font-ave-d text-16 text-gray">
                        1.7M
                        </div>
                        <div class="unit font-ave-d text-16 text-gray">
                        Riproduzioni
                        </div>
                    </div>
                </div>
            </div> <!-- item --> 



        </div>


        <div class="shapes absolute inset-0 z-50">
        <div class="shape-2 animate-float-updown-light animation-duration-5000 absolute left--80 top-140">
        <svg class="h-140 shadow-pink" width="131" height="139" viewBox="0 0 131 139" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_612_1905)">
<path d="M126.867 76.7037L14.6966 2.16038C8.44291 -1.99556 0.286323 3.48416 1.76969 10.8449L26.0817 131.486C27.1921 136.996 33.2824 139.895 38.2596 137.284L126.118 91.1865C131.789 88.2112 132.201 80.2481 126.867 76.7037Z" fill="#E04E9D"/>
</g>
<defs>
<clipPath id="clip0_612_1905">
<rect width="130" height="139" fill="white" transform="translate(0.9375)"/>
</clipPath>
</defs>
</svg>

</div>

</div>

        <div class="gradients absolute inset-0 z--10">
        <div class="radial-gradient-pink absolute z-40 h-600 w-600 left--400 top--200 opacity-20"></div>

<div class="radial-gradient-violet absolute z-40 h-540 w-540 right--200 top--100 opacity-40"></div>
        </div>


    </div>



</div>


</section>




<section class="scuola-mission mb-120">
    <div class="container-xl mx-auto bg-brand-dark rounded-24 pb-120 relative">
        <div class="intro flex flex-col items-center text-center relative z-50 pt-120 pb-120">
            <div class="pretitle uppercase font-tt-eb text-16 mb-28 spacing-12 text-brand-violet">
            La nostra missione
            </div>
            <div class="title font-din-bc text-60 mb-20 max-w-1100 text-white">
            Produzione Hip Hop aiuta appassionati di musica a registrare, produrre e masterizzare brani professionali in autonomia
            </div>
            <div class="text font-ave-m leading-15 text-20 text-white opacity-80 max-w-900">
            Attraverso corsi specializzati e un affiancamento dedicato, ti supporterò nel raggiungimento dei tuoi obiettivi musicali con strategie mirate e un metodo collaudato.
            </div>
            <div class="background absolute inset-0 overflow-hidden z-10">

<div class="inner absolute-center-x top-0 bottom-0 z-10">
    <div class="items absolute right-500 top-0 bottom-0">
        <div class="item h-60 w-60 rounded-full overflow-hidden absolute right-100 top-48">
        <?php load_img(class: 'img img-cover', high: 229); ?>
        </div>

        <div class="item h-60 w-60 rounded-full overflow-hidden absolute right-10 bottom-80">
        <?php load_img(class: 'img img-cover', high: 230); ?>
        </div>

        <div class="item h-60 w-60 rounded-full overflow-hidden absolute right-140 top-300">
        <?php load_img(class: 'img img-cover', high: 231 ); ?>
        </div>

        <div class="item h-60 w-60 rounded-full overflow-hidden absolute right-300 top-200">
        <?php load_img(class: 'img img-cover', high: 232); ?>
        </div>


        <div class="item h-60 w-60 rounded-full overflow-hidden absolute right-300 top--20">
        <?php load_img(class: 'img img-cover', high: 233); ?>
        </div>
    </div>

    <div class="items absolute left-500 top-0 bottom-0">
        <div class="item h-60 w-60 rounded-full overflow-hidden absolute left-100 top-48">
        <?php load_img(class: 'img img-cover', high: 234); ?>
        </div>

        <div class="item h-60 w-60 rounded-full overflow-hidden absolute left-10 bottom-80">
        <?php load_img(class: 'img img-cover', high: 235); ?>
        </div>

        <div class="item h-60 w-60 rounded-full overflow-hidden absolute left-140 top-300">
        <?php load_img(class: 'img img-cover', high: 236); ?>
        </div>

        <div class="item h-60 w-60 rounded-full overflow-hidden absolute left-300 top-200">
        <?php load_img(class: 'img img-cover', high: 230); ?>
        </div>


        <div class="item h-60 w-60 rounded-full overflow-hidden absolute left-300 top--20">
        <?php load_img(class: 'img img-cover', high: 229); ?>
        </div>
    </div>
</div>
</div> <!-- background -->

        </div>

        <div class="content overflow-hidden mb-120 relative z-50">
            <div class="reviews flex gap-20">

                <div class="swiper reviews-swiper">
                    <div class="swiper-wrapper">
                        <div class="swiper-slide">
                            <div class="item ratio-9-16 w-400 relative overflow-hidden rounded-24">
                            <?php load_img(class: 'img img-cover', high: 152, low: 151); ?>
                            </div>
                        </div>

                        <div class="swiper-slide">
                            <div class="item ratio-9-16 w-400 relative overflow-hidden rounded-24">
                            <?php load_img(class: 'img img-cover', high: 154, low: 153); ?>
                            </div>
                        </div>

                        <div class="swiper-slide">
                            <div class="item ratio-9-16 w-400 relative overflow-hidden rounded-24">
                            <?php load_img(class: 'img img-cover', high: 156, low: 155); ?>
                            </div>
                        </div>

                        <div class="swiper-slide">
                            <div class="item ratio-9-16 w-400 relative overflow-hidden rounded-24">
                            <?php load_img(class: 'img img-cover', high: 158, low: 157); ?>
                            </div>
                        </div>

                        <div class="swiper-slide">
                            <div class="item ratio-9-16 w-400 relative overflow-hidden rounded-24">
                            <?php load_img(class: 'img img-cover', high: 160, low: 159); ?>
                            </div>
                        </div>

                        <div class="swiper-slide">
                            <div class="item ratio-9-16 w-400 relative overflow-hidden rounded-24">
                            <?php load_img(class: 'img img-cover', high: 162, low: 161); ?>
                            </div>
                        </div>

                        <div class="swiper-slide">
                            <div class="item ratio-9-16 w-400 relative overflow-hidden rounded-24">
                            <?php load_img(class: 'img img-cover', high: 164, low: 163); ?>
                            </div>
                        </div>

                        <div class="swiper-slide">
                            <div class="item ratio-9-16 w-400 relative overflow-hidden rounded-24">
                            <?php load_img(class: 'img img-cover', high: 166, low: 165); ?>
                            </div>
                        </div>

                        <div class="swiper-slide">
                            <div class="item ratio-9-16 w-400 relative overflow-hidden rounded-24">
                            <?php load_img(class: 'img img-cover', high: 168, low: 167); ?>
                            </div>
                        </div>

                        <div class="swiper-slide">
                            <div class="item ratio-9-16 w-400 relative overflow-hidden rounded-24">
                            <?php load_img(class: 'img img-cover', high: 170, low: 169); ?>
                            </div>
                        </div>
                    </div>
                </div>


            </div>

        </div> <!-- content -->


        <div class="cta flex gap-40 items-center justify-center relative z-50">
        <a href="" class="button h-72 w-400 rounded-16 text-center border-gradient-pink-violet-dark border-4 border-solid border-transparent rounded-12 flex gap-20 place-center hover-shadow-pink hover-up-2 text-white">
        <div class="text font-ave-b text-20">
            Vai alla pagina dei corsi
            </div>
            <div class="icon flex">
            <svg class="h-20" width="11" height="20" viewBox="0 0 11 20" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M10.5824 10.5826L2.43421 18.7308C1.87735 19.2877 0.974503 19.2877 0.417644 18.7308C-0.139215 18.1739 -0.139215 17.2711 0.417644 16.7142L7.55751 9.57436L0.417643 2.4345C-0.139215 1.87764 -0.139215 0.974792 0.417643 0.417934C0.974502 -0.138924 1.87735 -0.138924 2.43421 0.417934L10.5824 8.56608C11.1392 9.12294 11.1392 10.0258 10.5824 10.5826Z" fill="#192329"/>
</svg>



        </div>
</a>

<div class="trust flex gap-20 items-center">
        <div class="photo flex items-center">
            <div class="item mr--6 h-36 w-36 overflow-hidden rounded-full relative">
            <?php load_img(class: 'img img-cover', high: 229); ?>
            </div>
            <div class="item mr--10 h-36 w-36 overflow-hidden rounded-full relative">
            <?php load_img(class: 'img img-cover', high: 230); ?>
            </div>
            <div class="item h-36 w-36 overflow-hidden rounded-full relative">
            <?php load_img(class: 'img img-cover', high: 231); ?>
            </div>

        </div>
        <div class="stars">
        <svg class="h-20" width="95" height="19" viewBox="0 0 95 19" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M9.4391 0L11.5583 6.52226L18.4162 6.52226L12.8681 10.5532L14.9873 17.0755L9.4391 13.0445L3.89094 17.0755L6.01015 10.5532L0.461983 6.52226L7.31989 6.52226L9.4391 0Z" fill="#FFBB44"/>
<path d="M28.318 0L30.4372 6.52226L37.2951 6.52226L31.747 10.5532L33.8662 17.0755L28.318 13.0445L22.7698 17.0755L24.8891 10.5532L19.3409 6.52226L26.1988 6.52226L28.318 0Z" fill="#FFBB44"/>
<path d="M47.193 0L49.3122 6.52226L56.1701 6.52226L50.622 10.5532L52.7412 17.0755L47.193 13.0445L41.6448 17.0755L43.7641 10.5532L38.2159 6.52226L45.0738 6.52226L47.193 0Z" fill="#FFBB44"/>
<path d="M66.0719 0L68.1911 6.52226L75.049 6.52226L69.5009 10.5532L71.6201 17.0755L66.0719 13.0445L60.5237 17.0755L62.643 10.5532L57.0948 6.52226L63.9527 6.52226L66.0719 0Z" fill="#FFBB44"/>
<path d="M85.5602 0L87.6794 6.52226L94.5373 6.52226L88.9892 10.5532L91.1084 17.0755L85.5602 13.0445L80.012 17.0755L82.1312 10.5532L76.5831 6.52226L83.441 6.52226L85.5602 0Z" fill="#FFBB44"/>
</svg>

        </div>
        <div class="text font-ave-d text-18 text-white opacity-80">
        500+ Studenti
        </div>
    </div>


        </div>





        <div class="gradients absolute inset-0 overflow-hidden rounded-24 z-10">

<div class="radial-gradient-blue animate-float-around animate-opacity-70-100 animation-duration-6000 absolute z-40 h-540 w-540 left--240 top--240"></div>

<div class="radial-gradient-violet animate-float-around animation-duration-6000 absolute z-40 h-800 w-800 right--440 bottom--440 opacity-50"></div>
        </div>

        <div class="shapes absolute inset-0 z-20">
        <div class="shape animate-float-updown-light animation-duration-5000 absolute left--40 top-400">
        <svg class="h-84 shadow-blue" width="73" height="85" viewBox="0 0 73 85" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_589_667)">
<path d="M67.0395 74.8437L71.7773 11.0321C72.3886 2.79846 63.323 -2.58705 56.3847 1.88789L5.52556 34.6898C-0.511803 38.5836 -0.595878 47.3837 5.36599 51.3922L51.4874 82.402C57.8544 86.6828 66.4715 82.495 67.0395 74.8437Z" fill="#1289ED"/>
</g>
<defs>
<clipPath id="clip0_589_667">
<rect width="72" height="85" fill="white" transform="translate(0.941406)"/>
</clipPath>
</defs>
</svg>




    </div>

    <div class="shape animate-float-updown-light animation-duration-5000 absolute right--40 bottom-200">
        <svg class="h-84 shadow-violet" width="73" height="85" viewBox="0 0 73 85" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_589_667)">
<path d="M67.0395 74.8437L71.7773 11.0321C72.3886 2.79846 63.323 -2.58705 56.3847 1.88789L5.52556 34.6898C-0.511803 38.5836 -0.595878 47.3837 5.36599 51.3922L51.4874 82.402C57.8544 86.6828 66.4715 82.495 67.0395 74.8437Z" fill="#A973FF"/>
</g>
<defs>
<clipPath id="clip0_589_667">
<rect width="72" height="85" fill="white" transform="translate(0.941406)"/>
</clipPath>
</defs>
</svg>




    </div>
        </div>

    </div>
</section>




<section class="scuola-benefits">
    <div class="container-l mx-auto relative">

        <div class="intro flex flex-col items-center text-center">
            <div class="logo mb-60">
            <svg class="h-160" width="70" height="168" viewBox="0 0 70 168" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M0 54.2018L70 0V73.8864L38.0315 98.6345V138.556L28.937 145.682V105.687L23.563 109.873V149.84L14.4685 156.855V116.859L9.09449 121.018V161.013L0 168V128.365V115.612V75.9776L9.09449 68.9078V108.903L14.4685 104.744V64.7491L23.563 57.6793V97.7297L28.937 93.571V53.5205L38.0315 46.4507V86.4356L60.9055 68.6192V19.2687L0 66.4007V54.2018Z" fill="#192329"/>
</svg>

            </div>
            <div class="pretitle font-tt-eb uppercase text-16 spacing-12 mb-28 text-gradient-red-yellow">
            Perché scegliere Produzione Hip Hop
            </div>
            <div class="title font-din-bc text-60 max-w-900 mb-20">
            La passione per la musica è il valore più importante e alimenta ogni giorno il lavoro di Produzione Hip Hop
            </div>
            <div class="text font-ave-d text-20 leading-15 text-gray max-w-800">
            La passione per la musica è il valore più importante. E’ ciò che ha fatto nascere tutto e che alimenta ogni giorno il lavoro che mettiamo in Produzione Hip Hop.
            </div>
</div>

<div class="wrapper absolute-center-x top-0 bottom-0 z--10">
<div class="composition flex flex-col items-center gap-20 w-600 absolute top-0 right-500">
            <div class="row w-50-100">
                <div class="item h-60 grow-1 bg-gradient-white-gray rounded-full">
                    
                </div>
            </div>
            <div class="row flex gap-20 w-80-100 relative">
                <div class="item h-60 grow-5 bg-gradient-white-gray rounded-full">
                    
                </div>
                <div class="item h-60 grow-8 bg-gradient-violet-white bg-opacity-50 rounded-full flex place-center text-center">
                    <div class="text font-ave-b text-22">
                        Hip Hop
                    </div>
                </div>

                <div class="shapes absolute inset-0 z-50">
                    <div class="shape-1 animate-float-updown-light animation-duration-5000 absolute right-36 top--60">
                <svg class="h-72 shadow-violet" width="59" height="72" viewBox="0 0 59 72" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_596_645)">
<path d="M48.9873 64.848L57.9368 9.5359C59.0863 2.43098 51.0679 -2.51389 45.2354 1.70308L3.70263 31.7315C-0.662459 34.8875 -0.77208 41.3515 3.48348 44.6537L36.0667 69.9374C40.9092 73.695 48.0082 70.8987 48.9873 64.848Z" fill="#AB77FF"/>
</g>
<defs>
<clipPath id="clip0_596_645">
<rect width="59" height="72" fill="white"/>
</clipPath>
</defs>
</svg>
</div>
                </div>
            </div>
            <div class="row flex gap-20 w-100-100">
                <div class="item h-60 grow-5 bg-gradient-white-gray rounded-full">
                    
                </div>
                <div class="item h-60 grow-7 bg-gradient-white-yellow bg-opacity-50 rounded-full flex place-center text-center">
                   
                </div>
            </div>

            <div class="row flex gap-20 w-60-100">
                <div class="item h-60 grow-3 bg-gradient-white-gray rounded-full">
                    
                </div>
                <div class="item h-60 grow-7 bg-gradient-white-gray rounded-full flex place-center text-center">
                    
                </div>
            </div>

            <div class="row flex w-40-100">
                <div class="item h-60 grow-7 bg-gradient-white-sky bg-opacity-50 rounded-full flex place-center text-center">
                    <div class="text font-ave-b text-22">
                       Drill
                    </div>
                </div>
            </div>

            
        </div> <!-- composition -->


        <div class="composition flex flex-col items-center gap-20 w-600 absolute top-0 left-500">
            <div class="row w-50-100">
                <div class="item h-60 grow-1 bg-gradient-white-gray rounded-full">
                    
                </div>
            </div>
            <div class="row flex gap-20 w-80-100 relative">
                <div class="item h-60 grow-5 bg-gradient-white-gray rounded-full">
                    
                </div>
                <div class="item h-60 grow-8 bg-gradient-violet-white bg-opacity-50 rounded-full flex place-center text-center">
                    
                </div>

            </div>
            <div class="row flex gap-20 w-100-100">

            <div class="item h-60 grow-7 bg-gradient-white-yellow bg-opacity-50 rounded-full flex place-center text-center">
                <div class="text font-ave-b text-22">
                        Rap
                        </div>
                </div>
                <div class="item h-60 grow-5 bg-gradient-white-gray rounded-full">
                    
                </div>
            </div>

            <div class="row flex gap-20 w-60-100">

            <div class="item h-60 grow-7 bg-gradient-red-white bg-opacity-50 rounded-full flex place-center text-center">
                <div class="text font-ave-b text-22">
                        Trap
                        </div>
                </div>
                <div class="item h-60 grow-3 bg-gradient-white-gray rounded-full">
                    
                </div>


                <div class="shapes absolute inset-0 z-50">
                    <div class="shape-1 animate-float-updown-light animation-duration-8000 absolute left-60 top-260">

                    <svg class="h-72 shadow-red" width="82" height="73" viewBox="0 0 82 73" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_616_2149)">
<path d="M30.3969 67.9873L1.65208 12.3671C-1.62044 6.03487 4.31853 -1.12212 11.1457 0.926477L75.1848 20.1425C80.9838 21.8826 82.8756 29.1495 78.6613 33.4964L43.3669 69.9006C39.5257 73.8625 32.9305 72.8896 30.3969 67.9873Z" fill="#FF4444"/>
</g>
<defs>
<clipPath id="clip0_616_2149">
<rect width="81" height="73" fill="white" transform="translate(0.734375)"/>
</clipPath>
</defs>
</svg>


</div>
                </div>
            </div>

            <div class="row flex w-40-100">
                <div class="item h-60 grow-7 bg-gradient-white-sky bg-opacity-50 rounded-full flex place-center text-center">
                  
                </div>
            </div>

            
        </div>

</div>



    </div>

    <div class="content">

    <div class="comparison grid grid-cols-2">

        <div class="col bg-brand-green-light pr-100 flex flex-col items-end text-right pb-140 pt-140 relative">
            <div class="content z-30 relative">
            <div class="title font-ave-b text-20 mb-48">
            Produzione Hip Hop
            </div>
            <div class="items flex flex-col gap-20 items-end">

                <div class="item flex gap-28">
                    <div class="text font-ave-d text-20 leading-15 text-gray">
                    Siamo specializzati in questo genere di musica
                    </div>
                    <div class="icon flex shrink-0">
                    <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4 14L8.87524 17.7502C9.76603 18.4354 11.0464 18.2524 11.7094 17.345L20 6" stroke="#20CF01" stroke-width="4" stroke-linecap="round"/>
</svg>


                    </div>
                </div> <!-- item -->


                <div class="item flex gap-28">
                    <div class="text font-ave-d text-20 leading-15 text-gray">
                    Bastano pochi mesi per finire i nostri corsi
                    </div>
                    <div class="icon flex shrink-0">
                    <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4 14L8.87524 17.7502C9.76603 18.4354 11.0464 18.2524 11.7094 17.345L20 6" stroke="#20CF01" stroke-width="4" stroke-linecap="round"/>
</svg>


                    </div>
                </div> <!-- item -->


                <div class="item flex gap-28">
                    <div class="text font-ave-d text-20 leading-15 text-gray">
                    Libertà di fare i corsi dove e quando vuoi
                    </div>
                    <div class="icon flex shrink-0">
                    <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4 14L8.87524 17.7502C9.76603 18.4354 11.0464 18.2524 11.7094 17.345L20 6" stroke="#20CF01" stroke-width="4" stroke-linecap="round"/>
</svg>


                    </div>
                </div> <!-- item -->



                <div class="item flex gap-28">
                    <div class="text font-ave-d text-20 leading-15 text-gray">
                    Vieni seguito 1-1 dal docente durante il corso
                    </div>
                    <div class="icon flex shrink-0">
                    <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4 14L8.87524 17.7502C9.76603 18.4354 11.0464 18.2524 11.7094 17.345L20 6" stroke="#20CF01" stroke-width="4" stroke-linecap="round"/>
</svg>


                    </div>
                </div> <!-- item -->


                <div class="item flex gap-28">
                    <div class="text font-ave-d text-20 leading-15 text-gray">
                    Aiutiamo ogni studente a raggiungere il proprio obiettivo
                    </div>
                    <div class="icon flex shrink-0">
                    <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4 14L8.87524 17.7502C9.76603 18.4354 11.0464 18.2524 11.7094 17.345L20 6" stroke="#20CF01" stroke-width="4" stroke-linecap="round"/>
</svg>


                    </div>
                </div> <!-- item -->


                <div class="item flex gap-28">
                    <div class="text font-ave-d text-20 leading-15 text-gray">
                    Corsi completi che ti preparano al 100%
                    </div>
                    <div class="icon flex shrink-0">
                    <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4 14L8.87524 17.7502C9.76603 18.4354 11.0464 18.2524 11.7094 17.345L20 6" stroke="#20CF01" stroke-width="4" stroke-linecap="round"/>
</svg>


                    </div>
                </div> <!-- item -->



                <div class="item flex gap-28">
                    <div class="text font-ave-d text-20 leading-15 text-gray">
                    Abbiamo esperienza nel settore e lavorato con artisti famosi
                    </div>
                    <div class="icon flex shrink-0">
                    <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4 14L8.87524 17.7502C9.76603 18.4354 11.0464 18.2524 11.7094 17.345L20 6" stroke="#20CF01" stroke-width="4" stroke-linecap="round"/>
</svg>


                    </div>
                </div> <!-- item -->


                <div class="item flex gap-28">
                    <div class="text font-ave-d text-20 leading-15 text-gray">
                    Aggiornamento continuo alle dinamiche del mercato di oggi
                    </div>
                    <div class="icon flex shrink-0">
                    <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4 14L8.87524 17.7502C9.76603 18.4354 11.0464 18.2524 11.7094 17.345L20 6" stroke="#20CF01" stroke-width="4" stroke-linecap="round"/>
</svg>


                    </div>
                </div> <!-- item -->




            </div>
</div>

        </div> <!-- col -->




        <div class="col bg-brand-red-light pl-100 flex flex-col pb-140 pt-140 relative">
            <div class="content z-30 relative">
            <div class="title font-ave-b text-20 mb-48">
            Altre accademie
            </div>
            <div class="items flex flex-col gap-20">

                <div class="item flex gap-28">
                <div class="icon flex shrink-0">
                    <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4 4L20 20" stroke="#FF0000" stroke-width="4" stroke-linecap="round"/>
<path d="M20 4L4 20" stroke="#FF0000" stroke-width="4" stroke-linecap="round"/>
</svg>


                    </div>
                    <div class="text font-ave-d text-20 leading-15 text-gray">
                    Molte scuole non sono specializzate in questo mercato
                    </div>
                   
                </div> <!-- item -->


                <div class="item flex gap-28">
                <div class="icon flex shrink-0">
                    <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4 4L20 20" stroke="#FF0000" stroke-width="4" stroke-linecap="round"/>
<path d="M20 4L4 20" stroke="#FF0000" stroke-width="4" stroke-linecap="round"/>
</svg>


                    </div>
                    <div class="text font-ave-d text-20 leading-15 text-gray">
                    In alcuni casi ci metti anni a terminare il percorso di studi
                    </div>
                
                </div> <!-- item -->


                <div class="item flex gap-28">
                <div class="icon flex shrink-0">
                    <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4 4L20 20" stroke="#FF0000" stroke-width="4" stroke-linecap="round"/>
<path d="M20 4L4 20" stroke="#FF0000" stroke-width="4" stroke-linecap="round"/>
</svg>


                    </div>
                    <div class="text font-ave-d text-20 leading-15 text-gray">
                    Spesso hai vincoli di giorni e orari per fare le lezioni
                    </div>
                    
                </div> <!-- item -->



                <div class="item flex gap-28">
                <div class="icon flex shrink-0">
                    <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4 4L20 20" stroke="#FF0000" stroke-width="4" stroke-linecap="round"/>
<path d="M20 4L4 20" stroke="#FF0000" stroke-width="4" stroke-linecap="round"/>
</svg>


                    </div>
                    <div class="text font-ave-d text-20 leading-15 text-gray">
                    Non sempre vieni seguito dal docente
                    </div>
                   
                </div> <!-- item -->


                <div class="item flex gap-28">
                <div class="icon flex shrink-0">
                    <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4 4L20 20" stroke="#FF0000" stroke-width="4" stroke-linecap="round"/>
<path d="M20 4L4 20" stroke="#FF0000" stroke-width="4" stroke-linecap="round"/>
</svg>


                    </div>
                    <div class="text font-ave-d text-20 leading-15 text-gray">
                    Non tutti si interessano ai tuoi obiettivi specifici
                    </div>
                    
                </div> <!-- item -->


                <div class="item flex gap-28">
                <div class="icon flex shrink-0">
                    <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4 4L20 20" stroke="#FF0000" stroke-width="4" stroke-linecap="round"/>
<path d="M20 4L4 20" stroke="#FF0000" stroke-width="4" stroke-linecap="round"/>
</svg>


                    </div>
                    <div class="text font-ave-d text-20 leading-15 text-gray">
                    Spesso i corsi non sono completi o non ti specializzano
                    </div>
                   
                </div> <!-- item -->



                <div class="item flex gap-28">
                <div class="icon flex shrink-0">
                    <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4 4L20 20" stroke="#FF0000" stroke-width="4" stroke-linecap="round"/>
<path d="M20 4L4 20" stroke="#FF0000" stroke-width="4" stroke-linecap="round"/>
</svg>


                    </div>
                    <div class="text font-ave-d text-20 leading-15 text-gray">
                    Non tutti i docenti han esperienza lavorativa in questo settore
                    </div>
                    
                </div> <!-- item -->


                <div class="item flex gap-28">
                <div class="icon flex shrink-0">
                    <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M4 4L20 20" stroke="#FF0000" stroke-width="4" stroke-linecap="round"/>
<path d="M20 4L4 20" stroke="#FF0000" stroke-width="4" stroke-linecap="round"/>
</svg>


                    </div>
                    <div class="text font-ave-d text-20 leading-15 text-gray">
                    Rischio di perdere tempo a imparare cose obsolete
                    </div>
                   
                </div> <!-- item -->




            </div>

</div>
</div>
        </div> <!-- col -->



    </div>


</div>



</div>

</section>






<section class="home-gallery mb-140">

<div class="container max-w-2000 mx-auto">
    <div class="items grid grid-cols-3 gap-20">
        <div class="item ratio-3-4 relative">
            <?php load_img(class: 'img img-cover', high: 238, low: 237); ?>
        </div>
        <div class="item ratio-3-4 relative">
            <?php load_img(class: 'img img-cover', high: 240, low: 239); ?>
        </div>
        <div class="item ratio-3-4 relative">
            <?php load_img(class: 'img img-cover', high: 242, low: 241); ?>
        </div>
    </div>
</div>

</section>



<section class="scuola-varie mb-140">
    <div class="container-l mx-auto">

    <div class="intro flex items-center gap-140 justify-between mb-100">
        <div class="main">
        <div class="pretitle font-tt-eb uppercase text-16 spacing-12 mb-28 text-gradient-pink-violet">
        Incentivi formativi
        </div>
        <div class="title font-din-bc text-60 mb-20">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sapien libero
        </div>
        <div class="text font-ave-m text-20 leading-15 text-gray max-w-800">
        Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sapien libero, ultrices sed elit id, rhoncus sagittis augue.
        </div>
</div>
        <div class="side">
            <div class="visual">
            <?php load_img(class: 'img w-700', high: 172, low: 173); ?>
            </div>

</div>
    </div>

    <div class="content">
        <div class="items grid grid-cols-3 gap-20">

            <div class="item flex flex-col p-60 rounded-24 border-gray border-2 border-solid">
                <div class="icon mb-36">
                <svg class="h-60" width="75" height="60" viewBox="0 0 75 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_617_2211)">
<mask id="path-1-inside-1_617_2211" fill="white">
<path d="M44.52 58H41.48V49.76C41.08 50.096 40.392 50.304 39.48 50.304V48.24C40.856 48.176 41.544 47.36 41.88 46.8H44.52V58Z"/>
</mask>
<path d="M44.52 58V62H48.52V58H44.52ZM41.48 58H37.48V62H41.48V58ZM41.48 49.76H45.48V41.176L38.9072 46.6972L41.48 49.76ZM39.48 50.304H35.48V54.304H39.48V50.304ZM39.48 48.24L39.2942 44.2443L35.48 44.4217V48.24H39.48ZM41.88 46.8V42.8H39.6152L38.45 44.742L41.88 46.8ZM44.52 46.8H48.52V42.8H44.52V46.8ZM44.52 58V54H41.48V58V62H44.52V58ZM41.48 58H45.48V49.76H41.48H37.48V58H41.48ZM41.48 49.76L38.9072 46.6972C39.2477 46.4112 39.5294 46.3241 39.5943 46.3059C39.6717 46.2843 39.643 46.304 39.48 46.304V50.304V54.304C40.8535 54.304 42.6347 54.014 44.0528 52.8228L41.48 49.76ZM39.48 50.304H43.48V48.24H39.48H35.48V50.304H39.48ZM39.48 48.24L39.6658 52.2357C42.8896 52.0857 44.5975 50.0454 45.31 48.858L41.88 46.8L38.45 44.742C38.4537 44.7358 38.528 44.6093 38.7104 44.4761C38.8066 44.4058 38.9191 44.3441 39.04 44.3008C39.1617 44.2572 39.2534 44.2462 39.2942 44.2443L39.48 48.24ZM41.88 46.8V50.8H44.52V46.8V42.8H41.88V46.8ZM44.52 46.8H40.52V58H44.52H48.52V46.8H44.52Z" fill="#A973FF" mask="url(#path-1-inside-1_617_2211)"/>
<mask id="path-3-inside-2_617_2211" fill="white">
<path d="M34.08 57.04H31.68V54.56H29.2V52.08H31.68V49.6H34.08V52.08H36.56V54.56H34.08V57.04Z"/>
</mask>
<path d="M34.08 57.04V61.04H38.08V57.04H34.08ZM31.68 57.04H27.68V61.04H31.68V57.04ZM31.68 54.56H35.68V50.56H31.68V54.56ZM29.2 54.56H25.2V58.56H29.2V54.56ZM29.2 52.08V48.08H25.2V52.08H29.2ZM31.68 52.08V56.08H35.68V52.08H31.68ZM31.68 49.6V45.6H27.68V49.6H31.68ZM34.08 49.6H38.08V45.6H34.08V49.6ZM34.08 52.08H30.08V56.08H34.08V52.08ZM36.56 52.08H40.56V48.08H36.56V52.08ZM36.56 54.56V58.56H40.56V54.56H36.56ZM34.08 54.56V50.56H30.08V54.56H34.08ZM34.08 57.04V53.04H31.68V57.04V61.04H34.08V57.04ZM31.68 57.04H35.68V54.56H31.68H27.68V57.04H31.68ZM31.68 54.56V50.56H29.2V54.56V58.56H31.68V54.56ZM29.2 54.56H33.2V52.08H29.2H25.2V54.56H29.2ZM29.2 52.08V56.08H31.68V52.08V48.08H29.2V52.08ZM31.68 52.08H35.68V49.6H31.68H27.68V52.08H31.68ZM31.68 49.6V53.6H34.08V49.6V45.6H31.68V49.6ZM34.08 49.6H30.08V52.08H34.08H38.08V49.6H34.08ZM34.08 52.08V56.08H36.56V52.08V48.08H34.08V52.08ZM36.56 52.08H32.56V54.56H36.56H40.56V52.08H36.56ZM36.56 54.56V50.56H34.08V54.56V58.56H36.56V54.56ZM34.08 54.56H30.08V57.04H34.08H38.08V54.56H34.08Z" fill="#A973FF" mask="url(#path-3-inside-2_617_2211)"/>
<path d="M67.6452 43.8847C69.5893 43.107 71.015 41.2925 72.6999 38.1819C68.034 35.4601 62.9792 37.0154 59.6094 42.8478" stroke="#192329" stroke-width="3.3" stroke-miterlimit="10"/>
<path d="M53 51.9765C56.2402 55.0871 61.1654 57.2905 67.257 50.81C63.2392 46.9217 57.6659 47.0513 53 51.9765Z" stroke="#192329" stroke-width="3.3" stroke-miterlimit="10"/>
<path d="M69.4602 32.516C71.534 31.0903 72.9597 28.4981 73.3485 23.8321C67.7753 23.4433 63.6278 27.202 63.1094 33.9417" stroke="#192329" stroke-width="3.3" stroke-miterlimit="10"/>
<path d="M68.1632 19.4967C69.5889 17.4229 69.9778 14.4419 68.5521 10.0352C63.2381 11.7201 60.7755 16.7749 62.7196 23.1257" stroke="#192329" stroke-width="3.3" stroke-miterlimit="10"/>
<path d="M7.70321 43.8847C5.75907 43.107 4.33336 41.2925 2.64844 38.1819C7.31438 35.4601 12.3692 37.0154 15.739 42.8478" stroke="#192329" stroke-width="3.3" stroke-miterlimit="10"/>
<path d="M22.3469 51.9765C19.1067 55.0871 14.1815 57.2905 8.08984 50.81C12.1077 46.9217 17.681 47.0513 22.3469 51.9765Z" stroke="#192329" stroke-width="3.3" stroke-miterlimit="10"/>
<path d="M5.88829 32.516C3.81453 31.0903 2.38883 28.4981 2 23.8321C7.57321 23.4433 11.7207 27.202 12.2392 33.9417" stroke="#192329" stroke-width="3.3" stroke-miterlimit="10"/>
<path d="M7.18177 19.4967C5.75607 17.4229 5.36724 14.4419 6.79295 10.0352C12.1069 11.7201 14.5695 16.7749 12.6254 23.1257" stroke="#192329" stroke-width="3.3" stroke-miterlimit="10"/>
<path d="M48.4864 41H50C51.1046 41 52 40.1046 52 39V7C52 5.89543 51.1046 5 50 5H25C23.8954 5 23 5.89543 23 7V39C23 40.1046 23.8954 41 25 41H33.9427" stroke="#192329" stroke-width="3.5" stroke-miterlimit="10"/>
<path d="M40.8883 40.4016C43.0357 40.4016 44.7766 38.6607 44.7766 36.5133C44.7766 34.3658 43.0357 32.625 40.8883 32.625C38.7408 32.625 37 34.3658 37 36.5133C37 38.6607 38.7408 40.4016 40.8883 40.4016Z" stroke="#192329" stroke-width="2.97207" stroke-miterlimit="10"/>
<path d="M31 14H43.961" stroke="#192329" stroke-width="2.97207" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M31 19.1836H38.7766" stroke="#192329" stroke-width="2.97207" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M31 24.6328H41" stroke="#192329" stroke-width="2.97207" stroke-miterlimit="10" stroke-linecap="round" stroke-linejoin="round"/>
</g>
<defs>
<clipPath id="clip0_617_2211">
<rect width="75" height="60" fill="white"/>
</clipPath>
</defs>
</svg>

                </div>
                <div class="title uppercase font-tt-eb text-16 spacing-12 text-brand-violet mb-20">
                Credito scolastico
                </div>
                <div class="text font-ave-m text-20 leading-15 text-gray">
                Possibilità di ricevere un punto in più come voto al diploma per un’attività extra scolastica legata alla cultura e alle arti, per gli studenti delle superiori.
                </div>
            </div> <!-- item -->


            <div class="item flex flex-col p-60 rounded-24 border-gray border-2 border-solid">
                <div class="icon mb-36">
                <svg class="h-60" width="57" height="60" viewBox="0 0 57 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_617_2230)">
<path d="M20 38L37.5 20.5" stroke="#A973FF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M27.1006 4.16992C27.8294 3.45621 28.9679 3.41174 29.748 4.03613L29.8994 4.16992L34.6445 8.81738C35.7505 9.90058 37.2323 10.5142 38.7803 10.5303L45.4209 10.5996C46.4411 10.6102 47.2786 11.3837 47.3887 12.377L47.4004 12.5791L47.4697 19.2197C47.4848 20.6712 48.025 22.0646 48.9854 23.1445L49.1826 23.3555L53.8301 28.1006C54.5438 28.8294 54.5883 29.9679 53.9639 30.748L53.8301 30.8994L49.1826 35.6445C48.0994 36.7505 47.4858 38.2323 47.4697 39.7803L47.4004 46.4209C47.3898 47.4411 46.6163 48.2786 45.623 48.3887L45.4209 48.4004L38.7803 48.4697C37.2323 48.4858 35.7505 49.0994 34.6445 50.1826L29.8994 54.8301C29.1706 55.5438 28.0321 55.5883 27.252 54.9639L27.1006 54.8301L22.3555 50.1826C21.2495 49.0994 19.7677 48.4858 18.2197 48.4697L11.5791 48.4004C10.4908 48.3891 9.6109 47.5092 9.59961 46.4209L9.53027 39.7803C9.51521 38.3288 8.97498 36.9354 8.01465 35.8555L7.81738 35.6445L3.16992 30.8994C2.45621 30.1706 2.41174 29.0321 3.03613 28.252L3.16992 28.1006L7.81738 23.3555C8.90058 22.2495 9.51421 20.7677 9.53027 19.2197L9.59961 12.5791C9.6109 11.4908 10.4908 10.6109 11.5791 10.5996L18.2197 10.5303C19.7677 10.5142 21.2495 9.90058 22.3555 8.81738L27.1006 4.16992Z" stroke="#192329" stroke-width="4"/>
<circle cx="21.5" cy="21.5" r="3.5" fill="#A973FF"/>
<circle cx="35.5" cy="35.5" r="3.5" fill="#A973FF"/>
</g>
<defs>
<clipPath id="clip0_617_2230">
<rect width="57" height="60" fill="white"/>
</clipPath>
</defs>
</svg>


                </div>
                <div class="title uppercase font-tt-eb text-16 spacing-12 text-brand-violet mb-20">
                Offerte sui corsi fino al 50%
                </div>
                <div class="text font-ave-m text-20 leading-15 text-gray">
                Periodicamente, in base alle disponibilità, apriamo le selezioni per permettere ad alcuni studenti di avere uno sconto fino al 50%.
                </div>
            </div> <!-- item -->


            <div class="item flex flex-col p-60 rounded-24 border-gray border-2 border-solid">
                <div class="icon mb-36">
                <svg class="h-60" width="59" height="60" viewBox="0 0 59 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M58.0607 12.3913V24.1391C58.0607 24.5842 57.8838 25.011 57.5691 25.3258C57.2544 25.6405 56.8275 25.8173 56.3824 25.8173C55.9373 25.8173 55.5104 25.6405 55.1957 25.3258C54.881 25.011 54.7041 24.5842 54.7041 24.1391V12.3913C54.7041 11.056 54.1737 9.77536 53.2295 8.83116C52.2853 7.88696 51.0047 7.35651 49.6694 7.35651H9.39127C8.05597 7.35651 6.77536 7.88696 5.83116 8.83116C4.88696 9.77536 4.35651 11.056 4.35651 12.3913V42.5999C4.35651 43.9352 4.88696 45.2158 5.83116 46.16C6.77536 47.1042 8.05597 47.6346 9.39127 47.6346H37.9216C38.3667 47.6346 38.7936 47.8114 39.1083 48.1262C39.423 48.4409 39.5999 48.8678 39.5999 49.3129C39.5999 49.758 39.423 50.1848 39.1083 50.4996C38.7936 50.8143 38.3667 50.9911 37.9216 50.9911H9.39127C7.16577 50.9911 5.03142 50.1071 3.45775 48.5334C1.88408 46.9597 1 44.8254 1 42.5999V12.3913C1 10.1658 1.88408 8.03142 3.45775 6.45775C5.03142 4.88408 7.16577 4 9.39127 4H49.6694C51.8949 4 54.0292 4.88408 55.6029 6.45775C57.1766 8.03142 58.0607 10.1658 58.0607 12.3913ZM49.6694 27.4956C48.1122 27.4874 46.5837 27.9146 45.2564 28.7291C43.9291 29.5435 42.8559 30.7126 42.1578 32.1046C41.4597 33.4966 41.1645 35.056 41.3056 36.6069C41.4467 38.1577 42.0185 39.6382 42.9564 40.8813V40.9216V54.4584C42.9575 54.9136 43.0819 55.3599 43.3163 55.7501C43.5507 56.1403 43.8864 56.4597 44.2877 56.6745C44.689 56.8892 45.141 56.9913 45.5957 56.9699C46.0503 56.9485 46.4907 56.8044 46.8701 56.5529L49.6694 54.6833L52.4754 56.5529C52.8552 56.8029 53.2955 56.9456 53.7497 56.9658C54.2039 56.986 54.6552 56.8829 55.0556 56.6676C55.456 56.4522 55.7908 56.1326 56.0244 55.7425C56.2579 55.3524 56.3816 54.9064 56.3824 54.4517V40.9216V40.8813C57.3203 39.6382 57.892 38.1577 58.0331 36.6069C58.1742 35.056 57.8791 33.4966 57.181 32.1046C56.4829 30.7126 55.4097 29.5435 54.0824 28.7291C52.7551 27.9146 51.2266 27.4874 49.6694 27.4956ZM53.0259 52.8909L51.0724 51.5919C50.6599 51.3105 50.1721 51.16 49.6727 51.16C49.1734 51.16 48.6856 51.3105 48.2731 51.5919L46.3129 52.8976V43.5665C47.3698 44.0354 48.5132 44.2776 49.6694 44.2776C50.8256 44.2776 51.969 44.0354 53.0259 43.5665V52.8909ZM49.6694 40.9216C48.6736 40.9216 47.7002 40.6263 46.8722 40.0731C46.0443 39.5199 45.3989 38.7335 45.0179 37.8136C44.6368 36.8936 44.5371 35.8813 44.7314 34.9046C44.9256 33.928 45.4051 33.0308 46.1093 32.3267C46.8134 31.6226 47.7105 31.1431 48.6871 30.9488C49.6638 30.7545 50.6761 30.8543 51.5961 31.2353C52.5161 31.6164 53.3024 32.2617 53.8556 33.0897C54.4089 33.9176 54.7041 34.8911 54.7041 35.8868C54.7041 37.2221 54.1737 38.5028 53.2295 39.447C52.2853 40.3912 51.0047 40.9216 49.6694 40.9216Z" fill="#192329"/>
<path d="M36.2427 15.7484H47.9905C48.4356 15.7484 48.8624 15.5716 49.1772 15.2568C49.4919 14.9421 49.6687 14.5152 49.6687 14.0701C49.6687 13.625 49.4919 13.1982 49.1772 12.8834C48.8624 12.5687 48.4356 12.3919 47.9905 12.3919H36.2427C35.7976 12.3919 35.3707 12.5687 35.056 12.8834C34.7413 13.1982 34.5644 13.625 34.5644 14.0701C34.5644 14.5152 34.7413 14.9421 35.056 15.2568C35.3707 15.5716 35.7976 15.7484 36.2427 15.7484Z" fill="#A973FF"/>
<path d="M38.4125 19.5964C38.0978 19.9112 37.9209 20.338 37.9209 20.7831C37.9209 21.2282 38.0978 21.6551 38.4125 21.9699C38.7272 22.2846 39.1541 22.4614 39.5992 22.4614H47.9905C48.4356 22.4614 48.8624 22.2846 49.1772 21.9699C49.4919 21.6551 49.6687 21.2282 49.6687 20.7831C49.6687 20.338 49.4919 19.9112 49.1772 19.5964C48.8624 19.2817 48.4356 19.1049 47.9905 19.1049H39.5992C39.1541 19.1049 38.7272 19.2817 38.4125 19.5964Z" fill="#A973FF"/>
<path d="M37.4294 33.7176C37.7441 33.4029 37.9209 32.976 37.9209 32.5309C37.9209 32.0858 37.7441 31.659 37.4294 31.3442C37.1147 31.0295 36.6878 30.8527 36.2427 30.8527H11.0689C10.6238 30.8527 10.1969 31.0295 9.88217 31.3442C9.56744 31.659 9.39063 32.0858 9.39063 32.5309C9.39063 32.976 9.56744 33.4029 9.88217 33.7176C10.1969 34.0324 10.6238 34.2092 11.0689 34.2092H36.2427C36.6878 34.2092 37.1147 34.0324 37.4294 33.7176Z" fill="#A973FF"/>
<path d="M37.4294 40.4307C37.7441 40.1159 37.9209 39.689 37.9209 39.2439C37.9209 38.7988 37.7441 38.372 37.4294 38.0572C37.1147 37.7425 36.6878 37.5657 36.2427 37.5657H11.0689C10.6238 37.5657 10.1969 37.7425 9.88217 38.0572C9.56744 38.372 9.39063 38.7988 9.39063 39.2439C9.39063 39.689 9.56744 40.1159 9.88217 40.4307C10.1969 40.7454 10.6238 40.9222 11.0689 40.9222H36.2427C36.6878 40.9222 37.1147 40.7454 37.4294 40.4307Z" fill="#A973FF"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M11.0689 27.4962C11.514 27.4962 11.9408 27.3194 12.2556 27.0046C12.5703 26.6899 12.7471 26.263 12.7471 25.8179C12.7471 24.9277 13.1008 24.074 13.7302 23.4445C14.3597 22.815 15.2134 22.4614 16.1036 22.4614C16.9938 22.4614 17.8476 22.815 18.4771 23.4445C19.1065 24.074 19.4602 24.9277 19.4602 25.8179C19.4602 26.263 19.637 26.6899 19.9517 27.0046C20.2664 27.3194 20.6933 27.4962 21.1384 27.4962C21.5835 27.4962 22.0104 27.3194 22.3251 27.0046C22.6398 26.6899 22.8167 26.263 22.8167 25.8179C22.8132 24.8253 22.5884 23.846 22.1588 22.9513C21.7291 22.0565 21.1053 21.2688 20.3328 20.6455C21.1327 19.8157 21.6707 18.7689 21.8799 17.6355C22.0892 16.5021 21.9603 15.3322 21.5095 14.2715C21.0587 13.2108 20.3057 12.3062 19.3445 11.6703C18.3833 11.0344 17.2562 10.6953 16.1036 10.6953C14.9511 10.6953 13.824 11.0344 12.8628 11.6703C11.9016 12.3062 11.1486 13.2108 10.6978 14.2715C10.247 15.3322 10.1181 16.5021 10.3273 17.6355C10.5366 18.7689 11.0746 19.8157 11.8744 20.6455C11.102 21.2688 10.4782 22.0565 10.0485 22.9513C9.61884 23.846 9.39409 24.8253 9.39063 25.8179C9.39062 26.263 9.56744 26.6899 9.88217 27.0046C10.1969 27.3194 10.6238 27.4962 11.0689 27.4962ZM17.5022 14.4944C17.0882 14.2178 16.6015 14.0701 16.1036 14.0701C15.4363 14.071 14.7965 14.3365 14.3246 14.8084C13.8527 15.2803 13.5871 15.9201 13.5863 16.5875C13.5863 17.0854 13.7339 17.5721 14.0105 17.9861C14.2871 18.4001 14.6803 18.7227 15.1403 18.9133C15.6003 19.1038 16.1064 19.1537 16.5948 19.0565C17.0831 18.9594 17.5316 18.7196 17.8837 18.3676C18.2358 18.0155 18.4755 17.567 18.5727 17.0786C18.6698 16.5903 18.6199 16.0841 18.4294 15.6242C18.2389 15.1642 17.9162 14.771 17.5022 14.4944Z" fill="#A973FF"/>
</svg>


                </div>
                <div class="title uppercase font-tt-eb text-16 spacing-12 text-brand-violet mb-20">
                Attestato di frequenza
                </div>
                <div class="text font-ave-m text-20 leading-15 text-gray">
                Seguendo le lezioni del corso e rispondendo ai quiz di ogni modulo si può ottenere un attestato di frequenza nominale.
                </div>
            </div> <!-- item -->





            <div class="item flex flex-col p-60 rounded-24 border-gray border-2 border-solid">
                <div class="icon mb-36">
                <svg class="h-60" width="58" height="60" viewBox="0 0 58 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<path fill-rule="evenodd" clip-rule="evenodd" d="M6.22321 9C4.83793 9 3.50939 9.55312 2.52984 10.5377C1.5503 11.5223 1 12.8576 1 14.25L1 45.75C1 47.1424 1.5503 48.4777 2.52984 49.4623C3.50939 50.4469 4.83793 51 6.22321 51H51.4911C52.8764 51 54.2049 50.4469 55.1844 49.4623C56.164 48.4777 56.7143 47.1424 56.7143 45.75V14.25C56.7143 12.8576 56.164 11.5223 55.1844 10.5377C54.2049 9.55312 52.8764 9 51.4911 9H6.22321ZM52.7222 13.0126C52.3957 12.6844 51.9528 12.5 51.4911 12.5H6.22321C5.76145 12.5 5.31861 12.6844 4.99209 13.0126C4.66558 13.3408 4.48214 13.7859 4.48214 14.25V17.75H53.2321V14.25C53.2321 13.7859 53.0487 13.3408 52.7222 13.0126ZM53.2321 24.75V21.25H4.48214V24.75H53.2321ZM6.22321 47.5H51.4911C51.9528 47.5 52.3957 47.3156 52.7222 46.9874C53.0487 46.6593 53.2321 46.2141 53.2321 45.75V28.25H4.48214V45.75C4.48214 46.2141 4.66558 46.6593 4.99209 46.9874C5.31861 47.3156 5.76145 47.5 6.22321 47.5Z" fill="#192329"/>
<path d="M44.4594 44.1429H37.2594C35.8272 44.1429 34.4537 43.5559 33.441 42.511C32.4283 41.4662 31.8594 40.0491 31.8594 38.5714C31.8594 37.0938 32.4283 35.6767 33.441 34.6318C34.4537 33.587 35.8272 33 37.2594 33H44.4594C45.8915 33 47.2651 33.587 48.2778 34.6318C49.2904 35.6767 49.8594 37.0938 49.8594 38.5714C49.8594 40.0491 49.2904 41.4662 48.2778 42.511C47.2651 43.5559 45.8915 44.1429 44.4594 44.1429ZM37.2594 36.7143C36.782 36.7143 36.3241 36.9099 35.9866 37.2582C35.649 37.6065 35.4594 38.0789 35.4594 38.5714C35.4594 39.064 35.649 39.5363 35.9866 39.8846C36.3241 40.2329 36.782 40.4286 37.2594 40.4286H44.4594C44.9368 40.4286 45.3946 40.2329 45.7322 39.8846C46.0697 39.5363 46.2594 39.064 46.2594 38.5714C46.2594 38.0789 46.0697 37.6065 45.7322 37.2582C45.3946 36.9099 44.9368 36.7143 44.4594 36.7143H37.2594Z" fill="#A973FF"/>
<path d="M13.0022 37.2857H9.57366C9.119 37.2857 8.68297 37.06 8.36148 36.6581C8.03999 36.2562 7.85938 35.7112 7.85938 35.1429C7.85938 34.5745 8.03999 34.0295 8.36148 33.6276C8.68297 33.2258 9.119 33 9.57366 33H13.0022C13.4569 33 13.8929 33.2258 14.2144 33.6276C14.5359 34.0295 14.7165 34.5745 14.7165 35.1429C14.7165 35.7112 14.5359 36.2562 14.2144 36.6581C13.8929 37.06 13.4569 37.2857 13.0022 37.2857Z" fill="#A973FF"/>
<path d="M19.8594 44.1451H9.57366C9.119 44.1451 8.68297 43.9193 8.36148 43.5175C8.03999 43.1156 7.85938 42.5706 7.85938 42.0022C7.85938 41.4339 8.03999 40.8889 8.36148 40.487C8.68297 40.0851 9.119 39.8594 9.57366 39.8594H19.8594C20.314 39.8594 20.7501 40.0851 21.0716 40.487C21.393 40.8889 21.5737 41.4339 21.5737 42.0022C21.5737 42.5706 21.393 43.1156 21.0716 43.5175C20.7501 43.9193 20.314 44.1451 19.8594 44.1451Z" fill="#A973FF"/>
</svg>


                </div>
                <div class="title uppercase font-tt-eb text-16 spacing-12 text-brand-violet mb-20">
                Pagamento con carta studente
                </div>
                <div class="text font-ave-m text-20 leading-15 text-gray">
                Possibilità di utilizzare il bonus da 500€ della carta studente per l’acquisto di corsi Produzione Hip Hop.
                </div>
            </div> <!-- item -->




            <div class="item flex flex-col p-60 rounded-24 border-gray border-2 border-solid">
                <div class="icon mb-36">
                <svg class="h-60" width="42" height="60" viewBox="0 0 42 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M41 46.1105C40.9882 41.6746 39.0803 37.3095 35.743 34.368C34.0537 32.8795 32.0986 31.7573 29.9486 31.078C28.3538 30.57 26.6704 30.3633 24.9988 30.3633C22.5121 30.3633 20.0254 30.3633 17.5387 30.3633C16.186 30.3633 14.8511 30.4342 13.5162 30.7C11.4312 31.1193 9.39338 32.0053 7.67454 33.2457C5.8671 34.5511 4.32546 36.1814 3.18547 38.1069C2.01595 40.0857 1.34259 42.2711 1.0886 44.5511C1.02953 45.0709 1.00591 45.5907 1 46.1105C1 47.0378 1.81512 47.9238 2.772 47.8825C3.73479 47.8411 4.544 47.1028 4.544 46.1105C4.544 45.4785 4.58535 44.8523 4.66804 44.2262C4.64442 44.3857 4.6267 44.5393 4.60307 44.6988C4.76846 43.4879 5.09332 42.3066 5.55995 41.1784C5.50088 41.3202 5.44182 41.4619 5.38275 41.6037C5.65446 40.9598 5.97342 40.3337 6.33963 39.7313C6.52274 39.4359 6.71175 39.1406 6.91848 38.8571C7.0189 38.7212 7.39692 38.2546 6.98936 38.7508C7.10159 38.609 7.21382 38.4672 7.33195 38.3314C7.77495 37.8116 8.25339 37.3213 8.76136 36.8724C8.8854 36.7602 9.01535 36.6539 9.1453 36.5476C9.20436 36.5003 9.26343 36.4531 9.3284 36.3999C9.53514 36.2345 9.1512 36.5417 9.1512 36.5358C9.16892 36.5003 9.23981 36.4649 9.27525 36.4412C9.55286 36.2345 9.84229 36.0337 10.1376 35.8447C10.8051 35.4194 11.508 35.0532 12.2404 34.7401C12.0986 34.7992 11.9569 34.8583 11.8151 34.9173C12.9433 34.4448 14.1246 34.1258 15.3355 33.9604C15.176 33.9841 15.0224 34.0018 14.863 34.0254C16.3396 33.8305 17.8517 33.9014 19.3343 33.9014C21.1122 33.9014 22.8901 33.9014 24.6621 33.9014C25.4891 33.9014 26.316 33.9191 27.137 34.0254C26.9775 34.0018 26.824 33.9841 26.6645 33.9604C27.8754 34.1258 29.0567 34.4507 30.1849 34.9173C30.0431 34.8583 29.9013 34.7992 29.7596 34.7401C30.4034 35.0118 31.0295 35.3308 31.632 35.697C31.9273 35.8801 32.2227 36.0691 32.5062 36.2759C32.642 36.3763 33.1087 36.7543 32.6125 36.3467C32.7543 36.459 32.896 36.5712 33.0319 36.6893C33.5517 37.1323 34.0419 37.6108 34.4908 38.1187C34.603 38.2428 34.7094 38.3727 34.8157 38.5027C34.8629 38.5617 34.9102 38.6208 34.9634 38.6858C35.1287 38.8925 34.8216 38.5086 34.8275 38.5086C34.8629 38.5263 34.8984 38.5972 34.922 38.6326C35.1287 38.9102 35.3296 39.1997 35.5186 39.495C35.9439 40.1624 36.3101 40.8653 36.6231 41.5978C36.5641 41.456 36.505 41.3142 36.4459 41.1725C36.9185 42.3007 37.2374 43.482 37.4028 44.6929C37.3792 44.5334 37.3615 44.3798 37.3378 44.2203C37.4205 44.8464 37.4619 45.4725 37.4619 46.1046C37.4619 47.0319 38.277 47.9179 39.2339 47.8766C40.1908 47.8411 41.0059 47.1028 41 46.1105Z" fill="black"/>
<path d="M32.7014 16.7076C32.6896 14.3509 31.9808 11.9527 30.6046 10.0272C29.1574 8.013 27.2141 6.51861 24.881 5.66214C20.4274 4.03781 15.0877 5.47904 12.0753 9.14117C10.4805 11.0786 9.53548 13.3644 9.32874 15.8748C9.13382 18.2493 9.74221 20.7301 10.9885 22.762C12.1758 24.7052 13.9596 26.3591 16.0446 27.2924C18.3837 28.3438 20.9058 28.645 23.428 28.1488C28.0056 27.2392 31.8213 23.3113 32.5301 18.6864C32.6247 18.0366 32.7014 17.3751 32.7014 16.7076C32.7073 15.7803 31.8863 14.8943 30.9294 14.9356C29.9666 14.977 29.1633 15.7153 29.1574 16.7076C29.1574 17.1447 29.1279 17.5877 29.0688 18.0189C29.0925 17.8594 29.1102 17.7058 29.1338 17.5464C29.0157 18.4206 28.7853 19.2652 28.4486 20.0803C28.5077 19.9386 28.5668 19.7968 28.6258 19.655C28.3837 20.228 28.0883 20.7832 27.7398 21.303C27.6512 21.433 27.5626 21.557 27.474 21.6869C27.2732 21.9586 27.5449 21.6633 27.5626 21.5747C27.5508 21.6279 27.4563 21.7106 27.415 21.7578C27.2082 22.0059 26.9956 22.2481 26.7652 22.4725C26.5467 22.6911 26.3163 22.8978 26.0801 23.0986C26.021 23.1459 25.956 23.1931 25.897 23.2463C25.7493 23.3703 26.2986 22.951 26.0092 23.1577C25.8851 23.2463 25.7552 23.3408 25.6312 23.4235C25.0996 23.7779 24.5384 24.0791 23.9478 24.3331C24.0895 24.2741 24.2313 24.215 24.373 24.1559C23.5579 24.4926 22.7133 24.723 21.8391 24.8411C21.9986 24.8175 22.1521 24.7998 22.3116 24.7761C21.4374 24.8884 20.5573 24.8884 19.6831 24.7761C19.8426 24.7998 19.9962 24.8175 20.1557 24.8411C19.2815 24.723 18.4368 24.4926 17.6217 24.1559C17.7635 24.215 17.9052 24.2741 18.047 24.3331C17.474 24.091 16.9188 23.7956 16.399 23.4471C16.2691 23.3585 16.145 23.2699 16.0151 23.1813C15.7434 22.9805 16.0387 23.2522 16.1273 23.2699C16.0742 23.2581 15.9915 23.1636 15.9442 23.1223C15.6961 22.9155 15.454 22.7029 15.2295 22.4725C15.011 22.254 14.8042 22.0236 14.6034 21.7874C14.5561 21.7283 14.5089 21.6633 14.4557 21.6042C14.3317 21.4566 14.7511 22.0059 14.5443 21.7165C14.4557 21.5924 14.3612 21.4625 14.2785 21.3384C13.9241 20.8068 13.6229 20.2457 13.3689 19.655C13.428 19.7968 13.487 19.9386 13.5461 20.0803C13.2094 19.2652 12.9791 18.4206 12.8609 17.5464C12.8846 17.7058 12.9023 17.8594 12.9259 18.0189C12.8137 17.1447 12.8137 16.2646 12.9259 15.3904C12.9023 15.5499 12.8846 15.7035 12.8609 15.863C12.9791 14.9888 13.2094 14.1441 13.5461 13.329C13.487 13.4708 13.428 13.6125 13.3689 13.7543C13.6111 13.1813 13.9064 12.6261 14.2549 12.1063C14.3435 11.9764 14.4321 11.8523 14.5207 11.7224C14.7215 11.4507 14.4498 11.746 14.4321 11.8346C14.4439 11.7815 14.5384 11.6988 14.5798 11.6515C14.7865 11.4034 14.9991 11.1613 15.2295 10.9368C15.4481 10.7183 15.6784 10.5115 15.9147 10.3107C15.9737 10.2634 16.0387 10.2162 16.0978 10.163C16.2455 10.039 15.6961 10.4584 15.9856 10.2516C16.1096 10.163 16.2395 10.0685 16.3636 9.98583C16.8952 9.63143 17.4563 9.33019 18.047 9.0762C17.9052 9.13527 17.7635 9.19433 17.6217 9.2534C18.4368 8.91672 19.2815 8.68636 20.1557 8.56823C19.9962 8.59185 19.8426 8.60957 19.6831 8.6332C20.5573 8.52097 21.4374 8.52097 22.3116 8.6332C22.1521 8.60957 21.9986 8.59185 21.8391 8.56823C22.7133 8.68636 23.5579 8.91672 24.373 9.2534C24.2313 9.19433 24.0895 9.13527 23.9478 9.0762C24.5207 9.31837 25.0759 9.61371 25.5957 9.9622C25.7257 10.0508 25.8497 10.1394 25.9796 10.228C26.2514 10.4288 25.956 10.1571 25.8674 10.1394C25.9206 10.1512 26.0033 10.2457 26.0505 10.2871C26.2986 10.4938 26.5408 10.7064 26.7652 10.9368C26.9838 11.1553 27.1905 11.3857 27.3913 11.622C27.4386 11.681 27.4858 11.746 27.539 11.8051C27.663 11.9527 27.2437 11.4034 27.4504 11.6929C27.539 11.8169 27.6335 11.9468 27.7162 12.0709C28.0706 12.6025 28.3718 13.1636 28.6258 13.7543C28.5668 13.6125 28.5077 13.4708 28.4486 13.329C28.7853 14.1441 29.0157 14.9888 29.1338 15.863C29.1102 15.7035 29.0925 15.5499 29.0688 15.3904C29.122 15.8275 29.1515 16.2646 29.1574 16.7017C29.1633 17.6291 29.9726 18.5151 30.9294 18.4737C31.8863 18.4383 32.7073 17.6999 32.7014 16.7076Z" fill="black"/>
<path d="M22.8784 35.2324C21.6262 35.2324 20.374 35.2324 19.1218 35.2324C19.6298 35.5277 20.1437 35.8171 20.6516 36.1125C19.9665 34.6949 19.2813 33.2714 18.602 31.8538C18.5016 31.6529 18.4071 31.4462 18.3067 31.2454C17.7987 32.1314 17.2848 33.0233 16.7769 33.9093C19.2458 33.9093 21.7207 33.9093 24.1897 33.9093C24.5323 33.9093 24.8808 33.9093 25.2234 33.9093C24.7154 33.0233 24.2015 32.1314 23.6936 31.2454C23.0084 32.663 22.3232 34.0865 21.644 35.5041C21.5435 35.7049 21.449 35.9116 21.3486 36.1125C21.136 36.5496 21.0356 36.9985 21.1714 37.4769C21.2836 37.8904 21.6026 38.3334 21.9865 38.5342C22.778 38.9536 23.983 38.7882 24.4083 37.8963C25.0934 36.4787 25.7786 35.0552 26.4579 33.6376C26.5583 33.4367 26.6528 33.23 26.7532 33.0292C27.0367 32.4444 27.0958 31.8183 26.7532 31.2395C26.4402 30.702 25.8495 30.3594 25.2234 30.3594C22.7544 30.3594 20.2795 30.3594 17.8105 30.3594C17.4679 30.3594 17.1194 30.3594 16.7769 30.3594C16.1507 30.3594 15.5601 30.6961 15.247 31.2395C14.9044 31.8183 14.9635 32.4444 15.247 33.0292C15.9322 34.4468 16.6174 35.8703 17.2966 37.2879C17.3971 37.4887 17.4916 37.6955 17.592 37.8963C17.8578 38.4456 18.537 38.7764 19.1218 38.7764C20.374 38.7764 21.6262 38.7764 22.8784 38.7764C23.8058 38.7764 24.6918 37.9613 24.6504 37.0044C24.6091 36.0475 23.8708 35.2324 22.8784 35.2324Z" fill="#A973FF"/>
<path d="M23.9586 50.0326C22.73 51.143 21.5014 52.2594 20.2669 53.3699C20.0897 53.5293 19.9184 53.6888 19.7412 53.8424C20.574 53.8424 21.4128 53.8424 22.2456 53.8424C21.017 52.7319 19.7885 51.6156 18.554 50.5051C18.3768 50.3456 18.2055 50.1862 18.0283 50.0326C18.1818 50.6055 18.3295 51.1844 18.4831 51.7573C18.7489 50.1389 19.0147 48.5264 19.2746 46.908C19.6939 44.3563 20.1074 41.7987 20.5268 39.247C20.6213 38.6563 20.7217 38.0657 20.8162 37.475C20.2492 37.9062 19.6762 38.3433 19.1092 38.7745C20.3614 38.7745 21.6136 38.7745 22.8658 38.7745C22.2988 38.3433 21.7258 37.9062 21.1588 37.475C21.4246 39.0934 21.6904 40.706 21.9503 42.3244C22.3697 44.8761 22.7831 47.4336 23.2025 49.9853C23.297 50.576 23.3974 51.1667 23.4919 51.7573C23.5569 52.1708 23.9645 52.6138 24.3071 52.8146C24.6851 53.0391 25.2462 53.1336 25.6715 52.9918C26.1027 52.856 26.5161 52.5902 26.7288 52.1767C26.9651 51.7278 26.9887 51.3084 26.906 50.8123C26.6402 49.1938 26.3744 47.5813 26.1145 45.9629C25.6951 43.4112 25.2817 40.8536 24.8623 38.3019C24.7678 37.7113 24.6674 37.1206 24.5729 36.5299C24.5138 36.1637 24.2125 35.8211 23.9349 35.6085C23.6278 35.3722 23.2616 35.2305 22.8599 35.2305C21.6077 35.2305 20.3555 35.2305 19.1033 35.2305C18.7075 35.2305 18.3354 35.3722 18.0283 35.6085C17.7507 35.8211 17.4494 36.1696 17.3903 36.5299C17.2486 37.41 17.1009 38.2901 16.9592 39.1643C16.6579 41.0131 16.3567 42.8619 16.0495 44.7107C15.7837 46.3468 15.5179 47.9771 15.2462 49.6132C15.1872 49.9912 15.1222 50.3634 15.0631 50.7414C15.0572 50.765 15.0572 50.7886 15.0513 50.8064C14.9627 51.3734 15.004 51.8814 15.3703 52.3539C15.4943 52.5134 15.4648 52.4839 15.5356 52.5547C15.6892 52.7083 15.8605 52.8501 16.02 52.9918C17.225 54.0787 18.4299 55.1714 19.6349 56.2582C19.6644 56.2877 19.6939 56.3114 19.7235 56.3409C20.4618 57.0084 21.4896 57.0084 22.2279 56.3409C23.4565 55.2305 24.6851 54.1141 25.9196 53.0036C26.0968 52.8442 26.2681 52.6847 26.4453 52.5311C27.1363 51.905 27.1245 50.6528 26.4453 50.0267C25.7306 49.3533 24.6969 49.3651 23.9586 50.0326Z" fill="#A973FF"/>
</svg>


                </div>
                <div class="title uppercase font-tt-eb text-16 spacing-12 text-brand-violet mb-20">
                Pagamento con carta docenti
                </div>
                <div class="text font-ave-m text-20 leading-15 text-gray">
                Possibilità di utilizzare il bonus destinato ai professori delle scuole medie e superiori per la propria formazione.
                </div>
            </div> <!-- item -->





            <div class="item flex flex-col p-60 rounded-24 border-gray border-2 border-solid">
                <div class="icon mb-36">
                <svg class="h-60" width="88" height="60" viewBox="0 0 88 60" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M14.5391 25.0273L27.5391 30.5273" stroke="#A973FF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M24.0391 22.0273L37.0391 27.5273" stroke="#A973FF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M36.0391 17.0273L49.0391 22.5273" stroke="#A973FF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M44.0391 14.0273L57.0391 19.5273" stroke="#A973FF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M52.0391 11.0273L65.0391 16.5273" stroke="#A973FF" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M3 28.5L56.1758 9.47381C57.0329 9.16714 57.9689 9.162 58.8294 9.45924L85 18.5M3 28.5L30.5498 38.9499C31.4828 39.3038 32.5143 39.2962 33.4419 38.9287L85 18.5M3 28.5V36.8262C3 38.4446 3.97519 39.9035 5.47059 40.5223L30.4682 50.8661C31.4489 51.272 32.5508 51.2714 33.5311 50.8644L82.5335 30.5238C84.0267 29.904 85 28.4462 85 26.8295V18.5" stroke="#192329" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
</svg>


                </div>
                <div class="title uppercase font-tt-eb text-16 spacing-12 text-brand-violet mb-20">
                Sconti su acquisto strumenti
                </div>
                <div class="text font-ave-m text-20 leading-15 text-gray">
                Attraverso le nostre partnership è possibile risparmiare sull’acquisto di attrezzatura e strumenti musicali.
                </div>
            </div> <!-- item -->



        </div>

    </div>

    </div>
</section>





<section class="home-social mb-140">
    <div class="container-l mx-auto">
    <div class="intro flex flex-col text-center items-center mb-100">
    <div class="pretitle mb-36 uppercase font-tt-eb text-16 spacing-12 text-gradient-pink-violet">
    Seguici sui social
    </div>
    <div class="title mb-20 font-din-bc text-60 max-w-1000">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sapien libero
    </div>
    <div class="text font-ave-d text-20 leading-15 text-gray max-w-800">
    Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sapien libero, ultrices sed elit id, rhoncus sagittis augue.
    </div>

    </div>
    
    <div class="cta mb-120">

    <div class="items grid grid-cols-4 gap-20">


    <a href="" target="_blank" class="button h-260 w-100-100 rounded-24 bg-white text-center border-4 border-solid border-gray border-animated-instagram overflow-hidden hover-up-2 hover-scale hover-shadow-instagram">
        <div class="inner flex flex-col place-center h-100-100 w-100-100">
            <div class="icon mb-28">
            <svg class="h-28 width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_597_1062)">
<path d="M15.9287 0H6.07108C2.72349 0 0 2.72362 0 6.0712V15.9288C0 19.2766 2.72349 22 6.07108 22H15.9287C19.2766 22 22 19.2764 22 15.9288V6.0712C22.0001 2.72362 19.2766 0 15.9287 0ZM20.0482 15.9288C20.0482 18.2002 18.2002 20.048 15.9288 20.048H6.07108C3.79979 20.0482 1.95195 18.2002 1.95195 15.9288V6.0712C1.95195 3.79992 3.79979 1.95195 6.07108 1.95195H15.9287C18.2001 1.95195 20.048 3.79992 20.048 6.0712L20.0482 15.9288Z" fill="#192329"/>
<path d="M10.997 5.33203C7.87112 5.33203 5.32812 7.87503 5.32812 11.0009C5.32812 14.1266 7.87112 16.6695 10.997 16.6695C14.1228 16.6695 16.6658 14.1266 16.6658 11.0009C16.6658 7.87503 14.1228 5.33203 10.997 5.33203ZM10.997 14.7174C8.94755 14.7174 7.28007 13.0502 7.28007 11.0008C7.28007 8.9512 8.94742 7.28385 10.997 7.28385C13.0465 7.28385 14.7138 8.9512 14.7138 11.0008C14.7138 13.0502 13.0464 14.7174 10.997 14.7174Z" fill="#192329"/>
<path d="M16.9119 3.67578C16.5358 3.67578 16.1664 3.82804 15.9008 4.0948C15.634 4.36026 15.4805 4.72983 15.4805 5.10721C15.4805 5.48341 15.6341 5.85286 15.9008 6.11962C16.1663 6.38508 16.5358 6.53863 16.9119 6.53863C17.2893 6.53863 17.6576 6.38508 17.9243 6.11962C18.1911 5.85286 18.3433 5.48329 18.3433 5.10721C18.3433 4.72983 18.1911 4.36026 17.9243 4.0948C17.6588 3.82804 17.2893 3.67578 16.9119 3.67578Z" fill="#192329"/>
</g>
<defs>
<clipPath id="clip0_597_1062">
<rect width="22" height="22" fill="white"/>
</clipPath>
</defs>
</svg>

            </div>
            <div class="text font-ave-d text-22 mb-20">
            Instagram
</div>
<div class="handle font-ave-d text-gray text-18">
    @produzionehiphop
</div>
        </div>
</a>


<a href="" target="_blank" class="button h-260 w-100-100 rounded-24 bg-white text-center border-4 border-solid border-gray border-animated-tiktok overflow-hidden hover-up-2 hover-shadow-tiktok">
        <div class="inner flex flex-col place-center h-100-100 w-100-100">
            <div class="icon mb-28">
            <svg class="h-28" width="17" height="22" viewBox="0 0 17 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M17 9.20169C15.3289 9.2058 13.6988 8.66934 12.3399 7.66809V14.6511C12.3395 15.9444 11.9555 17.2068 11.2392 18.2694C10.523 19.332 9.50867 20.1442 8.33186 20.5974C7.15509 21.0505 5.87197 21.1231 4.65406 20.8053C3.43615 20.4876 2.3415 19.7946 1.5165 18.8192C0.691494 17.8437 0.175453 16.6323 0.0373795 15.3468C-0.100703 14.0613 0.145765 12.763 0.743827 11.6256C1.34188 10.4882 2.26302 9.56583 3.38408 8.98185C4.50512 8.39788 5.77266 8.18013 7.01718 8.35772V11.8699C6.44768 11.6855 5.83614 11.6911 5.2699 11.8858C4.70365 12.0805 4.21165 12.4544 3.86417 12.9541C3.51668 13.4539 3.33147 14.0538 3.33499 14.6684C3.3385 15.2829 3.53056 15.8806 3.88375 16.376C4.23693 16.8715 4.73317 17.2394 5.30161 17.4272C5.87005 17.6151 6.4816 17.6132 7.04895 17.4219C7.61629 17.2306 8.11041 16.8597 8.46074 16.3621C8.81106 15.8645 8.99972 15.2657 8.99972 14.6511V1H12.3399C12.3377 1.29036 12.3613 1.58033 12.4105 1.86624C12.5266 2.50447 12.7679 3.11163 13.1198 3.65057C13.4716 4.1895 13.9266 4.64889 14.4568 5.00062C15.2111 5.51407 16.0956 5.78774 17 5.78754V9.20169Z" fill="#192329"/>
</svg>

            </div>
            <div class="text font-ave-d text-22 mb-20">
            TikTok
</div>
<div class="handle font-ave-d text-gray text-18">
    @produzionehiphop
</div>
        </div>
</a>


<a href="" target="_blank" class="button h-260 w-100-100 rounded-24 bg-white text-center border-4 border-solid border-gray border-animated-youtube overflow-hidden hover-up-2 hover-shadow-youtube">
        <div class="inner flex flex-col place-center h-100-100 w-100-100">
            <div class="icon mb-28">
            <svg class="h-28" width="26" height="22" viewBox="0 0 26 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M25.8548 5.24706C25.8548 5.02354 25.5147 3.2353 24.7209 2.45294C23.7003 1.3353 22.5663 1.22353 21.9992 1.22353H21.8859C18.3706 1 13.1542 1 13.0408 1C13.0408 1 7.71109 1 4.19574 1.22353H4.08235C3.51535 1.22353 2.38137 1.3353 1.36079 2.45294C0.566992 3.34706 0.226797 5.1353 0.226797 5.35883C0.226797 5.47059 0 7.48237 0 9.6059V11.5059C0 13.6294 0.226797 15.6412 0.226797 15.753C0.226797 15.9765 0.566992 17.7647 1.36079 18.547C2.26797 19.553 3.40196 19.6647 4.08235 19.7765C4.19574 19.7765 4.30913 19.7765 4.42253 19.7765C6.46371 20 12.7006 20 12.9274 20C12.9274 20 18.2572 20 21.7725 19.7765H21.8859C22.4529 19.6647 23.5869 19.553 24.6074 18.547C25.4012 17.653 25.7414 15.8647 25.7414 15.6412C25.7414 15.5294 25.9683 13.5177 25.9683 11.3942V9.49414C26.0816 7.48237 25.8548 5.35883 25.8548 5.24706ZM17.4634 10.7236L10.6595 14.3C10.5461 14.3 10.5461 14.4119 10.4327 14.4119C10.3193 14.4119 10.2059 14.4119 10.2059 14.3C10.0925 14.1883 9.97906 14.0765 9.97906 13.853V6.58825C9.97906 6.36472 10.0925 6.25295 10.2059 6.14119C10.3193 6.02942 10.5461 6.02942 10.7728 6.14119L17.5768 9.71767C17.8035 9.82943 17.917 9.9412 17.917 10.1647C17.917 10.3883 17.6901 10.6117 17.4634 10.7236Z" fill="#192329"/>
</svg>

            </div>
            <div class="text font-ave-d text-22 mb-20">
            YouTube
</div>
<div class="handle font-ave-d text-gray text-18">
    @produzionehiphop
</div>
        </div>
</a>



<a href="" target="_blank" class="button h-260 w-100-100 rounded-24 bg-white text-center border-4 border-solid border-gray border-animated-facebook overflow-hidden hover-up-2 hover-shadow-facebook">
        <div class="inner flex flex-col place-center h-100-100 w-100-100">
            <div class="icon mb-28">
            <svg class="h-28" width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_597_1068)">
<path d="M22.0378 11.0404C22.0378 4.94301 17.0948 0 10.9974 0C4.90004 0 -0.0429688 4.94301 -0.0429688 11.0404C-0.0429688 16.2179 3.52176 20.5625 8.33052 21.7558V14.4144H6.05399V11.0404H8.33052V9.58661C8.33052 5.82889 10.0312 4.08716 13.7204 4.08716C14.42 4.08716 15.6269 4.2245 16.1206 4.3614V7.4196C15.8601 7.39221 15.4074 7.37853 14.8452 7.37853C13.0351 7.37853 12.3355 8.06435 12.3355 9.84716V11.0404H15.9418L15.3222 14.4144H12.3355V22C17.8023 21.3398 22.0383 16.6851 22.0383 11.0404H22.0378Z" fill="#192329"/>
</g>
<defs>
<clipPath id="clip0_597_1068">
<rect width="22" height="22" fill="white"/>
</clipPath>
</defs>
</svg>


            </div>
            <div class="text font-ave-d text-22 mb-20">
            Facebook
</div>
<div class="handle font-ave-d text-gray text-18">
    @produzionehiphop
</div>
        </div>
</a>

    </div>

    </div>
    <div class="content mb--60 max-w-1200 mx-auto">

    <div class="items grid grid-cols-3 gap-40">


        <div class="item flex items-center gap-28">
            <div class="icon flex">
            <svg class="h-72" width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="70" height="70" rx="15" fill="#EEFAFE"/>
<path d="M50 40V46.6667C50 47.5507 49.6488 48.3986 49.0237 49.0237C48.3986 49.6488 47.5507 50 46.6667 50H23.3333C22.4493 50 21.6014 49.6488 20.9763 49.0237C20.3512 48.3986 20 47.5507 20 46.6667V40" stroke="black" stroke-width="3.33333" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M26.668 31.668L35.0013 40.0013L43.3346 31.668" stroke="black" stroke-width="3.33333" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M35 40V20" stroke="black" stroke-width="3.33333" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

            </div>
            <div class="text font-ave-d text-20">
            Risorse gratuite
            </div>
        </div> <!-- item -->

        <div class="item flex items-center gap-28">
            <div class="icon flex">
            <svg class="h-72" width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="70" height="70" rx="15" fill="#E8EDFB"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M37.0746 46.0281H32.4912L28.605 52.0544C28.1116 52.8196 27.1046 53.063 26.3162 52.6078L26.2452 52.5668C25.4677 52.1179 25.2013 51.1237 25.6502 50.3462C25.6635 50.3232 25.6774 50.3004 25.6919 50.278L28.4325 46.0281H17.7075C16.7645 46.0281 16 45.2636 16 44.3206C16 43.3775 16.7645 42.613 17.7075 42.613V22.1226C17.7075 20.2365 19.2365 18.7075 21.1226 18.7075H26.391C26.7426 17.7127 27.6913 17 28.8065 17H40.7593C41.8745 17 42.8232 17.7127 43.1748 18.7075H48.4432C50.3293 18.7075 51.8582 20.2365 51.8582 22.1226V42.613C52.8013 42.613 53.5658 43.3775 53.5658 44.3206C53.5658 45.2636 52.8013 46.0281 51.8582 46.0281H41.1333L43.8739 50.278C43.8884 50.3004 43.9023 50.3232 43.9156 50.3462C44.3645 51.1237 44.0981 52.1179 43.3206 52.5668L43.2496 52.6078C42.4611 53.063 41.4542 52.8196 40.9608 52.0544L37.0746 46.0281ZM26.2452 22.1226H21.1226V42.613H48.4432V22.1226H43.3206V22.9764C43.3206 24.3909 42.1738 25.5377 40.7593 25.5377H28.8065C27.3919 25.5377 26.2452 24.3909 26.2452 22.9764V22.1226ZM28.0374 33.9928C27.5172 34.472 26.7071 34.4387 26.2279 33.9185C25.7487 33.3983 25.7819 32.5881 26.3021 32.1089C27.7039 30.8176 28.7759 30.0653 29.6674 29.8306C31.2144 29.4234 32.2153 30.4769 32.2153 32.0598C32.2153 33.6288 32.5637 33.9243 32.9549 33.5418C33.1868 33.315 33.4194 33.0553 33.8054 32.6009C34.8292 31.3959 35.0957 31.1086 35.6447 30.739C36.7518 29.9936 37.9999 30.1594 38.7821 31.3613C39.0056 31.7046 39.1702 32.0702 39.281 32.4548C39.4982 33.2085 39.5006 33.8399 39.3931 34.81C39.3843 34.89 39.3769 34.9572 39.3707 35.0143C39.561 34.9936 39.7848 34.7369 40.3271 34.057C41.2056 32.9557 41.537 32.1289 41.4498 31.6191C41.3304 30.9219 41.7988 30.26 42.496 30.1407C43.1931 30.0214 43.855 30.4898 43.9743 31.1869C44.2099 32.563 43.6187 34.0379 42.3294 35.6542C40.5653 37.8657 38.9201 38.0097 37.4372 36.8642C36.9577 36.4939 36.7944 36.004 36.7816 35.4366C36.7759 35.1871 36.7829 35.1101 36.8474 34.5279C36.9204 33.8692 36.919 33.5082 36.8199 33.1641C36.8128 33.1397 36.8053 33.1156 36.7972 33.0918C36.5877 33.2925 36.2989 33.6219 35.7574 34.2592C35.3228 34.7708 35.0499 35.0756 34.7455 35.3732C32.4411 37.6262 29.9592 35.7641 29.68 32.647C29.2615 32.9294 28.7032 33.3795 28.0374 33.9928ZM29.6603 20.4151V22.1226H39.9055V20.4151H29.6603Z" fill="black"/>
</svg>


            </div>
            <div class="text font-ave-d text-20">
            Tutorial
            </div>
        </div> <!-- item -->

        <div class="item flex items-center gap-28">
            <div class="icon flex">
            <svg class="h-72" width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="70" height="70" rx="15" fill="#FFECEC"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M20.9589 32.2188H26.7124V48.9997H20.9589C20.4293 48.9997 20 48.4218 20 47.7089V33.5096C20 32.7967 20.4293 32.2188 20.9589 32.2188Z" stroke="black" stroke-width="3.3562" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M26.7109 33.4774L32.2481 22.7491C33.0661 21.164 35.0142 20.5423 36.5993 21.3604C36.8954 21.5132 37.1661 21.711 37.4017 21.9466C38.3458 22.8907 38.8762 24.1712 38.8762 25.5064V30.5407H47.6876C48.4096 30.5407 49.0969 30.8507 49.5747 31.392C50.0525 31.9333 50.2749 32.6536 50.1853 33.3701L48.2975 46.7949C48.14 48.0545 47.0692 48.9998 45.7998 48.9998H26.7109" stroke="black" stroke-width="3.3562" stroke-linecap="round" stroke-linejoin="round"/>
</svg>

            </div>
            <div class="text font-ave-d text-20">
            Recensione prodotti
            </div>
        </div> <!-- item -->





        <div class="item flex items-center gap-28">
            <div class="icon flex">
            <svg class="h-72" width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="70" height="70" rx="15" fill="#FCEDF5"/>
<path d="M48.3432 25.574C49.8164 25.574 51.0107 26.7683 51.0107 28.2416V35.3551C51.0107 36.5165 50.2684 37.5046 49.2323 37.8708V48.6929C49.2323 50.6572 47.6399 52.2496 45.6756 52.2496H24.3351C22.3708 52.2496 20.7784 50.6572 20.7784 48.6929V37.8708C19.7423 37.5046 19 36.5165 19 35.3551V28.2416C19 26.7683 20.1943 25.574 21.6676 25.574H26.4942C24.8963 22.684 25.4688 19.1368 28.0697 17.6352C30.339 16.325 33.1849 17.1307 35.0054 19.2364C36.8258 17.1307 39.6717 16.325 41.941 17.6352C44.542 19.1368 45.1145 22.684 43.5165 25.574H48.3432ZM36.7837 48.6929H45.6756V38.0226H36.7837V48.6929ZM33.227 48.6929V38.0226H24.3351V48.6929H33.227ZM33.227 34.4659V29.1307H22.5567V34.4659H33.227ZM36.7837 34.4659H47.454V29.1307H36.7837V34.4659ZM32.5156 25.3357C33.2812 24.8938 33.4968 23.4783 32.722 22.1364C31.9472 20.7944 30.6136 20.2734 29.8481 20.7154C29.0825 21.1574 28.867 22.5728 29.6417 23.9148C30.4165 25.2567 31.7501 25.7777 32.5156 25.3357ZM37.4951 25.3357C38.2606 25.7777 39.5942 25.2567 40.369 23.9148C41.1438 22.5728 40.9282 21.1574 40.1626 20.7154C39.3971 20.2734 38.0635 20.7944 37.2887 22.1364C36.514 23.4783 36.7295 24.8938 37.4951 25.3357Z" fill="black"/>
</svg>


            </div>
            <div class="text font-ave-d text-20">
            Giveaway
            </div>
        </div> <!-- item -->

        <div class="item flex items-center gap-28">
            <div class="icon flex">
            <svg class="h-72"width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="70" height="70" rx="15" fill="#F6F1FF"/>
<path d="M52.2382 17.4763C52.0198 17.2864 51.7617 17.1478 51.4828 17.0705C51.2039 16.9932 50.9113 16.9793 50.6263 17.0296L31.2059 20.9137C30.7606 21.0045 30.3613 21.2486 30.0774 21.6034C29.7936 21.9583 29.6431 22.4014 29.6523 22.8558V39.3437C28.4513 38.723 27.12 38.3969 25.7682 38.3921C23.8394 38.2746 21.942 38.9223 20.4877 40.1948C19.0334 41.4673 18.1396 43.2619 18 45.1893C18.1396 47.1166 19.0334 48.9112 20.4877 50.1837C21.942 51.4562 23.8394 52.1039 25.7682 51.9864C27.697 52.1039 29.5944 51.4562 31.0486 50.1837C32.5029 48.9112 33.3968 47.1166 33.5363 45.1893V24.4482L49.0727 21.341V35.4596C47.8718 34.839 46.5404 34.5128 45.1886 34.508C43.2598 34.3905 41.3624 35.0383 39.9082 36.3107C38.4539 37.5832 37.56 39.3778 37.4204 41.3052C37.56 43.2325 38.4539 45.0271 39.9082 46.2996C41.3624 47.5721 43.2598 48.2198 45.1886 48.1023C47.1174 48.2198 49.0148 47.5721 50.4691 46.2996C51.9233 45.0271 52.8172 43.2325 52.9568 41.3052V18.9717C52.9549 18.6848 52.8895 18.4019 52.7652 18.1434C52.641 17.8848 52.461 17.657 52.2382 17.4763ZM25.7682 48.1023C23.6708 48.1023 21.8841 46.7623 21.8841 45.1893C21.8841 43.6162 23.6708 42.2762 25.7682 42.2762C27.8656 42.2762 29.6523 43.6162 29.6523 45.1893C29.6523 46.7623 27.8656 48.1023 25.7682 48.1023ZM45.1886 44.2182C43.0912 44.2182 41.3045 42.8782 41.3045 41.3052C41.3045 39.7321 43.0912 38.3921 45.1886 38.3921C47.286 38.3921 49.0727 39.7321 49.0727 41.3052C49.0727 42.8782 47.286 44.2182 45.1886 44.2182Z" fill="black"/>
</svg>


            </div>
            <div class="text font-ave-d text-20">
            Analisi brani famosi
            </div>
        </div> <!-- item -->

        <div class="item flex items-center gap-28">
            <div class="icon flex">
            <svg class="h-72" width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="70" height="70" rx="15" fill="#E7F3FD"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M19.0006 48.04C19.0006 45.8648 19.0003 30.4359 19.0001 22.9948C19 20.7856 20.7909 19 23.0001 19H48.0007C50.2098 19 52.0007 20.7856 52.0007 22.9947C52.0007 30.4358 52.0007 45.8648 52.0007 48.04C52.0007 50.227 50.139 52 47.8426 52H23.1586C20.8622 52 19.0006 50.227 19.0006 48.04Z" stroke="black" stroke-width="3.66667" stroke-linecap="round" stroke-linejoin="round"/>
<path fill-rule="evenodd" clip-rule="evenodd" d="M26.332 26.332V33.6654H44.6654V26.332H26.332Z" stroke="black" stroke-width="3.66667" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M26.332 40H37.332" stroke="black" stroke-width="3.66667" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M26 45H33" stroke="black" stroke-width="3.66667" stroke-linecap="round" stroke-linejoin="round"/>
</svg>


            </div>
            <div class="text font-ave-d text-20">
            Novità nel settore
            </div>
        </div> <!-- item -->






        <div class="item flex items-center gap-28">
            <div class="icon flex">
            <svg class="h-72" width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="70" height="70" rx="15" fill="#F5F2F1"/>
<path d="M51.1369 32.2135L47.8683 28.9449V24.305C47.8683 23.4593 47.5323 22.6482 46.9343 22.0502C46.3363 21.4521 45.5252 21.1162 44.6794 21.1162H40.0556L36.787 17.9273C36.1895 17.3334 35.3813 17 34.5388 17C33.6964 17 32.8881 17.3334 32.2907 17.9273L29.0221 21.1321H24.3822C23.5365 21.1321 22.7254 21.4681 22.1274 22.0661C21.5293 22.6641 21.1934 23.4752 21.1934 24.321V28.9449L18.0045 32.2135C17.6964 32.5029 17.4489 32.8507 17.2765 33.2367C17.1041 33.6226 17.0101 34.039 17 34.4616C16.9993 35.3037 17.3318 36.1119 17.9248 36.7098L21.2093 39.9784V44.6182C21.2093 45.4639 21.5453 46.275 22.1433 46.873C22.7413 47.4711 23.5524 47.807 24.3982 47.807H29.0221L32.2907 51.0756C32.8885 51.6686 33.6967 52.0011 34.5388 52.0004C35.3809 52.0011 36.1891 51.6686 36.787 51.0756L40.0556 47.7911H44.6954C45.5411 47.7911 46.3522 47.4551 46.9502 46.8571C47.5483 46.2591 47.8842 45.448 47.8842 44.6022V39.9784L51.1528 36.7098C51.7468 36.1123 52.0801 35.3041 52.0801 34.4616C52.0801 33.6192 51.7468 32.8109 51.1528 32.2135H51.1369ZM44.6794 38.6709V44.6022H38.7481L34.5388 48.8115L30.3295 44.6022H24.3982V38.6709L20.1889 34.4616L24.3982 30.2523V24.321H30.3295L34.5388 20.1117L38.7481 24.321H44.6794V30.2523L48.8887 34.4616L44.6794 38.6709Z" fill="black"/>
<path d="M38.1095 27.8433L28.5429 39.0044C28.4068 39.1635 28.3034 39.3479 28.2385 39.547C28.1737 39.7461 28.1487 39.956 28.165 40.1648C28.1979 40.5864 28.3969 40.9776 28.7183 41.2525C29.0397 41.5274 29.4571 41.6633 29.8787 41.6304C30.3003 41.5975 30.6916 41.3985 30.9664 41.0771L40.5331 29.9161C40.8079 29.5947 40.9439 29.1773 40.911 28.7557C40.8781 28.334 40.6791 27.9428 40.3577 27.6679C40.0363 27.393 39.6189 27.2571 39.1973 27.29C38.7757 27.3229 38.3844 27.5219 38.1095 27.8433Z" fill="black"/>
<path d="M31.2156 31.9901C31.6717 31.8584 32.0783 31.5938 32.3836 31.2302C32.6888 30.8666 32.879 30.4203 32.9297 29.9482C32.9805 29.4762 32.8897 28.9997 32.6687 28.5795C32.4478 28.1592 32.1068 27.8142 31.6891 27.5885C31.2714 27.3627 30.796 27.2664 30.3234 27.3117C29.8508 27.3571 29.4024 27.542 29.0352 27.8431C28.6681 28.1442 28.3989 28.5477 28.2619 29.0023C28.1249 29.4568 28.1263 29.9419 28.2659 30.3957C28.4538 30.9928 28.8669 31.4932 29.4176 31.7908C29.9682 32.0885 30.6131 32.16 31.2156 31.9901Z" fill="black"/>
<path d="M37.8607 36.9301C37.4046 37.0619 36.998 37.3265 36.6927 37.6901C36.3874 38.0537 36.1973 38.5 36.1465 38.972C36.0958 39.4441 36.1866 39.9206 36.4076 40.3408C36.6285 40.7611 36.9695 41.106 37.3872 41.3318C37.8049 41.5576 38.2803 41.6539 38.7529 41.6086C39.2255 41.5632 39.6739 41.3782 40.041 41.0772C40.4082 40.7761 40.6774 40.3726 40.8144 39.918C40.9514 39.4634 40.95 38.9784 40.8104 38.5246C40.6225 37.9275 40.2094 37.4271 39.6587 37.1295C39.1081 36.8318 38.4632 36.7603 37.8607 36.9301Z" fill="black"/>
</svg>


            </div>
            <div class="text font-ave-d text-20">
            Promozioni
            </div>
        </div> <!-- item -->

        <div class="item flex items-center gap-28">
            <div class="icon flex">
            <svg class="h-72" width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="70" height="70" rx="15" fill="#F6FBF0"/>
<path d="M40.2893 49.1592C40.2893 42.2339 43.5697 40.047 43.5697 40.047C48.1258 35.4908 48.3518 27.9714 43.7993 23.4153C41.612 21.2285 38.6457 20 35.5528 20C32.4598 20 29.4935 21.2285 27.3062 23.4153C22.7501 27.9714 22.9761 35.4908 27.5322 40.047C27.5322 40.047 30.8126 42.2339 30.8126 49.1592H40.2893Z" stroke="black" stroke-width="3.7" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M37.7332 54H33.3594" stroke="black" stroke-width="3.7" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M39.196 33.6719H31.9062" stroke="black" stroke-width="3.7" stroke-linecap="round" stroke-linejoin="round"/>
<path d="M35.5508 40.9617L35.5508 33.6719" stroke="black" stroke-width="3.7" stroke-linecap="round" stroke-linejoin="round"/>
</svg>



            </div>
            <div class="text font-ave-d text-20">
            Tips & tricks
            </div>
        </div> <!-- item -->

        <div class="item flex items-center gap-28">
            <div class="icon flex">
            <svg class="h-72" width="70" height="70" viewBox="0 0 70 70" fill="none" xmlns="http://www.w3.org/2000/svg">
<rect width="70" height="70" rx="15" fill="#FFF8EC"/>
<circle cx="25.5" cy="34.5" r="2.5" fill="black"/>
<circle cx="36.5" cy="34.5" r="2.5" fill="black"/>
<circle cx="47.5" cy="34.5" r="2.5" fill="black"/>
</svg>


            </div>
            <div class="text font-ave-d text-20">
            E molto altro, seguici!
            </div>
        </div> <!-- item -->



    </div>

    </div>
</div>
    <div class="visual overflow-hidden relative">
        <div class="cols flex gap-20 items-end absolute-center-x">


        <div class="col flex gap-20 flex-col w-280">
            <?php load_img(class: 'img w-100-100 rounded-8', high: 112, low: 112); ?>
            <?php load_img(class: 'img w-100-100 rounded-8', high: 112, low: 112); ?>
            </div>


            <div class="col flex gap-20 flex-col w-280">
                <?php load_img(class: 'img w-100-100 rounded-8', high: 111, low: 111); ?>
                <?php load_img(class: 'img w-100-100 rounded-8', high: 112, low: 112); ?>
            </div>

            <div class="col flex gap-20 flex-col w-280">
                <?php load_img(class: 'img w-100-100 rounded-8', high: 111, low: 111); ?>
            </div>

            <div class="col flex gap-20 flex-col w-280">
            <?php load_img(class: 'img w-100-100 rounded-8', high: 112, low: 112); ?>
            <?php load_img(class: 'img w-100-100 rounded-8', high: 112, low: 112); ?>
            </div>



            <div class="col flex gap-20 flex-col w-280">
                <?php load_img(class: 'img w-100-100 rounded-8', high: 111, low: 111); ?>
            </div>


            <div class="col flex gap-20 flex-col w-280">
            <?php load_img(class: 'img w-100-100 rounded-8', high: 112, low: 112); ?>
            <?php load_img(class: 'img w-100-100 rounded-8', high: 112, low: 112); ?>
            </div>



            <div class="col flex gap-20 flex-col w-280">
                <?php load_img(class: 'img w-100-100 rounded-8', high: 111, low: 111); ?>
            </div>

            <div class="col flex gap-20 flex-col w-280">
                <?php load_img(class: 'img w-100-100 rounded-8', high: 111, low: 111); ?>
                <?php load_img(class: 'img w-100-100 rounded-8', high: 112, low: 112); ?>
            </div>


            <div class="col flex gap-20 flex-col w-280">
            <?php load_img(class: 'img w-100-100 rounded-8', high: 112, low: 112); ?>
            <?php load_img(class: 'img w-100-100 rounded-8', high: 112, low: 112); ?>
            </div>



        </div>

        <div class="opacity-0 w-280">

        <?php load_img(class: 'img w-100-100', high: 111, low: 111); ?>

<?php load_img(class: 'img w-100-100', high: 112, low: 112); ?>
        </div>

    </div>
    
</section>  















<?php get_template_part('templates/parts/footer-cta'); ?>



<?php get_template_part('templates/parts/footer-classic'); ?>




<?php
get_sidebar();
get_footer();
