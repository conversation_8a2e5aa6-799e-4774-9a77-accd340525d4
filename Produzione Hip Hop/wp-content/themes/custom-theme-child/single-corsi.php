<?php
/**
 * The template for displaying all pages
 *
 * This is the template that displays all pages by default.
 * Please note that this is the WordPress construct of pages
 * and that other 'pages' on your WordPress site may use a
 * different template.
 *
 * @link https://developer.wordpress.org/themes/basics/template-hierarchy/
 *
 * @package Custom_theme
 */

get_header();

if ( have_posts() ) :
	while ( have_posts() ) :
		the_post();

		$corso_cover_id      = null;
		$corso_icona_id      = null;
		$corso_durata        = '';
		$corso_descrizione   = '';
		$color_class_name    = 'violet';
		$rwmb_get_value_func = 'rwmb_get_value';

		if ( function_exists( $rwmb_get_value_func ) ) {
			$corso_cover_data = $rwmb_get_value_func( 'corso_cover', [ 'size' => 'large' ] );
			$corso_cover_id   = $corso_cover_data['ID'] ?? null;

			$corso_icona_data = $rwmb_get_value_func( 'corso_icona' );
			$corso_icona_id   = $corso_icona_data['ID'] ?? null;

			$corso_durata      = $rwmb_get_value_func( 'corso_durata' );
			$corso_descrizione = $rwmb_get_value_func( 'corso_descrizione' );
			$db_color          = $rwmb_get_value_func( 'corso_colore' );
			if ( ! empty( $db_color ) ) {
				$color_class_name = $db_color;
			}
      $color_map = [
        'blue' => '#1289ED',
        'sky' => '#56CCF2', 
        'pink' => '#E04E9D',
        'violet' => '#A973FF',
        'brown' => '#9D8271',
        'green' => '#25D366',
        'yellow' => '#FFBB44',
        'red' => '#FF4444',
        'ocean' => '#194DD4'
      ];
      
      $color_map_medium = [
        'blue' => '#B2D9F9',
        'sky' => '#C8EFFB', 
        'pink' => '#F5C5DF',
        'violet' => '#E3D1FF',
        'brown' => '#DFD6D1',
        'green' => '#92E9B2',
        'yellow' => '#FFE9C2',
        'red' => '#FFC2C2',
        'ocean' => '#B4C5F1'
      ];
      
      $color_map_light = [
        'blue' => '#E7F3FD',
        'sky' => '#EEFAFE', 
        'pink' => '#FCEDF5',
        'violet' => '#F6F1FF',
        'brown' => '#F5F2F1',
        'green' => '#E9FBF0',
        'yellow' => '#FFF8EC',
        'red' => '#FFECEC',
        'ocean' => '#E8EDFB'
      ];




		}
		?>

<style>
button[name="custom_submit_button-1_5"] {
    border-color: <?php echo esc_attr( $color_map[ $color_class_name ] ); ?> !important;
}

button[name="custom_submit_button-1_5"]:hover {
    box-shadow: 0px 8px 28px rgba(<?php 
        $hex = $color_map[ $color_class_name ];
        $r = hexdec(substr($hex, 1, 2));
        $g = hexdec(substr($hex, 3, 2));
        $b = hexdec(substr($hex, 5, 2));
        echo "$r, $g, $b";
    ?>, var(--shadow-opacity, 0.45)) !important;
}
</style>

<?php get_template_part('templates/parts/nav'); ?>





<section class="page-header">

  <div class="container-xl mx-auto h-800 flex items-center px-80 rounded-24 bg-brand-dark relative">

    <div class="main max-w-800 relative z-60 flex flex-col items-start">
      <div
        class="pretitle uppercase font-tt-eb text-16 mb-36 spacing-12 text-brand-<?php echo esc_attr( $color_class_name ); ?>">
        Corso
      </div>
      <div class="title font-din-bc text-72 mb-36 text-white">
        <?php the_title(); ?>
      </div>

      <div class="trust flex gap-20 mb-40 items-center">
        <div class="photo flex items-center">

          <div class="item mr--6 h-36 w-36 overflow-hidden rounded-full relative">
            <?php load_img(class: 'img img-cover', high: 229); ?>
          </div>
          <div class="item mr--10 h-36 w-36 overflow-hidden rounded-full relative">
            <?php load_img(class: 'img img-cover', high: 230); ?>
          </div>
          <div class="item h-36 w-36 overflow-hidden rounded-full relative">
            <?php load_img(class: 'img img-cover', high: 231); ?>
          </div>


        </div>
        <div class="stars">
          <svg class="h-20" width="95" height="19" viewBox="0 0 95 19" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path
              d="M9.4391 0L11.5583 6.52226L18.4162 6.52226L12.8681 10.5532L14.9873 17.0755L9.4391 13.0445L3.89094 17.0755L6.01015 10.5532L0.461983 6.52226L7.31989 6.52226L9.4391 0Z"
              fill="#FFBB44" />
            <path
              d="M28.318 0L30.4372 6.52226L37.2951 6.52226L31.747 10.5532L33.8662 17.0755L28.318 13.0445L22.7698 17.0755L24.8891 10.5532L19.3409 6.52226L26.1988 6.52226L28.318 0Z"
              fill="#FFBB44" />
            <path
              d="M47.193 0L49.3122 6.52226L56.1701 6.52226L50.622 10.5532L52.7412 17.0755L47.193 13.0445L41.6448 17.0755L43.7641 10.5532L38.2159 6.52226L45.0738 6.52226L47.193 0Z"
              fill="#FFBB44" />
            <path
              d="M66.0719 0L68.1911 6.52226L75.049 6.52226L69.5009 10.5532L71.6201 17.0755L66.0719 13.0445L60.5237 17.0755L62.643 10.5532L57.0948 6.52226L63.9527 6.52226L66.0719 0Z"
              fill="#FFBB44" />
            <path
              d="M85.5602 0L87.6794 6.52226L94.5373 6.52226L88.9892 10.5532L91.1084 17.0755L85.5602 13.0445L80.012 17.0755L82.1312 10.5532L76.5831 6.52226L83.441 6.52226L85.5602 0Z"
              fill="#FFBB44" />
          </svg>

        </div>
        <div class="text font-ave-d text-18 text-white opacity-80">
          500+ Studenti
        </div>
      </div>

      <?php if ( ! empty( $corso_descrizione ) ) : ?>
      <div class="text text-white opacity-80 font-ave-m leading-15 text-20 mb-40 max-w-500">
        <?php echo esc_html( $corso_descrizione ); ?>
      </div>
      <?php endif; ?>



      <div class="info flex gap-48 mb-60">
        <?php if ( ! empty( $corso_durata ) ) : ?>
        <div class="item flex gap-20 items-center">
          <div class="icon flex">
            <svg class="h-28" width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
              <circle cx="13" cy="13" r="11.75" stroke="#fff" stroke-width="2.5" />
              <path d="M13 6.5V13L9 15.3094" stroke="#fff" stroke-width="2.5" stroke-linecap="round"
                stroke-linejoin="round" />
            </svg>

          </div>
          <div class="text font-ave-d text-20 text-white">
            <?php echo esc_html( $corso_durata ); ?>
          </div>
        </div> <!-- item -->
        <?php endif; ?>

        <div class="item flex gap-20 items-center">
          <div class="icon flex">
            <svg class="h-28" width="26" height="26" viewBox="0 0 26 26" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M25 13C25 6.4 19.6 1 13 1C6.4 1 1 6.4 1 13C1 19.6 6.4 25 13 25C19.6 25 25 19.6 25 13ZM18.3 10H22.6C22.9 10.9 23.1 12 23.1 13C23.1 14 22.9 15.1 22.6 16H18.4C18.5 15.1 18.6 14.1 18.6 13L18.3 10ZM21.6 8H17.8C17.3 6.2 16.7 4.6 15.9 3.5C18.4 4.2 20.4 5.9 21.6 8ZM13 23C12.4 22.7 11 21 10.2 18H15.9C15 21 13.6 22.6 13 23ZM9.7 16C9.6 15.1 9.5 14.1 9.5 13C9.5 11.9 9.6 10.9 9.7 10H16.3C16.4 10.9 16.5 11.9 16.5 13C16.5 14.1 16.4 15.1 16.3 16H9.7ZM13 3C13.5 3.3 15 4.9 15.9 8H10.2C11 4.9 12.5 3.2 13 3ZM10 3.5C9.2 4.6 8.6 6.2 8.1 8H4.4C5.6 5.9 7.6 4.2 10 3.5ZM7.7 16H3.5C3.2 15.1 3 14 3 13C3 12 3.2 10.9 3.5 10H7.8C7.7 11 7.6 12 7.6 13C7.5 14.1 7.6 15.1 7.7 16ZM4.4 18H8.1C8.6 19.9 9.3 21.4 10 22.5C7.6 21.8 5.6 20.1 4.4 18ZM16 22.5C16.8 21.3 17.5 19.8 17.9 18H21.6C20.4 20.1 18.4 21.8 16 22.5Z"
                fill="white" />
            </svg>



          </div>
          <div class="text font-ave-d text-20 text-white">
            Online
          </div>
        </div> <!-- item -->


      </div>

      <div
        class="button mt-auto h-72 w-80-100 rounded-16 text-center text-white border-brand-<?php echo esc_attr( $color_class_name ); ?> border-4 border-solid rounded-12 flex place-center z-20 relative cursor-pointer hover-shadow-<?php echo esc_attr( $color_class_name ); ?> hover-up-2">
        <div class="text font-ave-b text-20">
          Iscriviti ora
        </div>
      </div>


    </div>

    <div class="background absolute top-0 right-0 w-60-100 bottom-0 z-10 rounded-24 overflow-hidden">
      <?php if ( $corso_cover_id ) : ?>
      <?php load_img( class: 'img img-cover', high: $corso_cover_id ); ?>
      <?php endif; ?>
    </div>


    <div class="gradients absolute inset-0 z-40 overflow-hidden rounded-24">

      <div
        class="radial-gradient-<?php echo esc_attr( $color_class_name ); ?> animate-float-around animation-duration-8000 absolute z-20 h-800 w-800 left--400 top--300 opacity-60">
      </div>
      <div
        class="radial-gradient-<?php echo esc_attr( $color_class_name ); ?> absolute z-20 h-800 w-800 right--400 bottom--300">
      </div>
    </div>



    <div class="shapes z-50 absolute inset-0">

      <div class="shape-1 animate-float-updown-light animation-duration-5000 absolute left--40 top-40">
        <svg class="h-80 shadow-<?php echo esc_attr( $color_class_name ); ?>" width="132" height="140"
          viewBox="0 0 132 140" fill="none" xmlns="http://www.w3.org/2000/svg">
          <g clip-path="url(#clip0_596_641)">
            <path
              d="M126.867 77.9576L14.6966 3.41429C8.44291 -0.741649 0.286323 4.73807 1.76969 12.0988L26.0817 132.74C27.1921 138.25 33.2824 141.149 38.2596 138.538L126.118 92.4404C131.789 89.4651 132.201 81.502 126.867 77.9576Z"
              fill="<?php echo esc_attr( $color_map[ $color_class_name ] ); ?>" />
          </g>
          <defs>
            <clipPath id="clip0_596_641">
              <rect width="131" height="140" fill="white" transform="translate(0.9375)" />
            </clipPath>
          </defs>
        </svg>


      </div>

    </div>

  </div>

</section>







<section class="corso-content mb-140 relative z-40">

  <div class="container-l mx-auto grid grid-cols-12 gap-140">
    <div class="main col-span-7 pt-120">

      <div class="gutenberg">
        <?php
// Ora che abbiamo il hook globale, possiamo semplicemente usare the_content()
// Il hook 'render_block' gestirà automaticamente tutti i blocchi Meta Box ovunque si trovino
the_content();
?>







      </div>

    </div>

    <div class="side col-span-5 pb-100">

      <div class="box relative w-500 sticky top-140 mt--80">




        <svg class="svg-white-top w-100-100 h-auto absolute z-20 top--88 left-0" width="500" height="99"
          viewBox="0 0 500 99" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M0 68.1671C0 60.5261 5.74388 54.1062 13.3379 53.2595L483.338 0.85771C492.225 -0.133171 500 6.8229 500 15.7653L500 99H0L0 68.1671Z"
            fill="white" />
        </svg>

        <svg class="svg-color-top w-110-100 h-auto absolute z-10 top--88 left--5-100" width="550" height="97"
          viewBox="0 0 550 97" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M1.60957e-08 25.0598C7.52248e-09 10.114 13.0271 -1.49254 27.8738 0.22548L527.874 58.0842C540.485 59.5435 550 70.2232 550 82.9185V97H5.73623e-08L1.60957e-08 25.0598Z"
            fill="<?php echo esc_attr( $color_map[ $color_class_name ] ); ?>" />
        </svg>





        <svg class="svg-white-bottom w-100-100 h-auto absolute z-20 bottom--88 left-0 reflect-y" width="500" height="99"
          viewBox="0 0 500 99" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M0 68.1671C0 60.5261 5.74388 54.1062 13.3379 53.2595L483.338 0.85771C492.225 -0.133171 500 6.8229 500 15.7653L500 99H0L0 68.1671Z"
            fill="white" />
        </svg>

        <svg class="svg-color-bottom w-110-100 h-auto absolute z-10 bottom--88 left--5-100 reflect-y" width="550"
          height="97" viewBox="0 0 550 97" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M1.60957e-08 25.0598C7.52248e-09 10.114 13.0271 -1.49254 27.8738 0.22548L527.874 58.0842C540.485 59.5435 550 70.2232 550 82.9185V97H5.73623e-08L1.60957e-08 25.0598Z"
            fill="<?php echo esc_attr( $color_map[ $color_class_name ] ); ?>" />
        </svg>







        <div class="inner relative z-40 px-60 pb-60 flex flex-col items-center text-center">


          <div class="back absolute left--5-100 w-110-100 h-100-100 right--5-100 z-20 overflow-hidden">
            <div class="bg-brand-<?php echo esc_attr( $color_class_name ); ?> w-100-100 h-100-100 shadow-24"></div>
          </div>

          <div class="background inset-0 absolute overflow-hidden px-20 mx--20 z-30">
            <div class="bg-white w-100-100 h-100-100 shadow-24"></div>

          </div>

          <div class="content relative z-50">

            <div class="icon mt--140 mb-60">


              <?php if ( $corso_icona_id ) : ?>
              <?php load_img( class: 'img h-180 animate-float-updown-very-light animation-duration-5000 shadow-' . $color_class_name, high: $corso_icona_id ); ?>
              <?php endif; ?>



            </div>
            <div class="title font-din-bc text-60 mb-36">
              <?php the_title(); ?>
            </div>
            <div class="text font-ave-m text-20 text-gray mb-40 leading-15">
              <?php echo esc_html( $corso_descrizione ); ?>
            </div>

            <div
              class="button mt-auto h-72 w-100-100 rounded-16 text-center border-brand-<?php echo esc_attr( $color_class_name ); ?> border-4 border-solid rounded-12 flex place-center z-20 relative cursor-pointer hover-shadow-<?php echo esc_attr( $color_class_name ); ?> hover-up-2">
              <div class="text font-ave-b text-20">
                Iscriviti ora
              </div>
            </div>
          </div> <!-- content -->



        </div> <!-- inner -->




      </div>

    </div>





  </div>
</section>







<section class="corso-cta bg-brand-dark mb-140 relative">


  <div class="background absolute top-0 right-0 w-60-100 h-700 z-10 rounded-24 overflow-hidden">
    <div class="overlay overlay-dark-left inset-0 absolute z-50"></div>
    <div class="overlay overlay-dark-bottom inset-0 absolute z-50"></div>
    <?php if ( $corso_cover_id ) : ?>
    <?php load_img( class: 'img img-cover', high: $corso_cover_id ); ?>
    <?php endif; ?>
  </div>


  <div class="container-l mx-auto py-120 relative z-50">

    <div class="intro flex flex-col items-center text-center mb-200">
      <div class="logo mb-40">
        <svg class="h-160" width="66" height="158" viewBox="0 0 66 158" fill="none" xmlns="http://www.w3.org/2000/svg">
          <path
            d="M0 50.9755L66 0V69.4884L35.8583 92.7634V130.309L27.2835 137.01V99.3958L22.2165 103.333V140.921L13.6417 147.518V109.903L8.5748 113.815V151.429L0 158V120.725V108.73V71.4551L8.5748 64.8061V102.42L13.6417 98.5093V60.8949L22.2165 54.246V91.9124L27.2835 88.0013V50.3348L35.8583 43.6858V81.2906L57.4252 64.5348V18.1217L0 62.4483V50.9755Z"
            fill="white" />
        </svg>

      </div>
      <div class="title font-din-bc text-60 text-white max-w-900">
        inizia ora lorem ipsum Sed dolor ut tincidunt. Nulla eleifend
      </div>
    </div>

    <div class="frame mb-100">


      <div class="inner relative z-40 px-120 pb-60 flex flex-col items-center">



        <div class="back absolute left--40 right--40 top-40 bottom-40 rounded-24 z-20 overflow-hidden">
          <div class="bg-brand-<?php echo esc_attr( $color_class_name ); ?> w-100-100 h-100-100 shadow-24"></div>
        </div>

        <div class="background inset-0 absolute overflow-hidden px-20 mx--20 z-30">
          <div class="bg-white w-100-100 h-100-100 shadow-24 rounded-24"></div>

        </div>


        <div class="content relative z-50 w-100-100">
          <div class="head flex flex-col items-center text-center mb-80">

            <div class="icon mt--120 mb-40">


              <?php if ( $corso_icona_id ) : ?>
              <?php load_img( class: 'img h-200 animate-float-updown-very-light animation-duration-5000 shadow-' . $color_class_name, high: $corso_icona_id ); ?>
              <?php endif; ?>
            </div>

            <div
              class="pretitle uppercase font-tt-eb text-16 mb-24 spacing-12 text-brand-<?php echo esc_attr( $color_class_name ); ?>">
              Corso
            </div>
            <div class="title font-din-bc text-60 mb-36">
              <?php the_title(); ?>
            </div>
            <div class="text font-ave-m text-20 text-gray leading-15 max-w-700">
            <?php echo esc_html($corso_descrizione); ?>
            </div>

          </div>

          <?php /* ?>
          <div class="price mb-100">
            <div class="main flex justify-center gap-60 items-center">
              <div class="old relative flex items-center gap-12">
                <div
                  class="line absolute-center-y w-120-100 left--10-100 h-4 bg-brand-<?php echo esc_attr( $color_class_name ); ?> opacity-80">
                </div>
                <div class="value font-ave-r text-48">
                  1500
                </div>
                <div class="unit font-ave-r text-36">
                  €
                </div>
                <svg class="absolute top--48 right--24 h-72" width="55" height="54" viewBox="0 0 55 54" fill="none"
                  xmlns="http://www.w3.org/2000/svg">
                  <g clip-path="url(#clip0_675_1589)">
                    <path
                      d="M30.2785 1.05886C30.6341 0.742957 31.1869 0.814701 31.4501 1.2109L33.0763 3.65924C33.2968 3.99124 33.7316 4.10445 34.086 3.92215L36.6997 2.57777C37.1227 2.36022 37.6403 2.56719 37.7967 3.01639L38.7629 5.79222C38.8939 6.16863 39.2869 6.38642 39.6755 6.29799L42.5415 5.64584C43.0052 5.54031 43.4551 5.86951 43.4949 6.34348L43.7404 9.2724C43.7737 9.66956 44.1002 9.97824 44.4986 9.98924L47.4367 10.0703C47.9121 10.0834 48.266 10.5142 48.1866 10.9831L47.6961 13.8811C47.6295 14.2741 47.869 14.6542 48.2522 14.764L51.0778 15.5732C51.535 15.7041 51.7707 16.2093 51.5771 16.6438L50.3813 19.3287C50.2191 19.6928 50.3565 20.1206 50.7004 20.3222L53.236 21.8086C53.6463 22.0492 53.7489 22.5971 53.4534 22.9698L51.6274 25.273C51.3798 25.5853 51.4065 26.0338 51.6894 26.3146L53.7756 28.3849C54.1133 28.7199 54.0764 29.2762 53.6975 29.5637L51.3561 31.3404C51.0386 31.5813 50.9529 32.0224 51.1571 32.3647L52.6629 34.8888C52.9066 35.2973 52.7325 35.8269 52.2941 36.0111L49.5844 37.1497C49.2169 37.3041 49.0242 37.71 49.1369 38.0923L49.9677 40.9117C50.1022 41.3679 49.8019 41.8376 49.3313 41.907L46.4236 42.3359C46.0293 42.3941 45.7417 42.7393 45.7558 43.1377L45.8593 46.075C45.8761 46.5504 45.4684 46.9306 44.9954 46.8808L42.0724 46.5732C41.676 46.5315 41.3116 46.7943 41.2262 47.1836L40.596 50.0544C40.494 50.519 40.0046 50.7859 39.5588 50.6201L36.8041 49.5952C36.4306 49.4562 36.0122 49.6202 35.8327 49.976L34.5083 52.5999C34.294 53.0245 33.7536 53.1613 33.3631 52.8898L30.9498 51.2121C30.6226 50.9845 30.1766 51.0394 29.9142 51.3393L27.9789 53.5515C27.6657 53.9094 27.1083 53.9076 26.7975 53.5475L24.8773 51.3222C24.6169 51.0205 24.1714 50.9627 23.8426 51.188L21.418 52.8493C21.0256 53.1182 20.4861 52.9777 20.2747 52.5517L18.9682 49.9188C18.791 49.5618 18.3738 49.395 17.9994 49.5314L15.2378 50.5376C14.7909 50.7004 14.3033 50.4302 14.2045 49.965L13.5938 47.0899C13.511 46.7001 13.1484 46.4347 12.7517 46.4738L9.82665 46.7616C9.3533 46.8081 8.94822 46.4251 8.9682 45.9499L9.09169 43.0133C9.10844 42.6151 8.82321 42.268 8.42932 42.2071L5.52458 41.7585C5.05452 41.6859 4.75741 41.2142 4.89495 40.7588L5.74486 37.9452C5.86011 37.5637 5.67018 37.1565 5.3038 36.9996L2.60189 35.8426C2.16466 35.6554 1.99419 35.1247 2.24064 34.7179L3.76356 32.204C3.97008 31.8631 3.88738 31.4215 3.57152 31.1784L1.24223 29.3859C0.86529 29.0958 0.832173 28.5393 1.17204 28.2066L3.2723 26.1504C3.5571 25.8716 3.58683 25.4233 3.34135 25.1093L1.53102 22.7938C1.23806 22.4191 1.34438 21.8718 1.75632 21.6341L4.30194 20.1648C4.64713 19.9656 4.78742 19.5388 4.62773 19.1736L3.45012 16.4806C3.25955 16.0448 3.49862 15.5412 3.95675 15.4134L6.78777 14.6234C7.17167 14.5163 7.4137 14.1377 7.34985 13.7443L6.87894 10.8431C6.80274 10.3736 7.15954 9.94525 7.63507 9.93536L10.5736 9.87421C10.9721 9.86592 11.3007 9.55946 11.3367 9.16253L11.602 6.23535C11.645 5.76166 12.0971 5.43552 12.5602 5.54419L15.4216 6.21575C15.8096 6.30682 16.2041 6.0917 16.3376 5.71619L17.3227 2.94697C17.4821 2.49884 18.0011 2.29538 18.4226 2.5158L21.0271 3.87787C21.3803 4.06257 21.8158 3.95231 22.0386 3.62182L23.6813 1.18456C23.9472 0.790154 24.5005 0.72216 24.8539 1.04047L27.0379 3.00747C27.334 3.2742 27.7833 3.27573 28.0813 3.01101L30.2785 1.05886Z"
                      fill="#A973FF" />
                    <path
                      d="M43.064 31.4637C42.9159 32.6045 41.8741 33.3972 40.7333 33.2492C39.5926 33.1011 38.7876 32.0687 38.9356 30.928C39.0837 29.7872 40.1255 28.9945 41.2663 29.1425C42.407 29.2906 43.212 30.323 43.064 31.4637ZM41.8472 31.3058C41.9078 30.8387 41.5661 30.4077 41.1098 30.3485C40.6426 30.2878 40.213 30.6187 40.1524 31.0859C40.0918 31.553 40.4241 31.9718 40.8912 32.0324C41.3475 32.0916 41.7866 31.773 41.8472 31.3058ZM38.3483 26.9411C38.2017 28.071 37.1584 28.8746 36.0177 28.7265C34.877 28.5785 34.0734 27.5352 34.22 26.4054C34.3694 25.2538 35.4099 24.4719 36.5506 24.6199C37.6913 24.768 38.4978 25.7895 38.3483 26.9411ZM37.1315 26.7832C37.1922 26.316 36.8613 25.8865 36.3941 25.8258C35.927 25.7652 35.4974 26.0961 35.4368 26.5633C35.3761 27.0304 35.7084 27.4491 36.1756 27.5098C36.6427 27.5704 37.0709 27.2504 37.1315 26.7832ZM41.8822 25.588L36.3241 32.92L35.4192 32.3165L40.9353 24.968L41.8822 25.588Z"
                      fill="white" />
                    <path
                      d="M31.6705 29.6646C31.452 31.3485 29.8649 31.9158 28.4526 31.7325C27.0511 31.5507 25.859 30.7773 25.6322 29.2897L27.3848 29.0642C27.4598 29.5931 27.8691 30.0992 28.5644 30.1894C29.0859 30.2571 29.7173 30.0739 29.8103 29.3569C29.9118 28.5747 29.1375 28.2422 28.377 28.1435L27.8881 28.08L28.0616 26.7438L28.5939 26.8129C29.2783 26.9017 29.9636 26.8139 30.0595 26.0751C30.1314 25.521 29.7023 25.1671 29.2025 25.1022C28.6811 25.0345 28.2245 25.3178 28.0373 25.8237L26.3933 25.2127C26.9305 23.9678 28.2332 23.463 29.5152 23.6294C30.8732 23.8056 32.1301 24.6757 31.9384 26.1532C31.8284 27.0006 31.2034 27.5602 30.4284 27.6585L30.4242 27.6911C31.2665 28.0103 31.7819 28.8063 31.6705 29.6646Z"
                      fill="white" />
                    <path
                      d="M23.5601 28.6128C23.3416 30.2967 21.7545 30.8641 20.3422 30.6808C18.9407 30.4989 17.7486 29.7256 17.5219 28.2379L19.2745 28.0124C19.3494 28.5414 19.7588 29.0474 20.4541 29.1377C20.9756 29.2053 21.6069 29.0221 21.7 28.3051C21.8015 27.5229 21.0272 27.1904 20.2667 27.0917L19.7778 27.0283L19.9512 25.692L20.4835 25.7611C21.168 25.8499 21.8532 25.7621 21.9491 25.0233C22.021 24.4693 21.5919 24.1153 21.0922 24.0505C20.5707 23.9828 20.1142 24.266 19.927 24.772L18.283 24.1609C18.8201 22.916 20.1229 22.4112 21.4048 22.5776C22.7629 22.7538 24.0198 23.624 23.828 25.1015C23.718 25.9489 23.0931 26.5085 22.3181 26.6068L22.3138 26.6394C23.1562 26.9586 23.6715 27.7545 23.5601 28.6128Z"
                      fill="white" />
                    <path d="M15.7344 27.6962L12.9749 27.3381L13.1582 25.9258L15.9177 26.2839L15.7344 27.6962Z"
                      fill="white" />
                  </g>
                  <defs>
                    <clipPath id="clip0_675_1589">
                      <rect width="54" height="54" fill="white" transform="translate(0.246094)" />
                    </clipPath>
                  </defs>
                </svg>

              </div>
              <div class="new flex gap-12 items-center">
                <div class="value font-ave-r text-60">
                  980
                </div>
                <div class="unit font-ave-r text-36">
                  €
                </div>
              </div>
            </div>
          </div>
          <?php */ ?>


 
          <div class="boxes flex flex-col gap-20 mb-100">
            <?php
    $boxes = rwmb_get_value( 'corso_boxes' );
    if ( ! empty( $boxes ) ) :
        foreach ( $boxes as $box ) :
            $color_class_name_box    = $box['box_colore'] ?? 'violet';
            $box_icona_id        = $box['box_icona'] ?? null;
            $box_titolo          = $box['box_titolo'] ?? '';
            $box_immagine_sfondo_id = $box['box_immagine_sfondo'] ?? null;
            $checklist           = $box['box_checklist'] ?? [];
            ?>


            <div
              class="box bg-brand-<?php echo esc_attr( $color_class_name_box ); ?>-light rounded-24 p-80 grid grid-cols-12 gap-80 items-center relative overflow-hidden">
              <div class="head col-span-4">
                <div
                  class="icon h-80 w-80 rounded-12 place-center flex mb-32 bg-brand-<?php echo esc_attr( $color_class_name_box ); ?>-medium">
                  <?php if ( $box_icona_id ) : ?>
                  <?php echo wp_get_attachment_image( $box_icona_id, 'full', false, [ 'class' => 'img h-50-100 w-auto' ] ); ?>
                  <?php endif; ?>
                </div>
                <div class="title font-ave-d text-28 leading-15">
                  <?php echo esc_html( $box_titolo ); ?>
                </div>
              </div>
              <div class="body col-span-8">
                <div class="checklist flex flex-col gap-20">
                  <?php foreach ( $checklist as $item ) : ?>
                  <div class="item flex gap-28">
                    <div class="icon flex shrink-0">
                      <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none"
                        xmlns="http://www.w3.org/2000/svg">
                        <path d="M4 14L8.87524 17.7502C9.76603 18.4354 11.0464 18.2524 11.7094 17.345L20 6"
                          stroke="<?php echo esc_attr( $color_map[ $color_class_name_box ] ); ?>" stroke-width="4"
                          stroke-linecap="round"></path>
                      </svg>
                    </div>
                    <div class="text font-ave-d text-20 leading-15 text-gray">
                      <?php echo esc_html( $item['checklist_testo'] ); ?>
                      <?php if ( ! empty( $item['checklist_helper'] ) ) : ?>
                      <span class="helper-trigger cursor-pointer"
                        data-modal-content="<?php echo esc_attr( wp_kses_post( $item['checklist_helper'] ) ); ?>">
                        <svg class="h-24 ml-10 inline-block" width="24" height="24" viewBox="0 0 24 24" fill="none"
                          xmlns="http://www.w3.org/2000/svg">
                          <path
                            d="M12 22C17.5228 22 22 17.5228 22 12C22 6.47715 17.5228 2 12 2C6.47715 2 2 6.47715 2 12C2 17.5228 6.47715 22 12 22Z"
                            stroke="#BCC5CB" stroke-width="2.57812" />
                          <path
                            d="M11.0695 13.8888V13.3663C11.0695 12.8988 11.152 12.5276 11.317 12.2525C11.482 11.9683 11.7937 11.62 12.252 11.2075C12.7104 10.7858 12.9761 10.4833 13.0495 10.3C13.132 10.1075 13.1733 9.97459 13.1733 9.90125C13.1733 9.60792 13.0816 9.37417 12.8983 9.2C12.7241 9.02584 12.4811 8.93875 12.1695 8.93875C11.812 8.93875 11.5279 9.0625 11.317 9.31C11.1154 9.5575 11.0054 9.86 10.987 10.2175L8.80078 9.98375C8.90161 9.02125 9.26828 8.28334 9.90078 7.77C10.5424 7.25667 11.3262 7 12.252 7C13.1686 7 13.9386 7.24292 14.562 7.72875C15.1854 8.20542 15.497 8.87917 15.497 9.75C15.497 10.135 15.4283 10.4925 15.2908 10.8225C15.1624 11.1433 14.837 11.5238 14.3145 11.9638C13.8011 12.4038 13.4941 12.6926 13.3933 12.83C13.2924 12.9676 13.2374 13.1142 13.2283 13.2701C13.2191 13.4258 13.2145 13.5129 13.2145 13.5313V13.8888H11.0695ZM12.1558 17.1338C11.7891 17.1338 11.4729 17.01 11.207 16.7626C10.9412 16.5058 10.8083 16.1988 10.8083 15.8413C10.8083 15.4838 10.9412 15.1767 11.207 14.9201C11.4729 14.6633 11.7891 14.535 12.1558 14.535C12.5224 14.535 12.8341 14.6633 13.0908 14.9201C13.3566 15.1676 13.4895 15.47 13.4895 15.8275C13.4895 16.1942 13.3611 16.5058 13.1045 16.7626C12.8479 17.01 12.5316 17.1338 12.1558 17.1338Z"
                            fill="#BCC5CB" />
                        </svg>
                      </span>
                      <?php endif; ?>
                    </div>
                  </div> <!-- item -->
                  <?php endforeach; ?>
                </div>
              </div>
              <div class="background top-0 right-0 bottom-0 w-30-100 absolute">
                <div
                  class="overlay overlay-<?php echo esc_attr( $color_class_name_box ); ?>-light-left inset-0 absolute z-50">
                </div>
                <div
                  class="overlay overlay-<?php echo esc_attr( $color_class_name_box ); ?>-light-bottom inset-0 absolute z-50 hidden show-1120">
                </div>
                <?php if ( $box_immagine_sfondo_id ) : ?>
                <?php echo wp_get_attachment_image( $box_immagine_sfondo_id, 'full', false, [ 'class' => 'img img-cover' ] ); ?>
                <?php endif; ?>
              </div>
            </div>
            <?php endforeach; ?>
            <?php endif; ?>
          </div>




          <div class="candidati">

            <div class="head mb-100 text-center">
              <div class="title font-din-bc text-60 mb-80">
                Modulo per candidarti al corso
              </div>
              <div class="steps grid grid-cols-3 overflow-hidden relative">
                <div class="line absolute-center-x top-28 z-10 w-100-100">
                  <svg class="h-4 shrink-0" width="1263" height="4" viewBox="0 0 1263 4" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <path d="M0 2L1263 2.00011" stroke="<?php echo esc_attr( $color_map[ $color_class_name ] ); ?>" stroke-width="3" stroke-dasharray="12 12" />
                  </svg>

                </div>
                <div class="item flex flex-col items-center gap-40 z-30 relative">

                  <div class="bg-white absolute left-0 top-0 h-60 w-50-100 z-10"></div>

                  <div
                    class="w-60 h-60 shrink-0 font-ave-b text-20 text-center place-center flex text-brand-<?php echo esc_attr( $color_class_name ); ?> bg-brand-<?php echo esc_attr( $color_class_name ); ?>-light rounded-full relative z-20">
                    1 </div>

                  <div class="text font-ave-d text-20 leading-15 max-w-300">
                    Compila subito il modulo e riserva il tuo posto senza impegno
                  </div>
                </div> <!-- item -->



                <div class="item flex flex-col items-center gap-40 z-30 relative">

                  <div
                    class="w-60 h-60 shrink-0 font-ave-b text-20 text-center place-center flex text-brand-<?php echo esc_attr( $color_class_name ); ?> bg-brand-<?php echo esc_attr( $color_class_name ); ?>-light rounded-full">
                    2 </div>

                  <div class="text font-ave-d text-20 leading-15 max-w-300">
                    Attendi fino a 48 ore per essere ricontattato e ricevere ulteriori istruzioni
                  </div>
                </div>

                <div class="item flex flex-col items-center gap-40 z-30 relative">


                  <div class="bg-white absolute right-0 top-0 h-60 w-50-100 z-10"></div>


                  <div
                    class="w-60 h-60 shrink-0 font-ave-b text-20 text-center place-center flex text-brand-<?php echo esc_attr( $color_class_name ); ?> bg-brand-<?php echo esc_attr( $color_class_name ); ?>-light rounded-full z-30 relative">
                    3 </div>

                  <div class="text font-ave-d text-20 leading-15 max-w-300">
                    Conferma e procedi al pagamento per accedere immediatamente al corso!
                  </div>
                </div>






              </div>
            </div>


            <div class="body mb-100">
              <div class="form mb-20">
                <?php echo do_shortcode('[fluentform id="1"]'); ?>
              </div>
              <div
                class="info bg-brand-<?php echo esc_attr( $color_class_name ); ?>-light rounded-12 py-20 px-40 text-center font-ave-b text-18 text-brand-<?php echo esc_attr( $color_class_name ); ?>">
                Ti ricontatteremo entro 48 ore per fornirti ulteriori istruzioni in merito alla tua candidatura
              </div>

            </div>


          </div>


          <div class="offers grid grid-cols-12 gap-40 mb-80">

            <div class="box col-span-12 gap-100 flex items-center justify-between bg-brand-red-light p-80 rounded-24">
              <div class="main">
                <div class="title text-brand-red text-28 font-ave-b leading-13 mb-28 ">
                  Seguiamo ragazzi su selezione, <br>ultimi posti disponibili
                </div>
                <div class="text font-ave-d text-20">
                  Posti limitati a <span class="font-ave-b text-brand-red">30 persone</span>
                </div>
              </div>
              <div class="side">

                <div class="availability grid grid-cols-12 gap-6">

                  <div class="item bg-brand-red h-48 w-48 rounded-8 flex place-center">
                    <svg class="h-50-100" width="18" height="22" viewBox="0 0 18 22" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <circle cx="9.5" cy="6.5" r="4.75" stroke="white" stroke-width="2.5" />
                      <path
                        d="M15.0069 20H3.00692C2.45464 20 2.00388 19.5523 2.00083 19C1.98271 15.7141 2.19825 12.5 9.00692 12.5C15.3466 12.5 15.9446 15.781 16.001 19C16.0107 19.5522 15.5592 20 15.0069 20Z"
                        stroke="white" stroke-width="2.5" />
                    </svg>
                  </div>

                  <div class="item bg-brand-red h-48 w-48 rounded-8 flex place-center">
                    <svg class="h-50-100" width="18" height="22" viewBox="0 0 18 22" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <circle cx="9.5" cy="6.5" r="4.75" stroke="white" stroke-width="2.5" />
                      <path
                        d="M15.0069 20H3.00692C2.45464 20 2.00388 19.5523 2.00083 19C1.98271 15.7141 2.19825 12.5 9.00692 12.5C15.3466 12.5 15.9446 15.781 16.001 19C16.0107 19.5522 15.5592 20 15.0069 20Z"
                        stroke="white" stroke-width="2.5" />
                    </svg>
                  </div>

                  <div class="item bg-brand-red-medium h-48 w-48 rounded-8 flex place-center opacity-40">
                    <svg class="h-50-100" width="18" height="22" viewBox="0 0 18 22" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <circle cx="9.5" cy="6.5" r="4.75" stroke="#FF0000" stroke-width="2.5" />
                      <path
                        d="M15.0069 20H3.00692C2.45464 20 2.00388 19.5523 2.00083 19C1.98271 15.7141 2.19825 12.5 9.00692 12.5C15.3466 12.5 15.9446 15.781 16.001 19C16.0107 19.5522 15.5592 20 15.0069 20Z"
                        stroke="#FF0000" stroke-width="2.5" />
                    </svg>
                  </div>

                  <div class="item bg-brand-red-medium h-48 w-48 rounded-8 flex place-center opacity-40">
                    <svg class="h-50-100" width="18" height="22" viewBox="0 0 18 22" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <circle cx="9.5" cy="6.5" r="4.75" stroke="#FF0000" stroke-width="2.5" />
                      <path
                        d="M15.0069 20H3.00692C2.45464 20 2.00388 19.5523 2.00083 19C1.98271 15.7141 2.19825 12.5 9.00692 12.5C15.3466 12.5 15.9446 15.781 16.001 19C16.0107 19.5522 15.5592 20 15.0069 20Z"
                        stroke="#FF0000" stroke-width="2.5" />
                    </svg>
                  </div>


                  <div class="item bg-brand-red-medium h-48 w-48 rounded-8 flex place-center opacity-40">
                    <svg class="h-50-100" width="18" height="22" viewBox="0 0 18 22" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <circle cx="9.5" cy="6.5" r="4.75" stroke="#FF0000" stroke-width="2.5" />
                      <path
                        d="M15.0069 20H3.00692C2.45464 20 2.00388 19.5523 2.00083 19C1.98271 15.7141 2.19825 12.5 9.00692 12.5C15.3466 12.5 15.9446 15.781 16.001 19C16.0107 19.5522 15.5592 20 15.0069 20Z"
                        stroke="#FF0000" stroke-width="2.5" />
                    </svg>
                  </div>


                  <div class="item bg-brand-red-medium h-48 w-48 rounded-8 flex place-center opacity-40">
                    <svg class="h-50-100" width="18" height="22" viewBox="0 0 18 22" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <circle cx="9.5" cy="6.5" r="4.75" stroke="#FF0000" stroke-width="2.5" />
                      <path
                        d="M15.0069 20H3.00692C2.45464 20 2.00388 19.5523 2.00083 19C1.98271 15.7141 2.19825 12.5 9.00692 12.5C15.3466 12.5 15.9446 15.781 16.001 19C16.0107 19.5522 15.5592 20 15.0069 20Z"
                        stroke="#FF0000" stroke-width="2.5" />
                    </svg>
                  </div>

                  <div class="item bg-brand-red-medium h-48 w-48 rounded-8 flex place-center opacity-40">
                    <svg class="h-50-100" width="18" height="22" viewBox="0 0 18 22" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <circle cx="9.5" cy="6.5" r="4.75" stroke="#FF0000" stroke-width="2.5" />
                      <path
                        d="M15.0069 20H3.00692C2.45464 20 2.00388 19.5523 2.00083 19C1.98271 15.7141 2.19825 12.5 9.00692 12.5C15.3466 12.5 15.9446 15.781 16.001 19C16.0107 19.5522 15.5592 20 15.0069 20Z"
                        stroke="#FF0000" stroke-width="2.5" />
                    </svg>
                  </div>

                  <div class="item bg-brand-red-medium h-48 w-48 rounded-8 flex place-center opacity-40">
                    <svg class="h-50-100" width="18" height="22" viewBox="0 0 18 22" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <circle cx="9.5" cy="6.5" r="4.75" stroke="#FF0000" stroke-width="2.5" />
                      <path
                        d="M15.0069 20H3.00692C2.45464 20 2.00388 19.5523 2.00083 19C1.98271 15.7141 2.19825 12.5 9.00692 12.5C15.3466 12.5 15.9446 15.781 16.001 19C16.0107 19.5522 15.5592 20 15.0069 20Z"
                        stroke="#FF0000" stroke-width="2.5" />
                    </svg>
                  </div>

                  <div class="item bg-brand-red-medium h-48 w-48 rounded-8 flex place-center opacity-40">
                    <svg class="h-50-100" width="18" height="22" viewBox="0 0 18 22" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <circle cx="9.5" cy="6.5" r="4.75" stroke="#FF0000" stroke-width="2.5" />
                      <path
                        d="M15.0069 20H3.00692C2.45464 20 2.00388 19.5523 2.00083 19C1.98271 15.7141 2.19825 12.5 9.00692 12.5C15.3466 12.5 15.9446 15.781 16.001 19C16.0107 19.5522 15.5592 20 15.0069 20Z"
                        stroke="#FF0000" stroke-width="2.5" />
                    </svg>
                  </div>

                  <div class="item bg-brand-red-medium h-48 w-48 rounded-8 flex place-center opacity-40">
                    <svg class="h-50-100" width="18" height="22" viewBox="0 0 18 22" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <circle cx="9.5" cy="6.5" r="4.75" stroke="#FF0000" stroke-width="2.5" />
                      <path
                        d="M15.0069 20H3.00692C2.45464 20 2.00388 19.5523 2.00083 19C1.98271 15.7141 2.19825 12.5 9.00692 12.5C15.3466 12.5 15.9446 15.781 16.001 19C16.0107 19.5522 15.5592 20 15.0069 20Z"
                        stroke="#FF0000" stroke-width="2.5" />
                    </svg>
                  </div>

                  <div class="item bg-brand-red-medium h-48 w-48 rounded-8 flex place-center opacity-40">
                    <svg class="h-50-100" width="18" height="22" viewBox="0 0 18 22" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <circle cx="9.5" cy="6.5" r="4.75" stroke="#FF0000" stroke-width="2.5" />
                      <path
                        d="M15.0069 20H3.00692C2.45464 20 2.00388 19.5523 2.00083 19C1.98271 15.7141 2.19825 12.5 9.00692 12.5C15.3466 12.5 15.9446 15.781 16.001 19C16.0107 19.5522 15.5592 20 15.0069 20Z"
                        stroke="#FF0000" stroke-width="2.5" />
                    </svg>
                  </div>

                  <div class="item bg-brand-red-medium h-48 w-48 rounded-8 flex place-center opacity-40">
                    <svg class="h-50-100" width="18" height="22" viewBox="0 0 18 22" fill="none"
                      xmlns="http://www.w3.org/2000/svg">
                      <circle cx="9.5" cy="6.5" r="4.75" stroke="#FF0000" stroke-width="2.5" />
                      <path
                        d="M15.0069 20H3.00692C2.45464 20 2.00388 19.5523 2.00083 19C1.98271 15.7141 2.19825 12.5 9.00692 12.5C15.3466 12.5 15.9446 15.781 16.001 19C16.0107 19.5522 15.5592 20 15.0069 20Z"
                        stroke="#FF0000" stroke-width="2.5" />
                    </svg>
                  </div>




                </div>





              </div>
            </div> <!-- box -->



            <div class="box col-span-6 bg-brand-blue-light p-80 rounded-24 relative">

              <div class="title text-brand-blue text-28 font-ave-b leading-13 mb-28 ">
                Sei uno studente?
              </div>
              <div class="text font-ave-d text-20 leading-15">
                Investiamo nella formazione dei giovani e potresti avere diritto <span
                  class="font-ave-b text-brand-blue">fino al
                  50% di sconto.</span>
              </div>
              <svg class="absolute top-40 right--20 z-30 h-100" width="81" height="80" viewBox="0 0 81 80" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <g clip-path="url(#clip0_822_2044)">
                  <path
                    d="M44.5205 0.892826C45.0434 0.428222 45.8565 0.533736 46.2435 1.11643L48.6352 4.71722C48.9595 5.20549 49.5989 5.372 50.1202 5.10388L53.9642 3.12669C54.5862 2.80674 55.3475 3.11113 55.5775 3.77177L56.9985 7.85421C57.1912 8.4078 57.7692 8.7281 58.3407 8.59805L62.5557 7.63893C63.2378 7.48372 63.8994 7.96788 63.9578 8.66495L64.319 12.9725C64.3679 13.5566 64.8481 14.0106 65.434 14.0268L69.7551 14.146C70.4543 14.1653 70.9748 14.7988 70.858 15.4885L70.1366 19.7506C70.0387 20.3285 70.3909 20.8876 70.9544 21.049L75.1101 22.2391C75.7825 22.4317 76.1291 23.1747 75.8445 23.8137L74.0858 27.7624C73.8473 28.2979 74.0493 28.927 74.555 29.2235L78.2841 31.4097C78.8876 31.7634 79.0385 32.5693 78.6039 33.1174L75.9184 36.5047C75.5542 36.964 75.5935 37.6237 76.0096 38.0366L79.0779 41.0814C79.5744 41.5742 79.5201 42.3923 78.9629 42.8151L75.5194 45.4281C75.0524 45.7825 74.9264 46.4311 75.2267 46.9345L77.4414 50.6468C77.7998 51.2475 77.5438 52.0264 76.8989 52.2974L72.9137 53.9719C72.3733 54.199 72.0899 54.796 72.2556 55.3582L73.4775 59.5046C73.6752 60.1756 73.2336 60.8664 72.5415 60.9685L68.2651 61.5993C67.6853 61.6849 67.2623 62.1926 67.283 62.7784L67.4353 67.0984C67.4599 67.7975 66.8604 68.3567 66.1647 68.2835L61.8658 67.831C61.2828 67.7697 60.7469 68.1563 60.6212 68.7288L59.6944 72.951C59.5445 73.6342 58.8247 74.0268 58.169 73.7828L54.1177 72.2755C53.5683 72.0711 52.9531 72.3123 52.689 72.8356L50.7413 76.6946C50.4261 77.3191 49.6313 77.5203 49.0569 77.121L45.5077 74.6535C45.0264 74.3189 44.3706 74.3995 43.9846 74.8407L41.1384 78.0941C40.6778 78.6206 39.858 78.6178 39.401 78.0882L36.5769 74.8155C36.1939 74.3718 35.5386 74.2867 35.0551 74.6181L31.4892 77.0614C30.9121 77.4568 30.1187 77.2502 29.8078 76.6236L27.8863 72.7515C27.6257 72.2264 27.0122 71.9811 26.4614 72.1817L22.3999 73.6616C21.7427 73.901 21.0256 73.5036 20.8802 72.8194L19.9821 68.591C19.8603 68.0177 19.327 67.6274 18.7437 67.6848L14.4417 68.1081C13.7456 68.1766 13.1498 67.6133 13.1792 66.9144L13.3608 62.5956C13.3855 62.0099 12.966 61.4993 12.3867 61.4099L8.11466 60.75C7.42335 60.6432 6.98638 59.9495 7.18866 59.2799L8.43863 55.1418C8.60812 54.5807 8.3288 53.9819 7.78995 53.7511L3.81625 52.0496C3.1732 51.7742 2.92249 50.9936 3.28494 50.3954L5.52472 46.6982C5.82844 46.1968 5.70682 45.5473 5.24229 45.1898L1.81658 42.5535C1.26221 42.1269 1.21351 41.3085 1.71336 40.8191L4.80222 37.7951C5.22108 37.3851 5.2648 36.7257 4.90377 36.2639L2.2413 32.8585C1.81045 32.3074 1.96681 31.5026 2.57265 31.1529L6.31651 28.9921C6.82418 28.6991 7.03051 28.0713 6.79566 27.5343L5.06374 23.5737C4.78347 22.9328 5.13507 22.1922 5.80884 22.0041L9.97245 20.8423C10.537 20.6847 10.893 20.128 10.7991 19.5494L10.1065 15.2825C9.99446 14.5921 10.5192 13.9621 11.2186 13.9476L15.5403 13.8576C16.1264 13.8454 16.6096 13.3947 16.6625 12.8109L17.0528 8.50592C17.116 7.80926 17.7809 7.3296 18.4619 7.48943L22.6703 8.4771C23.2409 8.61103 23.8211 8.29465 24.0175 7.74239L25.4662 3.66968C25.7006 3.01062 26.4639 2.71138 27.0838 3.03555L30.9143 5.03877C31.4337 5.31041 32.0743 5.14824 32.4019 4.66218L34.8179 1.07769C35.2089 0.497637 36.0227 0.397636 36.5424 0.865775L39.7544 3.75866C40.19 4.15094 40.8508 4.15318 41.289 3.76386L44.5205 0.892826Z"
                    fill="#1289ED" />
                  <path
                    d="M62.7581 45.8315C62.5404 47.5092 61.0081 48.6751 59.3304 48.4573C57.6528 48.2396 56.4688 46.7212 56.6865 45.0436C56.9043 43.3659 58.4366 42.2 60.1142 42.4177C61.7919 42.6354 62.9758 44.1538 62.7581 45.8315ZM60.9686 45.5993C61.0578 44.9122 60.5551 44.2783 59.8841 44.1913C59.197 44.1021 58.5652 44.5887 58.4761 45.2758C58.3869 45.9628 58.8756 46.5787 59.5627 46.6678C60.2337 46.7549 60.8794 46.2863 60.9686 45.5993ZM55.8228 39.18C55.6071 40.8417 54.0728 42.0236 52.3951 41.8059C50.7174 41.5882 49.5356 40.0538 49.7512 38.3921C49.971 36.6985 51.5012 35.5485 53.1789 35.7663C54.8566 35.984 56.0426 37.4864 55.8228 39.18ZM54.0333 38.9478C54.1224 38.2608 53.6358 37.629 52.9487 37.5398C52.2617 37.4506 51.6299 37.9373 51.5407 38.6243C51.4516 39.3114 51.9403 39.9272 52.6273 40.0164C53.3144 40.1055 53.9441 39.6349 54.0333 38.9478ZM61.0201 37.19L52.8457 47.9732L51.5149 47.0856L59.6275 36.2782L61.0201 37.19Z"
                    fill="white" />
                  <path
                    d="M46.5844 40.8246C46.1676 44.0361 44.5925 46.6587 41.3969 46.244C38.1854 45.8272 37.3159 42.8874 37.7327 39.6758C38.1474 36.4803 39.7322 33.9077 42.9438 34.3245C46.1394 34.7392 46.9991 37.629 46.5844 40.8246ZM43.8362 40.4679C44.0208 39.0459 44.0554 36.7758 42.6494 36.5933C41.2273 36.4088 40.6654 38.6104 40.4809 40.0325C40.2922 41.4865 40.2735 43.7586 41.6955 43.9432C43.1016 44.1256 43.6475 41.9219 43.8362 40.4679Z"
                    fill="white" />
                  <path
                    d="M34.1166 41.1886C33.7599 43.9368 31.451 44.9531 29.23 44.6649C27.1689 44.3974 25.6003 43.089 25.3052 41.2311L27.8473 40.7974C27.9854 41.6114 28.6137 42.2778 29.4925 42.3919C30.4033 42.5101 31.2587 42.0525 31.3997 40.966C31.5739 39.6238 30.4094 39.0828 29.3069 38.9397C28.508 38.836 27.3147 38.8924 26.4785 39.0763L27.5899 32.6405L34.5242 33.5404L34.2257 35.8412L29.672 35.2503L29.3439 37.0273C29.6898 36.991 30.1573 37.0192 30.4928 37.0627C32.7617 37.3572 34.4255 38.8079 34.1166 41.1886Z"
                    fill="white" />
                  <path d="M22.5652 40.2903L18.5068 39.7636L18.7764 37.6865L22.8347 38.2132L22.5652 40.2903Z"
                    fill="white" />
                </g>
                <defs>
                  <clipPath id="clip0_822_2044">
                    <rect width="80" height="80" fill="white" transform="translate(0.983398)" />
                  </clipPath>
                </defs>
              </svg>


            </div> <!-- box -->



            <div class="box col-span-6 bg-brand-yellow-light p-80 rounded-24">
              <div class="container">
                <div class="title text-brand-yellow text-28 font-ave-b leading-13 mb-28 ">
                  Paga in 3 rate mensili
                </div>
                <div class="text font-ave-d text-20 leading-15">
                  Il pagamento può essere effettuato in 3 rate mensili con <span
                    class="font-ave-b text-brand-yellow">interessi
                    allo 0%.</span>
                </div>
              </div>

            </div> <!-- box -->





          </div>



          <a href="" class="whatsapp flex items-center gap-20 justify-center">
            <div class="icon flex">
              <svg class="h-32" width="31" height="31" viewBox="0 0 31 31" fill="none"
                xmlns="http://www.w3.org/2000/svg">
                <path
                  d="M15.4902 0.512695C19.4636 0.514355 23.19 2.05988 25.9941 4.86719C28.7107 7.5868 30.2431 11.1714 30.335 15.001L30.3398 15.3721C30.3364 23.5591 23.6732 30.2207 15.4902 30.2207H15.4844L15.0186 30.2139C12.6944 30.1403 10.4213 29.5227 8.38867 28.4141L8.20996 28.3164L8.01367 28.3682L0.728516 30.2783L2.67383 23.1748L2.73047 22.9688L2.62305 22.7842C1.32385 20.5329 0.640456 17.9782 0.641602 15.3604C0.644782 7.30167 7.09965 0.721475 15.1074 0.517578L15.4902 0.512695ZM15.4951 2.08203C8.16985 2.08223 2.21281 8.03895 2.20996 15.3613C2.20899 17.8694 2.91096 20.3131 4.24023 22.4277V22.4287L4.42383 22.7217L3.19434 27.2148L2.95996 28.0713L3.81836 27.8457L8.44238 26.6318L8.72559 26.7998V26.8008C10.7653 28.0109 13.1031 28.6504 15.4854 28.6514H15.4902C22.8106 28.6514 28.7673 22.6947 28.7705 15.3721C28.7719 11.8247 27.3913 8.48605 24.8848 5.97656C22.3783 3.46715 19.0422 2.08325 15.4961 2.08203H15.4951Z"
                  fill="#25D366" stroke="#25D366" stroke-width="1.02479" />
                <path
                  d="M10.0508 8.78613C10.3053 8.78613 10.5508 8.78835 10.7607 8.79883C10.8075 8.80117 10.8541 8.80093 10.8809 8.80078C10.9129 8.80061 10.9313 8.79988 10.9473 8.80078C10.9603 8.80153 10.9675 8.80304 10.9707 8.80371C10.9806 8.81007 11.0233 8.84277 11.0859 8.95312L11.1768 9.14258C11.3362 9.52579 11.6069 10.1878 11.8516 10.7822C12.0886 11.3581 12.3158 11.9057 12.375 12.0244V12.0254C12.4089 12.0933 12.4209 12.1351 12.4238 12.1621C12.4248 12.1712 12.4249 12.1813 12.4229 12.1934L12.4072 12.2393C12.2673 12.5189 12.233 12.6105 12.0918 12.7754C11.8671 13.0378 11.6989 13.2636 11.5439 13.418C11.4615 13.5001 11.2771 13.6758 11.1855 13.9219C11.0758 14.2171 11.1218 14.5253 11.2949 14.8223C11.5276 15.2214 12.3297 16.5337 13.5312 17.6055C15.0634 18.9721 16.3956 19.4199 16.7295 19.5869V19.5859C16.9553 19.6991 17.2171 19.7908 17.4971 19.7568C17.7977 19.7202 18.016 19.5539 18.1768 19.3701V19.3691C18.3951 19.1193 19.1546 18.229 19.4326 17.8115C19.4535 17.7802 19.471 17.7621 19.4814 17.751C19.488 17.7521 19.4961 17.7536 19.5059 17.7559C19.5509 17.7663 19.6084 17.7851 19.6943 17.8164C19.8393 17.8692 20.3682 18.1184 20.9463 18.3975C21.5079 18.6686 22.0731 18.9471 22.2627 19.042C22.4842 19.1529 22.6066 19.2064 22.7217 19.2695C22.7405 19.2798 22.7545 19.2904 22.7666 19.2979L22.7676 19.2988C22.7734 19.3732 22.7739 19.4839 22.7617 19.625C22.7374 19.9061 22.6664 20.2794 22.5215 20.6855C22.4208 20.9675 22.0808 21.3185 21.5889 21.6338C21.1092 21.9412 20.6105 22.1351 20.3379 22.1758C19.7193 22.2683 18.9688 22.3017 18.1514 22.042H18.1504C17.6007 21.8676 16.9039 21.6377 16.0107 21.252C12.7742 19.8544 10.5074 16.814 9.84473 15.8799L9.66113 15.6191C9.57057 15.4983 9.20006 15.0057 8.85254 14.3213C8.5014 13.6297 8.19638 12.7853 8.19629 11.9561C8.19629 10.2807 9.05583 9.48133 9.40527 9.09961C9.64351 8.83955 9.91243 8.78613 10.0508 8.78613Z"
                  fill="#25D366" stroke="#25D366" stroke-width="1.02479" />
              </svg>

            </div>
            <div class="text font-ave-b text-20">
              Domande sul corso? Scrivimi
            </div>

          </a>



        </div>


      </div>

    </div>


    <div class="reviews swiper">
          <div class="swiper-wrapper">
    
            <div class="swiper-slide item flex flex-col items-center text-center text-white">
              <div class="icon mb-20">
                <svg class="h-24" width="30" height="23" viewBox="0 0 30 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M18.9139 22.5664L17.7987 20.4673C19.6355 19.2428 21.0786 18.0839 22.1282 16.9906C23.1778 15.941 23.7026 14.8696 23.7026 13.7763C23.7026 12.9453 23.4839 12.2675 23.0466 11.7427C22.6093 11.2617 21.9096 10.6931 20.9475 10.0372C19.8979 9.33744 19.1107 8.63773 18.5859 7.93802C18.0611 7.28203 17.7987 6.42926 17.7987 5.37969C17.7987 3.80533 18.3017 2.51524 19.3075 1.5094C20.3133 0.503558 21.6472 0.000639235 23.309 0.000639235C25.2769 0.000639235 26.895 0.765951 28.1633 2.29657C29.3878 3.87093 30 5.79514 30 8.06921C30 10.8681 29.0379 13.5357 27.1137 16.0722C25.1457 18.6524 22.4125 20.8171 18.9139 22.5664ZM1.85839 22.5664L0.743218 20.4673C2.57997 19.2428 4.02313 18.1057 5.0727 17.0562C6.12227 16.0066 6.64705 14.9133 6.64705 13.7763C6.64705 12.9453 6.42839 12.2675 5.99107 11.7427C5.55375 11.2617 4.85404 10.6931 3.89193 10.0372C2.84236 9.33744 2.05518 8.63773 1.5304 7.93802C1.00561 7.28203 0.743218 6.42926 0.743218 5.37969C0.743218 3.80533 1.24614 2.51524 2.25198 1.5094C3.25782 0.503558 4.59165 0.000639235 6.25347 0.000639235C8.22141 0.000639235 9.8395 0.765951 11.1077 2.29657C12.3322 3.87093 12.9445 5.79514 12.9445 8.06921C12.9445 10.8681 11.9824 13.5357 10.0582 16.0722C8.09021 18.6524 5.35696 20.8171 1.85839 22.5664Z"
                    fill="<?php echo esc_attr( $color_map[ $color_class_name ] ); ?>" />
                </svg>
    
              </div>
              <div class="text font-ave-d text-20 leading-15 mb-20 opacity-80">
                “Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et
                dolore
                magna aliqua. Ut enim ad minim veniam, quis nostrud...”
              </div>
              <div class="author font-ave-b text-20">
                Marco
              </div>
            </div> <!-- item -->
    
    
            <div class="swiper-slide item flex flex-col items-center text-center text-white">
              <div class="icon mb-20">
                <svg class="h-24" width="30" height="23" viewBox="0 0 30 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M18.9139 22.5664L17.7987 20.4673C19.6355 19.2428 21.0786 18.0839 22.1282 16.9906C23.1778 15.941 23.7026 14.8696 23.7026 13.7763C23.7026 12.9453 23.4839 12.2675 23.0466 11.7427C22.6093 11.2617 21.9096 10.6931 20.9475 10.0372C19.8979 9.33744 19.1107 8.63773 18.5859 7.93802C18.0611 7.28203 17.7987 6.42926 17.7987 5.37969C17.7987 3.80533 18.3017 2.51524 19.3075 1.5094C20.3133 0.503558 21.6472 0.000639235 23.309 0.000639235C25.2769 0.000639235 26.895 0.765951 28.1633 2.29657C29.3878 3.87093 30 5.79514 30 8.06921C30 10.8681 29.0379 13.5357 27.1137 16.0722C25.1457 18.6524 22.4125 20.8171 18.9139 22.5664ZM1.85839 22.5664L0.743218 20.4673C2.57997 19.2428 4.02313 18.1057 5.0727 17.0562C6.12227 16.0066 6.64705 14.9133 6.64705 13.7763C6.64705 12.9453 6.42839 12.2675 5.99107 11.7427C5.55375 11.2617 4.85404 10.6931 3.89193 10.0372C2.84236 9.33744 2.05518 8.63773 1.5304 7.93802C1.00561 7.28203 0.743218 6.42926 0.743218 5.37969C0.743218 3.80533 1.24614 2.51524 2.25198 1.5094C3.25782 0.503558 4.59165 0.000639235 6.25347 0.000639235C8.22141 0.000639235 9.8395 0.765951 11.1077 2.29657C12.3322 3.87093 12.9445 5.79514 12.9445 8.06921C12.9445 10.8681 11.9824 13.5357 10.0582 16.0722C8.09021 18.6524 5.35696 20.8171 1.85839 22.5664Z"
                    fill="<?php echo esc_attr( $color_map[ $color_class_name ] ); ?>" />
                </svg>
    
              </div>
              <div class="text font-ave-d text-20 leading-15 mb-20 opacity-80">
                “Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et
                dolore
                magna aliqua. Ut enim ad minim veniam, quis nostrud...”
              </div>
              <div class="author font-ave-b text-20">
                Marco
              </div>
            </div> <!-- item -->
    
            <div class="swiper-slide item flex flex-col items-center text-center text-white">
              <div class="icon mb-20">
                <svg class="h-24" width="30" height="23" viewBox="0 0 30 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M18.9139 22.5664L17.7987 20.4673C19.6355 19.2428 21.0786 18.0839 22.1282 16.9906C23.1778 15.941 23.7026 14.8696 23.7026 13.7763C23.7026 12.9453 23.4839 12.2675 23.0466 11.7427C22.6093 11.2617 21.9096 10.6931 20.9475 10.0372C19.8979 9.33744 19.1107 8.63773 18.5859 7.93802C18.0611 7.28203 17.7987 6.42926 17.7987 5.37969C17.7987 3.80533 18.3017 2.51524 19.3075 1.5094C20.3133 0.503558 21.6472 0.000639235 23.309 0.000639235C25.2769 0.000639235 26.895 0.765951 28.1633 2.29657C29.3878 3.87093 30 5.79514 30 8.06921C30 10.8681 29.0379 13.5357 27.1137 16.0722C25.1457 18.6524 22.4125 20.8171 18.9139 22.5664ZM1.85839 22.5664L0.743218 20.4673C2.57997 19.2428 4.02313 18.1057 5.0727 17.0562C6.12227 16.0066 6.64705 14.9133 6.64705 13.7763C6.64705 12.9453 6.42839 12.2675 5.99107 11.7427C5.55375 11.2617 4.85404 10.6931 3.89193 10.0372C2.84236 9.33744 2.05518 8.63773 1.5304 7.93802C1.00561 7.28203 0.743218 6.42926 0.743218 5.37969C0.743218 3.80533 1.24614 2.51524 2.25198 1.5094C3.25782 0.503558 4.59165 0.000639235 6.25347 0.000639235C8.22141 0.000639235 9.8395 0.765951 11.1077 2.29657C12.3322 3.87093 12.9445 5.79514 12.9445 8.06921C12.9445 10.8681 11.9824 13.5357 10.0582 16.0722C8.09021 18.6524 5.35696 20.8171 1.85839 22.5664Z"
                    fill="<?php echo esc_attr( $color_map[ $color_class_name ] ); ?>" />
                </svg>
    
              </div>
              <div class="text font-ave-d text-20 leading-15 mb-20 opacity-80">
                “Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et
                dolore
                magna aliqua. Ut enim ad minim veniam, quis nostrud...”
              </div>
              <div class="author font-ave-b text-20">
                Marco
              </div>
            </div> <!-- item -->


            <div class="swiper-slide item flex flex-col items-center text-center text-white">
              <div class="icon mb-20">
                <svg class="h-24" width="30" height="23" viewBox="0 0 30 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M18.9139 22.5664L17.7987 20.4673C19.6355 19.2428 21.0786 18.0839 22.1282 16.9906C23.1778 15.941 23.7026 14.8696 23.7026 13.7763C23.7026 12.9453 23.4839 12.2675 23.0466 11.7427C22.6093 11.2617 21.9096 10.6931 20.9475 10.0372C19.8979 9.33744 19.1107 8.63773 18.5859 7.93802C18.0611 7.28203 17.7987 6.42926 17.7987 5.37969C17.7987 3.80533 18.3017 2.51524 19.3075 1.5094C20.3133 0.503558 21.6472 0.000639235 23.309 0.000639235C25.2769 0.000639235 26.895 0.765951 28.1633 2.29657C29.3878 3.87093 30 5.79514 30 8.06921C30 10.8681 29.0379 13.5357 27.1137 16.0722C25.1457 18.6524 22.4125 20.8171 18.9139 22.5664ZM1.85839 22.5664L0.743218 20.4673C2.57997 19.2428 4.02313 18.1057 5.0727 17.0562C6.12227 16.0066 6.64705 14.9133 6.64705 13.7763C6.64705 12.9453 6.42839 12.2675 5.99107 11.7427C5.55375 11.2617 4.85404 10.6931 3.89193 10.0372C2.84236 9.33744 2.05518 8.63773 1.5304 7.93802C1.00561 7.28203 0.743218 6.42926 0.743218 5.37969C0.743218 3.80533 1.24614 2.51524 2.25198 1.5094C3.25782 0.503558 4.59165 0.000639235 6.25347 0.000639235C8.22141 0.000639235 9.8395 0.765951 11.1077 2.29657C12.3322 3.87093 12.9445 5.79514 12.9445 8.06921C12.9445 10.8681 11.9824 13.5357 10.0582 16.0722C8.09021 18.6524 5.35696 20.8171 1.85839 22.5664Z"
                    fill="<?php echo esc_attr( $color_map[ $color_class_name ] ); ?>" />
                </svg>
    
              </div>
              <div class="text font-ave-d text-20 leading-15 mb-20 opacity-80">
                “Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et
                dolore
                magna aliqua. Ut enim ad minim veniam, quis nostrud...”
              </div>
              <div class="author font-ave-b text-20">
                Marco
              </div>
            </div> <!-- item -->


            <div class="swiper-slide item flex flex-col items-center text-center text-white">
              <div class="icon mb-20">
                <svg class="h-24" width="30" height="23" viewBox="0 0 30 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path
                    d="M18.9139 22.5664L17.7987 20.4673C19.6355 19.2428 21.0786 18.0839 22.1282 16.9906C23.1778 15.941 23.7026 14.8696 23.7026 13.7763C23.7026 12.9453 23.4839 12.2675 23.0466 11.7427C22.6093 11.2617 21.9096 10.6931 20.9475 10.0372C19.8979 9.33744 19.1107 8.63773 18.5859 7.93802C18.0611 7.28203 17.7987 6.42926 17.7987 5.37969C17.7987 3.80533 18.3017 2.51524 19.3075 1.5094C20.3133 0.503558 21.6472 0.000639235 23.309 0.000639235C25.2769 0.000639235 26.895 0.765951 28.1633 2.29657C29.3878 3.87093 30 5.79514 30 8.06921C30 10.8681 29.0379 13.5357 27.1137 16.0722C25.1457 18.6524 22.4125 20.8171 18.9139 22.5664ZM1.85839 22.5664L0.743218 20.4673C2.57997 19.2428 4.02313 18.1057 5.0727 17.0562C6.12227 16.0066 6.64705 14.9133 6.64705 13.7763C6.64705 12.9453 6.42839 12.2675 5.99107 11.7427C5.55375 11.2617 4.85404 10.6931 3.89193 10.0372C2.84236 9.33744 2.05518 8.63773 1.5304 7.93802C1.00561 7.28203 0.743218 6.42926 0.743218 5.37969C0.743218 3.80533 1.24614 2.51524 2.25198 1.5094C3.25782 0.503558 4.59165 0.000639235 6.25347 0.000639235C8.22141 0.000639235 9.8395 0.765951 11.1077 2.29657C12.3322 3.87093 12.9445 5.79514 12.9445 8.06921C12.9445 10.8681 11.9824 13.5357 10.0582 16.0722C8.09021 18.6524 5.35696 20.8171 1.85839 22.5664Z"
                    fill="<?php echo esc_attr( $color_map[ $color_class_name ] ); ?>" />
                </svg>
    
              </div>
              <div class="text font-ave-d text-20 leading-15 mb-20 opacity-80">
                “Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et
                dolore
                magna aliqua. Ut enim ad minim veniam, quis nostrud...”
              </div>
              <div class="author font-ave-b text-20">
                Marco
              </div>
            </div> <!-- item -->
          </div>
        </div>






  </div>
</section>





<section class="home-corsi mb-140">

  <div class="container-l mx-auto">


    <div class="head flex items-center justify-between gap-140 mb-100">
      <div class="main max-w-900 flex flex-col items-start">
        <div class="pretitle text-gradient-ocean-violet font-tt-eb text-16 uppercase spacing-12 mb-28">
          I corsi di Produzione Hip Hop
        </div>
        <div class="title font-din-bc text-60 mb-20">
          Lorem ipsum dolor sit amet, consectetur adipiscing
        </div>
        <div class="text text-gray font-ave-m leading-15 text-20 max-w-700">
          Lorem ipsum dolor sit amet, consectetur adipiscing elit. Quisque sapien libero, ultrices sed elit id, rhoncus
          sagittis augue.
        </div>

      </div>
      <div class="side relative">
        <a href="https://app-656e0683c1ac188b30f693df.closte.com/corsi/"
          class="button h-72 w-400 rounded-16 text-center border-gradient-ocean-violet border-4 border-solid border-transparent rounded-12 flex gap-20 place-center z-20 relative equalizer-1-hover hover-shadow-violet hover-up-2">
          <div class="text font-ave-b text-20">
            Vai alla pagina dei corsi
          </div>
          <div class="icon flex">
            <svg class="h-20" width="11" height="20" viewBox="0 0 11 20" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path
                d="M10.5824 10.5826L2.43421 18.7308C1.87735 19.2877 0.974503 19.2877 0.417644 18.7308C-0.139215 18.1739 -0.139215 17.2711 0.417644 16.7142L7.55751 9.57436L0.417643 2.4345C-0.139215 1.87764 -0.139215 0.974792 0.417643 0.417934C0.974502 -0.138924 1.87735 -0.138924 2.43421 0.417934L10.5824 8.56608C11.1392 9.12294 11.1392 10.0258 10.5824 10.5826Z"
                fill="#192329" />
            </svg>



          </div>
        </a>
      </div>

    </div>
    <div class="body">

      <div class="items grid grid-cols-3 gap-40">

        <?php
$args = array(
    'post_type' => 'corsi',
    'posts_per_page' => 3
);

$corsi_query = new WP_Query($args);

if ($corsi_query->have_posts()) :
    while ($corsi_query->have_posts()) : $corsi_query->the_post();

        $corso_cover_id    = null;
        $corso_icona_id    = null;
        $corso_durata      = '';
        $corso_descrizione = '';
        $color_class_name  = 'violet';
        $rwmb_get_value_func = 'rwmb_get_value';

        if (function_exists($rwmb_get_value_func)) {
            $corso_cover_data = $rwmb_get_value_func('corso_cover', ['size' => 'large']);
            $corso_cover_id   = $corso_cover_data['ID'] ?? null;

            $corso_icona_data = $rwmb_get_value_func('corso_icona');
            $corso_icona_id   = $corso_icona_data['ID'] ?? null;

            $corso_durata      = $rwmb_get_value_func('corso_durata');
            $corso_descrizione = $rwmb_get_value_func('corso_descrizione');
            $db_color          = $rwmb_get_value_func('corso_colore');
            if (!empty($db_color)) {
                $color_class_name = $db_color;
            }
        }
        ?>

        <div class="item rounded-24 bg-white">
          <div class="head relative mb-40">
            <div class="cover h-200 relative rounded-24 overflow-hidden">
              <?php if ($corso_cover_id) : ?>
              <?php load_img(class: 'img img-cover', high: $corso_cover_id); ?>
              <?php endif; ?>
            </div>
            <div class="badge absolute bottom--40 right-40 animate-float-updown-very-light animation-duration-5000">
              <?php if ($corso_icona_id) : ?>
              <?php load_img(class: 'img h-120 shadow-' . $color_class_name, high: $corso_icona_id); ?>
              <?php endif; ?>
            </div>
          </div>
          <div class="body">
            <div
              class="pretitle text-brand-<?php echo esc_attr($color_class_name); ?> font-tt-eb text-16 uppercase spacing-12 mb-28">
              Corso
            </div>
            <div class="title font-din-bc text-48 mb-20">
              <?php the_title(); ?>
            </div>
            <div class="text text-gray font-ave-m leading-15 text-20 mb-40 max-w-90-100">
              <?php echo esc_html($corso_descrizione); ?>
            </div>
            <div class="info flex gap-28 mb-40">
              <div class="time flex gap-20 items-center">
                <div class="icon flex">
                  <svg class="h-28" width="26" height="26" viewBox="0 0 26 26" fill="none"
                    xmlns="http://www.w3.org/2000/svg">
                    <circle cx="13" cy="13" r="11.75" stroke="#121D23" stroke-width="2.5" />
                    <path d="M13 6.5V13L9 15.3094" stroke="#121D23" stroke-width="2.5" stroke-linecap="round"
                      stroke-linejoin="round" />
                  </svg>
                </div>
                <div class="text font-ave-d text-20">
                  <?php echo esc_html($corso_durata); ?>
                </div>
              </div>
            </div>
            <a href="<?php the_permalink(); ?>"
              class="button mt-auto h-72 w-80-100 rounded-16 text-center border-brand-<?php echo esc_attr($color_class_name); ?> border-4 border-solid rounded-12 flex place-center z-20 relative hover-shadow-<?php echo esc_attr($color_class_name); ?> hover-up-2">
              <div class="text font-ave-b text-20">
                Maggiori informazioni
              </div>
            </a>
          </div>
        </div> <!-- item -->

        <?php
    endwhile;
    wp_reset_postdata();
else :
    ?>
        <p><?php _e('Nessun corso trovato.', 'custom-theme'); ?></p>
        <?php
endif;
?>

      </div>

    </div>


</section>



<?php get_template_part('templates/parts/footer-cta'); ?>



<?php get_template_part('templates/parts/footer-classic'); ?>


<?php
		endwhile;
endif;





get_sidebar();
get_footer();
