/*--------------------------------------------------------------

## Front Page - Intro

--------------------------------------------------------------*/

html, body {
  overflow-x:hidden;
}
 

/*--------------------------------------------------------------

## Front Page - Header

--------------------------------------------------------------*/
 

.home-header .background:before {
    content:"";
    inset:0;
    position:absolute;
    z-index:30;
    background:linear-gradient(90deg, rgba(25, 35, 41, 1) 0%, rgba(25, 35, 41,0) 100%);
}


.home-header .button {
    box-shadow:0px 8px 28px rgba(40,60,80,0);
    transition:all 0.3s ease;
    transform:translateY(0);
}

.home-header .button:hover {
    box-shadow:0px 8px 28px rgba(40,60,80,1);
    transition:all 0.3s ease;
    transform:translateY(-2px);
}

.home-header .radial-gradient-pink {
    bottom: calc((-43.75rem * var(--responsive-70)) * (var(--bottom--700, 1)));
    left: calc((-43.75rem * var(--responsive-70)) * (var(--left--700, 1)));
}

.home-header .radial-gradient-violet {
    top: calc((-43.75rem * var(--responsive-70)) * (var(--top--700, 1)));
    right: calc((-43.75rem * var(--responsive-70)) * (var(--right--700, 1)));
}


@media (max-width: 1920px) {
  
  
  
}

@media (max-width: 1680px) {
  
  
  
}

@media (max-width: 1440px) {
  
  
  
}

@media (max-width: 1280px) {
  
  
  
}

@media (max-width: 1120px) {
  
  
  
}

@media (max-width: 960px) {
  
  
  
}

@media (max-width: 640px) {
  
  
  
}

@media (max-width: 480px) {
  
  
  
}

@media (max-width: 360px) {
  
  
  
}


/*--------------------------------------------------------------

## Front Page - Objectives

--------------------------------------------------------------*/
 


.home-obj .side .item {
    animation: slide-up-obj 30s linear infinite;
  }
  
  
  
  @keyframes slide-up-obj {
    0% {
      transform: translate3d(0, 0, 0);
    }
    100% {
      transform: translate3d(0, -100px, 0); 
    }
  }
  
  
  
  .home-obj .items:before {
    content:"";
    position:absolute;
    top:0;
    left:0;
    right:0;
    background:linear-gradient(180deg, #FFFFFF 26.31%, rgba(255, 255, 255, 0) 100%);
    height: calc((18.75rem * var(--responsive-70)) * (var(--h-300, 1)));
    width:100%;
    z-index:50;
  }
  
  .home-obj .items:after {
    content:"";
    position:absolute;
    bottom:0;
    left:0;
    right:0;
    background:linear-gradient(180deg, #FFFFFF 26.31%, rgba(255, 255, 255, 0) 100%);
    height: calc((18.75rem * var(--responsive-70)) * (var(--h-300, 1)));
    width:100%;
    z-index:50;
    transform: matrix(1, 0, 0, -1, 0, 0);
  }



@media (max-width: 1920px) {
  
  
  
}

@media (max-width: 1680px) {
  
  
  
}

@media (max-width: 1440px) {
  
  
  
}

@media (max-width: 1280px) {
  
  
  
}

@media (max-width: 1120px) {
  
  
  
}

@media (max-width: 960px) {
  
  
  
}

@media (max-width: 640px) {
  
  
  
}

@media (max-width: 480px) {
  
  
  
}

@media (max-width: 360px) {
  
  
  
}


/*--------------------------------------------------------------

## Front Page - Target

--------------------------------------------------------------*/
 
.home-target {
    overflow:visible;
}

.home-target .subject img {
    height:calc(((31.25rem * var(--responsive-70)) * (var(--h-500, 1))) + 40px);
}

.home-target .background img {
    height:calc(((31.25rem * var(--responsive-70)) * (var(--h-500, 1))) + 40px);
    top:-40px;
}

.home-target .box .background:before {
    content:"";
    inset:0;
    position:absolute;
    z-index:60;
    background:linear-gradient(90deg, rgba(25, 35, 41, 1) 0%, rgba(25, 35, 41,0) 100%);
}


.home-target .box .subject:before {
    content:"";
    bottom:0;
    right:0;
    left:0;
    height:calc((31.25rem * var(--responsive-70)) * (var(--h-500, 1)));
    position:absolute;
    z-index:80;
    background:linear-gradient(90deg, rgba(25, 35, 41, 1) 0%, rgba(25, 35, 41,0) 40%);
}

@media (max-width: 1920px) {
  
  
  
}

@media (max-width: 1680px) {
  
  
  
}

@media (max-width: 1440px) {
  
  
  
}

@media (max-width: 1280px) {
  
  
  
}

@media (max-width: 1120px) {
  
  
  
}

@media (max-width: 960px) {
  
  
  
}

@media (max-width: 640px) {
  
  
  
}

@media (max-width: 480px) {
  
  
  
}

@media (max-width: 360px) {
  
  
  
}


/*--------------------------------------------------------------

## Front Page - Corsi

--------------------------------------------------------------*/
 

.home-corsi .pattern-container {
  --dot-color: #D9D9D9;
  --dot-size: 2px;        
  --dot-space: 10px;    
  background-image: radial-gradient(circle at center, var(--dot-color) var(--dot-size), transparent var(--dot-size));
  background-size: var(--dot-space) var(--dot-space);
  background-position: 0 0; 
}



#equalizer-container-1 {
  display: flex;
  gap: 6px;
  align-items: flex-end;
}

.equalizer-column-1 {
  display: flex;
  flex-direction: column-reverse;
  gap: 6px;
}
.equalizer-dot-1 {
  width: 4px;
  height: 4px;
  background-color: #d9d9d9;
  
  border-radius: 50%;
  transition: background-color 0.05s linear;
}
.equalizer-dot-1.highlight {
  background-color: rgba(25, 77, 212, 0.7);
}



@media (max-width: 1920px) {
  
  
  
}

@media (max-width: 1680px) {
  
  
  
}

@media (max-width: 1440px) {
  
  
  
}

@media (max-width: 1280px) {
  
  
  
}

@media (max-width: 1120px) {
  
  
  
}

@media (max-width: 960px) {
  
  
  
}

@media (max-width: 640px) {
  
  
  
}

@media (max-width: 480px) {
  
  
  
}

@media (max-width: 360px) {
  
  
  
}


/*--------------------------------------------------------------

## Front Page - Mockup

--------------------------------------------------------------*/
 

.home-mockup .item:before {
  content:"";
  inset:0;
  position:absolute;
  z-index:50;
  background: rgba(26, 36, 42, 0.6);
}


@media (max-width: 1920px) {
  
  
  
}

@media (max-width: 1680px) {
  
  
  
}

@media (max-width: 1440px) {
  
  
  
}

@media (max-width: 1280px) {
  
  
  
}

@media (max-width: 1120px) {
  
  
  
}

@media (max-width: 960px) {
  
  
  
}

@media (max-width: 640px) {
  
  
  
}

@media (max-width: 480px) {
  
  
  
}

@media (max-width: 360px) {
  
  
  
}


/*--------------------------------------------------------------

## Front Page - Key

--------------------------------------------------------------*/
 

.home-key .box .body::before {
  content: "";
  inset: 0;
  position: absolute;
  z-index: 40;
}



.home-key .box:nth-child(1) .body::before {
  background: linear-gradient(to bottom, #FFF8EC, rgba(255, 187, 68, 0.4));
}


.home-key .box:nth-child(2) .body::before {
  background: linear-gradient(to bottom, #F6F1FF, rgba(246, 241, 255, 0.4));
}


@media (max-width: 1920px) {
  
  
  
}

@media (max-width: 1680px) {
  
  
  
}

@media (max-width: 1440px) {
  
  
  
}

@media (max-width: 1280px) {
  
  
  
}

@media (max-width: 1120px) {
  
  
  
}

@media (max-width: 960px) {
  
  
  
}

@media (max-width: 640px) {
  
  
  
}

@media (max-width: 480px) {
  
  
  
}

@media (max-width: 360px) {
  
  
  
}


/*--------------------------------------------------------------

## Front Page - Reviews

--------------------------------------------------------------*/
 

.home-reviews .intro .item {
  background:#CFCFCF
}

.home-reviews .intro .item .inner {
background: rgba(255, 255, 255, 0.85);
backdrop-filter: blur(50px);
}

.home-reviews .content .item .play {
background: rgba(255, 255, 255, 0.3);
backdrop-filter: blur(4px);
}

.home-reviews .content .item .clip:before {
  content:"";
  inset:0;
  position:absolute;
  z-index:40;
  background: rgba(40,60,80,0.2);
}



@media (max-width: 1920px) {
  
  
  
}

@media (max-width: 1680px) {
  
  
  
}

@media (max-width: 1440px) {
  
  
  
}

@media (max-width: 1280px) {
  
  
  
}

@media (max-width: 1120px) {
  
  
  
}

@media (max-width: 960px) {
  
  
  
}

@media (max-width: 640px) {
  
  
  
}

@media (max-width: 480px) {
  
  
  
}

@media (max-width: 360px) {
  
  
  
}


/*--------------------------------------------------------------

## Front Page - About

--------------------------------------------------------------*/
 


.home-about .side .simone {
  background:#CFCFCF
}

.home-about .side .simone .inner {
  background: rgba(255, 255, 255, 0.85);
  backdrop-filter: blur(50px);
  }
  


@media (max-width: 1920px) {
  
  
  
}

@media (max-width: 1680px) {
  
  
  
}

@media (max-width: 1440px) {
  
  
  
}

@media (max-width: 1280px) {
  
  
  
}

@media (max-width: 1120px) {
  
  
  
}

@media (max-width: 960px) {
  
  
  
}

@media (max-width: 640px) {
  
  
  
}

@media (max-width: 480px) {
  
  
  
}

@media (max-width: 360px) {
  
  
  
}



/*--------------------------------------------------------------

## Front Page - 

--------------------------------------------------------------*/
 


@media (max-width: 1920px) {
  
  
  
}

@media (max-width: 1680px) {
  
  
  
}

@media (max-width: 1440px) {
  
  
  
}

@media (max-width: 1280px) {
  
  
  
}

@media (max-width: 1120px) {
  
  
  
}

@media (max-width: 960px) {
  
  
  
}

@media (max-width: 640px) {
  
  
  
}

@media (max-width: 480px) {
  
  
  
}

@media (max-width: 360px) {
  
  
  
}


/*--------------------------------------------------------------

## Front Page - 

--------------------------------------------------------------*/
 


@media (max-width: 1920px) {
  
  
  
}

@media (max-width: 1680px) {
  
  
  
}

@media (max-width: 1440px) {
  
  
  
}

@media (max-width: 1280px) {
  
  
  
}

@media (max-width: 1120px) {
  
  
  
}

@media (max-width: 960px) {
  
  
  
}

@media (max-width: 640px) {
  
  
  
}

@media (max-width: 480px) {
  
  
  
}

@media (max-width: 360px) {
  
  
  
}

