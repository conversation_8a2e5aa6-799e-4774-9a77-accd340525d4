
/*--------------------------------------------------------------

## Footer cta - WhatsApp

--------------------------------------------------------------*/
 


.box[data-box="whatsapp"] .subject img {
    height:calc(((31.25rem * var(--responsive-70)) * (var(--h-500, 1))) + 40px);
} 

.box[data-box="whatsapp"] .background img {
    height:calc(((31.25rem * var(--responsive-70)) * (var(--h-500, 1))) + 40px);
    top:-40px;
}
 
.box[data-box="whatsapp"] .container .background:before {
    content:"";
    inset:0;
    position:absolute;
    z-index:60;
    background:linear-gradient(90deg, #E9FBF0 0%, rgba(25, 35, 41,0) 100%);
}


.box[data-box="whatsapp"] .container .subject:before {
    content:"";
    bottom:0;
    right:0;
    left:0;
    height:calc((31.25rem * var(--responsive-70)) * (var(--h-500, 1)));
    position:absolute;
    z-index:80;
    background:linear-gradient(90deg, #E9FBF0 0%, rgba(25, 35, 41,0) 40%);
}



@media (max-width: 1920px) {
  
  
  
}

@media (max-width: 1680px) {
  
  
  
}

@media (max-width: 1440px) {
  
  
  
}

@media (max-width: 1280px) {
  
  
  
}

@media (max-width: 1120px) {
  
  
  
}

@media (max-width: 960px) {
  
  
  
}

@media (max-width: 640px) {
  
  
  
}

@media (max-width: 480px) {
  
  
  
}

@media (max-width: 360px) {
  
  
  
}




/*--------------------------------------------------------------

## Footer cta - Call

--------------------------------------------------------------*/
 


.box[data-box="call"] .subject img {
    height:calc(((31.25rem * var(--responsive-70)) * (var(--h-500, 1))) + 40px);
} 

.box[data-box="call"] .background img {
    height:calc(((31.25rem * var(--responsive-70)) * (var(--h-500, 1))) + 40px);
    top:-40px;
}
 
.box[data-box="call"] .container .background:before {
    content:"";
    inset:0;
    position:absolute;
    z-index:60;
    background:linear-gradient(90deg, #E7F3FD 0%, rgba(25, 35, 41,0) 100%);
}


.box[data-box="call"] .container .subject:before {
    content:"";
    bottom:0;
    right:0;
    left:0;
    height:calc((31.25rem * var(--responsive-70)) * (var(--h-500, 1)));
    position:absolute;
    z-index:80;
    background:linear-gradient(90deg, #E7F3FD 0%, rgba(25, 35, 41,0) 40%);
}



@media (max-width: 1920px) {
  
  
  
}

@media (max-width: 1680px) {
  
  
  
}

@media (max-width: 1440px) {
  
  
  
}

@media (max-width: 1280px) {
  
  
  
}

@media (max-width: 1120px) {
  
  
  
}

@media (max-width: 960px) {
  
  
  
}

@media (max-width: 640px) {
  
  
  
}

@media (max-width: 480px) {
  
  
  
}

@media (max-width: 360px) {
  
  
  
}