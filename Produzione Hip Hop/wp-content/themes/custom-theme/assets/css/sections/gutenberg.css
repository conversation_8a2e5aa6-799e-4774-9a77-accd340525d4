


/*--------------------------------------------------------------
 
## Reset

--------------------------------------------------------------*/










/*--------------------------------------------------------------
 
## Gutenberg

- Fare delle regole di margin condizionali a seconda dell'elemento successivo

--------------------------------------------------------------*/

.gutenberg > *:first-child {
    margin-top:0;
}


.gutenberg > *:last-child {
    margin-bottom:0;
}







/*--------------------------------------------------------------
## h2 h3
--------------------------------------------------------------*/


.gutenberg h2 {
    font-family: "DINNextLTPro", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-style: normal;
    font-stretch: condensed;
    font-weight: 700;
    text-transform:uppercase;
    line-height:0.85;
    font-size: max(
    calc((3.75rem * var(--responsive-30, 1)) * (var(--text-60, 1))),
    1.625rem * (var(--text-60-min, 1)));
    margin-bottom: calc((1.75rem * var(--responsive-70)) * (var(--mb-28, 1)));
    margin-top: calc((1.75rem * var(--responsive-70)) * (var(--mt-28, 1)));
}


.gutenberg h3 {
    font-size: max(
      calc((1.5rem * var(--responsive-50, 1)) * (var(--text-24, 1))),
      1.125rem * (var(--text-24-min, 1))
    );
    font-family: "AvenirNextLTProDemi", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-style: normal;
    font-stretch: normal;
    font-weight: 600;
    line-height: calc(1.5em * var(--leading-15, 1));
    margin-bottom: calc((1.75rem * var(--responsive-70)) * (var(--mb-28, 1)));
    margin-top: calc((1.75rem * var(--responsive-70)) * (var(--mt-28, 1)));
}



/*--------------------------------------------------------------
## h2, h3 - Stili specifici per l'editor di Gutenberg
--------------------------------------------------------------*/

.editor-styles-wrapper h2.wp-block-heading {
    font-family: "DINNextLTPro", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-style: normal;
    font-stretch: condensed;
    font-weight: 700;
    text-transform: uppercase;
    line-height: 0.85;
    font-size: max(
        calc((3.75rem * var(--responsive-30, 1)) * (var(--text-60, 1))),
        1.625rem * (var(--text-60-min, 1))
    );
    margin-bottom: calc((1.75rem * var(--responsive-70)) * (var(--mb-28, 1)));
    margin-top: calc((1.75rem * var(--responsive-70)) * (var(--mt-28, 1)));
}

.editor-styles-wrapper h3.wp-block-heading {
    font-size: max(
      calc((1.5rem * var(--responsive-50, 1)) * (var(--text-24, 1))),
      1.125rem * (var(--text-24-min, 1))
    );
    font-family: "AvenirNextLTProDemi", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-style: normal;
    font-stretch: normal;
    font-weight: 600;
    line-height: calc(1.5em * var(--leading-15, 1));
    margin-bottom: calc((1.75rem * var(--responsive-70)) * (var(--mb-28, 1)));
    margin-top: calc((1.75rem * var(--responsive-70)) * (var(--mt-28, 1)));
}




 
/*--------------------------------------------------------------
## p - Stili per il frontend (tramite classe .gutenberg)
--------------------------------------------------------------*/

.gutenberg p:first-child {
  margin-top:0;
}

.gutenberg p:last-child {
  margin-bottom:0;
}


.gutenberg > p:not(.has-large-font-size) {
  font-size: max(
    calc((1.25rem * var(--responsive-60, 1)) * (var(--text-20, 1))),
    1rem * (var(--text-20-min, 1))
  );
  line-height: calc(1.5em * var(--leading-15, 1));
  color: rgb(99, 108, 116);
  font-family: "AvenirNextLTProMedium", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-style: normal;
  font-stretch: normal;
  font-weight: 500;
  margin-bottom: calc((1.75rem * var(--responsive-70)) * (var(--mb-28, 1)));
  margin-top: calc((1.75rem * var(--responsive-70)) * (var(--mt-28, 1)));
}

.gutenberg > p.has-large-font-size {
  font-size: max(
    calc((1.75rem * var(--responsive-50, 1)) * (var(--text-28, 1))),
    1.25rem * (var(--text-28-min, 1))
  );
  font-family: "AvenirNextLTProMedium", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-style: normal;
  font-stretch: normal;
  font-weight: 500;
  line-height: calc(1.5em * var(--leading-15, 1));
  margin-bottom: calc((1.75rem * var(--responsive-70)) * (var(--mb-28, 1)));
  margin-top: calc((1.75rem * var(--responsive-70)) * (var(--mt-28, 1)));
}

/*--------------------------------------------------------------
## p - Stili specifici per l'editor di Gutenberg
--------------------------------------------------------------*/

/* Stile per paragrafo normale nell'editor */
.editor-styles-wrapper .wp-block-paragraph:not(.is-style-large):not(.has-large-font-size) {
  font-size: max(
    calc((1.25rem * var(--responsive-60, 1)) * (var(--text-20, 1))),
    1rem * (var(--text-20-min, 1))
  )!important;
  line-height: calc(1.5em * var(--leading-15, 1));
  color: rgb(99, 108, 116);
  font-family: "AvenirNextLTProMedium", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-style: normal;
  font-stretch: normal;
  font-weight: 500;
  margin-bottom: calc((1.75rem * var(--responsive-70)) * (var(--mb-28, 1))) !important;
  margin-top: calc((1.75rem * var(--responsive-70)) * (var(--mt-28, 1))) !important;
}

/* Stile per paragrafo "Large" nell'editor */
.editor-styles-wrapper .wp-block-paragraph.has-large-font-size {
  font-size: max(
    calc((1.75rem * var(--responsive-50, 1)) * (var(--text-28, 1))),
    1.25rem * (var(--text-28-min, 1))
  )!important;
  font-family: "AvenirNextLTProMedium", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-style: normal;
  font-stretch: normal;
  font-weight: 500;
  line-height: calc(1.5em * var(--leading-15, 1));
  margin-bottom: calc((1.75rem * var(--responsive-70)) * (var(--mb-28, 1))) !important;
  margin-top: calc((1.75rem * var(--responsive-70)) * (var(--mt-28, 1))) !important;
}




.editor-styles-wrapper p:first-child {
  margin-top:0;
}

.editor-styles-wrapper p:last-child {
  margin-bottom:0;
}


/*--------------------------------------------------------------
## a
--------------------------------------------------------------*/



.editor-styles-wrapper a.flex {
  color:initial!important;
  text-decoration:none!important;
}







/*--------------------------------------------------------------
## ul
--------------------------------------------------------------*/









 


/*--------------------------------------------------------------
## Video
--------------------------------------------------------------*/






/*--------------------------------------------------------------

## Gutenberg - Outline

--------------------------------------------------------------*/
 




/*--------------------------------------------------------------

## Gutenberg - Swiper

--------------------------------------------------------------*/
 

.gutenberg .swiper-slide {
    width: auto;
}


.gutenberg .swiper-button-disabled {
    opacity:0;
}




/*--------------------------------------------------------------

## Gutenberg - Gallery slider

--------------------------------------------------------------*/
 


/* Stile per paragrafo "Large" nell'editor */
.editor-styles-wrapper .block-gallery-slider img {
  max-height:600px;
}







/*--------------------------------------------------------------

## Gutenberg - FAQ

--------------------------------------------------------------*/
 



@media (max-width: 1920px) {
  
  
  
}

@media (max-width: 1680px) {
  
  
  
}

@media (max-width: 1440px) {
  
  
  
}

@media (max-width: 1280px) {
  
  
  
}

@media (max-width: 1120px) {
  
  
  
}

@media (max-width: 960px) {
  
  
  
}

@media (max-width: 640px) {
  
  
  
}

@media (max-width: 480px) {
  
  
  
}

@media (max-width: 360px) {
  
  
  
}



/*--------------------------------------------------------------

## Gutenberg - 

--------------------------------------------------------------*/
 


@media (max-width: 1920px) {
  
  
  
}

@media (max-width: 1680px) {
  
  
  
}

@media (max-width: 1440px) {
  
  
  
}

@media (max-width: 1280px) {
  
  
  
}

@media (max-width: 1120px) {
  
  
  
}

@media (max-width: 960px) {
  
  
  
}

@media (max-width: 640px) {
  
  
  
}

@media (max-width: 480px) {
  
  
  
}

@media (max-width: 360px) {
  
  
  
}

