
/*--------------------------------------------------------------

## Cookie - 

--------------------------------------------------------------*/
.cs_preview_container .policy_wrap a {
  color:white!important;
  font-size:14px!important
}

.cs_preview_container .bar_description_text,.cs_preview_container .bar_description_text span {
  font-size:14px!important;
}
 
@media (max-width:600px) {
  .cs_preview_container .policy_wrap a {
  color:white!important;
  font-size:12px!important
} 

.cs_preview_container .bar_description_text,.cs_preview_container .bar_description_text span {
  font-size:12px!important;
}
  
}


.cs-modal-open .cs_preview_popup {
  display:block!important;
}

.cs-info-sticky {
  display:none!important;
}

.cs-public-cookie-bar {
  background: transparent !important;
box-shadow: none !important;
  padding:40px 0px 0px!important;
  width:1000px!important;
}

.cs_preview_container .btn, .cs_preview_container a, .cs_preview_container button {
  text-transform:initial!important;
}

.cs-wrapper {
  background-color: rgba(32, 42, 51, 0.95) !important;
padding: 40px 50px!important;
border-radius: 18px!important;
}

.cs_footer_btn {
  display:none!important;
}

#wt-cli-cookie-banner {
  font-size:16px!important;
}
.cs_preview_container .policy_wrap a::after {
  background-color:#fff!important;
}

.bar_description_text,
.bar_description_text span {
  color:white!important;
  padding-right:30px!important;
  line-height:1.2!important;
  font-size:16px!important;
}

.cs-public-cookie-bar.bar_small.bottom, .cs-public-cookie-bar.bar_large.bottom {
  bottom:50px!important;
}

.cs-public-cookie-bar.bar_small .btn-row {
  display:grid!important;
  grid-template-columns:1fr 1fr;
  gap:10px;
}

.cs-public-cookie-bar.bar_small .btn-row button:nth-child(1) {
  grid-column:span 2;
  order:2;
}

.cs_preview_container .cs-cookie-bar-container .policy_wrap a {
  color:white!important;
  font-size:16px!important;
}

.cs_action_btn {
  background-color: #fff!important;
  font-size:16px!important;
  color:#333!important;
}

.options_btn {
  color:white!important;
  background:rgba(255,255,255,0.1)!important;
  font-size:16px!important;
}

  .btns_column {
    max-width:400px!important;
    flex:0 0 400px!important;
  }

.btns_column button {
  margin:0!important;
}

.cs-modal-body .btn {
  margin:0!important;
}


.cs-modal-body .cs_action_btn {
  background:#243946 !important;
  color:white!important;
}



.cs-modal-content {
  height:100%!important;
  border-radius:0px!important;
  overflow-y:scroll!important;
  padding: calc((var(--p-80)/100)*(var(--40)))!important;
  padding-right: calc((var(--pr-100)/100)*(var(--50)))!important;
  width:calc(100% + 20px)!important;
  margin-right:-20px!important;
}

.cs-modal-dialog {
  height:100lvh!important;
  margin:0px!important;
  overflow:hidden!important;
  max-width:700px!important;
  font-family:"Avenir Next LT W01 Medium"important;
}

.cs-modal-body {
  padding:0px!important;
}

.cs-privacy-content-text {
  margin-bottom:20px!important;
}

.custom-switch {
  margin-right:0px!important;
}

.cs-privacy-overview {
  padding-bottom:0px!important;
}

.cs-modal-dialog .row:nth-child(2) {
  display:grid!important;
  grid-template-columns:1fr!important;
  gap:10px;
  text-align:left!important;
}

.cs-modal-dialog div {
  font-size:15px!important;
  color: #798b97!important;
}

.cs-modal-dialog h4 {
  font-size:25px!important;
  margin-bottom:0px!important;
  line-height: 1.1 !important;
font-weight: initial !important;
  display:none!important;
}



.cs-modal-dialog h5 {
  font-size:25px!important;
  margin-bottom:0px!important;
  line-height: 1.1 !important;
font-weight: initial !important;
}

.cs_preview_container .cs-cookie-bar-container .cs-modal .cs-privacy-overview {
  margin-bottom:40px!important;
}

.cs-modal-dialog h3 {
  font-size:22px!important;
  margin-bottom:20px!important;
}

.col.right-aligned.green-text {
  text-align:left!important;
}

.cs_preview_container .cs-cookie-bar-container .cs-modal-backdrop {
  background-color: rgb(40,60,80)!important;
  opacity:0.35!important;
}

.cs-modal-body {
  max-height: none!important;
overflow-y: initial!important;
padding: 0px !important;
}

.cs_preview_container .cs-cookie-bar-container .cs-modal.cs-blowup {
  transition:none!important;
  transform:scale(1)!important;
}

.cs_preview_container .cs-cookie-bar-container .cs-modal-body .policy_wrap a {
  color:#444!important;
  text-decoration:underline!important;
}

.cs-tab-footer.btn-row {
  display:grid!important;
  grid-template-columns:1fr!important;
  gap: calc((var(--gap-10)/100)*(var(--70)));
  text-align:center!important;
  margin-top:0px!important;
}

.cs_preview_container .cs-cookie-bar-container .cs-modal .cs-tab-footer.btn-row .btn {
  margin-bottom:0px!important;
}

.row.no-gutters.no-padding.align-items-center.cs_policy_existing_page .btn-row {
  display:grid!important;
  margin-top:10px!important;
  grid-template-columns:1fr!important;
  gap: calc((var(--gap-10)/100)*(var(--70)))!important;
  text-align:center!important;
  
}

.cs-tab-footer .btn.disable_all_btn.cs_action_btn {
  display:block!important;
}

.cs_preview_container .cs-cookie-bar-container .cs-modal-body {
  padding:0px!important;
}

.cs_preview_container .cs-container-fluid.cs_deny_all_btn .cs-privacy-content .disable_all_btn {
  display:block!important;
}

.cs_preview_container .cs-cookie-bar-container .cs-modal .cs-tab-footer.btn-row .btn:nth-child(2),
.cs_preview_container .cs-cookie-bar-container .cs-modal .cs-tab-footer.btn-row .btn:nth-child(3){
  display:none!important
}

.cs_preview_container .cs-cookie-bar-container .cs-modal .cs-privacy-overview .btn-row .btn:nth-child(2) {
  order:-1;
}


@media (max-width:1525px) {
  
  
}

@media (max-width:1070px) {
  
.cs-public-cookie-bar {
  width:80%!important;
  max-width:600px!important;
  }
  
  .cs_preview_container .cs-cookie-bar-container .cs-info-bar:not(.popup_large) .btns_column, .cs_preview_container .cs-cookie-bar-container .cs-info-bar:not(.popup_small) .btns_column {
    margin-top:35px!important;
  }
  
.bar_description_text {
  padding-right:0px!important;
  text-align:center!important;
  }
  
  .cs-popup-row {
    flex-direction:column!important;
  }
  
  
  .cs-wrapper {
    padding:25px 30px !important;
  }
  
  .cs-wrapper .mr-20 {
    margin-right:0px;
  }
  
  .cs-public-cookie-bar.bar_large, .cs-public-cookie-bar.bar_small {
    text-align:center!important;
  }
  
  
  
  .cs_preview_container .cs-cookie-bar-container .cs-info-bar:not(.popup_large) .btns_column, .cs_preview_container .cs-cookie-bar-container .cs-info-bar:not(.popup_small) .btns_column {
    margin-top:25px!important;
  }
  
  .cs_preview_container .cs-cookie-bar-container .cs-info-bar:not(.popup_large) .btns_column .btn-row, .cs_preview_container .cs-cookie-bar-container .cs-info-bar:not(.popup_small) .btns_column .btn-row {
    display:grid!important;
  }
  
  .cs_preview_container .cs-cookie-bar-container .cs-info-bar:not(.popup_large) .btns_column.cs_deny_all_btn .disable_all_btn, .cs_preview_container .cs-cookie-bar-container .cs-info-bar:not(.popup_small) .btns_column.cs_deny_all_btn .disable_all_btn {
    margin:0px!important;
  }
  
}

@media (max-width:1070px) {
  
  
.cs-public-cookie-bar.bar_small.bottom, .cs-public-cookie-bar.bar_large.bottom {
  bottom:0px!important;
}

  
  .cs_preview_container .cs-cookie-bar-container .cs-info-bar:not(.popup_large) .btns_column.cs_deny_all_btn .cs_action_btn, .cs_preview_container .cs-cookie-bar-container .cs-info-bar:not(.popup_small) .btns_column.cs_deny_all_btn .cs_action_btn {
    margin:0px!important;
  }
  
  .cs_preview_container .cs-cookie-bar-container .cs-info-bar:not(.popup_large) .btns_column.cs_deny_all_btn .options_btn, .cs_preview_container .cs-cookie-bar-container .cs-info-bar:not(.popup_small) .btns_column.cs_deny_all_btn .options_btn {
    margin:0px!important;
  }
  
  
.col.btn-row {
  grid-template-columns:1fr!important;
  }
  
  
  .btns_column {
    display:block!important;
    flex:initial!important;
    max-width:none!important;
  }
  
  
.cs-tab-footer.btn-row {
  grid-template-columns:1fr!important;
}
  
  .cs-modal-content {
    padding-bottom:120px!important;
  }
  
  .cs_preview_container .cs-cookie-bar-container .cs-modal .cs_policy_existing_page > div > a {
    margin-bottom:0px!important;
  }
}




@media (max-width:500px) {
  
  .cs-modal-dialog h5 {
    font-size:20px!important;
  }
  
  .cs-modal-dialog div {
    line-height:1.3!important;
  }
  
.cs-public-cookie-bar {
  padding:20px 0px 0px!important;
  width:90%!important;
}

.cs-wrapper {
padding: 20px 25px!important;
border-radius: 12px!important;
}

#wt-cli-cookie-banner {
  font-size:13px!important;
}

.bar_description_text {
  line-height:1.1!important;
  font-size:13px!important;
  }
  
  .options_btn {
    padding:12px 0px!important;
  }
  
  .cs_preview_container .cs-cookie-bar-container .policy_wrap {
    margin-top:5px!important;
  }
  
  .cs_action_btn {
    padding:13px 10px!important;
    line-height:1!important;
  }

.cs_preview_container .cs-cookie-bar-container .policy_wrap a {

  font-size:13px!important;
}

.cs_action_btn {
  font-size:13px!important;
}

.options_btn {
  font-size:13px!important;
}
  
  
  .cs_preview_container .cs-cookie-bar-container .cs-info-bar:not(.popup_large) .btns_column, .cs_preview_container .cs-cookie-bar-container .cs-info-bar:not(.popup_small) .btns_column {
    margin-top:10px!important;
  }
  
  .cs_preview_container .cs-cookie-bar-container .cs-info-bar:not(.popup_large) .btns_column .btn-row, .cs_preview_container .cs-cookie-bar-container .cs-info-bar:not(.popup_small) .btns_column .btn-row {
    gap:10px!important;
  }
  
  
}


























