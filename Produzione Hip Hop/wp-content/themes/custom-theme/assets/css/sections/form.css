/*--------------------------------------------------------------

## Form - reset
 
--------------------------------------------------------------*/

input {
    outline:none;
  }
  
  textarea {
    outline:none;
  }
  
   
  
  
  /*--------------------------------------------------------------
  
  ## Form - field - input-text
   
  --------------------------------------------------------------*/
    
  .field.text input:focus + label,
  .field.text input:not(:placeholder-shown) + label {
    top: 30%;
    font-size: 0.75rem;
    transition: 0.25s linear all;
  }
  
  .field.text:has(input:focus-visible) {
      outline: 2px solid #323841; /* A custom color you choose */
      outline-offset: 2px;
    }
  
    .field.text.error {
      border-color:rgba(239, 68, 68);
    }
  
  
  .field.text:has(input[readonly]) {
    border:2px solid rgb(241, 245, 246);
    background-color:rgba(241, 245, 246,1);
    outline:none;
  }
  
  
  
  .field.text:has(input[readonly]) .unlock {
    display:flex;
  }
  
  .field.text input[readonly] {
    pointer-events:none;
  }
  
  
  
  
  /*--------------------------------------------------------------
  
  ## Form - field - input-textarea
   
  --------------------------------------------------------------*/
    /*  
  .field.textarea textarea:focus + label,
  .field.textarea textarea:not(:placeholder-shown) + label {
    top: 10px;
    font-size: 0.75rem;
    transition: 0.25s linear all;
  }
  
  
  
    .field.textarea:before {
      content:"";
      position:absolute;
      top:0;
      left:0;
      right:0;
      height:46px;
      z-index:50;
      border-radius:12px;

background: linear-gradient(180deg, #FFFFFF 0%, #FFFFFF 51.92%, rgba(255, 255, 255, 0.09) 100%);

  
    }
  */
  
    .field.textarea.error {
      border-color:rgba(239, 68, 68);
    }
  
  .field.textarea:has(textarea:focus-visible) {
      outline: 2px solid #323841; 
      outline-offset: 2px;
    }
  
  /*--------------------------------------------------------------
  
  ## Form - field - input-number
   
  --------------------------------------------------------------*/
    
  
  /* Chrome, Safari, Edge, Opera */
  .field.number input[type="number"]::-webkit-outer-spin-button,
  .field.number input[type="number"]::-webkit-inner-spin-button {
    -webkit-appearance: none;
    margin: 0;
  }
  
  /* Firefox */
  .field.number input[type="number"] {
    -moz-appearance: textfield;
  }
  
  .field.number input:focus + label,
  .field.number input:not(:placeholder-shown) + label {
    top: 30%;
    font-size: 0.75rem;
    transition: 0.25s linear all;
  }
  
  .field.number:has(input:focus-visible) {
      outline: 2px solid #323841; /* A custom color you choose */
      outline-offset: 2px;
    }
  
    .field.number.error {
      border-color:rgba(239, 68, 68);
    }
  
  
  .field.number:has(input[readonly]) {
    border:2px solid rgb(241, 245, 246);
    background-color:rgba(241, 245, 246,1);
    pointer-events:none;
    outline:none;
  }
  
  
  
  
  
  
  
  
  
  /*--------------------------------------------------------------
  
  ## Form - field - input-password
  
  --------------------------------------------------------------*/
  
  .field.password input:focus + label,
  .field.password input:valid + label {
    top: 30%;
    font-size: 0.75rem;
    transition: 0.25s linear all;
  }
  
  
  .field.password input:focus-visible {
      outline: 2px solid #323841; /* A custom color you choose */
      outline-offset: 2px;
    }
  
    .field.password.error input {
      border-color:rgba(239, 68, 68);
    }
  
  
  
  
  
  
  
  
  
  
  /*--------------------------------------------------------------
  
  ## Form - input - checkbox
  
  --------------------------------------------------------------*/
  
  .checkbox input:disabled {
    opacity:0.5;
  }
  
  .checkbox input:checked {
    background-color: rgba(169, 115, 255, var(--background-opacity, 1));
    border-color: rgba(169, 115, 255, var(--background-opacity, 1));
  }
  
  .checkbox input:checked::before {
    content:"";
    background-color:white;
    width:100%;
    height:100%;
    display:block;
    mask: url("data:image/svg+xml,%3Csvg width='14' height='11' viewBox='0 0 14 11' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath fill-rule='evenodd' clip-rule='evenodd' d='M13.0894 0.997366C13.6653 1.44447 13.7696 2.27371 13.3225 2.84952L7.51449 10.3295C7.29636 10.6104 6.97409 10.7915 6.6207 10.8316C6.2673 10.8717 5.91267 10.7674 5.63715 10.5425L1.32515 7.02252C0.760407 6.56151 0.676319 5.72997 1.13733 5.16523C1.59834 4.60049 2.42988 4.51641 2.99462 4.97742L6.25882 7.64207L11.2373 1.23042C11.6844 0.654604 12.5136 0.550263 13.0894 0.997366Z' fill='white'/%3E%3C/svg%3E%0A") no-repeat 50% 50%;
  }
  
  
  /* Quando il radio interno è selezionato, il bordo dell'intero box diventa rosso */
  .option.border-gray.checkbox:has(input:checked) {
    border-color: rgba(169, 115, 255, 1);
  }
  
  
  
  
  /*--------------------------------------------------------------
  
  ## Form - input - radio
  
  --------------------------------------------------------------*/
  
  
  /* Radio disabilitato */
  .option.radio input:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  
  /* Quando il radio interno è selezionato, il bordo dell'intero box diventa rosso */
  .option.radio:has(input:checked) {
    border-color: rgba(149, 40, 33, 1);
  }
  
  /* Colore del testo quando l’opzione è selezionata */
  .option.radio:has(input:checked) span {
    color: rgb(50, 56, 65);
  }
  
  
  /* Stato attivo: colore di sfondo e di bordo */
  .option.radio input:checked {
    background-color: rgba(149, 40, 33, var(--background-opacity, 1));
    border-color: rgba(149, 40, 33, var(--background-opacity, 1));
  }
  
  /* Cerchio interno “check” */
  .option.radio input:checked::before {
    content: "";
    display: block;
    width: 50%;
    height: 50%;
    background-color: #fff;
    border-radius: 50%;
    position: absolute;
    top: 25%;
    left: 25%;
  }
  
  
  
  
  /*--------------------------------------------------------------
  
  ## Form - input - phone
  
  --------------------------------------------------------------*/
  
  .field.phone.focused label {
    top: 30%;
    font-size: 0.75rem;
    transition: 0.25s linear all;
  }
  
  
  .field.phone .iti {
      width:100%;
      height:100%;
  }
  
  
  .field.phone:has(input:focus-visible) {
      outline: 2px solid #323841; /* A custom color you choose */
      outline-offset: 2px;
    }
  
  
    .field.phone.error {
      border-color:rgba(239, 68, 68);
    }
  
  
    
    
  /*--------------------------------------------------------------
  
  ## Form - field - input-file
   
  --------------------------------------------------------------*/
    
  .field.file input:focus + label,
  .field.file input:not(:placeholder-shown) + label {
    top: 30%;
    font-size: 0.75rem;
    transition: 0.25s linear all;
  }