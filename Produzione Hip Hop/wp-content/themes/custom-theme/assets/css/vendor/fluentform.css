

/*--------------------------------------------------------------

## Form - Generale

--------------------------------------------------------------*/

.S-form-sfs-desc {
    display:none;
  }
  
  
  /*--------------------------------------------------------------
  
  ## Form - Reset
  
  --------------------------------------------------------------*/
  
  .fluentform .ff-el-group {
    margin-bottom: 0px!important;
  }
  
  .fluentform .ff-el-form-check label.ff-el-form-check-label {
    margin-bottom:0px!important;
  }
  
  .ff-el-group.ff-el-is-error .ff-el-form-check-label {
    color:initial!important;
  }
  
  .ff-el-group.mb-8 {
    margin-bottom: calc((0.5rem * var(--responsive-70)) * (var(--mb-8, 1)))!important;
  }
  
  .ff-el-group.mb-10 {
    margin-bottom: calc((0.625rem * var(--responsive-70)) * (var(--mb-10, 1)))!important;
  }
  
  .ff-el-group.mb-14 {
    margin-bottom: calc((0.875rem * var(--responsive-70)) * (var(--mb-14, 1)))!important;
  }
  
  .ff-el-group.mb-28 {
    margin-bottom: calc((1.75rem * var(--responsive-70)) * (var(--mb-28, 1)))!important;
  }
  
  .ff-el-group.mb-48 {
    margin-bottom: calc((3rem * var(--responsive-60)) * (var(--mb-48, 1)))!important;
  }



  /*--------------------------------------------------------------
  
  ## Form - Container
  
  --------------------------------------------------------------*/


  .fluentform fieldset {
    display:grid;
    grid-template-columns: repeat(12, minmax(0, 1fr));
  }

  

  
  /*--------------------------------------------------------------
  
  ## Form - Cols
  
  --------------------------------------------------------------*/

  .fluent-cols-2 .ff-t-column-1 {
    display:grid!important;
    grid-template-columns: repeat(2, minmax(0, 1fr));
    gap: calc((1.25rem * var(--responsive-70)) * (var(--gap-20, 1)));
  }

  .fluent-cols-3 .ff-t-column-1 {
    display:grid!important;
    grid-template-columns: repeat(3, minmax(0, 1fr));
    gap: calc((1.25rem * var(--responsive-70)) * (var(--gap-20, 1)));
  }


  /*--------------------------------------------------------------
  
  ## Form - Birthdate
  
  --------------------------------------------------------------*/


  .fluent-birthdate .ff-t-column-1 {
    display:flex!important;
    flex-direction:row!important;
    gap: calc((1.25rem * var(--responsive-70)) * (var(--gap-20, 1)));
    padding-top: calc((2rem * var(--responsive-70)) * (var(--pt-32, 1)));
  }

  .fluent-birthdate select {
    border:none!important;
    padding:0px!important;
    font-family: "AvenirNextLTProDemi", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"!important;
    color:#192329!important;
  }

  .fluent-birthdate {
    width:100%;
    height: calc((5rem * var(--responsive-40)) * (var(--h-80, 1)))!important;
    min-height: 3.75rem;
    border:2px solid #E4EBF0;
    font-family: "AvenirNextLTProMedium", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"!important;
    border-radius: calc((0.75rem * var(--responsive-70)) * (var(--rounded-12, 1)));
    font-size: max(
    calc((1.25rem * var(--responsive-60, 1)) * (var(--text-20, 1))),
    1rem * (var(--text-20-min, 1))
  );
    padding-left: calc((2.5rem * var(--responsive-60)) * (var(--pl-40, 1)))!important;
    position:relative;
  }

  .fluent-birthdate .title {
    position:absolute!important;
    top:50%!important;
    left: calc((2.5rem * var(--responsive-60)) * (var(--left-40, 1)))!important;
    transform:translateY(-180%)!important;
    opacity:1!important;
    font-size:11px!important;
    font-family: "AvenirNextLTProMedium", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"!important;
    letter-spacing:0.3px!important;
  }

  .fluent-day {
    grid-column: span 4;
  }

  .fluent-month {
    grid-column: span 4;
  }

  .fluent-year {
    grid-column: span 4;
  }
  
  
  /*--------------------------------------------------------------
  
  ## Form - Text
  
  --------------------------------------------------------------*/
  
  .fluent-text {
    width:100%;
    height: calc((5rem * var(--responsive-40)) * (var(--h-80, 1)))!important;
    min-height: 3.75rem;
    border:2px solid #E4EBF0;
    font-family: "AvenirNextLTProMedium", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"!important;
    border-radius: calc((0.75rem * var(--responsive-70)) * (var(--rounded-12, 1)));
    font-size: max(
    calc((1.25rem * var(--responsive-60, 1)) * (var(--text-20, 1))),
    1rem * (var(--text-20-min, 1))
  );
    display:flex;
    align-items:center;
    padding-left: calc((2.5rem * var(--responsive-60)) * (var(--pl-40, 1)))!important;
    position:relative;
  }
  
  .fluent-text .ff-el-input--label {
    position:absolute!important;
    top:50%!important;
    transform:translateY(-50%)!important;
    left: calc((2.5rem * var(--responsive-60)) * (var(--left-40, 1)))!important;
    font-size: max(
    calc((1.25rem * var(--responsive-60, 1)) * (var(--text-20, 1))),
    1rem * (var(--text-20-min, 1))
  )!important;
    transition:0.15s linear all!important;
    font-family: "AvenirNextLTProDemi", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"!important;
    color:rgb(99, 108, 116)!important;
    letter-spacing:0.3px!important;
  }
  
  .fluent-text .ff-el-input--label label:after {
    display:none!important;
  }
  
  .fluent-text input {
    position:absolute;
    top:0;
    left:0;
    right:0;
    bottom:0;
    padding-top: calc((1.25rem * var(--responsive-70)) * (var(--pt-20, 1)))!important;
    border:none!important;
    height:100%;
    width:100%!important;
    box-shadow:none;
    font-family: "AvenirNextLTProDemi", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"!important;
    color:#192329!important;
    background:transparent!important;
    padding-left: calc((2.5rem * var(--responsive-60)) * (var(--pl-40, 1)))!important;
    font-size: max(
    calc((1.25rem * var(--responsive-60, 1)) * (var(--text-20, 1))),
    1rem * (var(--text-20-min, 1))
  )!important;
  }
  
  .fluent-text:has(input:focus-visible) {
    outline: 2px solid #192821;
    outline-offset: 2px;
}
  
  .fluent-text.focus .ff-el-input--label {
    transform:translateY(-180%)!important;
    opacity:1!important;
    font-size:11px!important;
    transition:0.15s linear all;
    letter-spacing:0.3px!important;
  }
  
  .ff-errors-in-stack .error {
    width:100%;
    text-align:center;
    margin-top:0px!important;
  }
  
  .fluent-text .error.text-danger {
    position: absolute;
  top: 50%;
  right: 30px;
  transform: translateY(-150%);
  margin: 0!important;
  text-align: right;
  }
  
  
  
  @media (max-width: 1920px) {
    
    
    
  }
  
  @media (max-width: 1680px) {
    
    
    
  }
  
  @media (max-width: 1440px) {
    
    
    
  }
  
  @media (max-width: 1280px) {
    
  .fluent-text.focus .ff-el-input--label {
    transform:translateY(-150%)!important;
    letter-spacing:0px!important;
    }
    
    
  .fluent-text input {
    padding-top:20px!important;
    }
    
    
    
    
  .fluent-text .ff-el-input--label {
    letter-spacing:0px!important;
    
    }
    
  }
  
  @media (max-width: 1120px) {
    
    
    
  }
  
  @media (max-width: 960px) {
    
    
    
  }
  
  @media (max-width: 640px) {
    
    
    
  }
  
  @media (max-width: 480px) {
    
    
    
  }
  
  @media (max-width: 360px) {
    
    
    
  }
  
  
  
  
  
  
  
  
  
  /*--------------------------------------------------------------
  
  ## Form - Radio
  
  --------------------------------------------------------------*/
  
  .fluent-radio input {
    display:none!important;
  }
  
  .fluent-radio label {
    width:100%;
    height: calc((5rem * var(--responsive-40)) * (var(--h-80, 1)))!important;
    border:2px solid #E4EBF0;
    font-family: "AvenirNextLTProDemi", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    color:rgb(99, 108, 116)!important;
    border-radius: calc((0.75rem * var(--responsive-70)) * (var(--rounded-12, 1)));
    font-size: max(
    calc((1.25rem * var(--responsive-60, 1)) * (var(--text-20, 1))),
    1rem * (var(--text-20-min, 1))
  );
    display:flex;
    align-items:center;
    padding-left: calc((5rem * var(--responsive-40)) * (var(--pl-80, 1)));
    position:relative;
  }

  .fluent-radio .ff_item_selected label {
    color:#192329!important;
  }
  
  
  .fluent-radio label:before {
    content:"";
    height: calc((1.5rem * var(--responsive-70)) * (var(--h-24, 1)));
    width: calc((1.5rem * var(--responsive-70)) * (var(--w-24, 1)));
    border-radius:25px;
    border:2px solid #e4ebf0;
    background:white;
    position:absolute;
    top:50%;
    transform:translateY(-50%);
    left: calc((1.75rem * var(--responsive-70)) * (var(--left-28, 1)));
    z-index:20;
  }
  
  .fluent-radio .ff_item_selected label {
    border:2px solid #192821;
  }
  
  .fluent-radio .ff_item_selected label:before {
    border:calc((0.5rem * var(--responsive-70)) * (var(--h-8, 1))) solid #192821;
    background:white;
  }
  

  .fluent-radio.fluent-cols-2 .ff-el-input--content {
    display:grid;
    grid-template-columns:repeat(2, minmax(0, 1fr));
    gap: calc((1.25rem * var(--responsive-70)) * (var(--gap-20, 1)));
  }
  
  
  
  @media (max-width: 1920px) {
    
    
    
  }
  
  @media (max-width: 1680px) {
    
    
    
  }
  
  @media (max-width: 1440px) {
    
    
    
  }
  
  @media (max-width: 1280px) {
    
    .fluent-radio label {
      --pl-80:100px;
    }
    
  }
  
  @media (max-width: 1120px) {
    
    
    
  }
  
  @media (max-width: 960px) {
    
    
    
  }
  
  @media (max-width: 640px) {
    
    
    
  }
  
  @media (max-width: 480px) {
    
    
    
  }
  
  @media (max-width: 360px) {
    
    
    
  }



  
  /*--------------------------------------------------------------
  
  ## Form - Radio images
  
  --------------------------------------------------------------*/
  
  .fluent-radio-images input {
    display:none!important;
  }

  .fluent-radio-images .ff-el-image-holder {
    border:2px solid #E4EBF0!important;
    border-radius: calc((0.75rem * var(--responsive-70)) * (var(--rounded-12, 1)));
    position:relative!important;
  }

  .fluent-radio-images .ff-el-image-input-src {
    background-size:contain!important;
    background-position:center!important;
    height:160px!important;
    width:100%!important;
  }
  
  .fluent-radio-images .ff-el-form-check-label {
    width:100%;
    
    
    font-family: "AvenirNextLTProDemi", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    color:rgb(99, 108, 116)!important;
    text-align:center!important;
    
    
    
    
  }

  .fluent-radio-images .ff-el-form-check-label span {
    background-color: initial!important;
    border-color: initial!important;
    box-shadow: none!important;
    color: rgb(99, 108, 116)!important;
    padding: calc((2.5rem * var(--responsive-60)) * (var(--p-40, 1)))!important;
    font-size: max(
        calc((1.25rem * var(--responsive-60, 1)) * (var(--text-20, 1))),
        1rem * (var(--text-20-min, 1))
      )!important;
  }




  .fluent-radio-images .ff_item_selected .ff-el-form-check-label span {
    color:#192329!important;
    font-family: "AvenirNextLTProDemi", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"!important;
  }
  
  
  .fluent-radio-images .ff-el-image-holder:before {
    content:"";
    height: calc((1.5rem * var(--responsive-70)) * (var(--h-24, 1)));
    width: calc((1.5rem * var(--responsive-70)) * (var(--w-24, 1)));
    border-radius:25px;
    border:2px solid #e4ebf0;
    background:white;
    position:absolute;
    top: calc((1.75rem * var(--responsive-70)) * (var(--top-28, 1)));
    left: calc((1.75rem * var(--responsive-70)) * (var(--left-28, 1)));
    z-index:20;
  }
  
  .fluent-radio-images .ff_item_selected.ff-el-image-holder {
    border:2px solid #192821!important;
  }
  
  .fluent-radio-images .ff_item_selected.ff-el-image-holder:before {
    border:calc((0.5rem * var(--responsive-70)) * (var(--h-8, 1))) solid #192821;
    background:white;
  }
  

  .fluent-radio-images.fluent-cols-3 .ff_el_checkable_photo_holders {
    display:grid;
    grid-template-columns:repeat(3, minmax(0, 1fr));
    gap: calc((1.25rem * var(--responsive-70)) * (var(--gap-20, 1)));
  }
  
  
  
  @media (max-width: 1920px) {
    
    
    
  }
  
  @media (max-width: 1680px) {
    
    
    
  }
  
  @media (max-width: 1440px) {
    
    
    
  }
  
  @media (max-width: 1280px) {
    
    .fluent-radio label {
      --pl-80:100px;
    }
    
  }
  
  @media (max-width: 1120px) {
    
    
    
  }
  
  @media (max-width: 960px) {
    
    
    
  }
  
  @media (max-width: 640px) {
    
    
    
  }
  
  @media (max-width: 480px) {
    
    
    
  }
  
  @media (max-width: 360px) {
    
    
    
  }
  
  /*--------------------------------------------------------------
  
  ## Form - Phone
  
  --------------------------------------------------------------*/
  
  .fluent-phone {
    width:100%;
    height: calc((5rem * var(--responsive-40)) * (var(--h-80, 1)))!important;
    border:2px solid #E4EBF0;
    font-family: "AvenirNextLTProDemi", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    border-radius: calc((0.75rem * var(--responsive-70)) * (var(--rounded-12, 1)));
    font-size: max(
    calc((1.25rem * var(--responsive-60, 1)) * (var(--text-20, 1))),
    1rem * (var(--text-20-min, 1))
  );
    display:flex;
    align-items:center;
    padding-left: calc((2.5rem * var(--responsive-60)) * (var(--pl-40, 1)));
    position:relative;
  }

  .fluent-phone .iti {
    display:flex;
    position:static!important;
    height:100%;
  }


  .fluent-phone:has(input:focus-visible) {
    outline: 2px solid #192821;
    outline-offset: 2px;
}

  .fluent-phone .ff-el-input--content,
  .fluent-phone .iti__selected-flag {
    height:100%;
  }
  
  
  .fluent-phone .ff-el-input--label label:after {
    display:none!important;
  }
  
  .fluent-phone input {
    position:absolute!important;
    top:0;
    left:120px!important;
    right:0;
    bottom:0;
    padding-top: calc((1.25rem * var(--responsive-70)) * (var(--pt-20, 1)))!important;
    border:none!important;
    height:100%;
    width:100%!important;
    box-shadow:none;
    font-family: "AvenirNextLTProDemi", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"!important;
    color:#192329!important;
    background:transparent!important;
    padding-left: 0px!important;
    font-size: max(
    calc((1.25rem * var(--responsive-60, 1)) * (var(--text-20, 1))),
    1rem * (var(--text-20-min, 1))
  )!important;
  }
  
  .fluent-phone .ff-el-input--label {
    position:absolute!important;
    top:50%!important;
    transform:translateY(-50%)!important;
    opacity:0.4!important;
    left:120px;
    font-size: max(
    calc((1.25rem * var(--responsive-60, 1)) * (var(--text-20, 1))),
    1rem * (var(--text-20-min, 1))
  )!important;
    transition:0.15s linear all!important;
    letter-spacing:0.3px!important;
    transform:translateY(-180%)!important;
    opacity:1!important;
    font-size:11px!important;
    font-family: "AvenirNextLTProDemi", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"!important;
    transition:0.15s linear all;
    color:rgb(99, 108, 116)!important;
    letter-spacing:0.3px!important;
  }
  
  .ff-errors-in-stack .error {
    width:100%;
    text-align:center;
    margin-top:0px!important;
  }
  
  .fluent-phone .error.text-danger {
    position: absolute;
  top: 50%;
  right: 30px;
  transform: translateY(-150%);
  margin: 0!important;
  text-align: right;
  }
  
  .fluent-phone .iti__selected-flag {
    background:transparent!important;
  }


 .fluent-phone .iti.iti--allow-dropdown .iti__flag-container::after {
    content: "";
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    width: 1px;
    height: 60%;
    background: #ccc;
  }

  .fluent-phone  .iti.iti--allow-dropdown .iti__flag-container {
    padding-right: 10px;
  position: relative;
  } 



  .fluent-phone  .iti__selected-dial-code {
      font-family: "AvenirNextLTPro", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
      font-style: normal;
      font-stretch: normal;
      font-weight: 600;
  }
  
  
  
  @media (max-width: 1920px) {
    
    
    
  }
  
  @media (max-width: 1680px) {
    
  .fluent-phone .ff-el-input--label {
    left:95px!important;
    }
    
    
  }
  
  @media (max-width: 1440px) {
    
    
  .fluent-phone .ff-el-input--label {
    left:93px!important;
    }
    
  }
  
  @media (max-width: 1280px) {
    
    
  .fluent-phone .ff-el-input--label {
    left:89px!important;
    transform:translateY(-150%)!important;
    letter-spacing:0px!important;
    }
    
    
    
  .fluent-phone input {
    padding-top:20px!important;
    }
    
    
    
    
    
  }
  
  @media (max-width: 1120px) {
    
    
  .fluent-phone .ff-el-input--label {
    left:86px!important;
    }
    
  }
  
  @media (max-width: 960px) {
    
  .fluent-phone .ff-el-input--label {
    left:84px!important;
    }
    
    
  }
  
  @media (max-width: 640px) {
    
    
  .fluent-phone .ff-el-input--label {
    left:82px!important;
    }
    
  }
  
  @media (max-width: 480px) {
    
    
  .fluent-phone .ff-el-input--label {
    left:79px!important;
    }
    
  }
  
  @media (max-width: 360px) {
    
    
  .fluent-phone .ff-el-input--label {
    left:77px!important;
    }
    
  }
  
  
  /*--------------------------------------------------------------
  
  ## Form - Terms and conditions
  
  --------------------------------------------------------------*/
  
  .fluentform .ff_t_c {
    font-family:"AvenirNextLTProMedium", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"!important;
    color:#192821!important;
    position:relative;
    padding-left: calc((3rem * var(--responsive-60)) * (var(--pl-48, 1)))!important;
    font-size: max(
    calc((1.125rem * var(--responsive-60, 1)) * (var(--text-18, 1))),
    1rem * (var(--text-18-min, 1))
  );
    line-height: 1.4;
  }
  
  
  .fluentform .ff_t_c:before {
    content:"";
    width: calc((1.75rem * var(--responsive-70)) * (var(--w-28, 1)));
    height: calc((1.75rem * var(--responsive-70)) * (var(--h-28, 1)));
    border:2px solid #E4EBF0;
    background:white;
    border-radius:6px;
    position:absolute;
    left:0px;
    top:50%;
    transform:translateY(-50%);
  }
  
  .fluentform .ff-el-form-check.ff_item_selected .ff_t_c:before {
    background:#192821;
    content:url("data:image/svg+xml,%3Csvg width='12' height='10' viewBox='0 0 12 10' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M2 5.53846L4.69231 8.23077L10 2' stroke='white' stroke-width='2.49231' stroke-linecap='round' stroke-linejoin='round'/%3E%3C/svg%3E%0A");
    border:2px solid white;
    display:flex;
    align-items:center;
    justify-content:center;
  }
    
    
  .fluentform .ff_tc_checkbox {
    display:none!important;
  }
  
  
  
  .fluentform .ff-el-tc a {
    text-decoration:underline!important;
  }
  
  
  /*--------------------------------------------------------------
  
  ## Form - CTA
  
  --------------------------------------------------------------*/
  
  


  .fluent-cta button {
    background:#fff!important;
    color:#192821!important;
    display:flex!important;
    align-items:center!important;
    justify-content:center!important;
    font-family: "AvenirNextLTProBold", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji"!important;
    border-radius: calc((1rem * var(--responsive-60)) * (var(--rounded-16, 1)))!important;
    font-size: max(
    calc((1.25rem * var(--responsive-60, 1)) * (var(--text-20, 1))),
    1rem * (var(--text-20-min, 1))
  )!important;
    width: 100%;
    height: calc((5rem * var(--responsive-40)) * (var(--h-80, 1)))!important;
    min-height: 3.75rem;
    opacity:1!important;
    cursor:pointer!important;
    font-weight:initial!important;
    box-shadow: 0px 8px 28px rgba(40, 60, 80, 0);
    transition: all 0.3s ease;
    translate: 0 0;
    border-width: 4px;
    border-style: solid;
    border-color: rgba(171, 119, 255, var(--border-opacity, 1));
    
  }

  .fluent-cta button:hover {
    transition: all 0.3s ease;
    translate: 0 -2px!important;
    box-shadow: 0px 8px 28px rgba(171, 119, 255, var(--shadow-opacity, 0.45))!important;
  }
  
  
  
  @media (max-width: 1920px) {
    
    
  }
  
  @media (max-width: 1680px) {
    
    
    
  }
  
  @media (max-width: 1440px) {
    
    
    
  }
  
  @media (max-width: 1280px) {
    
    
    
  }
  
  @media (max-width: 1120px) {
    
    
    
  }
  
  @media (max-width: 960px) {
    
    
    
  }
  
  @media (max-width: 640px) {
    
    
    
    
    
  }
  
  @media (max-width: 480px) {
    
    
    
  }
  
  @media (max-width: 360px) {
    
    
    
  }
  
  
  
  
  /*--------------------------------------------------------------
  
  ## Form - Errors
  
  --------------------------------------------------------------*/
  
  
  .ff-errors-in-stack .text-danger {
    padding: calc((var(--p-20)/100)*(var(--50)));
    font-size: 14px;
    line-height:1.3;
    font-family:"TT Norms W01 Medium";
    color:#952821;
    background:#F4EAE9;
    border-radius:6px;
    margin-top:30px;
    display: inline-block;
    border: 0;
    box-shadow: none;
    color: #212121;
  }
  
  .error-clear {
    display:none!important;
  }
  
  
  
  @media (max-width: 1920px) {
    
    
    
  }
  
  @media (max-width: 1680px) {
    
    
    
  }
  
  @media (max-width: 1440px) {
    
    
    
  }
  
  @media (max-width: 1280px) {
    
    
    
  }
  
  @media (max-width: 1120px) {
    
    
    
  }
  
  @media (max-width: 960px) {
    
    
    
  }
  
  @media (max-width: 640px) {
    
    
    
  }
  
  @media (max-width: 480px) {
    
    
    
  }
  
  @media (max-width: 360px) {
    
    
    
  }
  
  
  
  /*--------------------------------------------------------------
  
  ## Form - Success
  
  --------------------------------------------------------------*/
  
  .ff-message-success {
    padding: calc((var(--p-20)/100)*(var(--50)));
    font-size: 14px;
    line-height:1.3;
    font-family:"TT Norms W01 Medium";
    color:#212121;
    background:#F5F5F5;
    border-radius:6px;
    margin-top:30px;
    display:inline-block;
    box-shadow:none!important;
  }
  
  
  
  @media (max-width: 1920px) {
    
    
    
  }
  
  @media (max-width: 1680px) {
    
    
    
  }
  
  @media (max-width: 1440px) {
    
    
    
  }
  
  @media (max-width: 1280px) {
    
    
    
  }
  
  @media (max-width: 1120px) {
    
    
    
  }
  
  @media (max-width: 960px) {
    
    
    
  }
  
  @media (max-width: 640px) {
    
    
    
  }
  
  @media (max-width: 480px) {
    
    
    
  }
  
  @media (max-width: 360px) {
    
    
    
  }
  
  
  
  
  /*--------------------------------------------------------------
  
  ## Form - Recupera password - CTA
  
  --------------------------------------------------------------*/
  
  .S-forgot-cont {
    min-height:100lvh;
  }
  
  .S-forgot-f-cta button {
    background:#952821!important;
    color:white!important;
    display:flex!important;
    align-items:center!important;
    justify-content:center!important;
    font-family: "Avenir Next LT W01 Bold"!important;
    border-radius: calc((var(--br-12)/100)*(var(--70)))!important;
    font-size: max(calc((var(--f-22)/100)*(var(--50))), 14px)!important;
    width: 100%!important;
    height: calc((var(--h-80)/100)*(var(--40)))!important;
    opacity:1!important;
    cursor:pointer!important;
  }
  
  
  
  
  @media (max-width: 1920px) {
    
    
    
  }
  
  @media (max-width: 1680px) {
    
    
    
  }
  
  @media (max-width: 1440px) {
    
    
    
  }
  
  @media (max-width: 1280px) {
    
    
    
  }
  
  @media (max-width: 1120px) {
    
    
    
  }
  
  @media (max-width: 960px) {
    
    
    
  }
  
  @media (max-width: 640px) {
    
    
    
  }
  
  @media (max-width: 480px) {
    
    
    
  }
  
  @media (max-width: 360px) {
    
    
    
  }
  
  
  
  
  /*--------------------------------------------------------------
  
  ## Form - Ratings
  
  --------------------------------------------------------------*/
  
  .S-form-ratings p {
    margin:0px!important;
  }
  
  .S-form-ratings .ff-el-group {
    margin-bottom:0px!important;
  }
  
  .S-form-ratings .ff-el-ratings {
    display:flex!important;
    justify-content:space-between;
  }
  
  .S-form-ratings .ff-el-ratings label:before {
    border-radius:30px;
    height:30px;
    width:30px;
    border:1px solid #ddd;
    text-align:center;
    display:flex;
    align-items:center;
    justify-content:center;
    font-family:"Avenir Next LT W01 Medium";
    font-size:10px;
  }
  
  
  .S-form-ratings .ff-el-ratings label:nth-child(1):before {
    content:"1";
  }
  
  .S-form-ratings .ff-el-ratings label:nth-child(2):before {
    content:"2";
  }
  .S-form-ratings .ff-el-ratings label:nth-child(3):before {
    content:"3";
  }
  .S-form-ratings .ff-el-ratings label:nth-child(4):before {
    content:"4";
  }
  .S-form-ratings .ff-el-ratings label:nth-child(5):before {
    content:"5";
  }
  .S-form-ratings .ff-el-ratings label:nth-child(6):before {
    content:"6";
  }
  .S-form-ratings .ff-el-ratings label:nth-child(7):before {
    content:"7";
  }
  .S-form-ratings .ff-el-ratings label:nth-child(8):before {
    content:"8";
  }
  .S-form-ratings .ff-el-ratings label:nth-child(9):before {
    content:"9";
  }
  
  .S-form-ratings .ff-el-ratings label:nth-child(10):before {
    content:"10";
  }
  
  .S-form-ratings .ff-el-ratings label.active:before {
    border:1px solid #000;
    color:#000;
    font-family:"Avenir Next LT W01 Bold";
  }
  
  
  .S-form-ratings .ff-el-ratings svg {
    display:none;
  }
  
  
    .S-form-ratings p {
      line-height:1.3;
  }
  
  
  @media (max-width:768px) {
    
    .S-form-ratings p {
      margin-bottom:10px!important;
    }
    
    .S-form-ratings.mb-10 {
    margin-bottom: calc((var(--mb-30)/100)*(var(--50)));
    }
    
    .S-lavoro-h-logo svg {
      height:100px;
    }
    
    .S-lavoro-h-cont {
      text-align:left;
      align-items:flex-start;
    }
    
    
  }
  
  
  
  @media (max-width:370px) {
    
    
  
  .S-form-ratings .ff-el-ratings label:before {
    border-radius:25px;
    height:25px;
    width:25px;
  }
    
  }
  
  
  
  .ff_upload_btn {
    width:100%!important;
    height:60px!important;
    text-align:center!important;
    background:#212121!important;
    font-family:"Avenir Next LT W01 Bold"!important;
    border-radius:8px!important;
    display:flex!important;
    align-items:center;
    justify-content:center;
  }
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  
  