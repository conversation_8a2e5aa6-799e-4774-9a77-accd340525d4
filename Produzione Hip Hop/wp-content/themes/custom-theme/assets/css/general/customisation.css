
/*--------------------------------------------------------------

## Brand colors

--------------------------------------------------------------*/

.bg-brand-dark {
    background-color:#121D23; 
    background-color:#192329;
   }
   
   .bg-brand-whatsapp {
     background-color:rgba(37,211,102,var(--background-opacity,1));
   }
   
   .bg-brand-whatsapp-medium {
     background-color:rgba(146, 233, 178, var(--background-opacity,1));
   }
   
   .bg-brand-whatsapp-light {
     background-color:rgba(233, 251, 240, var(--background-opacity,1));
   }
   
   
   
   
   /*--------------------------------------------------------------
   
   ## Background colors
   
   --------------------------------------------------------------*/
   
   
   .bg-brand-violet {
     background-color:rgba(171, 119, 255, var(--background-opacity,1));
   }
   
   .bg-brand-pink {
     background-color:rgba(224, 78, 157, var(--background-opacity,1));
   }
   
   .bg-brand-yellow {
     background-color:rgba(255, 187, 68, var(--background-opacity,1));
   }
   
   .bg-brand-brown {
     background-color:rgba(157, 130, 113, var(--background-opacity,1));
   }
   
   .bg-brand-green {
     background-color:rgba(37,211,102,var(--background-opacity,1));
   }
   
   .bg-brand-red {
     background-color:rgba(255, 68, 68, var(--background-opacity,1));
   }
   
   .bg-brand-ocean {
     background-color:rgba(25, 77, 212, var(--background-opacity,1));
   }
   
   .bg-brand-blue {
     background-color:rgba(18, 137, 237, var(--background-opacity,1));
   }
   
   .bg-brand-sky {
     background-color:rgba(86, 204, 242, var(--background-opacity,1));
   }
   
   
   
   
   
   .bg-brand-violet-medium {
     background-color:rgba(227, 209, 255, var(--background-opacity,1));
   }
   
   .bg-brand-pink-medium {
     background-color:rgba(245, 197, 223, var(--background-opacity,1));
   }
   
   .bg-brand-yellow-medium {
     background-color:rgba(255, 233, 194, var(--background-opacity,1));
   }
   
   .bg-brand-brown-medium {
     background-color:rgba(223, 214, 209, var(--background-opacity,1));
   }
   
   .bg-brand-green-medium {
     background-color:rgba(146, 233, 178, var(--background-opacity,1));
   }
   
   .bg-brand-red-medium {
     background-color:rgba(255, 194, 194, var(--background-opacity,1));
   }
   
   .bg-brand-ocean-medium {
     background-color:rgba(180, 197, 241, var(--background-opacity,1));
   }
   
   .bg-brand-blue-medium {
     background-color:rgba(178, 217, 249, var(--background-opacity,1));
   }
   
   .bg-brand-sky-medium {
     background-color:rgba(200, 239, 251, var(--background-opacity,1));
   }
   
   
   
   
   .bg-brand-violet-light {
     background-color:rgba(246, 241, 255, var(--background-opacity,1));
   }
   
   .bg-brand-pink-light {
     background-color:rgba(252, 237, 245, var(--background-opacity,1));
   }
   
   .bg-brand-yellow-light {
     background-color:rgba(255, 248, 236, var(--background-opacity,1));
   }
   
   .bg-brand-brown-light {
     background-color:rgba(245, 242, 241, var(--background-opacity,1));
   }
   
   .bg-brand-green-light {
     background-color:rgba(233, 251, 240, var(--background-opacity,1));
   }
   
   .bg-brand-red-light {
     background-color:rgba(255, 236, 236, var(--background-opacity,1));
   }
   
   .bg-brand-ocean-light {
     background-color:rgba(232, 237, 251, var(--background-opacity,1));
   }
   
   .bg-brand-blue-light {
     background-color:rgba(231, 243, 253, var(--background-opacity,1));
   }
   
   .bg-brand-sky-light {
     background-color:rgba(238, 250, 254, var(--background-opacity,1));
   }
   
   
   /*--------------------------------------------------------------
   
   ## Border colors
   
   --------------------------------------------------------------*/
   
   
   .border-brand-whatsapp {
     border-color:rgba(37,211,102,var(--border-opacity,1));
   }
   
   .border-brand-violet {
     border-color: rgba(171, 119, 255, var(--border-opacity,1));
   }
   
   .border-brand-pink {
     border-color: rgba(224, 78, 157, var(--border-opacity,1));
   }
   
   .border-brand-yellow {
     border-color: rgba(255, 187, 68, var(--border-opacity,1));
   }
   
   .border-brand-brown {
     border-color: rgba(157, 130, 113, var(--border-opacity,1));
   }
   
   .border-brand-green {
     border-color: rgba(37,211,102, var(--border-opacity,1));
   }
   
   .border-brand-red {
     border-color: rgba(255, 68, 68, var(--border-opacity,1));
   }
   
   .border-brand-ocean {
     border-color: rgba(25, 77, 212, var(--border-opacity,1));
   }
   
   .border-brand-blue {
     border-color: rgba(18, 137, 237, var(--border-opacity,1));
   }
   
   .border-brand-sky {
     border-color: rgba(86, 204, 242, var(--border-opacity,1));
   }
   
   
   /*--------------------------------------------------------------
   
   ## Overlay gradient
   
   --------------------------------------------------------------*/
   
   .overlay-violet-left {
    background: linear-gradient(90deg, #192329 0%, #192329 15.38%, rgba(25, 35, 41, 0.89) 26.92%, rgba(25, 35, 41, 0) 100%);
   }

   .overlay-violet-bottom {
    background: linear-gradient(180deg, rgba(25, 35, 41, 0) 0%, rgba(25, 35, 41, 0.89) 73.08%, #192329 84.62%, #192329 100%);
   }

   .overlay-violet-light-left {
    background: linear-gradient(90deg, #F6F1FF 0%, #F6F1FF 15.38%, rgba(246, 241, 255, 0.89) 26.92%, rgba(246, 241, 255, 0) 100%);
   }

   .overlay-violet-light-bottom {
    background: linear-gradient(180deg, rgba(246, 241, 255, 0) 0%, rgba(246, 241, 255, 0.89) 73.08%, #F6F1FF 84.62%, #F6F1FF 100%);
   }

   .overlay-violet-medium-left {
    background: linear-gradient(90deg, #E3D1FF 0%, #E3D1FF 15.38%, rgba(227, 209, 255, 0.89) 26.92%, rgba(227, 209, 255, 0) 100%);
   }

   .overlay-violet-medium-bottom {
    background: linear-gradient(180deg, rgba(227, 209, 255, 0) 0%, rgba(227, 209, 255, 0.89) 73.08%, #E3D1FF 84.62%, #E3D1FF 100%);
   }





   .overlay-pink-left {
    background: linear-gradient(90deg, #E04E9D 0%, #E04E9D 15.38%, rgba(224, 78, 157, 0.89) 26.92%, rgba(224, 78, 157, 0) 100%);
   }

   .overlay-pink-bottom {
    background: linear-gradient(180deg, rgba(224, 78, 157, 0) 0%, rgba(224, 78, 157, 0.89) 73.08%, #E04E9D 84.62%, #E04E9D 100%);
   }

   .overlay-pink-light-left {
    background: linear-gradient(90deg, #FCEDF5 0%, #FCEDF5 15.38%, rgba(252, 237, 245, 0.89) 26.92%, rgba(252, 237, 245, 0) 100%);
   }

   .overlay-pink-light-bottom {
    background: linear-gradient(180deg, rgba(252, 237, 245, 0) 0%, rgba(252, 237, 245, 0.89) 73.08%, #FCEDF5 84.62%, #FCEDF5 100%);
   }

   .overlay-pink-medium-left {
    background: linear-gradient(90deg, #F5C5DF 0%, #F5C5DF 15.38%, rgba(245, 197, 223, 0.89) 26.92%, rgba(245, 197, 223, 0) 100%);
   }

   .overlay-pink-medium-bottom {
    background: linear-gradient(180deg, rgba(245, 197, 223, 0) 0%, rgba(245, 197, 223, 0.89) 73.08%, #F5C5DF 84.62%, #F5C5DF 100%);
   }





   .overlay-red-left {
    background: linear-gradient(90deg, #FF4444 0%, #FF4444 15.38%, rgba(255, 68, 68, 0.89) 26.92%, rgba(255, 68, 68, 0) 100%);
   }

   .overlay-red-bottom {
    background: linear-gradient(180deg, rgba(255, 68, 68, 0) 0%, rgba(255, 68, 68, 0.89) 73.08%, #FF4444 84.62%, #FF4444 100%);
   }

   .overlay-red-light-left {
    background: linear-gradient(90deg, #FFECEC 0%, #FFECEC 15.38%, rgba(255, 236, 236, 0.89) 26.92%, rgba(255, 236, 236, 0) 100%);
   }

   .overlay-red-light-bottom {
    background: linear-gradient(180deg, rgba(255, 236, 236, 0) 0%, rgba(255, 236, 236, 0.89) 73.08%, #FFECEC 84.62%, #FFECEC 100%);
   }

   .overlay-red-medium-left {
    background: linear-gradient(90deg, #FFC2C2 0%, #FFC2C2 15.38%, rgba(255, 194, 194, 0.89) 26.92%, rgba(255, 194, 194, 0) 100%);
   }

   .overlay-red-medium-bottom {
    background: linear-gradient(180deg, rgba(255, 194, 194, 0) 0%, rgba(255, 194, 194, 0.89) 73.08%, #FFC2C2 84.62%, #FFC2C2 100%);
   }






   .overlay-yellow-left {
    background: linear-gradient(90deg, #FFBB44 0%, #FFBB44 15.38%, rgba(255, 187, 68, 0.89) 26.92%, rgba(255, 187, 68, 0) 100%);
   }

   .overlay-yellow-bottom {
    background: linear-gradient(180deg, rgba(255, 187, 68, 0) 0%, rgba(255, 187, 68, 0.89) 73.08%, #FFBB44 84.62%, #FFBB44 100%);
   }

   .overlay-yellow-light-left {
    background: linear-gradient(90deg, #FFF8EC 0%, #FFF8EC 15.38%, rgba(255, 248, 236, 0.89) 26.92%, rgba(255, 248, 236, 0) 100%);
   }

   .overlay-yellow-light-bottom {
    background: linear-gradient(180deg, rgba(255, 248, 236, 0) 0%, rgba(255, 248, 236, 0.89) 73.08%, #FFF8EC 84.62%, #FFF8EC 100%);
   }

   .overlay-yellow-medium-left {
    background: linear-gradient(90deg, #FFE9C2 0%, #FFE9C2 15.38%, rgba(255, 233, 194, 0.89) 26.92%, rgba(255, 233, 194, 0) 100%);
   }

   .overlay-yellow-medium-bottom {
    background: linear-gradient(180deg, rgba(255, 233, 194, 0) 0%, rgba(255, 233, 194, 0.89) 73.08%, #FFE9C2 84.62%, #FFE9C2 100%);
   }




   .overlay-green-left {
    background: linear-gradient(90deg, #25D366 0%, #25D366 15.38%, rgba(37, 211, 102, 0.89) 26.92%, rgba(37, 211, 102, 0) 100%);
   }

   .overlay-green-bottom {
    background: linear-gradient(180deg, rgba(37, 211, 102, 0) 0%, rgba(37, 211, 102, 0.89) 73.08%, #25D366 84.62%, #25D366 100%);
   }

   .overlay-green-light-left {
    background: linear-gradient(90deg, #E9FBF0 0%, #E9FBF0 15.38%, rgba(233, 251, 240, 0.89) 26.92%, rgba(233, 251, 240, 0) 100%);
   }

   .overlay-green-light-bottom {
    background: linear-gradient(180deg, rgba(233, 251, 240, 0) 0%, rgba(233, 251, 240, 0.89) 73.08%, #E9FBF0 84.62%, #E9FBF0 100%);
   }

   .overlay-green-medium-left {
    background: linear-gradient(90deg, #92E9B2 0%, #92E9B2 15.38%, rgba(146, 233, 178, 0.89) 26.92%, rgba(146, 233, 178, 0) 100%);
   }

   .overlay-green-medium-bottom {
    background: linear-gradient(180deg, rgba(146, 233, 178, 0) 0%, rgba(146, 233, 178, 0.89) 73.08%, #92E9B2 84.62%, #92E9B2 100%);
   }





   .overlay-brown-left {
    background: linear-gradient(90deg, #9D8271 0%, #9D8271 15.38%, rgba(157, 130, 113, 0.89) 26.92%, rgba(157, 130, 113, 0) 100%);
   }

   .overlay-brown-bottom {
    background: linear-gradient(180deg, rgba(157, 130, 113, 0) 0%, rgba(157, 130, 113, 0.89) 73.08%, #9D8271 84.62%, #9D8271 100%);
   }

   .overlay-brown-light-left {
    background: linear-gradient(90deg, #F5F2F1 0%, #F5F2F1 15.38%, rgba(245, 242, 241, 0.89) 26.92%, rgba(245, 242, 241, 0) 100%);
   }

   .overlay-brown-light-bottom {
    background: linear-gradient(180deg, rgba(245, 242, 241, 0) 0%, rgba(245, 242, 241, 0.89) 73.08%, #F5F2F1 84.62%, #F5F2F1 100%);
   }

   .overlay-brown-medium-left {
    background: linear-gradient(90deg, #DFD6D1 0%, #DFD6D1 15.38%, rgba(223, 214, 209, 0.89) 26.92%, rgba(223, 214, 209, 0) 100%);
   }

   .overlay-brown-medium-bottom {
    background: linear-gradient(180deg, rgba(223, 214, 209, 0) 0%, rgba(223, 214, 209, 0.89) 73.08%, #DFD6D1 84.62%, #DFD6D1 100%);
   }






   .overlay-ocean-left {
    background: linear-gradient(90deg, #194DD4 0%, #194DD4 15.38%, rgba(25, 77, 212, 0.89) 26.92%, rgba(25, 77, 212, 0) 100%);
   }

   .overlay-ocean-bottom {
    background: linear-gradient(180deg, rgba(25, 77, 212, 0) 0%, rgba(25, 77, 212, 0.89) 73.08%, #194DD4 84.62%, #194DD4 100%);
   }

   .overlay-ocean-light-left {
    background: linear-gradient(90deg, #E8EDFB 0%, #E8EDFB 15.38%, rgba(232, 237, 251, 0.89) 26.92%, rgba(232, 237, 251, 0) 100%);
   }

   .overlay-ocean-light-bottom {
    background: linear-gradient(180deg, rgba(232, 237, 251, 0) 0%, rgba(232, 237, 251, 0.89) 73.08%, #E8EDFB 84.62%, #E8EDFB 100%);
   }

   .overlay-ocean-medium-left {
    background: linear-gradient(90deg, #B4C5F1 0%, #B4C5F1 15.38%, rgba(180, 197, 241, 0.89) 26.92%, rgba(180, 197, 241, 0) 100%);
   }

   .overlay-ocean-medium-bottom {
    background: linear-gradient(180deg, rgba(180, 197, 241, 0) 0%, rgba(180, 197, 241, 0.89) 73.08%, #B4C5F1 84.62%, #B4C5F1 100%);
   }



   .overlay-blue-left {
    background: linear-gradient(90deg, #1289ED 0%, #1289ED 15.38%, rgba(18, 137, 237, 0.89) 26.92%, rgba(18, 137, 237, 0) 100%);
   }

   .overlay-blue-bottom {
    background: linear-gradient(180deg, rgba(18, 137, 237, 0) 0%, rgba(18, 137, 237, 0.89) 73.08%, #1289ED 84.62%, #1289ED 100%);
   }

   .overlay-blue-light-left {
    background: linear-gradient(90deg, #E7F3FD 0%, #E7F3FD 15.38%, rgba(231, 243, 253, 0.89) 26.92%, rgba(231, 243, 253, 0) 100%);
   }

   .overlay-blue-light-bottom {
    background: linear-gradient(180deg, rgba(231, 243, 253, 0) 0%, rgba(231, 243, 253, 0.89) 73.08%, #E7F3FD 84.62%, #E7F3FD 100%);
   }

   .overlay-blue-medium-left {
    background: linear-gradient(90deg, #B2D9F9 0%, #B2D9F9 15.38%, rgba(178, 217, 249, 0.89) 26.92%, rgba(178, 217, 249, 0) 100%);
   }

   .overlay-blue-medium-bottom {
    background: linear-gradient(180deg, rgba(178, 217, 249, 0) 0%, rgba(178, 217, 249, 0.89) 73.08%, #B2D9F9 84.62%, #B2D9F9 100%);
   }


   
   .overlay-sky-left {
    background: linear-gradient(90deg, #56CCF2 0%, #56CCF2 15.38%, rgba(86, 204, 242, 0.89) 26.92%, rgba(86, 204, 242, 0) 100%);
   }

   .overlay-sky-bottom {
    background: linear-gradient(180deg, rgba(86, 204, 242, 0) 0%, rgba(86, 204, 242, 0.89) 73.08%, #56CCF2 84.62%, #56CCF2 100%);
   }

   .overlay-sky-medium-left {
    background: linear-gradient(90deg, #C8EFFB 0%, #C8EFFB 15.38%, rgba(200, 239, 251, 0.89) 26.92%, rgba(200, 239, 251, 0) 100%);
   }

   .overlay-sky-medium-bottom {
    background: linear-gradient(180deg, rgba(200, 239, 251, 0) 0%, rgba(200, 239, 251, 0.89) 73.08%, #C8EFFB 84.62%, #C8EFFB 100%);
   }

   .overlay-sky-light-left {
    background: linear-gradient(90deg, #EEFAFE 0%, #EEFAFE 15.38%, rgba(238, 250, 254, 0.89) 26.92%, rgba(238, 250, 254, 0) 100%);
   }

   .overlay-sky-light-bottom {
    background: linear-gradient(180deg, rgba(238, 250, 254, 0) 0%, rgba(238, 250, 254, 0.89) 73.08%, #EEFAFE 84.62%, #EEFAFE 100%);
   }


   
   .overlay-dark-left {
    background: linear-gradient(90deg, #192329 0%, #192329 15.38%, rgba(25, 35, 41, 0.89) 26.92%, rgba(25, 35, 41, 0) 100%);
   }

   .overlay-dark-bottom {
    background: linear-gradient(180deg, rgba(25, 35, 41, 0) 0%, rgba(25, 35, 41, 0.89) 73.08%, #192329 84.62%, #192329 100%);
   }

   
   .overlay-white-left {
    background: linear-gradient(90deg, #FFFFFF 0%, #FFFFFF 15.38%, rgba(255, 255, 255, 0.89) 26.92%, rgba(255, 255, 255, 0) 100%);
   }

   .overlay-white-bottom {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(255, 255, 255, 0.89) 73.08%, #FFFFFF 84.62%, #FFFFFF 100%);
   }


   
   
   
   /*--------------------------------------------------------------
   
   ## Border gradient colors
   
   --------------------------------------------------------------*/
   
   
   .border-gradient-pink-violet {
     background: linear-gradient(white, white) padding-box,
                 linear-gradient(to right, #E04E9D, #A973FF) border-box;
   } 
   
   .border-gradient-yellow-green {
     background: linear-gradient(white, white) padding-box,
                 linear-gradient(to right, #FFBB44, #25D366) border-box;
   }
   
   .border-gradient-sky-violet {
     background: linear-gradient(white, white) padding-box,
                 linear-gradient(to right, #56CCF2, #A973FF) border-box;
   }
   
   .border-gradient-ocean-violet {
     background: linear-gradient(white, white) padding-box,
                 linear-gradient(to right, #194DD4, #A973FF) border-box;
   }
   
   .border-gradient-red-pink {
     background: linear-gradient(white, white) padding-box,
                 linear-gradient(to right, #FF4444, #E04E9D) border-box;
   }
   
   .border-gradient-brown-pink {
     background: linear-gradient(white, white) padding-box,
                 linear-gradient(to right, #9D8271, #E04E9D) border-box;
   }
   
   .border-gradient-green-violet {
     background: linear-gradient(white, white) padding-box,
                 linear-gradient(to right, #25D366, #A973FF) border-box;
   }
   
   .border-gradient-blue-sky {
     background: linear-gradient(white, white) padding-box,
                 linear-gradient(to right, #1289ED, #56CCF2) border-box;
   }
   
   .border-gradient-red-violet {
     background: linear-gradient(white, white) padding-box,
                 linear-gradient(to right, #FF4444, #A973FF) border-box;
   }
   
   .border-gradient-yellow-pink {
     background: linear-gradient(white, white) padding-box,
                 linear-gradient(to right, #FFBB44, #E04E9D) border-box;
   }
   
   .border-gradient-ocean-pink {
     background: linear-gradient(white, white) padding-box,
                 linear-gradient(to right, #194DD4, #E04E9D) border-box;
   }
   
   .border-gradient-brown-red {
     background: linear-gradient(white, white) padding-box,
                 linear-gradient(to right, #9D8271, #FF4444) border-box;
   }
   
   .border-gradient-brown-yellow {
     background: linear-gradient(white, white) padding-box,
                 linear-gradient(to right, #9D8271, #FFBB44) border-box;
   }
   
   .border-gradient-brown-purple {
     background: linear-gradient(white, white) padding-box,
                 linear-gradient(to right, #9D8271, #A973FF) border-box;
   }
   
   .border-gradient-pink-red {
     background: linear-gradient(white, white) padding-box,
                 linear-gradient(to right, #E04E9D, #FF4444) border-box;
   }
   
   .border-gradient-red-yellow {
     background: linear-gradient(white, white) padding-box,
                 linear-gradient(to right, #FF4444, #FFBB44) border-box;
   }
   
   .border-gradient-sky-yellow {
     background: linear-gradient(white, white) padding-box,
                 linear-gradient(to right, #56CCF2, #FFBB44) border-box;
   }
   
   .border-gradient-sky-green {
     background: linear-gradient(white, white) padding-box,
                 linear-gradient(to right, #56CCF2, #25D366) border-box;
   }
   
   .border-gradient-yellow-violet {
     background: linear-gradient(white, white) padding-box,
                 linear-gradient(to right, #FFBB44, #A973FF) border-box;
   }
   
   
   
   
   .border-gradient-pink-violet-dark {
     background: linear-gradient(#192329, #192329) padding-box,
                 linear-gradient(to right, #E04E9D, #A973FF) border-box;
   } 
   
   .border-gradient-yellow-green-dark {
     background: linear-gradient(#192329, #192329) padding-box,
                 linear-gradient(to right, #FFBB44, #25D366) border-box;
   }
   
   .border-gradient-sky-violet-dark {
     background: linear-gradient(#192329, #192329) padding-box,
                 linear-gradient(to right, #56CCF2, #A973FF) border-box;
   }
   
   .border-gradient-ocean-violet-dark {
     background: linear-gradient(#192329, #192329) padding-box,
                 linear-gradient(to right, #194DD4, #A973FF) border-box;
   }
   
   .border-gradient-red-pink-dark {
     background: linear-gradient(#192329, #192329) padding-box,
                 linear-gradient(to right, #FF4444, #E04E9D) border-box;
   }
   
   .border-gradient-brown-pink-dark {
     background: linear-gradient(#192329, #192329) padding-box,
                 linear-gradient(to right, #9D8271, #E04E9D) border-box;
   }
   
   .border-gradient-green-violet-dark {
     background: linear-gradient(#192329, #192329) padding-box,
                 linear-gradient(to right, #25D366, #A973FF) border-box;
   }
   
   .border-gradient-blue-sky-dark {
     background: linear-gradient(#192329, #192329) padding-box,
                 linear-gradient(to right, #1289ED, #56CCF2) border-box;
   }
   
   .border-gradient-red-violet-dark {
     background: linear-gradient(#192329, #192329) padding-box,
                 linear-gradient(to right, #FF4444, #A973FF) border-box;
   }
   
   .border-gradient-yellow-pink-dark {
     background: linear-gradient(#192329, #192329) padding-box,
                 linear-gradient(to right, #FFBB44, #E04E9D) border-box;
   }
   
   .border-gradient-ocean-pink-dark {
     background: linear-gradient(#192329, #192329) padding-box,
                 linear-gradient(to right, #194DD4, #E04E9D) border-box;
   }
   
   .border-gradient-brown-red-dark {
     background: linear-gradient(#192329, #192329) padding-box,
                 linear-gradient(to right, #9D8271, #FF4444) border-box;
   }
   
   .border-gradient-brown-yellow-dark {
     background: linear-gradient(#192329, #192329) padding-box,
                 linear-gradient(to right, #9D8271, #FFBB44) border-box;
   }
   
   .border-gradient-brown-purple-dark {
     background: linear-gradient(#192329, #192329) padding-box,
                 linear-gradient(to right, #9D8271, #A973FF) border-box;
   }
   
   .border-gradient-pink-red-dark {
     background: linear-gradient(#192329, #192329) padding-box,
                 linear-gradient(to right, #E04E9D, #FF4444) border-box;
   }
   
   .border-gradient-red-yellow-dark {
     background: linear-gradient(#192329, #192329) padding-box,
                 linear-gradient(to right, #FF4444, #FFBB44) border-box;
   }
   
   .border-gradient-sky-yellow-dark {
     background: linear-gradient(#192329, #192329) padding-box,
                 linear-gradient(to right, #56CCF2, #FFBB44) border-box;
   }
   
   .border-gradient-sky-green-dark {
     background: linear-gradient(#192329, #192329) padding-box,
                 linear-gradient(to right, #56CCF2, #25D366) border-box;
   }
   
   .border-gradient-yellow-violet-dark {
     background: linear-gradient(#192329, #192329) padding-box,
                 linear-gradient(to right, #FFBB44, #A973FF) border-box;
   }
   
   
   
   
   
   /*--------------------------------------------------------------
   
   ## Text colors
   
   --------------------------------------------------------------*/
   
   .text-brand-violet {
     color: #A973FF;
   }
   
   .text-brand-pink {
     color: #E04E9D;
   }
   
   .text-brand-yellow {
     color: #FFBB44;
   }
   
   .text-brand-brown {
     color: #9D8271;
   }
   
   .text-brand-green {
     color: #25D366;
   }
   
   .text-brand-red {
     color: #FF4444;
   }
   
   .text-brand-ocean {
     color: #194DD4;
   }
   
   .text-brand-blue {
     color: #1289ED;
   }
   
   .text-brand-sky {
     color: #56CCF2;
   }
   
   .text-brand-whatsapp {
     color: #25D366;
   }
   
   
   
   
   /*--------------------------------------------------------------
   
   ## Text gradient colors
   
   --------------------------------------------------------------*/
   
   
   
   .text-gradient-pink-violet {
     background-image: linear-gradient(to right, #E04E9D, #A973FF);
     -webkit-background-clip: text;
     -moz-background-clip: text;
     background-clip: text;
     color: transparent;
     -webkit-text-fill-color: transparent;
     -moz-text-fill-color: transparent;
   }
   
   .text-gradient-yellow-green {
     background-image: linear-gradient(to right, #FFBB44, #25D366);
     -webkit-background-clip: text;
     -moz-background-clip: text;
     background-clip: text;
     color: transparent;
     -webkit-text-fill-color: transparent;
     -moz-text-fill-color: transparent;
   }
   
   .text-gradient-sky-violet {
     background-image: linear-gradient(to right, #56CCF2, #A973FF);
     -webkit-background-clip: text;
     -moz-background-clip: text;
     background-clip: text;
     color: transparent;
     -webkit-text-fill-color: transparent;
     -moz-text-fill-color: transparent;
   }
   
   .text-gradient-ocean-violet {
     background-image: linear-gradient(to right, #194DD4, #A973FF);
     -webkit-background-clip: text;
     -moz-background-clip: text;
     background-clip: text;
     color: transparent;
     -webkit-text-fill-color: transparent;
     -moz-text-fill-color: transparent;
   }
   
   .text-gradient-violet-ocean {
     background-image: linear-gradient(to right, #A973FF, #194DD4);
     -webkit-background-clip: text;
     -moz-background-clip: text;
     background-clip: text;
     color: transparent;
     -webkit-text-fill-color: transparent;
     -moz-text-fill-color: transparent;
   }
   
   .text-gradient-red-pink {
     background-image: linear-gradient(to right, #FF4444, #E04E9D);
     -webkit-background-clip: text;
     -moz-background-clip: text;
     background-clip: text;
     color: transparent;
     -webkit-text-fill-color: transparent;
     -moz-text-fill-color: transparent;
   }
   
   .text-gradient-brown-pink {
     background-image: linear-gradient(to right, #9D8271, #E04E9D);
     -webkit-background-clip: text;
     -moz-background-clip: text;
     background-clip: text;
     color: transparent;
     -webkit-text-fill-color: transparent;
     -moz-text-fill-color: transparent;
   }
   
   .text-gradient-green-violet {
     background-image: linear-gradient(to right, #25D366, #A973FF);
     -webkit-background-clip: text;
     -moz-background-clip: text;
     background-clip: text;
     color: transparent;
     -webkit-text-fill-color: transparent;
     -moz-text-fill-color: transparent;
   }
   
   .text-gradient-blue-sky {
     background-image: linear-gradient(to right, #1289ED, #56CCF2);
     -webkit-background-clip: text;
     -moz-background-clip: text;
     background-clip: text;
     color: transparent;
     -webkit-text-fill-color: transparent;
     -moz-text-fill-color: transparent;
   }
   
   .text-gradient-red-violet {
     background-image: linear-gradient(to right, #FF4444, #A973FF);
     -webkit-background-clip: text;
     -moz-background-clip: text;
     background-clip: text;
     color: transparent;
     -webkit-text-fill-color: transparent;
     -moz-text-fill-color: transparent;
   }
   
   .text-gradient-yellow-pink {
     background-image: linear-gradient(to right, #FFBB44, #E04E9D);
     -webkit-background-clip: text;
     -moz-background-clip: text;
     background-clip: text;
     color: transparent;
     -webkit-text-fill-color: transparent;
     -moz-text-fill-color: transparent;
   }
   
   .text-gradient-ocean-pink {
     background-image: linear-gradient(to right, #194DD4, #E04E9D);
     -webkit-background-clip: text;
     -moz-background-clip: text;
     background-clip: text;
     color: transparent;
     -webkit-text-fill-color: transparent;
     -moz-text-fill-color: transparent;
   }
   
   .text-gradient-brown-red {
     background-image: linear-gradient(to right, #9D8271, #FF4444);
     -webkit-background-clip: text;
     -moz-background-clip: text;
     background-clip: text;
     color: transparent;
     -webkit-text-fill-color: transparent;
     -moz-text-fill-color: transparent;
   }
   
   .text-gradient-brown-yellow {
     background-image: linear-gradient(to right, #9D8271, #FFBB44);
     -webkit-background-clip: text;
     -moz-background-clip: text;
     background-clip: text;
     color: transparent;
     -webkit-text-fill-color: transparent;
     -moz-text-fill-color: transparent;
   }
   
   .text-gradient-brown-purple {
     background-image: linear-gradient(to right, #9D8271, #A973FF);
     -webkit-background-clip: text;
     -moz-background-clip: text;
     background-clip: text;
     color: transparent;
     -webkit-text-fill-color: transparent;
     -moz-text-fill-color: transparent;
   }
   
   .text-gradient-pink-red {
     background-image: linear-gradient(to right, #E04E9D, #FF4444);
     -webkit-background-clip: text;
     -moz-background-clip: text;
     background-clip: text;
     color: transparent;
     -webkit-text-fill-color: transparent;
     -moz-text-fill-color: transparent;
   }
   
   .text-gradient-red-yellow {
     background-image: linear-gradient(to right, #FF4444, #FFBB44);
     -webkit-background-clip: text;
     -moz-background-clip: text;
     background-clip: text;
     color: transparent;
     -webkit-text-fill-color: transparent;
     -moz-text-fill-color: transparent;
   }
   
   .text-gradient-sky-yellow {
     background-image: linear-gradient(to right, #56CCF2, #FFBB44);
     -webkit-background-clip: text;
     -moz-background-clip: text;
     background-clip: text;
     color: transparent;
     -webkit-text-fill-color: transparent;
     -moz-text-fill-color: transparent;
   }
   
   .text-gradient-sky-green {
     background-image: linear-gradient(to right, #56CCF2, #25D366);
     -webkit-background-clip: text;
     -moz-background-clip: text;
     background-clip: text;
     color: transparent;
     -webkit-text-fill-color: transparent;
     -moz-text-fill-color: transparent;
   }
   
   .text-gradient-yellow-violet {
     background-image: linear-gradient(to right, #FFBB44, #A973FF);
     -webkit-background-clip: text;
     -moz-background-clip: text;
     background-clip: text;
     color: transparent;
     -webkit-text-fill-color: transparent;
     -moz-text-fill-color: transparent;
   }
   
   
   
   
   /*--------------------------------------------------------------
   
   ## Background gradient colors
   
   --------------------------------------------------------------*/
   
   
   .bg-gradient-yellow-violet {
     background: linear-gradient(to right, rgba(255, 187, 68, var(--background-opacity, 1)), rgba(169, 115, 255, var(--background-opacity, 1)));
   }
   
   .bg-gradient-sky-yellow {
     background: linear-gradient(to right, rgba(86, 204, 242, var(--background-opacity, 1)), rgba(255, 187, 68, var(--background-opacity, 1)));
   }
   
   .bg-gradient-sky-green {
     background: linear-gradient(to right, rgba(86, 204, 242, var(--background-opacity, 1)), rgba(37, 211, 102, var(--background-opacity, 1)));
   }
   
   .bg-gradient-red-yellow {
     background: linear-gradient(to right, rgba(255, 68, 68, var(--background-opacity, 1)), rgba(255, 187, 68, var(--background-opacity, 1)));
   }
   
   .bg-gradient-pink-violet {
     background: linear-gradient(to right, rgba(224, 78, 157, var(--background-opacity, 1)), rgba(169, 115, 255, var(--background-opacity, 1)));
   }
   
   .bg-gradient-yellow-green {
     background: linear-gradient(to right, rgba(255, 187, 68, var(--background-opacity, 1)), rgba(37, 211, 102, var(--background-opacity, 1)));
   }
   
   .bg-gradient-sky-violet {
     background: linear-gradient(to right, rgba(86, 204, 242, var(--background-opacity, 1)), rgba(169, 115, 255, var(--background-opacity, 1)));
   }
   
   .bg-gradient-ocean-violet {
     background: linear-gradient(to right, rgba(25, 77, 212, var(--background-opacity, 1)), rgba(169, 115, 255, var(--background-opacity, 1)));
   }
   
   .bg-gradient-red-pink {
     background: linear-gradient(to right, rgba(255, 68, 68, var(--background-opacity, 1)), rgba(224, 78, 157, var(--background-opacity, 1)));
   }
   
   .bg-gradient-brown-pink {
     background: linear-gradient(to right, rgba(157, 130, 113, var(--background-opacity, 1)), rgba(224, 78, 157, var(--background-opacity, 1)));
   }
   
   .bg-gradient-green-violet {
     background: linear-gradient(to right, rgba(37, 211, 102, var(--background-opacity, 1)), rgba(169, 115, 255, var(--background-opacity, 1)));
   }
   
   .bg-gradient-blue-sky {
     background: linear-gradient(to right, rgba(18, 137, 237, var(--background-opacity, 1)), rgba(86, 204, 242, var(--background-opacity, 1)));
   }
   
   .bg-gradient-red-violet {
     background: linear-gradient(to right, rgba(255, 68, 68, var(--background-opacity, 1)), rgba(169, 115, 255, var(--background-opacity, 1)));
   }
   
   .bg-gradient-yellow-pink {
     background: linear-gradient(to right, rgba(255, 187, 68, var(--background-opacity, 1)), rgba(224, 78, 157, var(--background-opacity, 1)));
   }
   
   .bg-gradient-ocean-pink {
     background: linear-gradient(to right, rgba(25, 77, 212, var(--background-opacity, 1)), rgba(224, 78, 157, var(--background-opacity, 1)));
   }
   
   .bg-gradient-brown-red {
     background: linear-gradient(to right, rgba(157, 130, 113, var(--background-opacity, 1)), rgba(255, 68, 68, var(--background-opacity, 1)));
   }
   
   .bg-gradient-brown-yellow {
     background: linear-gradient(to right, rgba(157, 130, 113, var(--background-opacity, 1)), rgba(255, 187, 68, var(--background-opacity, 1)));
   }
   
   .bg-gradient-brown-purple {
     background: linear-gradient(to right, rgba(157, 130, 113, var(--background-opacity, 1)), rgba(169, 115, 255, var(--background-opacity, 1)));
   }
   
   .bg-gradient-pink-red {
     background: linear-gradient(to right, rgba(224, 78, 157, var(--background-opacity, 1)), rgba(255, 68, 68, var(--background-opacity, 1)));
   }
   
   .bg-gradient-yellow-white {
     background: linear-gradient(to right, rgba(255, 187, 68, var(--background-opacity, 1)), rgba(255, 255, 255, var(--background-opacity, 1)));
   }
   
   .bg-gradient-sky-white {
     background: linear-gradient(to right, rgba(86, 204, 242, var(--background-opacity, 1)), rgba(255, 255, 255, var(--background-opacity, 1)));
   }
   
   .bg-gradient-red-white {
     background: linear-gradient(to right, rgba(255, 68, 68, var(--background-opacity, 1)), rgba(255, 255, 255, var(--background-opacity, 1)));
   }
   
   .bg-gradient-pink-white {
     background: linear-gradient(to right, rgba(224, 78, 157, var(--background-opacity, 1)), rgba(255, 255, 255, var(--background-opacity, 1)));
   }
   
   .bg-gradient-violet-white {
     background: linear-gradient(to right, rgba(169, 115, 255, var(--background-opacity, 1)), rgba(255, 255, 255, var(--background-opacity, 1)));
   }
   
   .bg-gradient-green-white {
     background: linear-gradient(to right, rgba(37, 211, 102, var(--background-opacity, 1)), rgba(255, 255, 255, var(--background-opacity, 1)));
   }
   
   .bg-gradient-blue-white {
     background: linear-gradient(to right, rgba(18, 137, 237, var(--background-opacity, 1)), rgba(255, 255, 255, var(--background-opacity, 1)));
   }
   
   .bg-gradient-ocean-white {
     background: linear-gradient(to right, rgba(25, 77, 212, var(--background-opacity, 1)), rgba(255, 255, 255, var(--background-opacity, 1)));
   }
   
   .bg-gradient-brown-white {
     background: linear-gradient(to right, rgba(157, 130, 113, var(--background-opacity, 1)), rgba(255, 255, 255, var(--background-opacity, 1)));
   }
   
   .bg-gradient-white-yellow {
     background: linear-gradient(to right, rgba(255, 255, 255, var(--background-opacity, 1)), rgba(255, 187, 68, var(--background-opacity, 1)));
   }
   
   .bg-gradient-white-sky {
     background: linear-gradient(to right, rgba(255, 255, 255, var(--background-opacity, 1)), rgba(86, 204, 242, var(--background-opacity, 1)));
   }
   
   .bg-gradient-white-red {
     background: linear-gradient(to right, rgba(255, 255, 255, var(--background-opacity, 1)), rgba(255, 68, 68, var(--background-opacity, 1)));
   }
   
   .bg-gradient-white-pink {
     background: linear-gradient(to right, rgba(255, 255, 255, var(--background-opacity, 1)), rgba(224, 78, 157, var(--background-opacity, 1)));
   }
   
   .bg-gradient-white-violet {
     background: linear-gradient(to right, rgba(255, 255, 255, var(--background-opacity, 1)), rgba(169, 115, 255, var(--background-opacity, 1)));
   }
   
   .bg-gradient-white-green {
     background: linear-gradient(to right, rgba(255, 255, 255, var(--background-opacity, 1)), rgba(37, 211, 102, var(--background-opacity, 1)));
   }
   
   .bg-gradient-white-blue {
     background: linear-gradient(to right, rgba(255, 255, 255, var(--background-opacity, 1)), rgba(18, 137, 237, var(--background-opacity, 1)));
   }
   
   .bg-gradient-white-ocean {
     background: linear-gradient(to right, rgba(255, 255, 255, var(--background-opacity, 1)), rgba(25, 77, 212, var(--background-opacity, 1)));
   }
   
   .bg-gradient-white-brown {
     background: linear-gradient(to right, rgba(255, 255, 255, var(--background-opacity, 1)), rgba(157, 130, 113, var(--background-opacity, 1)));
   }
   
   .bg-gradient-white-gray { 
     background: linear-gradient(to right, rgba(255, 255, 255, var(--background-opacity, 1)), rgba(239, 242, 246, var(--background-opacity, 1)));
   }
   
   .bg-gradient-gray-white {
     background: linear-gradient(to right, rgba(239, 242, 246, var(--background-opacity, 1)), rgba(255, 255, 255, var(--background-opacity, 1)));
   }
   
   
   
   
   
   /*--------------------------------------------------------------
   
   ## Radial gradient colors
   
   --------------------------------------------------------------*/
   
   
   .radial-gradient-pink {
   background: radial-gradient(50% 50% at 50% 50%, #E04E9D 0%, rgba(18, 30, 36, 0) 100%);
   }
   
   .radial-gradient-violet {
     background: radial-gradient(50% 50% at 50% 50%, #A973FF 0%, rgba(18, 30, 36, 0) 100%);
     }
   
   .radial-gradient-yellow {
     background: radial-gradient(50% 50% at 50% 50%, #FFBB44 0%, rgba(18, 30, 36, 0) 100%);
   }
   
   .radial-gradient-brown {
     background: radial-gradient(50% 50% at 50% 50%, #9D8271 0%, rgba(18, 30, 36, 0) 100%);
   }
   
   .radial-gradient-green {
     background: radial-gradient(50% 50% at 50% 50%, #25D366 0%, rgba(18, 30, 36, 0) 100%);
   }
   
   .radial-gradient-red {
     background: radial-gradient(50% 50% at 50% 50%, #FF4444 0%, rgba(18, 30, 36, 0) 100%);
   }
   
   .radial-gradient-ocean {
     background: radial-gradient(50% 50% at 50% 50%, #194DD4 0%, rgba(18, 30, 36, 0) 100%);
   }
   
   .radial-gradient-blue {
     background: radial-gradient(50% 50% at 50% 50%, #1289ED 0%, rgba(18, 30, 36, 0) 100%);
   }
   
   .radial-gradient-sky {
     background: radial-gradient(50% 50% at 50% 50%, #56CCF2 0%, rgba(18, 30, 36, 0) 100%);
   }
   
   
   
   /*--------------------------------------------------------------
   
   ## Border gradient colors - Animated
   
   --------------------------------------------------------------*/
   
   /**
    * `@property` is required for the animation to work.
    * Without it, the angle values wonâ€™t interpolate properly.
    *
    * @see https://dev.to/afif/we-can-finally-animate-css-gradient-kdk
    */
    @property --bg-angle {
     inherits: false;
     initial-value: 0deg;
     syntax: "<angle>";
   }
   
   /**
    * To animate the gradient, we set the custom property to 1 full
    * rotation. The animation starts at the default value of `0deg`.
    */
   @keyframes spin {
     to {
       --bg-angle: 360deg;
     }
   }
   
   
   .border-animated-violet {
     background: transparent;
     border: 4px solid #A973FF;
     transition: all 0.3s ease;
   }
   
   
   
   .border-animated-violet:hover {
     transition: all 0.3s ease;
     animation: spin 2s infinite linear;
     background:
       linear-gradient(
           to bottom,
           oklch(0.2493 0.0182 234.8),
           oklch(0.2493 0.0182 234.8)
         )
         padding-box,
      conic-gradient(
     from var(--bg-angle) in oklch,
     oklch(0.65 0.25 340) 0%,
     oklch(0.70 0.25 290) 45%,
     oklch(0.70 0.25 290) 65%,
     oklch(0.65 0.25 340) 100%
   )
         border-box;
     border: 4px solid transparent;
   }
   
   
   
   .hover-border-animated-violet-light {
     background: transparent;
     border: 4px solid #A973FF;
     transition: all 0.3s ease;
   }
   
   
   
   .hover-border-animated-violet-light:hover {
     transition: all 0.3s ease;
     animation: spin 2s infinite linear;
     background:
       linear-gradient(
           to bottom,
           white,
           white
         )
         padding-box,
      conic-gradient(
     from var(--bg-angle) in oklch,
     oklch(0.65 0.25 340) 0%,
     oklch(0.70 0.25 290) 45%,
     oklch(0.70 0.25 290) 65%,
     oklch(0.65 0.25 340) 100%
   )
         border-box;
     border: 4px solid transparent;
   }
   
   
   
   
   
   .border-animated-violet-light {
     transition: all 0.3s ease;
     animation: spin 2s infinite linear;
     background:
       linear-gradient(
           to bottom,
           white,
           white
         )
         padding-box,
      conic-gradient(
     from var(--bg-angle) in oklch,
     oklch(0.65 0.25 340) 0%,
     oklch(0.70 0.25 290) 45%,
     oklch(0.70 0.25 290) 65%,
     oklch(0.65 0.25 340) 100%
   )
         border-box;
     border: 8px solid transparent;
   }
   
   
   
   
   
   .border-animated-blue-light {
     transition: all 0.3s ease;
     animation: spin 2s infinite linear;
     background:
       linear-gradient(
           to bottom,
           white,
           white
         )
         padding-box,
      conic-gradient(
     from var(--bg-angle) in oklch,
     oklch(0.65 0.25 220) 0%,
     oklch(0.70 0.25 180) 45%,
     oklch(0.70 0.25 180) 65%,
     oklch(0.65 0.25 220) 100%
   )
         border-box;
     border: 8px solid transparent;
   }
   
   
   
   
   
   
   /*--------------------------------------------------------------
   
   ## Shadow colors
   
   --------------------------------------------------------------*/
   
   .shadow-dark {
     filter: drop-shadow(0px 18px 12px rgba(40, 60, 80, var(--shadow-opacity,0.35)));
   }
   
   .shadow-pink {
     filter: drop-shadow(4px 14px 10px rgba(224, 78, 157, var(--shadow-opacity,0.45)));
   }
   
   .shadow-violet {
     filter: drop-shadow(4px 14px 10px rgba(171, 119, 255, var(--shadow-opacity,0.45)));
   }
   
   .shadow-yellow {
     filter: drop-shadow(4px 14px 10px rgba(255, 187, 68, var(--shadow-opacity,0.45)));
   }
   
   .shadow-brown {
     filter: drop-shadow(4px 14px 10px rgba(157, 130, 113, var(--shadow-opacity,0.45)));
   }
   
   .shadow-green {
       filter: drop-shadow(4px 14px 10px rgba(37, 211, 102, var(--shadow-opacity,0.45)));
   }
   
   .shadow-red {
     filter: drop-shadow(4px 14px 10px rgba(255, 68, 68, var(--shadow-opacity,0.45)));
   }
   
   .shadow-ocean {
     filter: drop-shadow(4px 14px 10px rgba(25, 77, 212, var(--shadow-opacity,0.45)));
   }
   
   .shadow-blue {
     filter: drop-shadow(4px 14px 10px rgba(18, 137, 237, var(--shadow-opacity,0.45)));
   }
   
   .shadow-sky {
     filter: drop-shadow(4px 14px 10px rgba(86, 204, 242, var(--shadow-opacity,0.45)));
   }
   
   
   
   /*--------------------------------------------------------------
   
   ## Float animations
   
   --------------------------------------------------------------*/
   
   .animate-float-around {
     animation-name: float-around;
     animation-timing-function: ease-in-out;
     animation-iteration-count: infinite;
   }
   
   
   .animate-float-updown-light {
     animation-name: float-updown-light;
     animation-timing-function: ease-in-out;
     animation-iteration-count: infinite;
   }
   
   
   
   .animate-float-updown-very-light {
     animation-name: float-updown-very-light;
     animation-timing-function: ease-in-out;
     animation-iteration-count: infinite;
   }
   
   
   @keyframes float-updown-very-light {
     0% {
       transform: translate(0, 5px);
     }
     50% {
       transform: translate(0, -5px);
     }
     100% {
       transform: translate(0, 5px);
     }
   }
   
   
   @keyframes float-updown-light {
     0% {
       transform: translate(0, 10px);
     }
     50% {
       transform: translate(0, -10px);
     }
     100% {
       transform: translate(0, 10px);
     }
   }
   
   @keyframes float-around {
     0% {
       transform: translate(0, 0);
     }
     25% {
       transform: translate(100px, -100px);
     }
     50% {
       transform: translate(0, -200px);
     }
     75% {
       transform: translate(-100px, -100px);
     }
     100% {
       transform: translate(0, 0);
     }
   }
   
   
   
   
   
   /*--------------------------------------------------------------
   
   ## Opacity animations
   
   --------------------------------------------------------------*/
   
   
   
   .animate-opacity-70-100 {
     animation-name: opacity-70-100;
     animation-timing-function: ease-in-out;
     animation-iteration-count: infinite;
   }
   
   
   
   @keyframes opacity-70-100 {
     0% {
       opacity: 0.7;
     }
     25% {
       opacity: 0.85;
     }
     50% {
       opacity: 1;
     }
     75% {
       opacity: 0.85;
     }
     100% {
       opacity: 0.7;
     }
   }
   
   
   
   
   /*--------------------------------------------------------------
   
   ## Animation combos
   
   --------------------------------------------------------------*/
   
   
   
   /* Rule for combined float and opacity animation */
   .animate-float-around.animate-opacity-70-100 {
     animation-name: float-around, opacity-70-100;
     animation-timing-function: ease-in-out; /* Applies to both */
     animation-iteration-count: infinite; /* Applies to both */
   }
   
   /* Rule for combined float and opacity animation for -2 variants */
   .animate-float-2.animate-opacity-70-100 {
     animation-name: float-2, opacity-70-100;
     animation-timing-function: ease-in-out; /* Applies to both */
     animation-iteration-count: infinite; /* Applies to both */
   }
   
   
   /*--------------------------------------------------------------
   
   ## Button
   
   --------------------------------------------------------------*/
   
   
   .button {
     box-shadow:0px 8px 28px rgba(40,60,80,0);
     transition:all 0.3s ease;
     translate:0 0;
   }
   
   .hover-dark-shadow:hover {
     box-shadow:0px 8px 28px rgba(40,60,80,1);
     transition:all 0.3s ease;
   }
   
   
   .hover-light-shadow:hover {
     box-shadow:0px 8px 28px rgba(40,60,80,0.25);
     transition:all 0.3s ease;
   }
   
   .hover-shadow-violet:hover {
     box-shadow: 0px 8px 28px rgba(171, 119, 255, var(--shadow-opacity,0.45));
     transition: all 0.3s ease;
   }
   
   .hover-shadow-pink:hover {
     box-shadow: 0px 8px 28px rgba(224, 78, 157, var(--shadow-opacity,0.45));
     transition: all 0.3s ease;
   }
   
   .hover-shadow-yellow:hover {
     box-shadow: 0px 8px 28px rgba(255, 187, 68, var(--shadow-opacity,0.45));
     transition: all 0.3s ease;
   }
   
   .hover-shadow-brown:hover {
     box-shadow: 0px 8px 28px rgba(157, 130, 113, var(--shadow-opacity,0.45));
     transition: all 0.3s ease;
   }
   
   .hover-shadow-green:hover {
     box-shadow: 0px 8px 28px rgba(37, 211, 102, var(--shadow-opacity,0.45));
     transition: all 0.3s ease;
   }
   
   .hover-shadow-red:hover {
     box-shadow: 0px 8px 28px rgba(255, 68, 68, var(--shadow-opacity,0.45));
     transition: all 0.3s ease;
   }
   
   .hover-shadow-ocean:hover {
     box-shadow: 0px 8px 28px rgba(25, 77, 212, var(--shadow-opacity,0.45));
     transition: all 0.3s ease;
   }
   
   .hover-shadow-blue:hover {
     box-shadow: 0px 8px 28px rgba(18, 137, 237, var(--shadow-opacity,0.45));
     transition: all 0.3s ease;
   }
   
   .hover-shadow-sky:hover {
     box-shadow: 0px 8px 28px rgba(86, 204, 242, var(--shadow-opacity,0.45));
     transition: all 0.3s ease;
   }
   
   .hover-shadow-whatsapp:hover {
     box-shadow: 0px 8px 28px rgba(37,211,102, var(--shadow-opacity,0.45));
     transition: all 0.3s ease;
   }
   
   
   
   
   
   .hover-up-2:hover {
     transition:all 0.3s ease;
     translate:0 -2px;
   }
   
   
   
   
   
   /*--------------------------------------------------------------
   
   ## Page - Header
   
   --------------------------------------------------------------*/
   
   
   .page-header .background:before {
     content:"";
     position:absolute;
     inset:0;
     z-index:20;
     width:100%;
     height:100%;
   background: linear-gradient(90deg, #192329 0%, #192329 15.38%, rgba(25, 35, 41, 0.89) 26.92%, rgba(25, 35, 41, 0) 100%);
   
   
   }
   
   
   
   
   /*--------------------------------------------------------------
   
   ## Front Page - Social
   
   --------------------------------------------------------------*/
    
   
   /**
    * `@property` is required for the animation to work.
    * Without it, the angle values won't interpolate properly.
    *
    * @see https://dev.to/afif/we-can-finally-animate-css-gradient-kdk
    */
    @property --bg-angle-social {
     inherits: false;
     initial-value: 0deg;
     syntax: "<angle>";
   }
   
   /**
    * To animate the gradient, we set the custom property to 1 full
    * rotation. The animation starts at the default value of `0deg`.
    */
   @keyframes spin-social {
     to {
       --bg-angle-social: 360deg;
     }
   }
   
   
   
   
   
   
   
   
   
   
   .button.hover-shadow-tiktok:hover,
   .button.hover-shadow-tiktok.js-manual-hover {
     box-shadow: 0px 18px 40px rgba(37, 244, 238, 0.4);
     transition:all 0.3s ease;
   }
   
   .border-animated-tiktok {
     transition: all 0.3s ease;
     scale:1;
   }
   
   .border-animated-tiktok:hover,
   .border-animated-tiktok.js-manual-hover {
     transition: all 0.3s ease;
     scale:1.07;
     animation: spin-social 2s infinite linear;
     background:
       linear-gradient(
           to bottom,
           white,
           white
         )
         padding-box,
      conic-gradient(
         from var(--bg-angle-social) in oklch,
         /* --- Sezione ColoreA solido (45% dell'arco) --- */
         oklch(0.65 0.25 19.88) 0%,      /* ColoreA inizia a 0% */
         oklch(0.65 0.25 19.88) 45%,     /* ColoreA rimane solido fino al 45% */
         /* --- Sfumatura breve da ColoreA a ColoreB (5% dell'arco: 45%-50%) --- */
         oklch(0.87 0.21 195.38) 50%,   /* ColoreB raggiunto al 50% */
         /* --- Sezione ColoreB solido (45% dell'arco) --- */
         oklch(0.87 0.21 195.38) 95%,   /* ColoreB rimane solido fino al 95% (50% + 45% = 95%) */
         /* --- Sfumatura breve da ColoreB a ColoreA (5% dell'arco: 95%-100%) --- */
         oklch(0.65 0.25 19.88) 100%    /* ColoreA raggiunto al 100% (e si ricongiunge con lo 0%) */
       )
         border-box;
     border: 4px solid transparent;
   }
   
   
   .button.hover-shadow-instagram:hover,
   .button.hover-shadow-instagram.js-manual-hover {
     box-shadow: 0px 18px 40px rgba(255, 187, 68, 0.4);
     transition:all 0.3s ease;
   } 
   
   .border-animated-instagram {
     transition: all 0.3s ease;
     scale:1;
   }
   
   .border-animated-instagram:hover,
   .border-animated-instagram.js-manual-hover {
     transition: all 0.3s ease;
     scale:1.07;
     animation: spin-social 2s infinite linear;
     background:
       linear-gradient(
           to bottom,
           white,
           white
         )
         padding-box,
         conic-gradient(
           from var(--bg-angle-social) in oklch,
           oklch(0.91 0.11 86.4) 0%,    /* Giallo-arancio chiaro */
           oklch(0.74 0.18 40.4) 25%,   /* Arancione */
           oklch(0.67 0.26 20.0) 38%,   /* Rosso */
           oklch(0.61 0.21 327.9) 50%,  /* Viola-rosa */
           oklch(0.71 0.15 298.0) 100%  /* Viola chiaro */
         )
         border-box;
     border: 4px solid transparent;
   }
   
   
   
   .button.hover-shadow-youtube:hover,
   .button.hover-shadow-youtube.js-manual-hover {
     box-shadow: 0px 18px 40px rgba(255, 68, 68, 0.4);
     transition:all 0.3s ease;
   }
   
   .border-animated-youtube {
     transition: all 0.3s ease;
     scale:1;
   }
   
   .border-animated-youtube:hover,
   .border-animated-youtube.js-manual-hover {
     transition: all 0.3s ease;
     scale:1.07;
     animation: spin-social 2s infinite linear;
     background:
       linear-gradient(
           to bottom,
           white,
           white
         )
         padding-box,
         conic-gradient(
     from var(--bg-angle-social) in oklch,
     /* Lighter, slightly orange-ish vibrant red */
     oklch(0.8 0.2 20.0) 0%,
     /* Bright, classic red */
     oklch(0.7 0.23 29.2) 25%, /* This is our #FF0000 equivalent hue */
     /* Saturated, slightly cooler, more crimson/magenta-leaning red */
     oklch(0.65 0.25 10.0) 38%, /* Could also use ~355 for a similar feel from the other side of red */
     /* Deeper, more scarlet/warm red */
     oklch(0.6 0.22 35.0) 50%,
     /* A vibrant, slightly pinkish-red to end */
     oklch(0.75 0.21 350.0) 100%
   )
         border-box;
     border: 4px solid transparent;
   }
   
   
   
   .button.hover-shadow-facebook:hover,
   .button.hover-shadow-facebook.js-manual-hover {
     box-shadow: 0px 18px 40px rgba(0, 85, 255, 0.4);
     transition:all 0.3s ease;
   }
   
   .border-animated-facebook {
     transition: all 0.3s ease;
     scale:1;
   }
   
   .border-animated-facebook:hover,
   .border-animated-facebook.js-manual-hover {
     transition: all 0.3s ease;
     scale:1.07;
     animation: spin-social 2s infinite linear;
     background:
       linear-gradient(
           to bottom,
           white,
           white
         )
         padding-box,
         conic-gradient(
     from var(--bg-angle-social) in oklch,
     /* Lighter, slightly softer cyan-blue */
     oklch(0.75 0.15 235.0) 0%,
     /* Bright, vibrant azure/sky blue */
     oklch(0.68 0.18 245.0) 25%,
     /* Main blue - our #0866FF equivalent */
     oklch(0.603 0.198 263.3) 38%,
     /* Deeper, intense indigo blue */
     oklch(0.52 0.17 280.0) 50%,
     /* Vibrant, slightly purplish-blue to end */
     oklch(0.65 0.16 290.0) 100%
   )
         border-box;
     border: 4px solid transparent;
   }
   
   
   
   
   @media (max-width: 1920px) {
     
     
     
   }
   
   @media (max-width: 1680px) {
     
     
     
   }
   
   @media (max-width: 1440px) {
     
     
     
   }
   
   @media (max-width: 1280px) {
     
     
     
   }
   
   @media (max-width: 1120px) {
     
     
     
   }
   
   @media (max-width: 960px) {
     
     
     
   }
   
   @media (max-width: 640px) {
     
     
     
   }
   
   @media (max-width: 480px) {
     
     
     
   }
   
   @media (max-width: 360px) {
     
     
     
   }
   