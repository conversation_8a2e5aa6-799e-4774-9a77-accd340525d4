

  
@font-face {
  font-family: "AvenirNextLTProDemi";
  font-style: normal;
  font-weight: 600;
  font-stretch: normal;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextLTPro_normal_normal_600_subset1.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextLTPro_normal_normal_600_subset1.woff') format('woff');
  unicode-range: U+0020-0023,U+0026-003b,U+003d,U+003f-005b,U+005d-005e,U+0060-007a,U+007e,U+00a7,U+00ab,U+00b4,U+00b7,U+00bb,U+2010-2011,U+2013-2014,U+2018-201a,U+201c-201e,U+2020-2021,U+2026,U+2032-2033,U+2039-203a,U+0020-0024,U+00a5,U+00c0-00cf,U+00d1-00d4,U+00d6,U+00d8-00dc,U+00e0-00ef,U+00f1-00f4,U+00f6,U+00f8-00fc,U+00ff-0103,U+0112-0115,U+012a-012d,U+014c-014f,U+0152-0153,U+016a-016d,U+0178,U+1e9e,U+0020-0022,U+0024,U+0027-0029,U+002b-003b,U+0060-007b,U+007d-007e,U+00a3,U+00aa-00ab,U+00ba-00bb,U+00d1-00d6,U+00df-00ef,U+00f1-00f6,U+00ff,U+010d,U+0142,U+09f3,U+0e3f,U+10da,U+17db,U+2011,U+2014,U+20a1,U+20a9-20ae,U+20b1-20b2,U+20b4,U+20b8-20ba,U+20bd-20be,U+2191;
}
@font-face {
  font-family: "AvenirNextLTProBold";
  font-style: normal;
  font-weight: 700;
  font-stretch: normal;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextLTPro_normal_normal_700_subset1.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextLTPro_normal_normal_700_subset1.woff') format('woff');
  unicode-range: U+0020-0023,U+0026-003b,U+003d,U+003f-005b,U+005d-005e,U+0060-007a,U+007e,U+00a7,U+00ab,U+00b4,U+00b7,U+00bb,U+2010-2011,U+2013-2014,U+2018-201a,U+201c-201e,U+2020-2021,U+2026,U+2032-2033,U+2039-203a,U+0020-0024,U+00a5,U+00c0-00cf,U+00d1-00d4,U+00d6,U+00d8-00dc,U+00e0-00ef,U+00f1-00f4,U+00f6,U+00f8-00fc,U+00ff-0103,U+0112-0115,U+012a-012d,U+014c-014f,U+0152-0153,U+016a-016d,U+0178,U+1e9e,U+0020-0022,U+0024,U+0027-0029,U+002b-003b,U+0060-007b,U+007d-007e,U+00a3,U+00aa-00ab,U+00ba-00bb,U+00d1-00d6,U+00df-00ef,U+00f1-00f6,U+00ff,U+010d,U+0142,U+09f3,U+0e3f,U+10da,U+17db,U+2011,U+2014,U+20a1,U+20a9-20ae,U+20b1-20b2,U+20b4,U+20b8-20ba,U+20bd-20be,U+2191;
}
@font-face {
  font-family: "TTNormsPro";
  font-style: normal;
  font-weight: 750;
  font-stretch: normal;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/TTNormsPro_normal_normal_750_subset1.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/TTNormsPro_normal_normal_750_subset1.woff') format('woff');
  unicode-range: U+0020-0023,U+0026-003b,U+003d,U+003f-005b,U+005d-005e,U+0060-007a,U+007e,U+00a7,U+00ab,U+00b4,U+00b7,U+00bb,U+2010-2011,U+2013-2014,U+2018-201a,U+201c-201e,U+2020-2021,U+2026,U+2032-2033,U+2039-203a,U+0020-0024,U+00a5,U+00c0-00cf,U+00d1-00d4,U+00d6,U+00d8-00dc,U+00e0-00ef,U+00f1-00f4,U+00f6,U+00f8-00fc,U+00ff-0103,U+0112-0115,U+012a-012d,U+014c-014f,U+0152-0153,U+016a-016d,U+0178,U+1e9e,U+0020-0022,U+0024,U+0027-0029,U+002b-003b,U+0060-007b,U+007d-007e,U+00a3,U+00aa-00ab,U+00ba-00bb,U+00d1-00d6,U+00df-00ef,U+00f1-00f6,U+00ff,U+010d,U+0142,U+09f3,U+0e3f,U+10da,U+17db,U+2011,U+2014,U+20a1,U+20a9-20ae,U+20b1-20b2,U+20b4,U+20b8-20ba,U+20bd-20be,U+2191;
}
@font-face {
  font-family: "AvenirNextLTProMedium";
  font-style: normal;
  font-weight: 500;
  font-stretch: normal;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextLTPro_normal_normal_500_subset1.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextLTPro_normal_normal_500_subset1.woff') format('woff');
  unicode-range: U+0020-0023,U+0026-003b,U+003d,U+003f-005b,U+005d-005e,U+0060-007a,U+007e,U+00a7,U+00ab,U+00b4,U+00b7,U+00bb,U+2010-2011,U+2013-2014,U+2018-201a,U+201c-201e,U+2020-2021,U+2026,U+2032-2033,U+2039-203a,U+0020-0024,U+00a5,U+00c0-00cf,U+00d1-00d4,U+00d6,U+00d8-00dc,U+00e0-00ef,U+00f1-00f4,U+00f6,U+00f8-00fc,U+00ff-0103,U+0112-0115,U+012a-012d,U+014c-014f,U+0152-0153,U+016a-016d,U+0178,U+1e9e,U+0020-0022,U+0024,U+0027-0029,U+002b-003b,U+0060-007b,U+007d-007e,U+00a3,U+00aa-00ab,U+00ba-00bb,U+00d1-00d6,U+00df-00ef,U+00f1-00f6,U+00ff,U+010d,U+0142,U+09f3,U+0e3f,U+10da,U+17db,U+2011,U+2014,U+20a1,U+20a9-20ae,U+20b1-20b2,U+20b4,U+20b8-20ba,U+20bd-20be,U+2191;
}
@font-face {
  font-family: "AvenirNextLTProRegular";
  font-style: normal;
  font-weight: 400;
  font-stretch: normal;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextLTPro_normal_normal_400_subset1.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextLTPro_normal_normal_400_subset1.woff') format('woff');
  unicode-range: U+0020-0023,U+0026-003b,U+003d,U+003f-005b,U+005d-005e,U+0060-007a,U+007e,U+00a7,U+00ab,U+00b4,U+00b7,U+00bb,U+2010-2011,U+2013-2014,U+2018-201a,U+201c-201e,U+2020-2021,U+2026,U+2032-2033,U+2039-203a,U+0020-0024,U+00a5,U+00c0-00cf,U+00d1-00d4,U+00d6,U+00d8-00dc,U+00e0-00ef,U+00f1-00f4,U+00f6,U+00f8-00fc,U+00ff-0103,U+0112-0115,U+012a-012d,U+014c-014f,U+0152-0153,U+016a-016d,U+0178,U+1e9e,U+0020-0022,U+0024,U+0027-0029,U+002b-003b,U+0060-007b,U+007d-007e,U+00a3,U+00aa-00ab,U+00ba-00bb,U+00d1-00d6,U+00df-00ef,U+00f1-00f6,U+00ff,U+010d,U+0142,U+09f3,U+0e3f,U+10da,U+17db,U+2011,U+2014,U+20a1,U+20a9-20ae,U+20b1-20b2,U+20b4,U+20b8-20ba,U+20bd-20be,U+2191;
}
@font-face {
  font-family: "DINNextLTPro";
  font-style: normal;
  font-weight: 700;
  font-stretch: condensed;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/DINNextLTPro_normal_condensed_700_subset1.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/DINNextLTPro_normal_condensed_700_subset1.woff') format('woff');
  unicode-range: U+0020-0023,U+0026-003b,U+003d,U+003f-005b,U+005d-005e,U+0060-007a,U+007e,U+00a7,U+00ab,U+00b4,U+00b7,U+00bb,U+2010-2011,U+2013-2014,U+2018-201a,U+201c-201e,U+2020-2021,U+2026,U+2032-2033,U+2039-203a,U+0020-0024,U+00a5,U+00c0-00cf,U+00d1-00d4,U+00d6,U+00d8-00dc,U+00e0-00ef,U+00f1-00f4,U+00f6,U+00f8-00fc,U+00ff-0103,U+0112-0115,U+012a-012d,U+014c-014f,U+0152-0153,U+016a-016d,U+0178,U+1e9e,U+0020-0022,U+0024,U+0027-0029,U+002b-003b,U+0060-007b,U+007d-007e,U+00a3,U+00aa-00ab,U+00ba-00bb,U+00d1-00d6,U+00df-00ef,U+00f1-00f6,U+00ff,U+010d,U+0142,U+09f3,U+0e3f,U+10da,U+17db,U+2011,U+2014,U+20a1,U+20a9-20ae,U+20b1-20b2,U+20b4,U+20b8-20ba,U+20bd-20be,U+2191;
}



  /*
@font-face {
  font-family: "AvenirNextCondensedMedium";
  font-style: normal;
  font-stretch: condensed;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextCondensedMedium_normal_condensed.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextCondensedMedium_normal_condensed.woff') format('woff');
}
 */
/*
@font-face {
  font-family: "AvenirNextLight";
  font-style: normal;
  font-stretch: normal;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextLight_normal_normal.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextLight_normal_normal.woff') format('woff');
}
 */

/*
@font-face {
  font-family: "AvenirNextRegular";
  font-style: normal;
  font-stretch: normal;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextRegular_normal_normal.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextRegular_normal_normal.woff') format('woff');
}
 */
/*
@font-face {
  font-family: "AvenirNextMedium";
  font-style: normal;
  font-stretch: normal;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextMedium_normal_normal.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextMedium_normal_normal.woff') format('woff');
}
 

@font-face {
  font-family: "AvenirNextDemi";
  font-style: normal;
  font-stretch: normal;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextDemi_normal_normal.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextDemi_normal_normal.woff') format('woff');
}


@font-face {
  font-family: "AvenirNextBold";
  font-style: normal;
  font-stretch: normal;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextBold_normal_normal.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextBold_normal_normal.woff') format('woff');
}
 

@font-face {
  font-family: "TTNormsProExtraBold";
  font-style: normal;
  font-stretch: normal;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/TTNormsProExtraBold_normal_normal.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/TTNormsProExtraBold_normal_normal.woff') format('woff');
}
 */
/*
@font-face {
  font-family: "AvenirNextCondensedLight";
  font-style: normal;
  font-stretch: condensed;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextCondensedLight_normal_condensed.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextCondensedLight_normal_condensed.woff') format('woff');
}
 */
/*
@font-face {
  font-family: "AvenirNextCondensed";
  font-style: normal;
  font-stretch: condensed;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextCondensed_normal_condensed.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/AvenirNextCondensed_normal_condensed.woff') format('woff');
}
 */
/*
@font-face {
  font-family: "TTNormsProDemiBold";
  font-style: normal;
  font-stretch: normal;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/TTNormsProDemiBold_normal_normal.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/TTNormsProDemiBold_normal_normal.woff') format('woff');
}
 */
/*
@font-face {
  font-family: "FFDINCondensedBold";
  font-style: normal;
  font-stretch: condensed;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/FFDINCondensedBold_normal_condensed.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/FFDINCondensedBold_normal_condensed.woff') format('woff');
}*/
 
/*
@font-face {
  font-family: "FFDINBold";
  font-style: normal;
  font-stretch: normal;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/FFDINBold_normal_normal.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/FFDINBold_normal_normal.woff') format('woff');
}
 */
/*
@font-face {
  font-family: "TTNormsProBold";
  font-style: normal;
  font-stretch: normal;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/TTNormsProBold_normal_normal.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/TTNormsProBold_normal_normal.woff') format('woff');
}
 */

/*

    @font-face{
        font-family:"TTNormsProRegular";
  font-style: normal;
  font-stretch: normal;
  font-display: swap;
        src:url("/wp-content/themes/presidentvoyage/fonts/TTNormsProNormal.woff2") format("woff2"),url("/wp-content/themes/presidentvoyage/fonts/TTNormsProNormal.woff") format("woff");
    }

*/
/*
@font-face {
  font-family: "TTNormsProMedium";
  font-style: normal;
  font-stretch: normal;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/TTNormsProMedium_normal_normal.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/TTNormsProMedium_normal_normal.woff') format('woff');
}
 */
/*
@font-face {
  font-family: "OlympianRoman";
  font-style: normal;
  font-stretch: normal;
  font-display: swap;
  src: url('/wp-content/themes/custom-theme/assets/fonts/OlympianRoman_normal_normal.woff2') format('woff2'), url('/wp-content/themes/custom-theme/assets/fonts/OlympianRoman_normal_normal.woff') format('woff');
}
 */

/*--------------------------------------------------------------

## Fonts

--------------------------------------------------------------*/
/*
.font-din {
  font-family: "DIN Next LT W01 Heavy Cond It";
  text-transform: uppercase;
}

.font-din-rc {
  font-family:"DINNextW01-CondensedReg";
}

.font-din-bc {
  font-family:"FFDINCondensedBold";
  line-height:0.85;
  text-transform:uppercase;
}


.font-ave-c {
  font-family: "Avenir Next LT W01 Condensed";
}

.font-ave-cl {
  font-family: "Avenir Next LT W01 Light Cond";
}

/*
.ave-cm {
  font-family: "Avenir Next LT W01 Medium Cond";
}*/

/*

.font-champ {
  font-family: "PF Champion Script W01 Regular";
}

.font-ave-b {
  font-family: "AvenirNextBold";
}

.font-ave-d {
  font-family: "AvenirNextDemi";
}

.font-ave-l {
  font-family:"Avenir Next LT W01 Light";
}

.font-ave-m {
  font-family: "AvenirNextMedium";
}

.font-ave-r {
  font-family: "AvenirNextRegular";
}


.font-ave-t {
  font-family: "Avenir Next W01 Thin";
}

.font-oly-r {
  font-family: "OlympianRoman";
  letter-spacing:0px!important;
}

.font-tt-mono-l {
  font-family: "TTNormsProMonoLight";
}

.font-tt-m {
  font-family: "TTNormsProMedium";
} 
 
.font-tt-b {
  font-family: "TT Norms W01 Bold";
}

.font-tt-eb {
  font-family: "TTNormsProExtraBold";
}

.font-pro-t {
  font-family: "Proxima Soft W01 Thin";
}

.font-pro-l {
  font-family: "Proxima Soft W01 Light";
}

.font-pro-r {
  font-family: "Proxima Soft W01 Regular";
}

.font-pro-m {
  font-family: "Proxima Soft W01 Medium";
}

.font-pro-s {
  font-family: "Proxima Soft W01 Semibold";
}

.font-pro-b {
  font-family: "Proxima Soft W01 Bold";
}

.font-pro-e {
  font-family: "Proxima Soft W01 Extrabold";
}

.font-square-r {
  font-family:"SquarePegROB W05 Regular";
}

*/


.font-ave-r {
  font-family: "AvenirNextLTProRegular", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-weight: 400;
}


.font-ave-d {
    font-family: "AvenirNextLTProDemi", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
    font-weight: 600;
}

.font-ave-m {
  font-family: "AvenirNextLTProMedium", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-weight: 500;
}

.font-ave-b {
  font-family: "AvenirNextLTProBold", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-weight: 700;
}

.font-din-bc {
  font-family: "DINNextLTPro", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-weight: 700;
  text-transform:uppercase;
  line-height:0.85;
}

.font-tt-eb {
  font-family: "TTNormsPro", ui-sans-serif, system-ui, sans-serif, "Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji";
  font-weight: 750;
}