/*--------------------------------------------------------------

## Framework CSS

Last-edit: 9 December 2024
  
Non mi convince la questione del decadimento
Non mi piace s:proprieta per togliere decadimento
Su layout con sidebar i valori vengono sballati
Su layout piattaforma potrebbe non essere ottimale
E' forse ideale lasciare in mano il responsive a chi sviluppa con regole dedicate attivate a mano?
Magari si può valutare una regola che applica il decadimento?

I fixed non subiscono il responsive verticale

  


Valutare di togliere i container piccoli tipo 4xs 3xs che sono inutili ed è meglio usare max-w a quel punto

Un po' difficile gestire il responsive con i valori in % in quanto c'è anche il decadimento da tenere da conto. 
Però non si possono usare i pixel e non lavoro direttamente in rem quindi è l'unico metodo per cambiare le dimensioni senza mettere un valore fisso o creare molte variabili base


mi serve che padding e margin siano a responsive 30 su 80px e 120px e 140px. se devo tenere allineato tutto il resto (es. left, right, height etc. così da avere tutto in linea devo stare attento al fatto che height rende troppo piccole le cose a height 80 e quindi magari preferire height 76 che ha una classe responsive in meno
oppure valutare di aumentare overall e lasciare responsive 40 su height 80px

IMP:
deciso 200px come valore switch dove passare da responsive-30 a responsive-70 non ha molto senso. mantiene tutti i valori piccoli allineati e quelli grandi non decadono velocemente. 200px è il padding o margin più grande che utilizzo di norma.

i max sui font mi stan stretti ma è davvero difficile controllarli
difficile gestirli in modo naturale in quanto sono l'elemento più delicat

--

Non ha alcun senso che i contenitori piccoli poi diventino grandi. 



--------------------------------------------------------------*/

/*--------------------------------------------------------------

## Template

--------------------------------------------------------------*/
 


@media (max-width: 1920px) {
  
  
  
}

@media (max-width: 1680px) {
  
  
  
}

@media (max-width: 1440px) {
  
  
  
}

@media (max-width: 1280px) {
  
  
  
}

@media (max-width: 1120px) {
  
  
  
}

@media (max-width: 960px) {
  
  
  
}

@media (max-width: 640px) {
  
  
  
}

@media (max-width: 480px) {
  
  
  
}

@media (max-width: 360px) {
  
  
  
}





/*--------------------------------------------------------------

## Responsive

--------------------------------------------------------------*/

:root {
  --responsive-100: 1;
  --responsive-90: 1;
  --responsive-80: 1;
  --responsive-70: 1;
  --responsive-60: 1;
  --responsive-50: 1;
  --responsive-40: 1;
  --responsive-30: 1;
  --responsive-20: 1;
  --responsive-10: 1;
}

@media (min-width:1120px) and (max-height:896px) {
  :root {
    --responsive-90: 0.85;
    --responsive-80: 0.85;
    --responsive-70: 0.85;
    --responsive-60: 0.85;
    --responsive-50: 0.85;
    --responsive-40: 0.85;
    --responsive-30: 0.85;
    --responsive-20: 0.85;
    --responsive-10: 0.85;
  }
}

@media (max-width: 1920px) {
  :root {
    --responsive-90: 0.99;
    --responsive-80: 0.98;
    --responsive-70: 0.97;
    --responsive-60: 0.96;
    --responsive-50: 0.94;
    --responsive-40: 0.93;
    --responsive-30: 0.92;
    --responsive-20: 0.90;
    --responsive-10: 0.89;
  }
}

@media (min-width:1120px) and (max-width:1920px) and (max-height:896px) {
  :root {
    --responsive-90: 0.84;
    --responsive-80: 0.83;
    --responsive-70: 0.82;
    --responsive-60: 0.81;
    --responsive-50: 0.80;
    --responsive-40: 0.79;
    --responsive-30: 0.78;
    --responsive-20: 0.77;
    --responsive-10: 0.76;
  }
}

@media (max-width: 1680px) {
  :root {
    --responsive-90: 0.98;
    --responsive-80: 0.96;
    --responsive-70: 0.93;
    --responsive-60: 0.91;
    --responsive-50: 0.89;
    --responsive-40: 0.87;
    --responsive-30: 0.84;
    --responsive-20: 0.81;
    --responsive-10: 0.79;
  }
}

@media (min-width:1120px) and (max-width:1680px) and (max-height:896px) {
  :root {
    --responsive-90: 0.83;
    --responsive-80: 0.81;
    --responsive-70: 0.78;
    --responsive-60: 0.75;
    --responsive-50: 0.74;
    --responsive-40: 0.71;
    --responsive-30: 0.68;
    --responsive-20: 0.65;
    --responsive-10: 0.63;
  }
}

@media (max-width: 1440px) {
  :root {
    --responsive-90: 0.97;
    --responsive-80: 0.93;
    --responsive-70: 0.90;
    --responsive-60: 0.87;
    --responsive-50: 0.83;
    --responsive-40: 0.80;
    --responsive-30: 0.76;
    --responsive-20: 0.73;
    --responsive-10: 0.70;
  }
}

@media (min-width:1120px) and (max-width:1440px) and (max-height:896px) {
  :root {
    --responsive-90: 0.82;
    --responsive-80: 0.79;
    --responsive-70: 0.76;
    --responsive-60: 0.73;
    --responsive-50: 0.70;
    --responsive-40: 0.67;
    --responsive-30: 0.64;
    --responsive-20: 0.61;
    --responsive-10: 0.59;
  }
}

@media (max-width: 1280px) {
  :root {
    --responsive-90: 0.96;
    --responsive-80: 0.91;
    --responsive-70: 0.87;
    --responsive-60: 0.83;
    --responsive-50: 0.78;
    --responsive-40: 0.73;
    --responsive-30: 0.69;
    --responsive-20: 0.64;
    --responsive-10: 0.60;
  }
}

@media (min-width:1120px) and (max-width:1280px) and (max-height:896px) {
  :root {
    --responsive-90: 0.81;
    --responsive-80: 0.78;
    --responsive-70: 0.75;
    --responsive-60: 0.72;
    --responsive-50: 0.69;
    --responsive-40: 0.65;
    --responsive-30: 0.61;
    --responsive-20: 0.58;
    --responsive-10: 0.55;
  }
}

@media (max-width: 1120px) {
  :root {
    --responsive-90: 0.94;
    --responsive-80: 0.89;
    --responsive-70: 0.83;
    --responsive-60: 0.78;
    --responsive-50: 0.72;
    --responsive-40: 0.67;
    --responsive-30: 0.61;
    --responsive-20: 0.56;
    --responsive-10: 0.50;
  }
}

@media (max-width: 960px) {
  :root {
    --responsive-90: 0.93;
    --responsive-80: 0.87;
    --responsive-70: 0.80;
    --responsive-60: 0.74;
    --responsive-50: 0.67;
    --responsive-40: 0.60;
    --responsive-30: 0.54;
    --responsive-20: 0.47;
    --responsive-10: 0.41;
  }
}

@media (max-width: 640px) {
  :root {
    --responsive-90: 0.91;
    --responsive-80: 0.84;
    --responsive-70: 0.76;
    --responsive-60: 0.69;
    --responsive-50: 0.61;
    --responsive-40: 0.53;
    --responsive-30: 0.46;
    --responsive-20: 0.38;
    --responsive-10: 0.31;
  }
}

@media (max-width: 480px) {
  :root {
    --responsive-90: 0.90;
    --responsive-80: 0.82;
    --responsive-70: 0.73;
    --responsive-60: 0.65;
    --responsive-50: 0.56;
    --responsive-40: 0.47;
    --responsive-30: 0.39;
    --responsive-20: 0.30;
    --responsive-10: 0.22;
  }
}

@media (max-width: 360px) {
  :root {
    --responsive-90: 0.90;
    --responsive-80: 0.80;
    --responsive-70: 0.70;
    --responsive-60: 0.60;
    --responsive-50: 0.50;
    --responsive-40: 0.40;
    --responsive-30: 0.30;
    --responsive-20: 0.20;
    --responsive-10: 0.10;
  }
}




/*--------------------------------------------------------------

## Containers


## Wrapper

o wrapper con gutter

o wrapper sulle media query

o gutter

o container piccoli che rimangono piccoli


--------------------------------------------------------------*/



.container-xl {
  max-width: 1760px;
}
.container-l {
  max-width: 1600px;
}
.container-m {
  max-width: 1440px;
}
.container-s {
  max-width: 1280px;
}
.container-xs {
  max-width: 1120px;
}
.container-2xs {
  max-width: 960px;
}
.container-3xs {
  max-width: 640px;
}
.container-4xs {
  max-width: 480px;
}

@media (min-width:1120px) and (max-height:896px) {
  .container-xl {
    max-width: 1520px;
  }
  .container-l {
    max-width: 1520px;
  }
  .container-m {
    max-width: 1360px;
  }
  .container-s {
    max-width: 1200px;
  }
  .container-xs {
    max-width: 1120px;
  }
  .container-2xs {
    max-width: 800px;
  }
  .container-3xs {
    max-width: 560px;
  }
  .container-4xs {
    max-width: 420px; 
  }
}

@media (max-width:1920px) {
  .container-xl {
    max-width: 1520px;
  }
  .container-l {
    max-width: 1520px;
  }
  .container-m {
    max-width: 1360px;
  }
  .container-s {
    max-width: 1200px;
  }
  .container-xs {
    max-width: 1120px;
  }
  .container-2xs {
    max-width: 800px;
  }
  .container-3xs {
    max-width: 560px;
  }
  .container-4xs {
    max-width: 420px;
  }
}

@media (min-width:1120px) and (max-width:1920px) and (max-height:896px) {
  .container-xl {
    max-width: 1360px;
  }
  .container-l {
    max-width: 1360px;
  }
  .container-m {
    max-width: 1200px;
  }
  .container-s {
    max-width: 1120px;
  }
  .container-xs {
    max-width: 960px;
  }
  .container-2xs {
    max-width: 720px;
  }
  .container-3xs {
    max-width: 480px;
  }
  .container-4xs {
    max-width: 360px;
  }
}

@media (max-width:1680px) {
  .container-xl {
    max-width: 1280px;
  }
  .container-l {
    max-width: 1280px;
  }
  .container-m {
    max-width: 1200px;
  }
  .container-s {
    max-width: 1120px;
  }
  .container-xs {
    max-width: 960px;
  }
  .container-2xs {
    max-width: 720px;
  }
  .container-3xs {
    max-width: 480px;
  }
  .container-4xs {
    max-width: 360px;
  }
}

@media (min-width:1120px) and (max-width:1680px) and (max-height:896px) {
  .container-xl {
    max-width: 1120px;
  }
  .container-l {
    max-width: 1120px;
  }
  .container-m {
    max-width: 1120px;
  }
  .container-s {
    max-width: 960px;
  }
  .container-xs {
    max-width: 960px;
  }
  .container-2xs {
    max-width: 640px;
  }
  .container-3xs {
    max-width: 440px;
  }
  .container-4xs {
    max-width: 330px;
  }
}

@media (max-width:1440px) {
  .container-xl {
    max-width: 1120px;
  }
  .container-l {
    max-width: 1120px;
  }
  .container-m {
    max-width: 1120px;
  }
  .container-s {
    max-width: 960px;
  }
  .container-xs {
    max-width: 960px;
  }
  .container-2xs {
    max-width: 640px;
  }
  .container-3xs {
    max-width: 440px;
  }
  .container-4xs {
    max-width: 330px;
  }
}

@media (min-width:1120px) and (max-width:1440px) and (max-height:896px) {
  .container-xl {
    max-width: 960px;
  }
  .container-l {
    max-width: 960px;
  }
  .container-m {
    max-width: 960px;
  }
  .container-s {
    max-width: 960px;
  }
  .container-xs {
    max-width: 960px;
  }
  .container-2xs {
    max-width: 560px;
  }
  .container-3xs {
    max-width: 400px;
  }
  .container-4xs {
    max-width: 300px;
  }
}

@media (max-width:1280px) {
  .container-xl {
    max-width: 960px;
  }
  .container-l {
    max-width: 960px;
  }
  .container-m {
    max-width: 960px;
  }
  .container-s {
    max-width: 960px;
  }
  .container-xs {
    max-width: 960px;
  }
  .container-2xs {
    max-width: 560px;
  }
  .container-3xs {
    max-width: 400px;
  }
  .container-4xs {
    max-width: 300px;
  }
}

@media (min-width:1120px) and (max-width:1280px) and (max-height:896px) {
  .container-xl {
    max-width: 960px;
  }
  .container-l {
    max-width: 960px;
  }
  .container-m {
    max-width: 960px;
  }
  .container-s {
    max-width: 960px;
  }
  .container-xs {
    max-width: 960px;
  }
  .container-2xs {
    max-width: 480px;
  }
  .container-3xs {
    max-width: 360px;
  }
  .container-4xs {
    max-width: 300px;
  }
}

@media (max-width:1120px) {
  .container-xl {
    max-width: 800px;
  }
  .container-l {
    max-width: 800px;
  }
  .container-m {
    max-width: 800px;
  }
  .container-s {
    max-width: 800px;
  }
  .container-xs {
    max-width: 800px;
  }
  .container-2xs {
    max-width: 800px;
  }
  .container-3xs {
    max-width: 400px;
  }
  .container-4xs {
    max-width: 800px;
  }

  .gutter {
    margin-left:calc(100vw - 800px)/2;
    margin-right:calc(100vw - 800px)/2;
  }
}

@media (max-width:960px) {
  .container-xl {
    max-width: 83vw;
  }
  .container-l {
    max-width: 83vw;
  }
  .container-m {
    max-width: 83vw;
  }
  .container-s {
    max-width: 83vw;
  }
  .container-xs {
    max-width: 83vw;
  }
  .container-2xs {
    max-width: 83vw;
  }
  .container-3xs {
    max-width: 400px;
  }
  .container-4xs {
    max-width: 83vw;
  }


  .gutter {
    margin-left:8.5vw;
    margin-right:8.5vw;
  }
}


@media (max-width:480px) {
  .container-xl {
    max-width: 85vw;
  }
  .container-l {
    max-width: 85vw;
  }
  .container-m {
    max-width: 85vw;
  }
  .container-s {
    max-width: 85vw;
  }
  .container-xs {
    max-width: 85vw;
  }
  .container-2xs {
    max-width: 85vw;
  }
  .container-3xs {
    max-width: 85vw;
  }
  .container-4xs {
    max-width: 85vw;
  }


  .gutter {
    margin-left:7.5vw;
    margin-right:7.5vw;
  }
}


@media (max-width:360px) {
  .container-xl {
    max-width: 88vw;
  }
  .container-l {
    max-width: 88vw;
  }
  .container-m {
    max-width: 88vw;
  }
  .container-s {
    max-width: 88vw;
  }
  .container-xs {
    max-width: 88vw;
  }
  .container-2xs {
    max-width: 88vw;
  }
  .container-3xs {
    max-width: 88vw;
  }
  .container-4xs {
    max-width: 88vw;
  }


  .gutter {
    margin-left:6vw;
    margin-right:6vw;
  }

}



/* Base counters (no media query) */
.container--xl {
  margin-left: calc((100vw - 1760px)/-2);
  margin-right: calc((100vw - 1760px)/-2);
}
.container--l {
  margin-left: calc((100vw - 1600px)/-2);
  margin-right: calc((100vw - 1600px)/-2);
}
.container--m {
  margin-left: calc((100vw - 1440px)/-2);
  margin-right: calc((100vw - 1440px)/-2);
}
.container--s {
  margin-left: calc((100vw - 1280px)/-2);
  margin-right: calc((100vw - 1280px)/-2);
}
.container--xs {
  margin-left: calc((100vw - 1120px)/-2);
  margin-right: calc((100vw - 1120px)/-2);
}
.container--2xs {
  margin-left: calc((100vw - 960px)/-2);
  margin-right: calc((100vw - 960px)/-2);
}
.container--3xs {
  margin-left: calc((100vw - 640px)/-2);
  margin-right: calc((100vw - 640px)/-2);
}
.container--4xs {
  margin-left: calc((100vw - 480px)/-2);
  margin-right: calc((100vw - 480px)/-2);
}

/* (min-width:1120px) and (max-height:896px) */
@media (min-width:1120px) and (max-height:896px) {
  .container--xl {
    margin-left: calc((100vw - 1520px)/-2);
    margin-right: calc((100vw - 1520px)/-2);
  }
  .container--l {
    margin-left: calc((100vw - 1520px)/-2);
    margin-right: calc((100vw - 1520px)/-2);
  }
  .container--m {
    margin-left: calc((100vw - 1360px)/-2);
    margin-right: calc((100vw - 1360px)/-2);
  }
  .container--s {
    margin-left: calc((100vw - 1200px)/-2);
    margin-right: calc((100vw - 1200px)/-2);
  }
  .container--xs {
    margin-left: calc((100vw - 1120px)/-2);
    margin-right: calc((100vw - 1120px)/-2);
  }
  .container--2xs {
    margin-left: calc((100vw - 800px)/-2);
    margin-right: calc((100vw - 800px)/-2);
  }
  .container--3xs {
    margin-left: calc((100vw - 560px)/-2);
    margin-right: calc((100vw - 560px)/-2);
  }
  .container--4xs {
    margin-left: calc((100vw - 420px)/-2);
    margin-right: calc((100vw - 420px)/-2);
  }
}

/* (max-width:1920px) */
@media (max-width:1920px) {
  .container--xl {
    margin-left: calc((100vw - 1520px)/-2);
    margin-right: calc((100vw - 1520px)/-2);
  }
  .container--l {
    margin-left: calc((100vw - 1520px)/-2);
    margin-right: calc((100vw - 1520px)/-2);
  }
  .container--m {
    margin-left: calc((100vw - 1360px)/-2);
    margin-right: calc((100vw - 1360px)/-2);
  }
  .container--s {
    margin-left: calc((100vw - 1200px)/-2);
    margin-right: calc((100vw - 1200px)/-2);
  }
  .container--xs {
    margin-left: calc((100vw - 1120px)/-2);
    margin-right: calc((100vw - 1120px)/-2);
  }
  .container--2xs {
    margin-left: calc((100vw - 800px)/-2);
    margin-right: calc((100vw - 800px)/-2);
  }
  .container--3xs {
    margin-left: calc((100vw - 560px)/-2);
    margin-right: calc((100vw - 560px)/-2);
  }
  .container--4xs {
    margin-left: calc((100vw - 420px)/-2);
    margin-right: calc((100vw - 420px)/-2);
  }
}

/* (min-width:1120px) and (max-width:1920px) and (max-height:896px) */
@media (min-width:1120px) and (max-width:1920px) and (max-height:896px) {
  .container--xl {
    margin-left: calc((100vw - 1280px)/-2);
    margin-right: calc((100vw - 1280px)/-2);
  }
  .container--l {
    margin-left: calc((100vw - 1280px)/-2);
    margin-right: calc((100vw - 1280px)/-2);
  }
  .container--m {
    margin-left: calc((100vw - 1120px)/-2);
    margin-right: calc((100vw - 1120px)/-2);
  }
  .container--s {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--xs {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--2xs {
    margin-left: calc((100vw - 720px)/-2);
    margin-right: calc((100vw - 720px)/-2);
  }
  .container--3xs {
    margin-left: calc((100vw - 480px)/-2);
    margin-right: calc((100vw - 480px)/-2);
  }
  .container--4xs {
    margin-left: calc((100vw - 360px)/-2);
    margin-right: calc((100vw - 360px)/-2);
  }
}

/* (max-width:1680px) */
@media (max-width:1680px) {
  .container--xl {
    margin-left: calc((100vw - 1280px)/-2);
    margin-right: calc((100vw - 1280px)/-2);
  }
  .container--l {
    margin-left: calc((100vw - 1280px)/-2);
    margin-right: calc((100vw - 1280px)/-2);
  }
  .container--m {
    margin-left: calc((100vw - 1120px)/-2);
    margin-right: calc((100vw - 1120px)/-2);
  }
  .container--s {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--xs {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--2xs {
    margin-left: calc((100vw - 720px)/-2);
    margin-right: calc((100vw - 720px)/-2);
  }
  .container--3xs {
    margin-left: calc((100vw - 480px)/-2);
    margin-right: calc((100vw - 480px)/-2);
  }
  .container--4xs {
    margin-left: calc((100vw - 360px)/-2);
    margin-right: calc((100vw - 360px)/-2);
  }
}

/* (min-width:1120px) and (max-width:1680px) and (max-height:896px) */
@media (min-width:1120px) and (max-width:1680px) and (max-height:896px) {
  .container--xl {
    margin-left: calc((100vw - 1120px)/-2);
    margin-right: calc((100vw - 1120px)/-2);
  }
  .container--l {
    margin-left: calc((100vw - 1120px)/-2);
    margin-right: calc((100vw - 1120px)/-2);
  }
  .container--m {
    margin-left: calc((100vw - 1120px)/-2);
    margin-right: calc((100vw - 1120px)/-2);
  }
  .container--s {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--xs {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--2xs {
    margin-left: calc((100vw - 640px)/-2);
    margin-right: calc((100vw - 640px)/-2);
  }
  .container--3xs {
    margin-left: calc((100vw - 440px)/-2);
    margin-right: calc((100vw - 440px)/-2);
  }
  .container--4xs {
    margin-left: calc((100vw - 330px)/-2);
    margin-right: calc((100vw - 330px)/-2);
  }
}

/* (max-width:1440px) */
@media (max-width:1440px) {
  .container--xl {
    margin-left: calc((100vw - 1120px)/-2);
    margin-right: calc((100vw - 1120px)/-2);
  }
  .container--l {
    margin-left: calc((100vw - 1120px)/-2);
    margin-right: calc((100vw - 1120px)/-2);
  }
  .container--m {
    margin-left: calc((100vw - 1120px)/-2);
    margin-right: calc((100vw - 1120px)/-2);
  }
  .container--s {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--xs {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--2xs {
    margin-left: calc((100vw - 640px)/-2);
    margin-right: calc((100vw - 640px)/-2);
  }
  .container--3xs {
    margin-left: calc((100vw - 440px)/-2);
    margin-right: calc((100vw - 440px)/-2);
  }
  .container--4xs {
    margin-left: calc((100vw - 330px)/-2);
    margin-right: calc((100vw - 330px)/-2);
  }
}

/* (min-width:1120px) and (max-width:1440px) and (max-height:896px) */
@media (min-width:1120px) and (max-width:1440px) and (max-height:896px) {
  .container--xl {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--l {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--m {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--s {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--xs {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--2xs {
    margin-left: calc((100vw - 560px)/-2);
    margin-right: calc((100vw - 560px)/-2);
  }
  .container--3xs {
    margin-left: calc((100vw - 400px)/-2);
    margin-right: calc((100vw - 400px)/-2);
  }
  .container--4xs {
    margin-left: calc((100vw - 300px)/-2);
    margin-right: calc((100vw - 300px)/-2);
  }
}

/* (max-width:1280px) */
@media (max-width:1280px) {
  .container--xl {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--l {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--m {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--s {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--xs {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--2xs {
    margin-left: calc((100vw - 560px)/-2);
    margin-right: calc((100vw - 560px)/-2);
  }
  .container--3xs {
    margin-left: calc((100vw - 400px)/-2);
    margin-right: calc((100vw - 400px)/-2);
  }
  .container--4xs {
    margin-left: calc((100vw - 300px)/-2);
    margin-right: calc((100vw - 300px)/-2);
  }
}

/* (min-width:1120px) and (max-width:1280px) and (max-height:896px) */
@media (min-width:1120px) and (max-width:1280px) and (max-height:896px) {
  .container--xl {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--l {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--m {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--s {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--xs {
    margin-left: calc((100vw - 960px)/-2);
    margin-right: calc((100vw - 960px)/-2);
  }
  .container--2xs {
    margin-left: calc((100vw - 480px)/-2);
    margin-right: calc((100vw - 480px)/-2);
  }
  .container--3xs {
    margin-left: calc((100vw - 360px)/-2);
    margin-right: calc((100vw - 360px)/-2);
  }
  .container--4xs {
    margin-left: calc((100vw - 300px)/-2);
    margin-right: calc((100vw - 300px)/-2);
  }
}

/* (max-width:1120px) */
@media (max-width:1120px) {
  .container--xl,
  .container--l,
  .container--m,
  .container--s,
  .container--xs,
  .container--2xs,
  .container--3xs,
  .container--4xs {
    margin-left: calc((100vw - 800px)/-2);
    margin-right: calc((100vw - 800px)/-2);
  }
}

/* (max-width:960px) => all are 83vw */
@media (max-width:960px) {
  .container--xl,
  .container--l,
  .container--m,
  .container--s,
  .container--xs,
  .container--2xs,
  .container--3xs,
  .container--4xs {
    margin-left: -8.5vw;
    margin-right: -8.5vw;
  }
}


/* (max-width:960px) => all are 83vw */
@media (max-width:480px) {
  .container--xl,
  .container--l,
  .container--m,
  .container--s,
  .container--xs,
  .container--2xs,
  .container--3xs,
  .container--4xs {
    margin-left: -7.5vw;
    margin-right: -7.5vw;
  }
}


/* (max-width:960px) => all are 83vw */
@media (max-width:360px) {
  .container--xl,
  .container--l,
  .container--m,
  .container--s,
  .container--xs,
  .container--2xs,
  .container--3xs,
  .container--4xs {
    margin-left: -6vw;
    margin-right: -6vw;
  }
}




/*--------------------------------------------------------------

## Display

--------------------------------------------------------------*/

.flex {
  display: flex;
}

.inline-flex {
  display: inline-flex;
}

.grid {
  display: grid;
}

.inline-grid {
  display: inline-grid;
}

.inline-block {
  display: inline-block;
}

.block {
  display: block;
}

.hidden {
  display: none;
}

.table {
  display: table;
}

.inline-table {
  display: inline-table;
}

.table-cell {
  display: table-cell;
}

/*--------------------------------------------------------------

## Flex

--------------------------------------------------------------*/

.flex-row {
  flex-direction: row;
}

.flex-col {
  flex-direction: column;
}

.flex-row-reverse {
  flex-direction: row-reverse;
}

.flex-col-reverse {
  flex-direction: column-reverse;
}

.flex-wrap {
  flex-wrap: wrap;
}

.flex-wrap-reverse {
  flex-wrap: wrap-reverse;
}

.flex-nowrap {
  flex-wrap: none;
}

.grow-0 {
  flex-grow: 0;
}

.shrink-0 {
  flex-shrink: 0;
}

.grow-1 {
  flex-grow: 1;
}

.grow-2 {
  flex-grow: 2;
}

.grow-3 {
  flex-grow: 3;
}

.grow-4 {
  flex-grow: 4;
}

.grow-5 {
  flex-grow: 5;
}


.grow-6 {
  flex-grow: 6;
}

.grow-7 {
  flex-grow: 7;
}

.grow-8 {
  flex-grow: 8;
}

.grow-9 {
  flex-grow: 9;
}

.grow-10 {
  flex-grow: 10;
}

.grow-11 {
  flex-grow: 11;
}

.grow-12 {
  flex-grow: 12;
}

.shrink-1 {
  flex-shrink: 1;
}

/*--------------------------------------------------------------

## Order

--------------------------------------------------------------*/

.order-0 {
  order: 0;
}

.order-1 {
  order: 1;
}

.order-2 {
  order: 2;
}

.order-3 {
  order: 3;
}

.order-4 {
  order: 4;
}

.order-5 {
  order: 5;
}

.order-6 {
  order: 6;
}

.order-7 {
  order: 7;
}

.order-8 {
  order: 8;
}

.order-9 {
  order: 9;
}

.order-10 {
  order: 10;
}

.order-11 {
  order: 11;
}

.order-12 {
  order: 12;
}

.order-first {
  order: -9999;
}

.order-last {
  order: 9999;
}


/*--------------------------------------------------------------

## Place

--------------------------------------------------------------*/

.place-center {
  justify-content:center;
  align-items:center;
}



/*--------------------------------------------------------------

## Justify content

--------------------------------------------------------------*/

.justify-normal {
  justify-content: normal;
}

.justify-center {
  justify-content: center;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.justify-between {
  justify-content: space-between;
}

.justify-around {
  justify-content: space-around;
}

.justify-evenly {
  justify-content: space-evenly;
}

.justify-stretch {
  justify-content: stretch;
}

/*--------------------------------------------------------------

## Align content

--------------------------------------------------------------*/

.content-normal {
  align-content: normal;
}

.content-center {
  align-content: center;
}

.content-start {
  align-content: flex-start;
}

.content-end {
  align-content: flex-end;
}

.content-between {
  align-content: space-between;
}

.content-around {
  align-content: space-around;
}

.content-evenly {
  align-content: space-evenly;
}

.content-stretch {
  align-content: stretch;
}

/*--------------------------------------------------------------

## Align items

--------------------------------------------------------------*/

.items-center {
  align-items: center;
}

.items-start {
  align-items: flex-start;
}

.items-end {
  align-items: flex-end;
}

.items-baseline {
  align-items: baseline;
}

.items-stretch {
  align-items: stretch;
}

/*--------------------------------------------------------------

## Align self

--------------------------------------------------------------*/

.self-center {
  align-self: center;
}

.self-start {
  align-self: flex-start;
}

.self-end {
  align-self: flex-end;
}

.self-baseline {
  align-self: baseline;
}

.self-stretch {
  align-self: stretch;
}

/*--------------------------------------------------------------

## Grid

--------------------------------------------------------------*/

.col-1 {
  columns: 1;
}

.col-2 {
  columns: 2;
}

.col-3 {
  columns: 3;
}

.col-4 {
  columns: 4;
}

.col-5 {
  columns: 5;
}

.col-6 {
  columns: 6;
}

.col-7 {
  columns: 7;
}

.col-8 {
  columns: 8;
}

.col-9 {
  columns: 9;
}

.col-10 {
  columns: 10;
}

.col-11 {
  columns: 11;
}

.col-12 {
  columns: 12;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.grid-cols-2 {
  grid-template-columns: repeat(2, minmax(0, 1fr));
}

.grid-cols-3 {
  grid-template-columns: repeat(3, minmax(0, 1fr));
}

.grid-cols-4 {
  grid-template-columns: repeat(4, minmax(0, 1fr));
}

.grid-cols-5 {
  grid-template-columns: repeat(5, minmax(0, 1fr));
}

.grid-cols-6 {
  grid-template-columns: repeat(6, minmax(0, 1fr));
}

.grid-cols-7 {
  grid-template-columns: repeat(7, minmax(0, 1fr));
}

.grid-cols-8 {
  grid-template-columns: repeat(8, minmax(0, 1fr));
}

.grid-cols-9 {
  grid-template-columns: repeat(9, minmax(0, 1fr));
}

.grid-cols-10 {
  grid-template-columns: repeat(10, minmax(0, 1fr));
}

.grid-cols-11 {
  grid-template-columns: repeat(11, minmax(0, 1fr));
}

.grid-cols-12 {
  grid-template-columns: repeat(12, minmax(0, 1fr));
}

.grid-cols-none {
  grid-template-columns: none;
}

.col-auto {
  grid-column: auto;
}

.col-span-1 {
  grid-column: span 1 / span 1;
}

.col-span-2 {
  grid-column: span 2 / span 2;
}

.col-span-3 {
  grid-column: span 3 / span 3;
}

.col-span-4 {
  grid-column: span 4 / span 4;
}

.col-span-5 {
  grid-column: span 5 / span 5;
}

.col-span-6 {
  grid-column: span 6 / span 6;
}

.col-span-7 {
  grid-column: span 7 / span 7;
}

.col-span-8 {
  grid-column: span 8 / span 8;
}

.col-span-9 {
  grid-column: span 9 / span 9;
}

.col-span-10 {
  grid-column: span 10 / span 10;
}

.col-span-11 {
  grid-column: span 11 / span 11;
}

.col-span-12 {
  grid-column: span 12 / span 12;
}

.col-span-full {
  grid-column: 1 / -1;
}

.col-start-1 {
  grid-column-start: 1;
}

.col-start-2 {
  grid-column-start: 2;
}

.col-start-3 {
  grid-column-start: 3;
}

.col-start-4 {
  grid-column-start: 4;
}

.col-start-5 {
  grid-column-start: 5;
}

.col-start-6 {
  grid-column-start: 6;
}

.col-start-7 {
  grid-column-start: 7;
}

.col-start-8 {
  grid-column-start: 8;
}

.col-start-9 {
  grid-column-start: 9;
}

.col-start-10 {
  grid-column-start: 10;
}

.col-start-11 {
  grid-column-start: 11;
}

.col-start-12 {
  grid-column-start: 12;
}

.col-start-13 {
  grid-column-start: 13;
}

.col-start-auto {
  grid-column-start: auto;
}

.col-end-1 {
  grid-column-end: 1;
}

.col-end-2 {
  grid-column-end: 2;
}

.col-end-3 {
  grid-column-end: 3;
}

.col-end-4 {
  grid-column-end: 4;
}

.col-end-5 {
  grid-column-end: 5;
}

.col-end-6 {
  grid-column-end: 6;
}

.col-end-7 {
  grid-column-end: 7;
}

.col-end-8 {
  grid-column-end: 8;
}

.col-end-9 {
  grid-column-end: 9;
}

.col-end-10 {
  grid-column-end: 10;
}

.col-end-11 {
  grid-column-end: 11;
}

.col-end-12 {
  grid-column-end: 12;
}

.col-end-13 {
  grid-column-end: 13;
}

.col-end-auto {
  grid-column-end: auto;
}

.row-auto {
  grid-row: auto;
}

.row-span-1 {
  grid-row: span 1 / span 1;
}

.row-span-2 {
  grid-row: span 2 / span 2;
}

.row-span-3 {
  grid-row: span 3 / span 3;
}

.row-span-4 {
  grid-row: span 4 / span 4;
}

.row-span-5 {
  grid-row: span 5 / span 5;
}

.row-span-6 {
  grid-row: span 6 / span 6;
}

.row-span-7 {
  grid-row: span 7 / span 7;
}

.row-span-8 {
  grid-row: span 8 / span 8;
}

.row-span-9 {
  grid-row: span 9 / span 9;
}

.row-span-10 {
  grid-row: span 10 / span 10;
}

.row-span-11 {
  grid-row: span 11 / span 11;
}

.row-span-12 {
  grid-row: span 12 / span 12;
}

.row-span-full {
  grid-row: 1 / -1;
}

.row-start-1 {
  grid-row-start: 1;
}

.row-start-2 {
  grid-row-start: 2;
}

.row-start-3 {
  grid-row-start: 3;
}

.row-start-4 {
  grid-row-start: 4;
}

.row-start-5 {
  grid-row-start: 5;
}

.row-start-6 {
  grid-row-start: 6;
}

.row-start-7 {
  grid-row-start: 7;
}

.row-start-8 {
  grid-row-start: 8;
}

.row-start-9 {
  grid-row-start: 9;
}

.row-start-10 {
  grid-row-start: 10;
}

.row-start-11 {
  grid-row-start: 11;
}

.row-start-12 {
  grid-row-start: 12;
}

.row-start-13 {
  grid-row-start: 13;
}

.row-start-auto {
  grid-row-start: auto;
}

.row-end-1 {
  grid-row-end: 1;
}

.row-end-2 {
  grid-row-end: 2;
}

.row-end-3 {
  grid-row-end: 3;
}

.row-end-4 {
  grid-row-end: 4;
}

.row-end-5 {
  grid-row-end: 5;
}

.row-end-6 {
  grid-row-end: 6;
}

.row-end-7 {
  grid-row-end: 7;
}

.row-end-8 {
  grid-row-end: 8;
}

.row-end-9 {
  grid-row-end: 9;
}

.row-end-10 {
  grid-row-end: 10;
}

.row-end-11 {
  grid-row-end: 11;
}

.row-end-12 {
  grid-row-end: 12;
}

.row-end-13 {
  grid-row-end: 13;
}

.row-end-auto {
  grid-row-end: auto;
}

/*--------------------------------------------------------------

## Columns

--------------------------------------------------------------*/

.col-1 {
  columns: 1;
}

.col-2 {
  columns: 2;
}

.col-3 {
  columns: 3;
}

.col-4 {
  columns: 4;
}

.col-5 {
  columns: 5;
}

.col-6 {
  columns: 6;
}

.col-7 {
  columns: 7;
}

.col-8 {
  columns: 8;
}

.col-9 {
  columns: 9;
}

.col-10 {
  columns: 10;
}

.col-11 {
  columns: 11;
}

.col-12 {
  columns: 12;
}

/*--------------------------------------------------------------

## Gap

--------------------------------------------------------------*/

.gap-0 {
  gap: 0;
} 

.gap-1 {
  gap: 1px;
}

.gap-2 {
  gap: 2px;
}

.gap-4 {
  gap: 4px;
}

.gap-6 {
  gap: 6px;
}


.gap-7 {
  gap: 7px;
}


.gap-8 {
  gap: calc((0.5rem * var(--responsive-70)) * (var(--gap-8, 1)));
}

.gap-10 {
  gap: calc((0.625rem * var(--responsive-70)) * (var(--gap-10, 1)));
}

.gap-12 {
  gap: calc((0.75rem * var(--responsive-70)) * (var(--gap-12, 1)));
}

.gap-14 {
  gap: calc((0.875rem * var(--responsive-70)) * (var(--gap-14, 1)));
}

.gap-16 {
  gap: calc((1rem * var(--responsive-70)) * (var(--gap-16, 1)));
}

.gap-18 {
  gap: calc((1.125rem * var(--responsive-70)) * (var(--gap-18, 1)));
}

.gap-20 {
  gap: calc((1.25rem * var(--responsive-70)) * (var(--gap-20, 1)));
}

.gap-22 {
  gap: calc((1.375rem * var(--responsive-70)) * (var(--gap-22, 1)));
}

.gap-24 {
  gap: calc((1.5rem * var(--responsive-70)) * (var(--gap-24, 1)));
}

.gap-28 {
  gap: calc((1.75rem * var(--responsive-70)) * (var(--gap-28, 1)));
}

.gap-32 {
  gap: calc((2rem * var(--responsive-70)) * (var(--gap-32, 1)));
}

.gap-36 {
  gap: calc((2.25rem * var(--responsive-70)) * (var(--gap-36, 1)));
}

.gap-40 {
  gap: calc((2.5rem * var(--responsive-60)) * (var(--gap-40, 1)));
}

.gap-44 {
  gap: calc((2.75rem * var(--responsive-60)) * (var(--gap-44, 1)));
}

.gap-48 {
  gap: calc((3rem * var(--responsive-60)) * (var(--gap-48, 1)));
}

.gap-52 {
  gap: calc((3.25rem * var(--responsive-60)) * (var(--gap-52, 1)));
}

.gap-56 {
  gap: calc((3.5rem * var(--responsive-60)) * (var(--gap-56, 1)));
}

.gap-60 {
  gap: calc((3.75rem * var(--responsive-50)) * (var(--gap-60, 1)));
}

.gap-64 {
  gap: calc((4rem * var(--responsive-50)) * (var(--gap-64, 1)));
}

.gap-68 {
  gap: calc((4.25rem * var(--responsive-50)) * (var(--gap-68, 1)));
}

.gap-72 {
  gap: calc((4.5rem * var(--responsive-50)) * (var(--gap-72, 1)));
}

.gap-76 {
  gap: calc((4.75rem * var(--responsive-50)) * (var(--gap-76, 1)));
}

.gap-80 {
  gap: calc((5rem * var(--responsive-40)) * (var(--gap-80, 1)));
}

.gap-84 {
  gap: calc((5.25rem * var(--responsive-40)) * (var(--gap-84, 1)));
}

.gap-88 {
  gap: calc((5.5rem * var(--responsive-40)) * (var(--gap-88, 1)));
}

.gap-92 {
  gap: calc((5.75rem * var(--responsive-40)) * (var(--gap-92, 1)));
}

.gap-96 {
  gap: calc((6rem * var(--responsive-40)) * (var(--gap-96, 1)));
}

.gap-100 {
  gap: calc((6.25rem * var(--responsive-30)) * (var(--gap-100, 1)));
}

.gap-120 {
  gap: calc((7.5rem * var(--responsive-30)) * (var(--gap-120, 1)));
}

.gap-140 {
  gap: calc((8.75rem * var(--responsive-30)) * (var(--gap-140, 1)));
}

.gap-160 {
  gap: calc((10rem * var(--responsive-30)) * (var(--gap-160, 1)));
}

.gap-180 {
  gap: calc((11.25rem * var(--responsive-30)) * (var(--gap-180, 1)));
}

.gap-200 {
  gap: calc((12.5rem * var(--responsive-30)) * (var(--gap-200, 1)));
}

.gap-220 {
  gap: calc((13.75rem * var(--responsive-70)) * (var(--gap-220, 1)));
}

.gap-240 {
  gap: calc((15rem * var(--responsive-70)) * (var(--gap-240, 1)));
}

.gap-260 {
  gap: calc((16.25rem * var(--responsive-70)) * (var(--gap-260, 1)));
}

.gap-280 {
  gap: calc((17.5rem * var(--responsive-70)) * (var(--gap-280, 1)));
}

.gap-300 {
  gap: calc((18.75rem * var(--responsive-70)) * (var(--gap-300, 1)));
}

.gap-320 {
  gap: calc((20rem * var(--responsive-70)) * (var(--gap-320, 1)));
}

.gap-340 {
  gap: calc((21.25rem * var(--responsive-70)) * (var(--gap-340, 1)));
}

.gap-360 {
  gap: calc((22.5rem * var(--responsive-70)) * (var(--gap-360, 1)));
}

.gap-380 {
  gap: calc((23.75rem * var(--responsive-70)) * (var(--gap-380, 1)));
}

.gap-400 {
  gap: calc((25rem * var(--responsive-70)) * (var(--gap-400, 1)));
}

/*--------------------------------------------------------------

## Gap X (Colonna)

--------------------------------------------------------------*/

.gap-x-0 {
  column-gap: 0;
}

.gap-x-px {
  column-gap: 1px;
}

.gap-x-4 {
  column-gap: calc((0.25rem * var(--responsive-70)) * (var(--gap-x-4, 1)));
}

.gap-x-6 {
  column-gap: calc((0.375rem * var(--responsive-70)) * (var(--gap-x-6, 1)));
}

.gap-x-8 {
  column-gap: calc((0.5rem * var(--responsive-70)) * (var(--gap-x-8, 1)));
}

.gap-x-10 {
  column-gap: calc((0.625rem * var(--responsive-70)) * (var(--gap-x-10, 1)));
}

.gap-x-12 {
  column-gap: calc((0.75rem * var(--responsive-70)) * (var(--gap-x-12, 1)));
}

.gap-x-14 {
  column-gap: calc((0.875rem * var(--responsive-70)) * (var(--gap-x-14, 1)));
}

.gap-x-16 {
  column-gap: calc((1rem * var(--responsive-70)) * (var(--gap-x-16, 1)));
}

.gap-x-18 {
  column-gap: calc((1.125rem * var(--responsive-70)) * (var(--gap-x-18, 1)));
}

.gap-x-20 {
  column-gap: calc((1.25rem * var(--responsive-70)) * (var(--gap-x-20, 1)));
}

.gap-x-22 {
  column-gap: calc((1.375rem * var(--responsive-70)) * (var(--gap-x-22, 1)));
}

.gap-x-24 {
  column-gap: calc((1.5rem * var(--responsive-70)) * (var(--gap-x-24, 1)));
}

.gap-x-28 {
  column-gap: calc((1.75rem * var(--responsive-70)) * (var(--gap-x-28, 1)));
}

.gap-x-32 {
  column-gap: calc((2rem * var(--responsive-70)) * (var(--gap-x-32, 1)));
}

.gap-x-36 {
  column-gap: calc((2.25rem * var(--responsive-70)) * (var(--gap-x-36, 1)));
}

.gap-x-40 {
  column-gap: calc((2.5rem * var(--responsive-60)) * (var(--gap-x-40, 1)));
}

.gap-x-44 {
  column-gap: calc((2.75rem * var(--responsive-60)) * (var(--gap-x-44, 1)));
}

.gap-x-48 {
  column-gap: calc((3rem * var(--responsive-60)) * (var(--gap-x-48, 1)));
}

.gap-x-52 {
  column-gap: calc((3.25rem * var(--responsive-60)) * (var(--gap-x-52, 1)));
}

.gap-x-56 {
  column-gap: calc((3.5rem * var(--responsive-60)) * (var(--gap-x-56, 1)));
}

.gap-x-60 {
  column-gap: calc((3.75rem * var(--responsive-50)) * (var(--gap-x-60, 1)));
}

.gap-x-64 {
  column-gap: calc((4rem * var(--responsive-50)) * (var(--gap-x-64, 1)));
}

.gap-x-68 {
  column-gap: calc((4.25rem * var(--responsive-50)) * (var(--gap-x-68, 1)));
}

.gap-x-72 {
  column-gap: calc((4.5rem * var(--responsive-50)) * (var(--gap-x-72, 1)));
}

.gap-x-76 {
  column-gap: calc((4.75rem * var(--responsive-50)) * (var(--gap-x-76, 1)));
}

.gap-x-80 {
  column-gap: calc((5rem * var(--responsive-40)) * (var(--gap-x-80, 1)));
}

.gap-x-84 {
  column-gap: calc((5.25rem * var(--responsive-40)) * (var(--gap-x-84, 1)));
}

.gap-x-88 {
  column-gap: calc((5.5rem * var(--responsive-40)) * (var(--gap-x-88, 1)));
}

.gap-x-92 {
  column-gap: calc((5.75rem * var(--responsive-40)) * (var(--gap-x-92, 1)));
}

.gap-x-96 {
  column-gap: calc((6rem * var(--responsive-40)) * (var(--gap-x-96, 1)));
}

.gap-x-100 {
  column-gap: calc((6.25rem * var(--responsive-30)) * (var(--gap-x-100, 1)));
}

.gap-x-120 {
  column-gap: calc((7.5rem * var(--responsive-30)) * (var(--gap-x-120, 1)));
}

.gap-x-140 {
  column-gap: calc((8.75rem * var(--responsive-30)) * (var(--gap-x-140, 1)));
}

.gap-x-160 {
  column-gap: calc((10rem * var(--responsive-30)) * (var(--gap-x-160, 1)));
}

.gap-x-180 {
  column-gap: calc((11.25rem * var(--responsive-30)) * (var(--gap-x-180, 1)));
}

.gap-x-200 {
  column-gap: calc((12.5rem * var(--responsive-30)) * (var(--gap-x-200, 1)));
}

.gap-x-220 {
  column-gap: calc((13.75rem * var(--responsive-70)) * (var(--gap-x-220, 1)));
}

.gap-x-240 {
  column-gap: calc((15rem * var(--responsive-70)) * (var(--gap-x-240, 1)));
}

.gap-x-260 {
  column-gap: calc((16.25rem * var(--responsive-70)) * (var(--gap-x-260, 1)));
}

.gap-x-280 {
  column-gap: calc((17.5rem * var(--responsive-70)) * (var(--gap-x-280, 1)));
}

.gap-x-300 {
  column-gap: calc((18.75rem * var(--responsive-70)) * (var(--gap-x-300, 1)));
}

.gap-x-320 {
  column-gap: calc((20rem * var(--responsive-70)) * (var(--gap-x-320, 1)));
}

.gap-x-340 {
  column-gap: calc((21.25rem * var(--responsive-70)) * (var(--gap-x-340, 1)));
}

.gap-x-360 {
  column-gap: calc((22.5rem * var(--responsive-70)) * (var(--gap-x-360, 1)));
}

.gap-x-380 {
  column-gap: calc((23.75rem * var(--responsive-70)) * (var(--gap-x-380, 1)));
}

.gap-x-400 {
  column-gap: calc((25rem * var(--responsive-70)) * (var(--gap-x-400, 1)));
}

/*--------------------------------------------------------------

## Gap Y (Riga)

--------------------------------------------------------------*/

.gap-y-0 {
  row-gap: 0;
}

.gap-y-px {
  row-gap: 1px;
}

.gap-y-4 {
  row-gap: calc((0.25rem * var(--responsive-70)) * (var(--gap-y-4, 1)));
}

.gap-y-6 {
  row-gap: calc((0.375rem * var(--responsive-70)) * (var(--gap-y-6, 1)));
}

.gap-y-8 {
  row-gap: calc((0.5rem * var(--responsive-70)) * (var(--gap-y-8, 1)));
}

.gap-y-10 {
  row-gap: calc((0.625rem * var(--responsive-70)) * (var(--gap-y-10, 1)));
}

.gap-y-12 {
  row-gap: calc((0.75rem * var(--responsive-70)) * (var(--gap-y-12, 1)));
}

.gap-y-14 {
  row-gap: calc((0.875rem * var(--responsive-70)) * (var(--gap-y-14, 1)));
}

.gap-y-16 {
  row-gap: calc((1rem * var(--responsive-70)) * (var(--gap-y-16, 1)));
}

.gap-y-18 {
  row-gap: calc((1.125rem * var(--responsive-70)) * (var(--gap-y-18, 1)));
}

.gap-y-20 {
  row-gap: calc((1.25rem * var(--responsive-70)) * (var(--gap-y-20, 1)));
}

.gap-y-22 {
  row-gap: calc((1.375rem * var(--responsive-70)) * (var(--gap-y-22, 1)));
}

.gap-y-24 {
  row-gap: calc((1.5rem * var(--responsive-70)) * (var(--gap-y-24, 1)));
}

.gap-y-28 {
  row-gap: calc((1.75rem * var(--responsive-70)) * (var(--gap-y-28, 1)));
}

.gap-y-32 {
  row-gap: calc((2rem * var(--responsive-70)) * (var(--gap-y-32, 1)));
}

.gap-y-36 {
  row-gap: calc((2.25rem * var(--responsive-70)) * (var(--gap-y-36, 1)));
}

.gap-y-40 {
  row-gap: calc((2.5rem * var(--responsive-60)) * (var(--gap-y-40, 1)));
}

.gap-y-44 {
  row-gap: calc((2.75rem * var(--responsive-60)) * (var(--gap-y-44, 1)));
}

.gap-y-48 {
  row-gap: calc((3rem * var(--responsive-60)) * (var(--gap-y-48, 1)));
}

.gap-y-52 {
  row-gap: calc((3.25rem * var(--responsive-60)) * (var(--gap-y-52, 1)));
}

.gap-y-56 {
  row-gap: calc((3.5rem * var(--responsive-60)) * (var(--gap-y-56, 1)));
}

.gap-y-60 {
  row-gap: calc((3.75rem * var(--responsive-50)) * (var(--gap-y-60, 1)));
}

.gap-y-64 {
  row-gap: calc((4rem * var(--responsive-50)) * (var(--gap-y-64, 1)));
}

.gap-y-68 {
  row-gap: calc((4.25rem * var(--responsive-50)) * (var(--gap-y-68, 1)));
}

.gap-y-72 {
  row-gap: calc((4.5rem * var(--responsive-50)) * (var(--gap-y-72, 1)));
}

.gap-y-76 {
  row-gap: calc((4.75rem * var(--responsive-50)) * (var(--gap-y-76, 1)));
}

.gap-y-80 {
  row-gap: calc((5rem * var(--responsive-40)) * (var(--gap-y-80, 1)));
}

.gap-y-84 {
  row-gap: calc((5.25rem * var(--responsive-40)) * (var(--gap-y-84, 1)));
}

.gap-y-88 {
  row-gap: calc((5.5rem * var(--responsive-40)) * (var(--gap-y-88, 1)));
}

.gap-y-92 {
  row-gap: calc((5.75rem * var(--responsive-40)) * (var(--gap-y-92, 1)));
}

.gap-y-96 {
  row-gap: calc((6rem * var(--responsive-40)) * (var(--gap-y-96, 1)));
}

.gap-y-100 {
  row-gap: calc((6.25rem * var(--responsive-30)) * (var(--gap-y-100, 1)));
}

.gap-y-120 {
  row-gap: calc((7.5rem * var(--responsive-30)) * (var(--gap-y-120, 1)));
}

.gap-y-140 {
  row-gap: calc((8.75rem * var(--responsive-30)) * (var(--gap-y-140, 1)));
}

.gap-y-160 {
  row-gap: calc((10rem * var(--responsive-30)) * (var(--gap-y-160, 1)));
}

.gap-y-180 {
  row-gap: calc((11.25rem * var(--responsive-30)) * (var(--gap-y-180, 1)));
}

.gap-y-200 {
  row-gap: calc((12.5rem * var(--responsive-30)) * (var(--gap-y-200, 1)));
}

.gap-y-220 {
  row-gap: calc((13.75rem * var(--responsive-70)) * (var(--gap-y-220, 1)));
}

.gap-y-240 {
  row-gap: calc((15rem * var(--responsive-70)) * (var(--gap-y-240, 1)));
}

.gap-y-260 {
  row-gap: calc((16.25rem * var(--responsive-70)) * (var(--gap-y-260, 1)));
}

.gap-y-280 {
  row-gap: calc((17.5rem * var(--responsive-70)) * (var(--gap-y-280, 1)));
}

.gap-y-300 {
  row-gap: calc((18.75rem * var(--responsive-70)) * (var(--gap-y-300, 1)));
}

.gap-y-320 {
  row-gap: calc((20rem * var(--responsive-70)) * (var(--gap-y-320, 1)));
}

.gap-y-340 {
  row-gap: calc((21.25rem * var(--responsive-70)) * (var(--gap-y-340, 1)));
}

.gap-y-360 {
  row-gap: calc((22.5rem * var(--responsive-70)) * (var(--gap-y-360, 1)));
}

.gap-y-380 {
  row-gap: calc((23.75rem * var(--responsive-70)) * (var(--gap-y-380, 1)));
}

.gap-y-400 {
  row-gap: calc((25rem * var(--responsive-70)) * (var(--gap-y-400, 1)));
}

/* Gap */
.s\:gap-4 { gap: 0.25rem; }
.s\:gap-6 { gap: 0.375rem; }
.s\:gap-8 { gap: 0.5rem; }
.s\:gap-10 { gap: 0.625rem; }
.s\:gap-12 { gap: 0.75rem; }
.s\:gap-14 { gap: 0.875rem; }
.s\:gap-16 { gap: 1rem; }
.s\:gap-18 { gap: 1.125rem; }
.s\:gap-20 { gap: 1.25rem; }
.s\:gap-22 { gap: 1.375rem; }
.s\:gap-24 { gap: 1.5rem; }
.s\:gap-28 { gap: 1.75rem; }
.s\:gap-32 { gap: 2rem; }
.s\:gap-36 { gap: 2.25rem; }
.s\:gap-40 { gap: 2.5rem; }
.s\:gap-44 { gap: 2.75rem; }
.s\:gap-48 { gap: 3rem; }
.s\:gap-52 { gap: 3.25rem; }
.s\:gap-56 { gap: 3.5rem; }
.s\:gap-60 { gap: 3.75rem; }
.s\:gap-64 { gap: 4rem; }
.s\:gap-68 { gap: 4.25rem; }
.s\:gap-72 { gap: 4.5rem; }
.s\:gap-76 { gap: 4.75rem; }
.s\:gap-80 { gap: 5rem; }
.s\:gap-84 { gap: 5.25rem; }
.s\:gap-88 { gap: 5.5rem; }
.s\:gap-92 { gap: 5.75rem; }
.s\:gap-96 { gap: 6rem; }
.s\:gap-100 { gap: 6.25rem; }
.s\:gap-120 { gap: 7.5rem; }
.s\:gap-140 { gap: 8.75rem; }
.s\:gap-160 { gap: 10rem; }
.s\:gap-180 { gap: 11.25rem; }
.s\:gap-200 { gap: 12.5rem; }
.s\:gap-220 { gap: 13.75rem; }
.s\:gap-240 { gap: 15rem; }
.s\:gap-260 { gap: 16.25rem; }
.s\:gap-280 { gap: 17.5rem; }
.s\:gap-300 { gap: 18.75rem; }
.s\:gap-320 { gap: 20rem; }
.s\:gap-340 { gap: 21.25rem; }
.s\:gap-360 { gap: 22.5rem; }
.s\:gap-380 { gap: 23.75rem; }
.s\:gap-400 { gap: 25rem; }

/* Gap X (Column) */
.s\:gap-x-0 { column-gap: 0; }
.s\:gap-x-px { column-gap: 1px; }
.s\:gap-x-4 { column-gap: 0.25rem; }
.s\:gap-x-6 { column-gap: 0.375rem; }
.s\:gap-x-8 { column-gap: 0.5rem; }
.s\:gap-x-10 { column-gap: 0.625rem; }
.s\:gap-x-12 { column-gap: 0.75rem; }
.s\:gap-x-14 { column-gap: 0.875rem; }
.s\:gap-x-16 { column-gap: 1rem; }
.s\:gap-x-18 { column-gap: 1.125rem; }
.s\:gap-x-20 { column-gap: 1.25rem; }
.s\:gap-x-22 { column-gap: 1.375rem; }
.s\:gap-x-24 { column-gap: 1.5rem; }
.s\:gap-x-28 { column-gap: 1.75rem; }
.s\:gap-x-32 { column-gap: 2rem; }
.s\:gap-x-36 { column-gap: 2.25rem; }
.s\:gap-x-40 { column-gap: 2.5rem; }
.s\:gap-x-44 { column-gap: 2.75rem; }
.s\:gap-x-48 { column-gap: 3rem; }
.s\:gap-x-52 { column-gap: 3.25rem; }
.s\:gap-x-56 { column-gap: 3.5rem; }
.s\:gap-x-60 { column-gap: 3.75rem; }
.s\:gap-x-64 { column-gap: 4rem; }
.s\:gap-x-68 { column-gap: 4.25rem; }
.s\:gap-x-72 { column-gap: 4.5rem; }
.s\:gap-x-76 { column-gap: 4.75rem; }
.s\:gap-x-80 { column-gap: 5rem; }
.s\:gap-x-84 { column-gap: 5.25rem; }
.s\:gap-x-88 { column-gap: 5.5rem; }
.s\:gap-x-92 { column-gap: 5.75rem; }
.s\:gap-x-96 { column-gap: 6rem; }
.s\:gap-x-100 { column-gap: 6.25rem; }
.s\:gap-x-120 { column-gap: 7.5rem; }
.s\:gap-x-140 { column-gap: 8.75rem; }
.s\:gap-x-160 { column-gap: 10rem; }
.s\:gap-x-180 { column-gap: 11.25rem; }
.s\:gap-x-200 { column-gap: 12.5rem; }
.s\:gap-x-220 { column-gap: 13.75rem; }
.s\:gap-x-240 { column-gap: 15rem; }
.s\:gap-x-260 { column-gap: 16.25rem; }
.s\:gap-x-280 { column-gap: 17.5rem; }
.s\:gap-x-300 { column-gap: 18.75rem; }
.s\:gap-x-320 { column-gap: 20rem; }
.s\:gap-x-340 { column-gap: 21.25rem; }
.s\:gap-x-360 { column-gap: 22.5rem; }
.s\:gap-x-380 { column-gap: 23.75rem; }
.s\:gap-x-400 { column-gap: 25rem; }

/* Gap Y (Row) */
.s\:gap-y-0 { row-gap: 0; }
.s\:gap-y-px { row-gap: 1px; }
.s\:gap-y-4 { row-gap: 0.25rem; }
.s\:gap-y-6 { row-gap: 0.375rem; }
.s\:gap-y-8 { row-gap: 0.5rem; }
.s\:gap-y-10 { row-gap: 0.625rem; }
.s\:gap-y-12 { row-gap: 0.75rem; }
.s\:gap-y-14 { row-gap: 0.875rem; }
.s\:gap-y-16 { row-gap: 1rem; }
.s\:gap-y-18 { row-gap: 1.125rem; }
.s\:gap-y-20 { row-gap: 1.25rem; }
.s\:gap-y-22 { row-gap: 1.375rem; }
.s\:gap-y-24 { row-gap: 1.5rem; }
.s\:gap-y-28 { row-gap: 1.75rem; }
.s\:gap-y-32 { row-gap: 2rem; }
.s\:gap-y-36 { row-gap: 2.25rem; }
.s\:gap-y-40 { row-gap: 2.5rem; }
.s\:gap-y-44 { row-gap: 2.75rem; }
.s\:gap-y-48 { row-gap: 3rem; }
.s\:gap-y-52 { row-gap: 3.25rem; }
.s\:gap-y-56 { row-gap: 3.5rem; }
.s\:gap-y-60 { row-gap: 3.75rem; }
.s\:gap-y-64 { row-gap: 4rem; }
.s\:gap-y-68 { row-gap: 4.25rem; }
.s\:gap-y-72 { row-gap: 4.5rem; }
.s\:gap-y-76 { row-gap: 4.75rem; }
.s\:gap-y-80 { row-gap: 5rem; }
.s\:gap-y-84 { row-gap: 5.25rem; }
.s\:gap-y-88 { row-gap: 5.5rem; }
.s\:gap-y-92 { row-gap: 5.75rem; }
.s\:gap-y-96 { row-gap: 6rem; }
.s\:gap-y-100 { row-gap: 6.25rem; }
.s\:gap-y-120 { row-gap: 7.5rem; }
.s\:gap-y-140 { row-gap: 8.75rem; }
.s\:gap-y-160 { row-gap: 10rem; }
.s\:gap-y-180 { row-gap: 11.25rem; }
.s\:gap-y-200 { row-gap: 12.5rem; }
.s\:gap-y-220 { row-gap: 13.75rem; }
.s\:gap-y-240 { row-gap: 15rem; }
.s\:gap-y-260 { row-gap: 16.25rem; }
.s\:gap-y-280 { row-gap: 17.5rem; }
.s\:gap-y-300 { row-gap: 18.75rem; }
.s\:gap-y-320 { row-gap: 20rem; }
.s\:gap-y-340 { row-gap: 21.25rem; }
.s\:gap-y-360 { row-gap: 22.5rem; }
.s\:gap-y-380 { row-gap: 23.75rem; }
.s\:gap-y-400 { row-gap: 25rem; }

/*--------------------------------------------------------------

## Hide

--------------------------------------------------------------*/

@media (max-width:1920px) {
  .hide-1920 {
    display: none;
  }
}

@media (max-width:1680px) {
  .hide-1680 {
    display: none;
  }
}

@media (max-width:1440px) {
  .hide-1440 {
    display: none;
  }
}

@media (max-width:1280px) {
  .hide-1280 {
    display: none;
  }
}

@media (max-width:1120px) {
  .hide-1120 {
    display: none;
  }
}

@media (max-width:960px) {
  .hide-960 {
    display: none;
  }
}

@media (max-width:640px) {
  .hide-640 {
    display: none;
  }
}

@media (max-width:480px) {
  .hide-480 {
    display: none;
  }
}

@media (max-width:360px) {
  .hide-360 {
    display: none;
  }
}

/*--------------------------------------------------------------

## Show

--------------------------------------------------------------*/

@media (max-width:1920px) {
  .show-1920 {
    display: block;
  }
}

@media (max-width:1680px) {
  .show-1680 {
    display: block;
  }
}

@media (max-width:1440px) {
  .show-1440 {
    display: block;
  }
}

@media (max-width:1280px) {
  .show-1280 {
    display: block;
  }
}

@media (max-width:1120px) {
  .show-1120 {
    display: block;
  }
}

@media (max-width:960px) {
  .show-960 {
    display: block;
  }
}

@media (max-width:640px) {
  .show-640 {
    display: block;
  }
}

@media (max-width:480px) {
  .show-480 {
    display: block;
  }
}

@media (max-width:360px) {
  .show-360 {
    display: block;
  }
}

@media (max-width:1920px) {
  .show-flex-1920 {
    display: flex;
  }
}

@media (max-width:1680px) {
  .show-flex-1680 {
    display: flex;
  }
}

@media (max-width:1440px) {
  .show-flex-1440 {
    display: flex;
  }
}

@media (max-width:1280px) {
  .show-flex-1280 {
    display: flex;
  }
}

@media (max-width:1120px) {
  .show-flex-1120 {
    display: flex;
  }
}

@media (max-width:960px) {
  .show-flex-960 {
    display: flex;
  }
}

@media (max-width:640px) {
  .show-flex-640 {
    display: flex;
  }
}

@media (max-width:480px) {
  .show-flex-480 {
    display: flex;
  }
}

@media (max-width:360px) {
  .show-flex-360 {
    display: flex;
  }
}

@media (max-width:1920px) {
  .show-grid-1920 {
    display: grid;
  }
}

@media (max-width:1680px) {
  .show-grid-1680 {
    display: grid;
  }
}

@media (max-width:1440px) {
  .show-grid-1440 {
    display: grid;
  }
}

@media (max-width:1280px) {
  .show-grid-1280 {
    display: grid;
  }
}

@media (max-width:1120px) {
  .show-grid-1120 {
    display: grid;
  }
}

@media (max-width:960px) {
  .show-grid-960 {
    display: grid;
  }
}

@media (max-width:640px) {
  .show-grid-640 {
    display: grid;
  }
}

@media (max-width:480px) {
  .show-grid-480 {
    display: grid;
  }
}

@media (max-width:360px) {
  .show-grid-360 {
    display: grid;
  }
}

@media (max-width:1920px) {
  .show-inline-1920 {
    display: inline;
  }
}

@media (max-width:1680px) {
  .show-inline-1680 {
    display: inline;
  }
}

@media (max-width:1440px) {
  .show-inline-1440 {
    display: inline;
  }
}

@media (max-width:1280px) {
  .show-inline-1280 {
    display: inline;
  }
}

@media (max-width:1120px) {
  .show-inline-1120 {
    display: inline;
  }
}

@media (max-width:960px) {
  .show-inline-960 {
    display: inline;
  }
}

@media (max-width:640px) {
  .show-inline-640 {
    display: inline;
  }
}

@media (max-width:480px) {
  .show-inline-480 {
    display: inline;
  }
}

@media (max-width:360px) {
  .show-inline-360 {
    display: inline;
  }
}

@media (max-width:1920px) {
  .show-inline-block-1920 {
    display: inline-block;
  }
}

@media (max-width:1680px) {
  .show-inline-block-1680 {
    display: inline-block;
  }
}

@media (max-width:1440px) {
  .show-inline-block-1440 {
    display: inline-block;
  }
}

@media (max-width:1280px) {
  .show-inline-block-1280 {
    display: inline-block;
  }
}

@media (max-width:1120px) {
  .show-inline-block-1120 {
    display: inline-block;
  }
}

@media (max-width:960px) {
  .show-inline-block-960 {
    display: inline-block;
  }
}

@media (max-width:640px) {
  .show-inline-block-640 {
    display: inline-block;
  }
}

@media (max-width:480px) {
  .show-inline-block-480 {
    display: inline-block;
  }
}

@media (max-width:360px) {
  .show-inline-block-360 {
    display: inline-block;
  }
}

/*--------------------------------------------------------------

## Position

--------------------------------------------------------------*/

.static {
  position: static;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.sticky {
  position: sticky;
}

.absolute-cover {
  position: absolute;
  inset: 0;
  width: 100%;
  height: 100%;
}

.absolute-center {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.absolute-center-x {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

.absolute-center-y {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
}

.fixed-center {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.fixed-center-x {
  position: fixed;
  left: 50%;
  transform: translateX(-50%);
}

.fixed-center-y {
  position: fixed;
  top: 50%;
  transform: translateY(-50%);
}

.fixed-cover {
  position: fixed;
  inset: 0;
  width: 100%;
  height: 100%;
}


/*--------------------------------------------------------------

## Inset

--------------------------------------------------------------*/

.inset-0 {
  inset: 0;
}

/*--------------------------------------------------------------

## Top

--------------------------------------------------------------*/

.top-auto {
  top: auto;
}

.top-10-100 {
  top: 10%;
}

.top-20-100 {
  top: 20%;
}

.top-30-100 {
  top: 30%;
}

.top-40-100 {
  top: 40%;
}

.top-50-100 {
  top: 50%;
}

.top-60-100 {
  top: 60%;
}

.top-70-100 {
  top: 70%;
}

.top-80-100 {
  top: 80%;
}

.top-90-100 {
  top: 90%;
}

.top-100-100 {
  top: 100%;
}

.top-0 {
  top: 0;
}

.top-1 {
  top: 1px;
}
.top-2 {
  top: 2px;
}

.top-4 {
  top: calc((0.25rem * var(--responsive-70)) * (var(--top-4, 1)));
}

.top-6 {
  top: calc((0.375rem * var(--responsive-70)) * (var(--top-6, 1)));
}

.top-8 {
  top: calc((0.5rem * var(--responsive-70)) * (var(--top-8, 1)));
}

.top-10 {
  top: calc((0.625rem * var(--responsive-70)) * (var(--top-10, 1)));
}

.top-12 {
  top: calc((0.75rem * var(--responsive-70)) * (var(--top-12, 1)));
}

.top-14 {
  top: calc((0.875rem * var(--responsive-70)) * (var(--top-14, 1)));
}

.top-16 {
  top: calc((1rem * var(--responsive-70)) * (var(--top-16, 1)));
}

.top-18 {
  top: calc((1.125rem * var(--responsive-70)) * (var(--top-18, 1)));
}

.top-20 {
  top: calc((1.25rem * var(--responsive-70)) * (var(--top-20, 1)));
}

.top-22 {
  top: calc((1.375rem * var(--responsive-70)) * (var(--top-22, 1)));
}

.top-24 {
  top: calc((1.5rem * var(--responsive-70)) * (var(--top-24, 1)));
}

.top-28 {
  top: calc((1.75rem * var(--responsive-70)) * (var(--top-28, 1)));
}

.top-32 {
  top: calc((2rem * var(--responsive-70)) * (var(--top-32, 1)));
}

.top-36 {
  top: calc((2.25rem * var(--responsive-70)) * (var(--top-36, 1)));
}

.top-40 {
  top: calc((2.5rem * var(--responsive-60)) * (var(--top-40, 1)));
}

.top-44 {
  top: calc((2.75rem * var(--responsive-60)) * (var(--top-44, 1)));
}

.top-48 {
  top: calc((3rem * var(--responsive-60)) * (var(--top-48, 1)));
}

.top-52 {
  top: calc((3.25rem * var(--responsive-60)) * (var(--top-52, 1)));
}

.top-56 {
  top: calc((3.5rem * var(--responsive-60)) * (var(--top-56, 1)));
}

.top-60 {
  top: calc((3.75rem * var(--responsive-50)) * (var(--top-60, 1)));
}

.top-64 {
  top: calc((4rem * var(--responsive-50)) * (var(--top-64, 1)));
}

.top-68 {
  top: calc((4.25rem * var(--responsive-50)) * (var(--top-68, 1)));
}

.top-72 {
  top: calc((4.5rem * var(--responsive-50)) * (var(--top-72, 1)));
}

.top-76 {
  top: calc((4.75rem * var(--responsive-50)) * (var(--top-76, 1)));
}

.top-80 {
  top: calc((5rem * var(--responsive-40)) * (var(--top-80, 1)));
}

.top-84 {
  top: calc((5.25rem * var(--responsive-40)) * (var(--top-84, 1)));
}

.top-88 {
  top: calc((5.5rem * var(--responsive-40)) * (var(--top-88, 1)));
}

.top-92 {
  top: calc((5.75rem * var(--responsive-40)) * (var(--top-92, 1)));
}

.top-96 {
  top: calc((6rem * var(--responsive-40)) * (var(--top-96, 1)));
}

.top-100 {
  top: calc((6.25rem * var(--responsive-30)) * (var(--top-100, 1)));
}

.top-120 {
  top: calc((7.5rem * var(--responsive-30)) * (var(--top-120, 1)));
}

.top-140 {
  top: calc((8.75rem * var(--responsive-30)) * (var(--top-140, 1)));
}

.top-160 {
  top: calc((10rem * var(--responsive-30)) * (var(--top-160, 1)));
}

.top-180 {
  top: calc((11.25rem * var(--responsive-30)) * (var(--top-180, 1)));
}

.top-200 {
  top: calc((12.5rem * var(--responsive-30)) * (var(--top-200, 1)));
}

.top-220 {
  top: calc((13.75rem * var(--responsive-70)) * (var(--top-220, 1)));
}

.top-240 {
  top: calc((15rem * var(--responsive-70)) * (var(--top-240, 1)));
}

.top-260 {
  top: calc((16.25rem * var(--responsive-70)) * (var(--top-260, 1)));
}

.top-280 {
  top: calc((17.5rem * var(--responsive-70)) * (var(--top-280, 1)));
}

.top-300 {
  top: calc((18.75rem * var(--responsive-70)) * (var(--top-300, 1)));
}

.top-320 {
  top: calc((20rem * var(--responsive-70)) * (var(--top-320, 1)));
}

.top-340 {
  top: calc((21.25rem * var(--responsive-70)) * (var(--top-340, 1)));
}

.top-360 {
  top: calc((22.5rem * var(--responsive-70)) * (var(--top-360, 1)));
}

.top-380 {
  top: calc((23.75rem * var(--responsive-70)) * (var(--top-380, 1)));
}

.top-400 {
  top: calc((25rem * var(--responsive-70)) * (var(--top-400, 1)));
}

.top-420 {
  top: calc((26.25rem * var(--responsive-70)) * (var(--top-420, 1)));
}

.top-440 {
  top: calc((27.5rem * var(--responsive-70)) * (var(--top-440, 1)));
}

.top-460 {
  top: calc((28.75rem * var(--responsive-70)) * (var(--top-460, 1)));
}

.top-480 {
  top: calc((30rem * var(--responsive-70)) * (var(--top-480, 1)));
}

.top-500 {
  top: calc((31.25rem * var(--responsive-70)) * (var(--top-500, 1)));
}

.top-520 {
  top: calc((32.5rem * var(--responsive-70)) * (var(--top-520, 1)));
}

.top-540 {
  top: calc((33.75rem * var(--responsive-70)) * (var(--top-540, 1)));
}

.top-560 {
  top: calc((35rem * var(--responsive-70)) * (var(--top-560, 1)));
}

.top-580 {
  top: calc((36.25rem * var(--responsive-70)) * (var(--top-580, 1)));
}

.top-600 {
  top: calc((37.5rem * var(--responsive-70)) * (var(--top-600, 1)));
}

.top-620 {
  top: calc((38.75rem * var(--responsive-70)) * (var(--top-620, 1)));
}

.top-640 {
  top: calc((40rem * var(--responsive-70)) * (var(--top-640, 1)));
}

.top-660 {
  top: calc((41.25rem * var(--responsive-70)) * (var(--top-660, 1)));
}

.top-680 {
  top: calc((42.5rem * var(--responsive-70)) * (var(--top-680, 1)));
}

.top-700 {
  top: calc((43.75rem * var(--responsive-70)) * (var(--top-700, 1)));
}

.top-720 {
  top: calc((45rem * var(--responsive-70)) * (var(--top-720, 1)));
}

.top-740 {
  top: calc((46.25rem * var(--responsive-70)) * (var(--top-740, 1)));
}

.top-760 {
  top: calc((47.5rem * var(--responsive-70)) * (var(--top-760, 1)));
}

.top-780 {
  top: calc((48.75rem * var(--responsive-70)) * (var(--top-780, 1)));
}

.top-800 {
  top: calc((50rem * var(--responsive-70)) * (var(--top-800, 1)));
}

.top-900 {
  top: calc((56.25rem * var(--responsive-70)) * (var(--top-900, 1)));
}

.top-1000 {
  top: calc((62.5rem * var(--responsive-70)) * (var(--top-1000, 1)));
}

.top-1100 {
  top: calc((68.75rem * var(--responsive-70)) * (var(--top-1100, 1)));
}

.top-1200 {
  top: calc((75rem * var(--responsive-70)) * (var(--top-1200, 1)));
}



.top--10-100 {
  top: -10%;
}

.top--20-100 {
  top: -20%;
}

.top--30-100 {
  top: -30%;
}

.top--40-100 {
  top: -40%;
}

.top--50-100 {
  top: -50%;
}

.top--60-100 {
  top: -60%;
}

.top--70-100 {
  top: -70%;
}

.top--80-100 {
  top: -80%;
}

.top--90-100 {
  top: -90%;
}

.top--100-100 {
  top: -100%;
}

.top--1 {
  top: -1px;
}

.top--2 {
  top: -2px;
}

.top--4 {
  top: calc((-0.25rem * var(--responsive-70)) * (var(--top--4, 1)));
}

.top--6 {
  top: calc((-0.375rem * var(--responsive-70)) * (var(--top--6, 1)));
}

.top--8 {
  top: calc((-0.5rem * var(--responsive-70)) * (var(--top--8, 1)));
}

.top--10 {
  top: calc((-0.625rem * var(--responsive-70)) * (var(--top--10, 1)));
}

.top--12 {
  top: calc((-0.75rem * var(--responsive-70)) * (var(--top--12, 1)));
}

.top--14 {
  top: calc((-0.875rem * var(--responsive-70)) * (var(--top--14, 1)));
}

.top--16 {
  top: calc((-1rem * var(--responsive-70)) * (var(--top--16, 1)));
}

.top--18 {
  top: calc((-1.125rem * var(--responsive-70)) * (var(--top--18, 1)));
}

.top--20 {
  top: calc((-1.25rem * var(--responsive-70)) * (var(--top--20, 1)));
}

.top--22 {
  top: calc((-1.375rem * var(--responsive-70)) * (var(--top--22, 1)));
}

.top--24 {
  top: calc((-1.5rem * var(--responsive-70)) * (var(--top--24, 1)));
}

.top--28 {
  top: calc((-1.75rem * var(--responsive-70)) * (var(--top--28, 1)));
}

.top--32 {
  top: calc((-2rem * var(--responsive-70)) * (var(--top--32, 1)));
}

.top--36 {
  top: calc((-2.25rem * var(--responsive-70)) * (var(--top--36, 1)));
}

.top--40 {
  top: calc((-2.5rem * var(--responsive-60)) * (var(--top--40, 1)));
}

.top--44 {
  top: calc((-2.75rem * var(--responsive-60)) * (var(--top--44, 1)));
}

.top--48 {
  top: calc((-3rem * var(--responsive-60)) * (var(--top--48, 1)));
}

.top--52 {
  top: calc((-3.25rem * var(--responsive-60)) * (var(--top--52, 1)));
}

.top--56 {
  top: calc((-3.5rem * var(--responsive-60)) * (var(--top--56, 1)));
}

.top--60 {
  top: calc((-3.75rem * var(--responsive-50)) * (var(--top--60, 1)));
}

.top--64 {
  top: calc((-4rem * var(--responsive-50)) * (var(--top--64, 1)));
}

.top--68 {
  top: calc((-4.25rem * var(--responsive-50)) * (var(--top--68, 1)));
}

.top--72 {
  top: calc((-4.5rem * var(--responsive-50)) * (var(--top--72, 1)));
}

.top--76 {
  top: calc((-4.75rem * var(--responsive-50)) * (var(--top--76, 1)));
}

.top--80 {
  top: calc((-5rem * var(--responsive-40)) * (var(--top--80, 1)));
}

.top--84 {
  top: calc((-5.25rem * var(--responsive-40)) * (var(--top--84, 1)));
}

.top--88 {
  top: calc((-5.5rem * var(--responsive-40)) * (var(--top--88, 1)));
}

.top--92 {
  top: calc((-5.75rem * var(--responsive-40)) * (var(--top--92, 1)));
}

.top--96 {
  top: calc((-6rem * var(--responsive-40)) * (var(--top--96, 1)));
}

.top--100 {
  top: calc((-6.25rem * var(--responsive-30)) * (var(--top--100, 1)));
}

.top--120 {
  top: calc((-7.5rem * var(--responsive-30)) * (var(--top--120, 1)));
}

.top--140 {
  top: calc((-8.75rem * var(--responsive-30)) * (var(--top--140, 1)));
}

.top--160 {
  top: calc((-10rem * var(--responsive-30)) * (var(--top--160, 1)));
}

.top--180 {
  top: calc((-11.25rem * var(--responsive-30)) * (var(--top--180, 1)));
}

.top--200 {
  top: calc((-12.5rem * var(--responsive-30)) * (var(--top--200, 1)));
}

.top--220 {
  top: calc((-13.75rem * var(--responsive-70)) * (var(--top--220, 1)));
}

.top--240 {
  top: calc((-15rem * var(--responsive-70)) * (var(--top--240, 1)));
}

.top--260 {
  top: calc((-16.25rem * var(--responsive-70)) * (var(--top--260, 1)));
}

.top--280 {
  top: calc((-17.5rem * var(--responsive-70)) * (var(--top--280, 1)));
}

.top--300 {
  top: calc((-18.75rem * var(--responsive-70)) * (var(--top--300, 1)));
}

.top--320 {
  top: calc((-20rem * var(--responsive-70)) * (var(--top--320, 1)));
}

.top--340 {
  top: calc((-21.25rem * var(--responsive-70)) * (var(--top--340, 1)));
}

.top--360 {
  top: calc((-22.5rem * var(--responsive-70)) * (var(--top--360, 1)));
}

.top--380 {
  top: calc((-23.75rem * var(--responsive-70)) * (var(--top--380, 1)));
}

.top--400 {
  top: calc((-25rem * var(--responsive-70)) * (var(--top--400, 1)));
}

.top--420 {
  top: calc((-26.25rem * var(--responsive-70)) * (var(--top--420, 1)));
}

.top--440 {
  top: calc((-27.5rem * var(--responsive-70)) * (var(--top--440, 1)));
}

.top--460 {
  top: calc((-28.75rem * var(--responsive-70)) * (var(--top--460, 1)));
}

.top--480 {
  top: calc((-30rem * var(--responsive-70)) * (var(--top--480, 1)));
}

.top--500 {
  top: calc((-31.25rem * var(--responsive-70)) * (var(--top--500, 1)));
}

.top--520 {
  top: calc((-32.5rem * var(--responsive-70)) * (var(--top--520, 1)));
}

.top--540 {
  top: calc((-33.75rem * var(--responsive-70)) * (var(--top--540, 1)));
}

.top--560 {
  top: calc((-35rem * var(--responsive-70)) * (var(--top--560, 1)));
}

.top--580 {
  top: calc((-36.25rem * var(--responsive-70)) * (var(--top--580, 1)));
}

.top--600 {
  top: calc((-37.5rem * var(--responsive-70)) * (var(--top--600, 1)));
}

.top--620 {
  top: calc((-38.75rem * var(--responsive-70)) * (var(--top--620, 1)));
}

.top--640 {
  top: calc((-40rem * var(--responsive-70)) * (var(--top--640, 1)));
}

.top--660 {
  top: calc((-41.25rem * var(--responsive-70)) * (var(--top--660, 1)));
}

.top--680 {
  top: calc((-42.5rem * var(--responsive-70)) * (var(--top--680, 1)));
}

.top--700 {
  top: calc((-43.75rem * var(--responsive-70)) * (var(--top--700, 1)));
}

.top--720 {
  top: calc((-45rem * var(--responsive-70)) * (var(--top--720, 1)));
}

.top--740 {
  top: calc((-46.25rem * var(--responsive-70)) * (var(--top--740, 1)));
}

.top--760 {
  top: calc((-47.5rem * var(--responsive-70)) * (var(--top--760, 1)));
}

.top--780 {
  top: calc((-48.75rem * var(--responsive-70)) * (var(--top--780, 1)));
}

.top--800 {
  top: calc((-50rem * var(--responsive-70)) * (var(--top--800, 1)));
}

.top--900 {
  top: calc((-56.25rem * var(--responsive-70)) * (var(--top--900, 1)));
}

.top--1000 {
  top: calc((-62.5rem * var(--responsive-70)) * (var(--top--1000, 1)));
}

.top--1100 {
  top: calc((-68.75rem * var(--responsive-70)) * (var(--top--1100, 1)));
}

.top--1200 {
  top: calc((-75rem * var(--responsive-70)) * (var(--top--1200, 1)));
}


/*--------------------------------------------------------------

## Right

--------------------------------------------------------------*/

.right-auto {
  right: auto;
}

.right-10-100 {
  right: 10%;
}

.right-20-100 {
  right: 20%;
}

.right-30-100 {
  right: 30%;
}

.right-40-100 {
  right: 40%;
}

.right-50-100 {
  right: 50%;
}

.right-60-100 {
  right: 60%;
}

.right-70-100 {
  right: 70%;
}

.right-80-100 {
  right: 80%;
}

.right-90-100 {
  right: 90%;
}

.right-100-100 {
  right: 100%;
}

.right-0 {
  right: 0;
}

.right-1 {
  right: 1px;
}

.right-4 {
  right: calc((0.25rem * var(--responsive-70)) * (var(--right-4, 1)));
}

.right-6 {
  right: calc((0.375rem * var(--responsive-70)) * (var(--right-6, 1)));
}

.right-8 {
  right: calc((0.5rem * var(--responsive-70)) * (var(--right-8, 1)));
}

.right-10 {
  right: calc((0.625rem * var(--responsive-70)) * (var(--right-10, 1)));
}

.right-12 {
  right: calc((0.75rem * var(--responsive-70)) * (var(--right-12, 1)));
}

.right-14 {
  right: calc((0.875rem * var(--responsive-70)) * (var(--right-14, 1)));
}

.right-16 {
  right: calc((1rem * var(--responsive-70)) * (var(--right-16, 1)));
}

.right-18 {
  right: calc((1.125rem * var(--responsive-70)) * (var(--right-18, 1)));
}

.right-20 {
  right: calc((1.25rem * var(--responsive-70)) * (var(--right-20, 1)));
}

.right-22 {
  right: calc((1.375rem * var(--responsive-70)) * (var(--right-22, 1)));
}

.right-24 {
  right: calc((1.5rem * var(--responsive-70)) * (var(--right-24, 1)));
}

.right-28 {
  right: calc((1.75rem * var(--responsive-70)) * (var(--right-28, 1)));
}

.right-32 {
  right: calc((2rem * var(--responsive-70)) * (var(--right-32, 1)));
}

.right-36 {
  right: calc((2.25rem * var(--responsive-70)) * (var(--right-36, 1)));
}

.right-40 {
  right: calc((2.5rem * var(--responsive-60)) * (var(--right-40, 1)));
}

.right-44 {
  right: calc((2.75rem * var(--responsive-60)) * (var(--right-44, 1)));
}

.right-48 {
  right: calc((3rem * var(--responsive-60)) * (var(--right-48, 1)));
}

.right-52 {
  right: calc((3.25rem * var(--responsive-60)) * (var(--right-52, 1)));
}

.right-56 {
  right: calc((3.5rem * var(--responsive-60)) * (var(--right-56, 1)));
}

.right-60 {
  right: calc((3.75rem * var(--responsive-50)) * (var(--right-60, 1)));
}

.right-64 {
  right: calc((4rem * var(--responsive-50)) * (var(--right-64, 1)));
}

.right-68 {
  right: calc((4.25rem * var(--responsive-50)) * (var(--right-68, 1)));
}

.right-72 {
  right: calc((4.5rem * var(--responsive-50)) * (var(--right-72, 1)));
}

.right-76 {
  right: calc((4.75rem * var(--responsive-50)) * (var(--right-76, 1)));
}

.right-80 {
  right: calc((5rem * var(--responsive-40)) * (var(--right-80, 1)));
}

.right-84 {
  right: calc((5.25rem * var(--responsive-40)) * (var(--right-84, 1)));
}

.right-88 {
  right: calc((5.5rem * var(--responsive-40)) * (var(--right-88, 1)));
}

.right-92 {
  right: calc((5.75rem * var(--responsive-40)) * (var(--right-92, 1)));
}

.right-96 {
  right: calc((6rem * var(--responsive-40)) * (var(--right-96, 1)));
}

.right-100 {
  right: calc((6.25rem * var(--responsive-30)) * (var(--right-100, 1)));
}

.right-120 {
  right: calc((7.5rem * var(--responsive-30)) * (var(--right-120, 1)));
}

.right-140 {
  right: calc((8.75rem * var(--responsive-30)) * (var(--right-140, 1)));
}

.right-160 {
  right: calc((10rem * var(--responsive-30)) * (var(--right-160, 1)));
}

.right-180 {
  right: calc((11.25rem * var(--responsive-30)) * (var(--right-180, 1)));
}

.right-200 {
  right: calc((12.5rem * var(--responsive-30)) * (var(--right-200, 1)));
}

.right-220 {
  right: calc((13.75rem * var(--responsive-70)) * (var(--right-220, 1)));
}

.right-240 {
  right: calc((15rem * var(--responsive-70)) * (var(--right-240, 1)));
}

.right-260 {
  right: calc((16.25rem * var(--responsive-70)) * (var(--right-260, 1)));
}

.right-280 {
  right: calc((17.5rem * var(--responsive-70)) * (var(--right-280, 1)));
}

.right-300 {
  right: calc((18.75rem * var(--responsive-70)) * (var(--right-300, 1)));
}

.right-320 {
  right: calc((20rem * var(--responsive-70)) * (var(--right-320, 1)));
}

.right-340 {
  right: calc((21.25rem * var(--responsive-70)) * (var(--right-340, 1)));
}

.right-360 {
  right: calc((22.5rem * var(--responsive-70)) * (var(--right-360, 1)));
}

.right-380 {
  right: calc((23.75rem * var(--responsive-70)) * (var(--right-380, 1)));
}

.right-400 {
  right: calc((25rem * var(--responsive-70)) * (var(--right-400, 1)));
}

.right-420 {
  right: calc((26.25rem * var(--responsive-70)) * (var(--right-420, 1)));
}

.right-440 {
  right: calc((27.5rem * var(--responsive-70)) * (var(--right-440, 1)));
}

.right-460 {
  right: calc((28.75rem * var(--responsive-70)) * (var(--right-460, 1)));
}

.right-480 {
  right: calc((30rem * var(--responsive-70)) * (var(--right-480, 1)));
}

.right-500 {
  right: calc((31.25rem * var(--responsive-70)) * (var(--right-500, 1)));
}

.right-520 {
  right: calc((32.5rem * var(--responsive-70)) * (var(--right-520, 1)));
}

.right-540 {
  right: calc((33.75rem * var(--responsive-70)) * (var(--right-540, 1)));
}

.right-560 {
  right: calc((35rem * var(--responsive-70)) * (var(--right-560, 1)));
}

.right-580 {
  right: calc((36.25rem * var(--responsive-70)) * (var(--right-580, 1)));
}

.right-600 {
  right: calc((37.5rem * var(--responsive-70)) * (var(--right-600, 1)));
}

.right-620 {
  right: calc((38.75rem * var(--responsive-70)) * (var(--right-620, 1)));
}

.right-640 {
  right: calc((40rem * var(--responsive-70)) * (var(--right-640, 1)));
}

.right-660 {
  right: calc((41.25rem * var(--responsive-70)) * (var(--right-660, 1)));
}

.right-680 {
  right: calc((42.5rem * var(--responsive-70)) * (var(--right-680, 1)));
}

.right-700 {
  right: calc((43.75rem * var(--responsive-70)) * (var(--right-700, 1)));
}

.right-720 {
  right: calc((45rem * var(--responsive-70)) * (var(--right-720, 1)));
}

.right-740 {
  right: calc((46.25rem * var(--responsive-70)) * (var(--right-740, 1)));
}

.right-760 {
  right: calc((47.5rem * var(--responsive-70)) * (var(--right-760, 1)));
}

.right-780 {
  right: calc((48.75rem * var(--responsive-70)) * (var(--right-780, 1)));
}

.right-800 {
  right: calc((50rem * var(--responsive-70)) * (var(--right-800, 1)));
}

.right-900 {
  right: calc((56.25rem * var(--responsive-70)) * (var(--right-900, 1)));
}

.right-1000 {
  right: calc((62.5rem * var(--responsive-70)) * (var(--right-1000, 1)));
}

.right-1100 {
  right: calc((68.75rem * var(--responsive-70)) * (var(--right-1100, 1)));
}

.right-1200 {
  right: calc((75rem * var(--responsive-70)) * (var(--right-1200, 1)));
}

.right--5-100 {
  right: -5%;
}


.right--10-100 {
  right: -10%;
}

.right--20-100 {
  right: -20%;
}

.right--30-100 {
  right: -30%;
}

.right--40-100 {
  right: -40%;
}

.right--50-100 {
  right: -50%;
}

.right--60-100 {
  right: -60%;
}

.right--70-100 {
  right: -70%;
}

.right--80-100 {
  right: -80%;
}

.right--90-100 {
  right: -90%;
}

.right--100-100 {
  right: -100%;
}

.right--1 {
  right: -1px;
}

.right--4 {
  right: calc((-0.25rem * var(--responsive-70)) * (var(--right--4, 1)));
}

.right--6 {
  right: calc((-0.375rem * var(--responsive-70)) * (var(--right--6, 1)));
}

.right--8 {
  right: calc((-0.5rem * var(--responsive-70)) * (var(--right--8, 1)));
}

.right--10 {
  right: calc((-0.625rem * var(--responsive-70)) * (var(--right--10, 1)));
}

.right--12 {
  right: calc((-0.75rem * var(--responsive-70)) * (var(--right--12, 1)));
}

.right--14 {
  right: calc((-0.875rem * var(--responsive-70)) * (var(--right--14, 1)));
}

.right--16 {
  right: calc((-1rem * var(--responsive-70)) * (var(--right--16, 1)));
}

.right--18 {
  right: calc((-1.125rem * var(--responsive-70)) * (var(--right--18, 1)));
}

.right--20 {
  right: calc((-1.25rem * var(--responsive-70)) * (var(--right--20, 1)));
}

.right--22 {
  right: calc((-1.375rem * var(--responsive-70)) * (var(--right--22, 1)));
}

.right--24 {
  right: calc((-1.5rem * var(--responsive-70)) * (var(--right--24, 1)));
}

.right--28 {
  right: calc((-1.75rem * var(--responsive-70)) * (var(--right--28, 1)));
}

.right--32 {
  right: calc((-2rem * var(--responsive-70)) * (var(--right--32, 1)));
}

.right--36 {
  right: calc((-2.25rem * var(--responsive-70)) * (var(--right--36, 1)));
}

.right--40 {
  right: calc((-2.5rem * var(--responsive-60)) * (var(--right--40, 1)));
}

.right--44 {
  right: calc((-2.75rem * var(--responsive-60)) * (var(--right--44, 1)));
}

.right--48 {
  right: calc((-3rem * var(--responsive-60)) * (var(--right--48, 1)));
}

.right--52 {
  right: calc((-3.25rem * var(--responsive-60)) * (var(--right--52, 1)));
}

.right--56 {
  right: calc((-3.5rem * var(--responsive-60)) * (var(--right--56, 1)));
}

.right--60 {
  right: calc((-3.75rem * var(--responsive-50)) * (var(--right--60, 1)));
}

.right--64 {
  right: calc((-4rem * var(--responsive-50)) * (var(--right--64, 1)));
}

.right--68 {
  right: calc((-4.25rem * var(--responsive-50)) * (var(--right--68, 1)));
}

.right--72 {
  right: calc((-4.5rem * var(--responsive-50)) * (var(--right--72, 1)));
}

.right--76 {
  right: calc((-4.75rem * var(--responsive-50)) * (var(--right--76, 1)));
}

.right--80 {
  right: calc((-5rem * var(--responsive-40)) * (var(--right--80, 1)));
}

.right--84 {
  right: calc((-5.25rem * var(--responsive-40)) * (var(--right--84, 1)));
}

.right--88 {
  right: calc((-5.5rem * var(--responsive-40)) * (var(--right--88, 1)));
}

.right--92 {
  right: calc((-5.75rem * var(--responsive-40)) * (var(--right--92, 1)));
}

.right--96 {
  right: calc((-6rem * var(--responsive-40)) * (var(--right--96, 1)));
}

.right--100 {
  right: calc((-6.25rem * var(--responsive-30)) * (var(--right--100, 1)));
}

.right--120 {
  right: calc((-7.5rem * var(--responsive-30)) * (var(--right--120, 1)));
}

.right--140 {
  right: calc((-8.75rem * var(--responsive-30)) * (var(--right--140, 1)));
}

.right--160 {
  right: calc((-10rem * var(--responsive-30)) * (var(--right--160, 1)));
}

.right--180 {
  right: calc((-11.25rem * var(--responsive-30)) * (var(--right--180, 1)));
}

.right--200 {
  right: calc((-12.5rem * var(--responsive-30)) * (var(--right--200, 1)));
}

.right--220 {
  right: calc((-13.75rem * var(--responsive-70)) * (var(--right--220, 1)));
}

.right--240 {
  right: calc((-15rem * var(--responsive-70)) * (var(--right--240, 1)));
}

.right--260 {
  right: calc((-16.25rem * var(--responsive-70)) * (var(--right--260, 1)));
}

.right--280 {
  right: calc((-17.5rem * var(--responsive-70)) * (var(--right--280, 1)));
}

.right--300 {
  right: calc((-18.75rem * var(--responsive-70)) * (var(--right--300, 1)));
}

.right--320 {
  right: calc((-20rem * var(--responsive-70)) * (var(--right--320, 1)));
}

.right--340 {
  right: calc((-21.25rem * var(--responsive-70)) * (var(--right--340, 1)));
}

.right--360 {
  right: calc((-22.5rem * var(--responsive-70)) * (var(--right--360, 1)));
}

.right--380 {
  right: calc((-23.75rem * var(--responsive-70)) * (var(--right--380, 1)));
}

.right--400 {
  right: calc((-25rem * var(--responsive-70)) * (var(--right--400, 1)));
}

.right--420 {
  right: calc((-26.25rem * var(--responsive-70)) * (var(--right--420, 1)));
}

.right--440 {
  right: calc((-27.5rem * var(--responsive-70)) * (var(--right--440, 1)));
}

.right--460 {
  right: calc((-28.75rem * var(--responsive-70)) * (var(--right--460, 1)));
}

.right--480 {
  right: calc((-30rem * var(--responsive-70)) * (var(--right--480, 1)));
}

.right--500 {
  right: calc((-31.25rem * var(--responsive-70)) * (var(--right--500, 1)));
}

.right--520 {
  right: calc((-32.5rem * var(--responsive-70)) * (var(--right--520, 1)));
}

.right--540 {
  right: calc((-33.75rem * var(--responsive-70)) * (var(--right--540, 1)));
}

.right--560 {
  right: calc((-35rem * var(--responsive-70)) * (var(--right--560, 1)));
}

.right--580 {
  right: calc((-36.25rem * var(--responsive-70)) * (var(--right--580, 1)));
}

.right--600 {
  right: calc((-37.5rem * var(--responsive-70)) * (var(--right--600, 1)));
}

.right--620 {
  right: calc((-38.75rem * var(--responsive-70)) * (var(--right--620, 1)));
}

.right--640 {
  right: calc((-40rem * var(--responsive-70)) * (var(--right--640, 1)));
}

.right--660 {
  right: calc((-41.25rem * var(--responsive-70)) * (var(--right--660, 1)));
}

.right--680 {
  right: calc((-42.5rem * var(--responsive-70)) * (var(--right--680, 1)));
}

.right--700 {
  right: calc((-43.75rem * var(--responsive-70)) * (var(--right--700, 1)));
}

.right--720 {
  right: calc((-45rem * var(--responsive-70)) * (var(--right--720, 1)));
}

.right--740 {
  right: calc((-46.25rem * var(--responsive-70)) * (var(--right--740, 1)));
}

.right--760 {
  right: calc((-47.5rem * var(--responsive-70)) * (var(--right--760, 1)));
}

.right--780 {
  right: calc((-48.75rem * var(--responsive-70)) * (var(--right--780, 1)));
}

.right--800 {
  right: calc((-50rem * var(--responsive-70)) * (var(--right--800, 1)));
}

.right--900 {
  right: calc((-56.25rem * var(--responsive-70)) * (var(--right--900, 1)));
}

.right--1000 {
  right: calc((-62.5rem * var(--responsive-70)) * (var(--right--1000, 1)));
}

.right--1100 {
  right: calc((-68.75rem * var(--responsive-70)) * (var(--right--1100, 1)));
}

.right--1200 {
  right: calc((-75rem * var(--responsive-70)) * (var(--right--1200, 1)));
}


/*--------------------------------------------------------------

## Left

--------------------------------------------------------------*/

.left-auto {
  left: auto;
}

.left-10-100 {
  left: 10%;
}

.left-20-100 {
  left: 20%;
}

.left-30-100 {
  left: 30%;
}

.left-40-100 {
  left: 40%;
}

.left-50-100 {
  left: 50%;
}

.left-60-100 {
  left: 60%;
}

.left-70-100 {
  left: 70%;
}

.left-80-100 {
  left: 80%;
}

.left-90-100 {
  left: 90%;
}

.left-100-100 {
  left: 100%;
}

.left-0 {
  left: 0;
}

.left-1 {
  left: 1px;
}

.left-4 {
  left: calc((0.25rem * var(--responsive-70)) * (var(--left-4, 1)));
}

.left-6 {
  left: calc((0.375rem * var(--responsive-70)) * (var(--left-6, 1)));
}

.left-8 {
  left: calc((0.5rem * var(--responsive-70)) * (var(--left-8, 1)));
}

.left-10 {
  left: calc((0.625rem * var(--responsive-70)) * (var(--left-10, 1)));
}

.left-12 {
  left: calc((0.75rem * var(--responsive-70)) * (var(--left-12, 1)));
}

.left-14 {
  left: calc((0.875rem * var(--responsive-70)) * (var(--left-14, 1)));
}

.left-16 {
  left: calc((1rem * var(--responsive-70)) * (var(--left-16, 1)));
}

.left-18 {
  left: calc((1.125rem * var(--responsive-70)) * (var(--left-18, 1)));
}

.left-20 {
  left: calc((1.25rem * var(--responsive-70)) * (var(--left-20, 1)));
}

.left-22 {
  left: calc((1.375rem * var(--responsive-70)) * (var(--left-22, 1)));
}

.left-24 {
  left: calc((1.5rem * var(--responsive-70)) * (var(--left-24, 1)));
}

.left-28 {
  left: calc((1.75rem * var(--responsive-70)) * (var(--left-28, 1)));
}

.left-32 {
  left: calc((2rem * var(--responsive-70)) * (var(--left-32, 1)));
}

.left-36 {
  left: calc((2.25rem * var(--responsive-70)) * (var(--left-36, 1)));
}

.left-40 {
  left: calc((2.5rem * var(--responsive-60)) * (var(--left-40, 1)));
}

.left-44 {
  left: calc((2.75rem * var(--responsive-60)) * (var(--left-44, 1)));
}

.left-48 {
  left: calc((3rem * var(--responsive-60)) * (var(--left-48, 1)));
}

.left-52 {
  left: calc((3.25rem * var(--responsive-60)) * (var(--left-52, 1)));
}

.left-56 {
  left: calc((3.5rem * var(--responsive-60)) * (var(--left-56, 1)));
}

.left-60 {
  left: calc((3.75rem * var(--responsive-50)) * (var(--left-60, 1)));
}

.left-64 {
  left: calc((4rem * var(--responsive-50)) * (var(--left-64, 1)));
}

.left-68 {
  left: calc((4.25rem * var(--responsive-50)) * (var(--left-68, 1)));
}

.left-72 {
  left: calc((4.5rem * var(--responsive-50)) * (var(--left-72, 1)));
}

.left-76 {
  left: calc((4.75rem * var(--responsive-50)) * (var(--left-76, 1)));
}

.left-80 {
  left: calc((5rem * var(--responsive-40)) * (var(--left-80, 1)));
}

.left-84 {
  left: calc((5.25rem * var(--responsive-40)) * (var(--left-84, 1)));
}

.left-88 {
  left: calc((5.5rem * var(--responsive-40)) * (var(--left-88, 1)));
}

.left-92 {
  left: calc((5.75rem * var(--responsive-40)) * (var(--left-92, 1)));
}

.left-96 {
  left: calc((6rem * var(--responsive-40)) * (var(--left-96, 1)));
}

.left-100 {
  left: calc((6.25rem * var(--responsive-30)) * (var(--left-100, 1)));
}

.left-120 {
  left: calc((7.5rem * var(--responsive-30)) * (var(--left-120, 1)));
}

.left-140 {
  left: calc((8.75rem * var(--responsive-30)) * (var(--left-140, 1)));
}

.left-160 {
  left: calc((10rem * var(--responsive-30)) * (var(--left-160, 1)));
}

.left-180 {
  left: calc((11.25rem * var(--responsive-30)) * (var(--left-180, 1)));
}

.left-200 {
  left: calc((12.5rem * var(--responsive-30)) * (var(--left-200, 1)));
}

.left-220 {
  left: calc((13.75rem * var(--responsive-70)) * (var(--left-220, 1)));
}

.left-240 {
  left: calc((15rem * var(--responsive-70)) * (var(--left-240, 1)));
}

.left-260 {
  left: calc((16.25rem * var(--responsive-70)) * (var(--left-260, 1)));
}

.left-280 {
  left: calc((17.5rem * var(--responsive-70)) * (var(--left-280, 1)));
}

.left-300 {
  left: calc((18.75rem * var(--responsive-70)) * (var(--left-300, 1)));
}

.left-320 {
  left: calc((20rem * var(--responsive-70)) * (var(--left-320, 1)));
}

.left-340 {
  left: calc((21.25rem * var(--responsive-70)) * (var(--left-340, 1)));
}

.left-360 {
  left: calc((22.5rem * var(--responsive-70)) * (var(--left-360, 1)));
}

.left-380 {
  left: calc((23.75rem * var(--responsive-70)) * (var(--left-380, 1)));
}

.left-400 {
  left: calc((25rem * var(--responsive-70)) * (var(--left-400, 1)));
}

.left-420 {
  left: calc((26.25rem * var(--responsive-70)) * (var(--left-420, 1)));
}

.left-440 {
  left: calc((27.5rem * var(--responsive-70)) * (var(--left-440, 1)));
}

.left-460 {
  left: calc((28.75rem * var(--responsive-70)) * (var(--left-460, 1)));
}

.left-480 {
  left: calc((30rem * var(--responsive-70)) * (var(--left-480, 1)));
}

.left-500 {
  left: calc((31.25rem * var(--responsive-70)) * (var(--left-500, 1)));
}

.left-520 {
  left: calc((32.5rem * var(--responsive-70)) * (var(--left-520, 1)));
}

.left-540 {
  left: calc((33.75rem * var(--responsive-70)) * (var(--left-540, 1)));
}

.left-560 {
  left: calc((35rem * var(--responsive-70)) * (var(--left-560, 1)));
}

.left-580 {
  left: calc((36.25rem * var(--responsive-70)) * (var(--left-580, 1)));
}

.left-600 {
  left: calc((37.5rem * var(--responsive-70)) * (var(--left-600, 1)));
}

.left-620 {
  left: calc((38.75rem * var(--responsive-70)) * (var(--left-620, 1)));
}

.left-640 {
  left: calc((40rem * var(--responsive-70)) * (var(--left-640, 1)));
}

.left-660 {
  left: calc((41.25rem * var(--responsive-70)) * (var(--left-660, 1)));
}

.left-680 {
  left: calc((42.5rem * var(--responsive-70)) * (var(--left-680, 1)));
}

.left-700 {
  left: calc((43.75rem * var(--responsive-70)) * (var(--left-700, 1)));
}

.left-720 {
  left: calc((45rem * var(--responsive-70)) * (var(--left-720, 1)));
}

.left-740 {
  left: calc((46.25rem * var(--responsive-70)) * (var(--left-740, 1)));
}

.left-760 {
  left: calc((47.5rem * var(--responsive-70)) * (var(--left-760, 1)));
}

.left-780 {
  left: calc((48.75rem * var(--responsive-70)) * (var(--left-780, 1)));
}

.left-800 {
  left: calc((50rem * var(--responsive-70)) * (var(--left-800, 1)));
}

.left-900 {
  left: calc((56.25rem * var(--responsive-70)) * (var(--left-900, 1)));
}

.left-1000 {
  left: calc((62.5rem * var(--responsive-70)) * (var(--left-1000, 1)));
}

.left-1100 {
  left: calc((68.75rem * var(--responsive-70)) * (var(--left-1100, 1)));
}

.left-1200 {
  left: calc((75rem * var(--responsive-70)) * (var(--left-1200, 1)));
}

.left--5-100 {
  left: -5%;
}


.left--10-100 {
  left: -10%;
}

.left--20-100 {
  left: -20%;
}

.left--30-100 {
  left: -30%;
}

.left--40-100 {
  left: -40%;
}

.left--50-100 {
  left: -50%;
}

.left--60-100 {
  left: -60%;
}

.left--70-100 {
  left: -70%;
}

.left--80-100 {
  left: -80%;
}

.left--90-100 {
  left: -90%;
}

.left--100-100 {
  left: -100%;
}

.left--1 {
  left: -1px;
}

.left--4 {
  left: calc((-0.25rem * var(--responsive-70)) * (var(--left--4, 1)));
}

.left--6 {
  left: calc((-0.375rem * var(--responsive-70)) * (var(--left--6, 1)));
}

.left--8 {
  left: calc((-0.5rem * var(--responsive-70)) * (var(--left--8, 1)));
}

.left--10 {
  left: calc((-0.625rem * var(--responsive-70)) * (var(--left--10, 1)));
}

.left--12 {
  left: calc((-0.75rem * var(--responsive-70)) * (var(--left--12, 1)));
}

.left--14 {
  left: calc((-0.875rem * var(--responsive-70)) * (var(--left--14, 1)));
}

.left--16 {
  left: calc((-1rem * var(--responsive-70)) * (var(--left--16, 1)));
}

.left--18 {
  left: calc((-1.125rem * var(--responsive-70)) * (var(--left--18, 1)));
}

.left--20 {
  left: calc((-1.25rem * var(--responsive-70)) * (var(--left--20, 1)));
}

.left--22 {
  left: calc((-1.375rem * var(--responsive-70)) * (var(--left--22, 1)));
}

.left--24 {
  left: calc((-1.5rem * var(--responsive-70)) * (var(--left--24, 1)));
}

.left--28 {
  left: calc((-1.75rem * var(--responsive-70)) * (var(--left--28, 1)));
}

.left--32 {
  left: calc((-2rem * var(--responsive-70)) * (var(--left--32, 1)));
}

.left--36 {
  left: calc((-2.25rem * var(--responsive-70)) * (var(--left--36, 1)));
}

.left--40 {
  left: calc((-2.5rem * var(--responsive-60)) * (var(--left--40, 1)));
}

.left--44 {
  left: calc((-2.75rem * var(--responsive-60)) * (var(--left--44, 1)));
}

.left--48 {
  left: calc((-3rem * var(--responsive-60)) * (var(--left--48, 1)));
}

.left--52 {
  left: calc((-3.25rem * var(--responsive-60)) * (var(--left--52, 1)));
}

.left--56 {
  left: calc((-3.5rem * var(--responsive-60)) * (var(--left--56, 1)));
}

.left--60 {
  left: calc((-3.75rem * var(--responsive-50)) * (var(--left--60, 1)));
}

.left--64 {
  left: calc((-4rem * var(--responsive-50)) * (var(--left--64, 1)));
}

.left--68 {
  left: calc((-4.25rem * var(--responsive-50)) * (var(--left--68, 1)));
}

.left--72 {
  left: calc((-4.5rem * var(--responsive-50)) * (var(--left--72, 1)));
}

.left--76 {
  left: calc((-4.75rem * var(--responsive-50)) * (var(--left--76, 1)));
}

.left--80 {
  left: calc((-5rem * var(--responsive-40)) * (var(--left--80, 1)));
}

.left--84 {
  left: calc((-5.25rem * var(--responsive-40)) * (var(--left--84, 1)));
}

.left--88 {
  left: calc((-5.5rem * var(--responsive-40)) * (var(--left--88, 1)));
}

.left--92 {
  left: calc((-5.75rem * var(--responsive-40)) * (var(--left--92, 1)));
}

.left--96 {
  left: calc((-6rem * var(--responsive-40)) * (var(--left--96, 1)));
}

.left--100 {
  left: calc((-6.25rem * var(--responsive-30)) * (var(--left--100, 1)));
}

.left--120 {
  left: calc((-7.5rem * var(--responsive-30)) * (var(--left--120, 1)));
}

.left--140 {
  left: calc((-8.75rem * var(--responsive-30)) * (var(--left--140, 1)));
}

.left--160 {
  left: calc((-10rem * var(--responsive-30)) * (var(--left--160, 1)));
}

.left--180 {
  left: calc((-11.25rem * var(--responsive-30)) * (var(--left--180, 1)));
}

.left--200 {
  left: calc((-12.5rem * var(--responsive-30)) * (var(--left--200, 1)));
}

.left--220 {
  left: calc((-13.75rem * var(--responsive-70)) * (var(--left--220, 1)));
}

.left--240 {
  left: calc((-15rem * var(--responsive-70)) * (var(--left--240, 1)));
}

.left--260 {
  left: calc((-16.25rem * var(--responsive-70)) * (var(--left--260, 1)));
}

.left--280 {
  left: calc((-17.5rem * var(--responsive-70)) * (var(--left--280, 1)));
}

.left--300 {
  left: calc((-18.75rem * var(--responsive-70)) * (var(--left--300, 1)));
}

.left--320 {
  left: calc((-20rem * var(--responsive-70)) * (var(--left--320, 1)));
}

.left--340 {
  left: calc((-21.25rem * var(--responsive-70)) * (var(--left--340, 1)));
}

.left--360 {
  left: calc((-22.5rem * var(--responsive-70)) * (var(--left--360, 1)));
}

.left--380 {
  left: calc((-23.75rem * var(--responsive-70)) * (var(--left--380, 1)));
}

.left--400 {
  left: calc((-25rem * var(--responsive-70)) * (var(--left--400, 1)));
}

.left--420 {
  left: calc((-26.25rem * var(--responsive-70)) * (var(--left--420, 1)));
}

.left--440 {
  left: calc((-27.5rem * var(--responsive-70)) * (var(--left--440, 1)));
}

.left--460 {
  left: calc((-28.75rem * var(--responsive-70)) * (var(--left--460, 1)));
}

.left--480 {
  left: calc((-30rem * var(--responsive-70)) * (var(--left--480, 1)));
}

.left--500 {
  left: calc((-31.25rem * var(--responsive-70)) * (var(--left--500, 1)));
}

.left--520 {
  left: calc((-32.5rem * var(--responsive-70)) * (var(--left--520, 1)));
}

.left--540 {
  left: calc((-33.75rem * var(--responsive-70)) * (var(--left--540, 1)));
}

.left--560 {
  left: calc((-35rem * var(--responsive-70)) * (var(--left--560, 1)));
}

.left--580 {
  left: calc((-36.25rem * var(--responsive-70)) * (var(--left--580, 1)));
}

.left--600 {
  left: calc((-37.5rem * var(--responsive-70)) * (var(--left--600, 1)));
}

.left--620 {
  left: calc((-38.75rem * var(--responsive-70)) * (var(--left--620, 1)));
}

.left--640 {
  left: calc((-40rem * var(--responsive-70)) * (var(--left--640, 1)));
}

.left--660 {
  left: calc((-41.25rem * var(--responsive-70)) * (var(--left--660, 1)));
}

.left--680 {
  left: calc((-42.5rem * var(--responsive-70)) * (var(--left--680, 1)));
}

.left--700 {
  left: calc((-43.75rem * var(--responsive-70)) * (var(--left--700, 1)));
}

.left--720 {
  left: calc((-45rem * var(--responsive-70)) * (var(--left--720, 1)));
}

.left--740 {
  left: calc((-46.25rem * var(--responsive-70)) * (var(--left--740, 1)));
}

.left--760 {
  left: calc((-47.5rem * var(--responsive-70)) * (var(--left--760, 1)));
}

.left--780 {
  left: calc((-48.75rem * var(--responsive-70)) * (var(--left--780, 1)));
}

.left--800 {
  left: calc((-50rem * var(--responsive-70)) * (var(--left--800, 1)));
}

.left--900 {
  left: calc((-56.25rem * var(--responsive-70)) * (var(--left--900, 1)));
}

.left--1000 {
  left: calc((-62.5rem * var(--responsive-70)) * (var(--left--1000, 1)));
}

.left--1100 {
  left: calc((-68.75rem * var(--responsive-70)) * (var(--left--1100, 1)));
}

.left--1200 {
  left: calc((-75rem * var(--responsive-70)) * (var(--left--1200, 1)));
}



/* Top Position */
.s\:top-4 { top: 0.25rem; }
.s\:top-6 { top: 0.375rem; }
.s\:top-8 { top: 0.5rem; }
.s\:top-10 { top: 0.625rem; }
.s\:top-12 { top: 0.75rem; }
.s\:top-14 { top: 0.875rem; }
.s\:top-16 { top: 1rem; }
.s\:top-18 { top: 1.125rem; }
.s\:top-20 { top: 1.25rem; }
.s\:top-22 { top: 1.375rem; }
.s\:top-24 { top: 1.5rem; }
.s\:top-28 { top: 1.75rem; }
.s\:top-32 { top: 2rem; }
.s\:top-36 { top: 2.25rem; }
.s\:top-40 { top: 2.5rem; }
.s\:top-44 { top: 2.75rem; }
.s\:top-48 { top: 3rem; }
.s\:top-52 { top: 3.25rem; }
.s\:top-56 { top: 3.5rem; }
.s\:top-60 { top: 3.75rem; }
.s\:top-64 { top: 4rem; }
.s\:top-68 { top: 4.25rem; }
.s\:top-72 { top: 4.5rem; }
.s\:top-76 { top: 4.75rem; }
.s\:top-80 { top: 5rem; }
.s\:top-84 { top: 5.25rem; }
.s\:top-88 { top: 5.5rem; }
.s\:top-92 { top: 5.75rem; }
.s\:top-96 { top: 6rem; }
.s\:top-100 { top: 6.25rem; }
.s\:top-120 { top: 7.5rem; }
.s\:top-140 { top: 8.75rem; }
.s\:top-160 { top: 10rem; }
.s\:top-180 { top: 11.25rem; }
.s\:top-200 { top: 12.5rem; }
.s\:top-220 { top: 13.75rem; }
.s\:top-240 { top: 15rem; }
.s\:top-260 { top: 16.25rem; }
.s\:top-280 { top: 17.5rem; }
.s\:top-300 { top: 18.75rem; }
.s\:top-320 { top: 20rem; }
.s\:top-340 { top: 21.25rem; }
.s\:top-360 { top: 22.5rem; }
.s\:top-380 { top: 23.75rem; }
.s\:top-400 { top: 25rem; }

/* Top Percentage Negative */
.s\:top--10-100 { top: -10%; }
.s\:top--20-100 { top: -20%; }
.s\:top--30-100 { top: -30%; }
.s\:top--40-100 { top: -40%; }
.s\:top--50-100 { top: -50%; }
.s\:top--60-100 { top: -60%; }
.s\:top--70-100 { top: -70%; }
.s\:top--80-100 { top: -80%; }
.s\:top--90-100 { top: -90%; }
.s\:top--100-100 { top: -100%; }

/* Top Negative */
.s\:top--1 { top: -1px; }
.s\:top--4 { top: -0.25rem; }
.s\:top--6 { top: -0.375rem; }
.s\:top--8 { top: -0.5rem; }
.s\:top--10 { top: -0.625rem; }
.s\:top--12 { top: -0.75rem; }
.s\:top--14 { top: -0.875rem; }
.s\:top--16 { top: -1rem; }
.s\:top--18 { top: -1.125rem; }
.s\:top--20 { top: -1.25rem; }
.s\:top--22 { top: -1.375rem; }
.s\:top--24 { top: -1.5rem; }
.s\:top--28 { top: -1.75rem; }
.s\:top--32 { top: -2rem; }
.s\:top--36 { top: -2.25rem; }
.s\:top--40 { top: -2.5rem; }
.s\:top--44 { top: -2.75rem; }
.s\:top--48 { top: -3rem; }
.s\:top--52 { top: -3.25rem; }
.s\:top--56 { top: -3.5rem; }
.s\:top--60 { top: -3.75rem; }
.s\:top--64 { top: -4rem; }
.s\:top--68 { top: -4.25rem; }
.s\:top--72 { top: -4.5rem; }
.s\:top--76 { top: -4.75rem; }
.s\:top--80 { top: -5rem; }
.s\:top--84 { top: -5.25rem; }
.s\:top--88 { top: -5.5rem; }
.s\:top--92 { top: -5.75rem; }
.s\:top--96 { top: -6rem; }
.s\:top--100 { top: -6.25rem; }
.s\:top--120 { top: -7.5rem; }
.s\:top--140 { top: -8.75rem; }
.s\:top--160 { top: -10rem; }
.s\:top--180 { top: -11.25rem; }
.s\:top--200 { top: -12.5rem; }
.s\:top--220 { top: -13.75rem; }
.s\:top--240 { top: -15rem; }
.s\:top--260 { top: -16.25rem; }
.s\:top--280 { top: -17.5rem; }
.s\:top--300 { top: -18.75rem; }
.s\:top--320 { top: -20rem; }
.s\:top--340 { top: -21.25rem; }
.s\:top--360 { top: -22.5rem; }
.s\:top--380 { top: -23.75rem; }
.s\:top--400 { top: -25rem; }

/* Right */
.s\:right-auto { right: auto; }
.s\:right-10-100 { right: 10%; }
.s\:right-20-100 { right: 20%; }
.s\:right-30-100 { right: 30%; }
.s\:right-40-100 { right: 40%; }
.s\:right-50-100 { right: 50%; }
.s\:right-60-100 { right: 60%; }
.s\:right-70-100 { right: 70%; }
.s\:right-80-100 { right: 80%; }
.s\:right-90-100 { right: 90%; }
.s\:right-100-100 { right: 100%; }

.s\:right-0 { right: 0; }
.s\:right-1 { right: 1px; }
.s\:right-4 { right: 0.25rem; }
.s\:right-6 { right: 0.375rem; }
.s\:right-8 { right: 0.5rem; }
.s\:right-10 { right: 0.625rem; }
.s\:right-12 { right: 0.75rem; }
.s\:right-14 { right: 0.875rem; }
.s\:right-16 { right: 1rem; }
.s\:right-18 { right: 1.125rem; }
.s\:right-20 { right: 1.25rem; }
.s\:right-22 { right: 1.375rem; }
.s\:right-24 { right: 1.5rem; }
.s\:right-28 { right: 1.75rem; }
.s\:right-32 { right: 2rem; }
.s\:right-36 { right: 2.25rem; }
.s\:right-40 { right: 2.5rem; }
.s\:right-44 { right: 2.75rem; }
.s\:right-48 { right: 3rem; }
.s\:right-52 { right: 3.25rem; }
.s\:right-56 { right: 3.5rem; }
.s\:right-60 { right: 3.75rem; }
.s\:right-64 { right: 4rem; }
.s\:right-68 { right: 4.25rem; }
.s\:right-72 { right: 4.5rem; }
.s\:right-76 { right: 4.75rem; }
.s\:right-80 { right: 5rem; }
.s\:right-84 { right: 5.25rem; }
.s\:right-88 { right: 5.5rem; }
.s\:right-92 { right: 5.75rem; }
.s\:right-96 { right: 6rem; }
.s\:right-100 { right: 6.25rem; }
.s\:right-120 { right: 7.5rem; }
.s\:right-140 { right: 8.75rem; }
.s\:right-160 { right: 10rem; }
.s\:right-180 { right: 11.25rem; }
.s\:right-200 { right: 12.5rem; }
.s\:right-220 { right: 13.75rem; }
.s\:right-240 { right: 15rem; }
.s\:right-260 { right: 16.25rem; }
.s\:right-280 { right: 17.5rem; }
.s\:right-300 { right: 18.75rem; }
.s\:right-320 { right: 20rem; }
.s\:right-340 { right: 21.25rem; }
.s\:right-360 { right: 22.5rem; }
.s\:right-380 { right: 23.75rem; }
.s\:right-400 { right: 25rem; }

/* Right Negative */
.s\:right--10-100 { right: -10%; }
.s\:right--20-100 { right: -20%; }
.s\:right--30-100 { right: -30%; }
.s\:right--40-100 { right: -40%; }
.s\:right--50-100 { right: -50%; }
.s\:right--60-100 { right: -60%; }
.s\:right--70-100 { right: -70%; }
.s\:right--80-100 { right: -80%; }
.s\:right--90-100 { right: -90%; }
.s\:right--100-100 { right: -100%; }

.s\:right--1 { right: -1px; }
.s\:right--4 { right: -0.25rem; }
.s\:right--6 { right: -0.375rem; }
.s\:right--8 { right: -0.5rem; }
.s\:right--10 { right: -0.625rem; }
.s\:right--12 { right: -0.75rem; }
.s\:right--14 { right: -0.875rem; }
.s\:right--16 { right: -1rem; }
.s\:right--18 { right: -1.125rem; }
.s\:right--20 { right: -1.25rem; }
.s\:right--22 { right: -1.375rem; }
.s\:right--24 { right: -1.5rem; }
.s\:right--28 { right: -1.75rem; }
.s\:right--32 { right: -2rem; }
.s\:right--36 { right: -2.25rem; }
.s\:right--40 { right: -2.5rem; }
.s\:right--44 { right: -2.75rem; }
.s\:right--48 { right: -3rem; }
.s\:right--52 { right: -3.25rem; }
.s\:right--56 { right: -3.5rem; }
.s\:right--60 { right: -3.75rem; }
.s\:right--64 { right: -4rem; }
.s\:right--68 { right: -4.25rem; }
.s\:right--72 { right: -4.5rem; }
.s\:right--76 { right: -4.75rem; }
.s\:right--80 { right: -5rem; }
.s\:right--84 { right: -5.25rem; }
.s\:right--88 { right: -5.5rem; }
.s\:right--92 { right: -5.75rem; }
.s\:right--96 { right: -6rem; }
.s\:right--100 { right: -6.25rem; }
.s\:right--120 { right: -7.5rem; }
.s\:right--140 { right: -8.75rem; }
.s\:right--160 { right: -10rem; }
.s\:right--180 { right: -11.25rem; }
.s\:right--200 { right: -12.5rem; }
.s\:right--220 { right: -13.75rem; }
.s\:right--240 { right: -15rem; }
.s\:right--260 { right: -16.25rem; }
.s\:right--280 { right: -17.5rem; }
.s\:right--300 { right: -18.75rem; }
.s\:right--320 { right: -20rem; }
.s\:right--340 { right: -21.25rem; }
.s\:right--360 { right: -22.5rem; }
.s\:right--380 { right: -23.75rem; }
.s\:right--400 { right: -25rem; }

/* Left */
.s\:left-auto { left: auto; }
.s\:left-10-100 { left: 10%; }
.s\:left-20-100 { left: 20%; }
.s\:left-30-100 { left: 30%; }
.s\:left-40-100 { left: 40%; }
.s\:left-50-100 { left: 50%; }
.s\:left-60-100 { left: 60%; }
.s\:left-70-100 { left: 70%; }
.s\:left-80-100 { left: 80%; }
.s\:left-90-100 { left: 90%; }
.s\:left-100-100 { left: 100%; }

.s\:left-0 { left: 0; }
.s\:left-1 { left: 1px; }
.s\:left-4 { left: 0.25rem; }
.s\:left-6 { left: 0.375rem; }
.s\:left-8 { left: 0.5rem; }
.s\:left-10 { left: 0.625rem; }
.s\:left-12 { left: 0.75rem; }
.s\:left-14 { left: 0.875rem; }
.s\:left-16 { left: 1rem; }
.s\:left-18 { left: 1.125rem; }
.s\:left-20 { left: 1.25rem; }
.s\:left-22 { left: 1.375rem; }
.s\:left-24 { left: 1.5rem; }
.s\:left-28 { left: 1.75rem; }
.s\:left-32 { left: 2rem; }
.s\:left-36 { left: 2.25rem; }
.s\:left-40 { left: 2.5rem; }
.s\:left-44 { left: 2.75rem; }
.s\:left-48 { left: 3rem; }
.s\:left-52 { left: 3.25rem; }
.s\:left-56 { left: 3.5rem; }
.s\:left-60 { left: 3.75rem; }
.s\:left-64 { left: 4rem; }
.s\:left-68 { left: 4.25rem; }
.s\:left-72 { left: 4.5rem; }
.s\:left-76 { left: 4.75rem; }
.s\:left-80 { left: 5rem; }
.s\:left-84 { left: 5.25rem; }
.s\:left-88 { left: 5.5rem; }
.s\:left-92 { left: 5.75rem; }
.s\:left-96 { left: 6rem; }
.s\:left-100 { left: 6.25rem; }
.s\:left-120 { left: 7.5rem; }
.s\:left-140 { left: 8.75rem; }
.s\:left-160 { left: 10rem; }
.s\:left-180 { left: 11.25rem; }
.s\:left-200 { left: 12.5rem; }
.s\:left-220 { left: 13.75rem; }
.s\:left-240 { left: 15rem; }
.s\:left-260 { left: 16.25rem; }
.s\:left-280 { left: 17.5rem; }
.s\:left-300 { left: 18.75rem; }
.s\:left-320 { left: 20rem; }
.s\:left-340 { left: 21.25rem; }
.s\:left-360 { left: 22.5rem; }
.s\:left-380 { left: 23.75rem; }
.s\:left-400 { left: 25rem; }

/* Left Negative */
.s\:left--10-100 { left: -10%; }
.s\:left--20-100 { left: -20%; }
.s\:left--30-100 { left: -30%; }
.s\:left--40-100 { left: -40%; }
.s\:left--50-100 { left: -50%; }
.s\:left--60-100 { left: -60%; }
.s\:left--70-100 { left: -70%; }
.s\:left--80-100 { left: -80%; }
.s\:left--90-100 { left: -90%; }
.s\:left--100-100 { left: -100%; }

.s\:left--1 { left: -1px; }
.s\:left--4 { left: -0.25rem; }
.s\:left--6 { left: -0.375rem; }
.s\:left--8 { left: -0.5rem; }
.s\:left--10 { left: -0.625rem; }
.s\:left--12 { left: -0.75rem; }
.s\:left--14 { left: -0.875rem; }
.s\:left--16 { left: -1rem; }
.s\:left--18 { left: -1.125rem; }
.s\:left--20 { left: -1.25rem; }
.s\:left--22 { left: -1.375rem; }
.s\:left--24 { left: -1.5rem; }
.s\:left--28 { left: -1.75rem; }
.s\:left--32 { left: -2rem; }
.s\:left--36 { left: -2.25rem; }
.s\:left--40 { left: -2.5rem; }
.s\:left--44 { left: -2.75rem; }
.s\:left--48 { left: -3rem; }
.s\:left--52 { left: -3.25rem; }
.s\:left--56 { left: -3.5rem; }
.s\:left--60 { left: -3.75rem; }
.s\:left--64 { left: -4rem; }
.s\:left--68 { left: -4.25rem; }
.s\:left--72 { left: -4.5rem; }
.s\:left--76 { left: -4.75rem; }
.s\:left--80 { left: -5rem; }
.s\:left--84 { left: -5.25rem; }
.s\:left--88 { left: -5.5rem; }
.s\:left--92 { left: -5.75rem; }
.s\:left--96 { left: -6rem; }
.s\:left--100 { left: -6.25rem; }
.s\:left--120 { left: -7.5rem; }
.s\:left--140 { left: -8.75rem; }
.s\:left--160 { left: -10rem; }
.s\:left--180 { left: -11.25rem; }
.s\:left--200 { left: -12.5rem; }
.s\:left--220 { left: -13.75rem; }
.s\:left--240 { left: -15rem; }
.s\:left--260 { left: -16.25rem; }
.s\:left--280 { left: -17.5rem; }
.s\:left--300 { left: -18.75rem; }
.s\:left--320 { left: -20rem; }
.s\:left--340 { left: -21.25rem; }
.s\:left--360 { left: -22.5rem; }
.s\:left--380 { left: -23.75rem; }
.s\:left--400 { left: -25rem; }


/*--------------------------------------------------------------

## Bottom

--------------------------------------------------------------*/

.bottom-auto {
  bottom: auto;
}

.bottom-10-100 {
  bottom: 10%;
}

.bottom-20-100 {
  bottom: 20%;
}

.bottom-30-100 {
  bottom: 30%;
}

.bottom-40-100 {
  bottom: 40%;
}

.bottom-50-100 {
  bottom: 50%;
}

.bottom-60-100 {
  bottom: 60%;
}

.bottom-70-100 {
  bottom: 70%;
}

.bottom-80-100 {
  bottom: 80%;
}

.bottom-90-100 {
  bottom: 90%;
}

.bottom-100-100 {
  bottom: 100%;
}

.bottom-0 {
  bottom: 0;
}

.bottom-1 {
  bottom: 1px;
}

.bottom-4 {
  bottom: calc((0.25rem * var(--responsive-70)) * (var(--bottom-4, 1)));
}

.bottom-6 {
  bottom: calc((0.375rem * var(--responsive-70)) * (var(--bottom-6, 1)));
}

.bottom-8 {
  bottom: calc((0.5rem * var(--responsive-70)) * (var(--bottom-8, 1)));
}

.bottom-10 {
  bottom: calc((0.625rem * var(--responsive-70)) * (var(--bottom-10, 1)));
}

.bottom-12 {
  bottom: calc((0.75rem * var(--responsive-70)) * (var(--bottom-12, 1)));
}

.bottom-14 {
  bottom: calc((0.875rem * var(--responsive-70)) * (var(--bottom-14, 1)));
}

.bottom-16 {
  bottom: calc((1rem * var(--responsive-70)) * (var(--bottom-16, 1)));
}

.bottom-18 {
  bottom: calc((1.125rem * var(--responsive-70)) * (var(--bottom-18, 1)));
}

.bottom-20 {
  bottom: calc((1.25rem * var(--responsive-70)) * (var(--bottom-20, 1)));
}

.bottom-22 {
  bottom: calc((1.375rem * var(--responsive-70)) * (var(--bottom-22, 1)));
}

.bottom-24 {
  bottom: calc((1.5rem * var(--responsive-70)) * (var(--bottom-24, 1)));
}

.bottom-28 {
  bottom: calc((1.75rem * var(--responsive-70)) * (var(--bottom-28, 1)));
}

.bottom-32 {
  bottom: calc((2rem * var(--responsive-70)) * (var(--bottom-32, 1)));
}

.bottom-36 {
  bottom: calc((2.25rem * var(--responsive-70)) * (var(--bottom-36, 1)));
}

.bottom-40 {
  bottom: calc((2.5rem * var(--responsive-60)) * (var(--bottom-40, 1)));
}

.bottom-44 {
  bottom: calc((2.75rem * var(--responsive-60)) * (var(--bottom-44, 1)));
}

.bottom-48 {
  bottom: calc((3rem * var(--responsive-60)) * (var(--bottom-48, 1)));
}

.bottom-52 {
  bottom: calc((3.25rem * var(--responsive-60)) * (var(--bottom-52, 1)));
}

.bottom-56 {
  bottom: calc((3.5rem * var(--responsive-60)) * (var(--bottom-56, 1)));
}

.bottom-60 {
  bottom: calc((3.75rem * var(--responsive-50)) * (var(--bottom-60, 1)));
}

.bottom-64 {
  bottom: calc((4rem * var(--responsive-50)) * (var(--bottom-64, 1)));
}

.bottom-68 {
  bottom: calc((4.25rem * var(--responsive-50)) * (var(--bottom-68, 1)));
}

.bottom-72 {
  bottom: calc((4.5rem * var(--responsive-50)) * (var(--bottom-72, 1)));
}

.bottom-76 {
  bottom: calc((4.75rem * var(--responsive-50)) * (var(--bottom-76, 1)));
}

.bottom-80 {
  bottom: calc((5rem * var(--responsive-40)) * (var(--bottom-80, 1)));
}

.bottom-84 {
  bottom: calc((5.25rem * var(--responsive-40)) * (var(--bottom-84, 1)));
}

.bottom-88 {
  bottom: calc((5.5rem * var(--responsive-40)) * (var(--bottom-88, 1)));
}

.bottom-92 {
  bottom: calc((5.75rem * var(--responsive-40)) * (var(--bottom-92, 1)));
}

.bottom-96 {
  bottom: calc((6rem * var(--responsive-40)) * (var(--bottom-96, 1)));
}

.bottom-100 {
  bottom: calc((6.25rem * var(--responsive-30)) * (var(--bottom-100, 1)));
}

.bottom-120 {
  bottom: calc((7.5rem * var(--responsive-30)) * (var(--bottom-120, 1)));
}

.bottom-140 {
  bottom: calc((8.75rem * var(--responsive-30)) * (var(--bottom-140, 1)));
}

.bottom-160 {
  bottom: calc((10rem * var(--responsive-30)) * (var(--bottom-160, 1)));
}

.bottom-180 {
  bottom: calc((11.25rem * var(--responsive-30)) * (var(--bottom-180, 1)));
}

.bottom-200 {
  bottom: calc((12.5rem * var(--responsive-30)) * (var(--bottom-200, 1)));
}

.bottom-220 {
  bottom: calc((13.75rem * var(--responsive-70)) * (var(--bottom-220, 1)));
}

.bottom-240 {
  bottom: calc((15rem * var(--responsive-70)) * (var(--bottom-240, 1)));
}

.bottom-260 {
  bottom: calc((16.25rem * var(--responsive-70)) * (var(--bottom-260, 1)));
}

.bottom-280 {
  bottom: calc((17.5rem * var(--responsive-70)) * (var(--bottom-280, 1)));
}

.bottom-300 {
  bottom: calc((18.75rem * var(--responsive-70)) * (var(--bottom-300, 1)));
}

.bottom-320 {
  bottom: calc((20rem * var(--responsive-70)) * (var(--bottom-320, 1)));
}

.bottom-340 {
  bottom: calc((21.25rem * var(--responsive-70)) * (var(--bottom-340, 1)));
}

.bottom-360 {
  bottom: calc((22.5rem * var(--responsive-70)) * (var(--bottom-360, 1)));
}

.bottom-380 {
  bottom: calc((23.75rem * var(--responsive-70)) * (var(--bottom-380, 1)));
}

.bottom-400 {
  bottom: calc((25rem * var(--responsive-70)) * (var(--bottom-400, 1)));
}

.bottom-420 {
  bottom: calc((26.25rem * var(--responsive-70)) * (var(--bottom-420, 1)));
}

.bottom-440 {
  bottom: calc((27.5rem * var(--responsive-70)) * (var(--bottom-440, 1)));
}

.bottom-460 {
  bottom: calc((28.75rem * var(--responsive-70)) * (var(--bottom-460, 1)));
}

.bottom-480 {
  bottom: calc((30rem * var(--responsive-70)) * (var(--bottom-480, 1)));
}

.bottom-500 {
  bottom: calc((31.25rem * var(--responsive-70)) * (var(--bottom-500, 1)));
}

.bottom-520 {
  bottom: calc((32.5rem * var(--responsive-70)) * (var(--bottom-520, 1)));
}

.bottom-540 {
  bottom: calc((33.75rem * var(--responsive-70)) * (var(--bottom-540, 1)));
}

.bottom-560 {
  bottom: calc((35rem * var(--responsive-70)) * (var(--bottom-560, 1)));
}

.bottom-580 {
  bottom: calc((36.25rem * var(--responsive-70)) * (var(--bottom-580, 1)));
}

.bottom-600 {
  bottom: calc((37.5rem * var(--responsive-70)) * (var(--bottom-600, 1)));
}

.bottom-620 {
  bottom: calc((38.75rem * var(--responsive-70)) * (var(--bottom-620, 1)));
}

.bottom-640 {
  bottom: calc((40rem * var(--responsive-70)) * (var(--bottom-640, 1)));
}

.bottom-660 {
  bottom: calc((41.25rem * var(--responsive-70)) * (var(--bottom-660, 1)));
}

.bottom-680 {
  bottom: calc((42.5rem * var(--responsive-70)) * (var(--bottom-680, 1)));
}

.bottom-700 {
  bottom: calc((43.75rem * var(--responsive-70)) * (var(--bottom-700, 1)));
}

.bottom-720 {
  bottom: calc((45rem * var(--responsive-70)) * (var(--bottom-720, 1)));
}

.bottom-740 {
  bottom: calc((46.25rem * var(--responsive-70)) * (var(--bottom-740, 1)));
}

.bottom-760 {
  bottom: calc((47.5rem * var(--responsive-70)) * (var(--bottom-760, 1)));
}

.bottom-780 {
  bottom: calc((48.75rem * var(--responsive-70)) * (var(--bottom-780, 1)));
}

.bottom-800 {
  bottom: calc((50rem * var(--responsive-70)) * (var(--bottom-800, 1)));
}

.bottom-900 {
  bottom: calc((56.25rem * var(--responsive-70)) * (var(--bottom-900, 1)));
}

.bottom-1000 {
  bottom: calc((62.5rem * var(--responsive-70)) * (var(--bottom-1000, 1)));
}

.bottom-1100 {
  bottom: calc((68.75rem * var(--responsive-70)) * (var(--bottom-1100, 1)));
}

.bottom-1200 {
  bottom: calc((75rem * var(--responsive-70)) * (var(--bottom-1200, 1)));
}



.bottom--10-100 {
  bottom: -10%;
}

.bottom--20-100 {
  bottom: -20%;
}

.bottom--30-100 {
  bottom: -30%;
}

.bottom--40-100 {
  bottom: -40%;
}

.bottom--50-100 {
  bottom: -50%;
}

.bottom--60-100 {
  bottom: -60%;
}

.bottom--70-100 {
  bottom: -70%;
}

.bottom--80-100 {
  bottom: -80%;
}

.bottom--90-100 {
  bottom: -90%;
}

.bottom--100-100 {
  bottom: -100%;
}

.bottom--1 {
  bottom: -1px;
}

.bottom--4 {
  bottom: calc((-0.25rem * var(--responsive-70)) * (var(--bottom--4, 1)));
}

.bottom--6 {
  bottom: calc((-0.375rem * var(--responsive-70)) * (var(--bottom--6, 1)));
}

.bottom--8 {
  bottom: calc((-0.5rem * var(--responsive-70)) * (var(--bottom--8, 1)));
}

.bottom--10 {
  bottom: calc((-0.625rem * var(--responsive-70)) * (var(--bottom--10, 1)));
}

.bottom--12 {
  bottom: calc((-0.75rem * var(--responsive-70)) * (var(--bottom--12, 1)));
}

.bottom--14 {
  bottom: calc((-0.875rem * var(--responsive-70)) * (var(--bottom--14, 1)));
}

.bottom--16 {
  bottom: calc((-1rem * var(--responsive-70)) * (var(--bottom--16, 1)));
}

.bottom--18 {
  bottom: calc((-1.125rem * var(--responsive-70)) * (var(--bottom--18, 1)));
}

.bottom--20 {
  bottom: calc((-1.25rem * var(--responsive-70)) * (var(--bottom--20, 1)));
}

.bottom--22 {
  bottom: calc((-1.375rem * var(--responsive-70)) * (var(--bottom--22, 1)));
}

.bottom--24 {
  bottom: calc((-1.5rem * var(--responsive-70)) * (var(--bottom--24, 1)));
}

.bottom--28 {
  bottom: calc((-1.75rem * var(--responsive-70)) * (var(--bottom--28, 1)));
}

.bottom--32 {
  bottom: calc((-2rem * var(--responsive-70)) * (var(--bottom--32, 1)));
}

.bottom--36 {
  bottom: calc((-2.25rem * var(--responsive-70)) * (var(--bottom--36, 1)));
}

.bottom--40 {
  bottom: calc((-2.5rem * var(--responsive-60)) * (var(--bottom--40, 1)));
}

.bottom--44 {
  bottom: calc((-2.75rem * var(--responsive-60)) * (var(--bottom--44, 1)));
}

.bottom--48 {
  bottom: calc((-3rem * var(--responsive-60)) * (var(--bottom--48, 1)));
}

.bottom--52 {
  bottom: calc((-3.25rem * var(--responsive-60)) * (var(--bottom--52, 1)));
}

.bottom--56 {
  bottom: calc((-3.5rem * var(--responsive-60)) * (var(--bottom--56, 1)));
}

.bottom--60 {
  bottom: calc((-3.75rem * var(--responsive-50)) * (var(--bottom--60, 1)));
}

.bottom--64 {
  bottom: calc((-4rem * var(--responsive-50)) * (var(--bottom--64, 1)));
}

.bottom--68 {
  bottom: calc((-4.25rem * var(--responsive-50)) * (var(--bottom--68, 1)));
}

.bottom--72 {
  bottom: calc((-4.5rem * var(--responsive-50)) * (var(--bottom--72, 1)));
}

.bottom--76 {
  bottom: calc((-4.75rem * var(--responsive-50)) * (var(--bottom--76, 1)));
}

.bottom--80 {
  bottom: calc((-5rem * var(--responsive-40)) * (var(--bottom--80, 1)));
}

.bottom--84 {
  bottom: calc((-5.25rem * var(--responsive-40)) * (var(--bottom--84, 1)));
}

.bottom--88 {
  bottom: calc((-5.5rem * var(--responsive-40)) * (var(--bottom--88, 1)));
}

.bottom--92 {
  bottom: calc((-5.75rem * var(--responsive-40)) * (var(--bottom--92, 1)));
}

.bottom--96 {
  bottom: calc((-6rem * var(--responsive-40)) * (var(--bottom--96, 1)));
}

.bottom--100 {
  bottom: calc((-6.25rem * var(--responsive-30)) * (var(--bottom--100, 1)));
}

.bottom--120 {
  bottom: calc((-7.5rem * var(--responsive-30)) * (var(--bottom--120, 1)));
}

.bottom--140 {
  bottom: calc((-8.75rem * var(--responsive-30)) * (var(--bottom--140, 1)));
}

.bottom--160 {
  bottom: calc((-10rem * var(--responsive-30)) * (var(--bottom--160, 1)));
}

.bottom--180 {
  bottom: calc((-11.25rem * var(--responsive-30)) * (var(--bottom--180, 1)));
}

.bottom--200 {
  bottom: calc((-12.5rem * var(--responsive-30)) * (var(--bottom--200, 1)));
}

.bottom--220 {
  bottom: calc((-13.75rem * var(--responsive-70)) * (var(--bottom--220, 1)));
}

.bottom--240 {
  bottom: calc((-15rem * var(--responsive-70)) * (var(--bottom--240, 1)));
}

.bottom--260 {
  bottom: calc((-16.25rem * var(--responsive-70)) * (var(--bottom--260, 1)));
}

.bottom--280 {
  bottom: calc((-17.5rem * var(--responsive-70)) * (var(--bottom--280, 1)));
}

.bottom--300 {
  bottom: calc((-18.75rem * var(--responsive-70)) * (var(--bottom--300, 1)));
}

.bottom--320 {
  bottom: calc((-20rem * var(--responsive-70)) * (var(--bottom--320, 1)));
}

.bottom--340 {
  bottom: calc((-21.25rem * var(--responsive-70)) * (var(--bottom--340, 1)));
}

.bottom--360 {
  bottom: calc((-22.5rem * var(--responsive-70)) * (var(--bottom--360, 1)));
}

.bottom--380 {
  bottom: calc((-23.75rem * var(--responsive-70)) * (var(--bottom--380, 1)));
}

.bottom--400 {
  bottom: calc((-25rem * var(--responsive-70)) * (var(--bottom--400, 1)));
}

.bottom--420 {
  bottom: calc((-26.25rem * var(--responsive-70)) * (var(--bottom--420, 1)));
}

.bottom--440 {
  bottom: calc((-27.5rem * var(--responsive-70)) * (var(--bottom--440, 1)));
}

.bottom--460 {
  bottom: calc((-28.75rem * var(--responsive-70)) * (var(--bottom--460, 1)));
}

.bottom--480 {
  bottom: calc((-30rem * var(--responsive-70)) * (var(--bottom--480, 1)));
}

.bottom--500 {
  bottom: calc((-31.25rem * var(--responsive-70)) * (var(--bottom--500, 1)));
}

.bottom--520 {
  bottom: calc((-32.5rem * var(--responsive-70)) * (var(--bottom--520, 1)));
}

.bottom--540 {
  bottom: calc((-33.75rem * var(--responsive-70)) * (var(--bottom--540, 1)));
}

.bottom--560 {
  bottom: calc((-35rem * var(--responsive-70)) * (var(--bottom--560, 1)));
}

.bottom--580 {
  bottom: calc((-36.25rem * var(--responsive-70)) * (var(--bottom--580, 1)));
}

.bottom--600 {
  bottom: calc((-37.5rem * var(--responsive-70)) * (var(--bottom--600, 1)));
}

.bottom--620 {
  bottom: calc((-38.75rem * var(--responsive-70)) * (var(--bottom--620, 1)));
}

.bottom--640 {
  bottom: calc((-40rem * var(--responsive-70)) * (var(--bottom--640, 1)));
}

.bottom--660 {
  bottom: calc((-41.25rem * var(--responsive-70)) * (var(--bottom--660, 1)));
}

.bottom--680 {
  bottom: calc((-42.5rem * var(--responsive-70)) * (var(--bottom--680, 1)));
}

.bottom--700 {
  bottom: calc((-43.75rem * var(--responsive-70)) * (var(--bottom--700, 1)));
}

.bottom--720 {
  bottom: calc((-45rem * var(--responsive-70)) * (var(--bottom--720, 1)));
}

.bottom--740 {
  bottom: calc((-46.25rem * var(--responsive-70)) * (var(--bottom--740, 1)));
}

.bottom--760 {
  bottom: calc((-47.5rem * var(--responsive-70)) * (var(--bottom--760, 1)));
}

.bottom--780 {
  bottom: calc((-48.75rem * var(--responsive-70)) * (var(--bottom--780, 1)));
}

.bottom--800 {
  bottom: calc((-50rem * var(--responsive-70)) * (var(--bottom--800, 1)));
}

.bottom--900 {
  bottom: calc((-56.25rem * var(--responsive-70)) * (var(--bottom--900, 1)));
}

.bottom--1000 {
  bottom: calc((-62.5rem * var(--responsive-70)) * (var(--bottom--1000, 1)));
}

.bottom--1100 {
  bottom: calc((-68.75rem * var(--responsive-70)) * (var(--bottom--1100, 1)));
}

.bottom--1200 {
  bottom: calc((-75rem * var(--responsive-70)) * (var(--bottom--1200, 1)));
}


/*--------------------------------------------------------------

## Margin

--------------------------------------------------------------*/

.m-auto {
  margin: auto;
}

.mx-auto {
  margin-left: auto;
  margin-right: auto;
}

.my-auto {
  margin-top: auto;
  margin-bottom: auto;
}

.ml-auto {
  margin-left: auto;
}

.mr-auto {
  margin-right: auto;
}

.mt-auto {
  margin-top: auto;
}

.mb-auto {
  margin-bottom: auto;
}

/*--------------------------------------------------------------

## Margin Bottom

--------------------------------------------------------------*/

.mb-0 {
  margin-bottom: 0px;
}

.mb-1 {
  margin-bottom: 1px;
}

.mb-2 {
  margin-bottom: 0.125rem;
}

.mb-4 {
  margin-bottom: calc((0.25rem * var(--responsive-70)) * (var(--mb-4, 1)));
}

.mb-6 {
  margin-bottom: calc((0.375rem * var(--responsive-70)) * (var(--mb-6, 1)));
}

.mb-8 {
  margin-bottom: calc((0.5rem * var(--responsive-70)) * (var(--mb-8, 1)));
}

.mb-10 {
  margin-bottom: calc((0.625rem * var(--responsive-70)) * (var(--mb-10, 1)));
}

.mb-12 {
  margin-bottom: calc((0.75rem * var(--responsive-70)) * (var(--mb-12, 1)));
}

.mb-14 {
  margin-bottom: calc((0.875rem * var(--responsive-70)) * (var(--mb-14, 1)));
}

.mb-16 {
  margin-bottom: calc((1rem * var(--responsive-70)) * (var(--mb-16, 1)));
}

.mb-18 {
  margin-bottom: calc((1.125rem * var(--responsive-70)) * (var(--mb-18, 1)));
}

.mb-20 {
  margin-bottom: calc((1.25rem * var(--responsive-70)) * (var(--mb-20, 1)));
} 

.mb-22 {
  margin-bottom: calc((1.375rem * var(--responsive-70)) * (var(--mb-22, 1)));
}

.mb-24 {
  margin-bottom: calc((1.5rem * var(--responsive-70)) * (var(--mb-24, 1)));
}

.mb-28 {
  margin-bottom: calc((1.75rem * var(--responsive-70)) * (var(--mb-28, 1)));
}

.mb-32 {
  margin-bottom: calc((2rem * var(--responsive-70)) * (var(--mb-32, 1)));
}

.mb-36 {
  margin-bottom: calc((2.25rem * var(--responsive-70)) * (var(--mb-36, 1)));
}

.mb-40 {
  margin-bottom: calc((2.5rem * var(--responsive-60)) * (var(--mb-40, 1)));
}

.mb-44 {
  margin-bottom: calc((2.75rem * var(--responsive-60)) * (var(--mb-44, 1)));
}

.mb-48 {
  margin-bottom: calc((3rem * var(--responsive-60)) * (var(--mb-48, 1)));
}

.mb-52 {
  margin-bottom: calc((3.25rem * var(--responsive-60)) * (var(--mb-52, 1)));
}

.mb-56 {
  margin-bottom: calc((3.5rem * var(--responsive-60)) * (var(--mb-56, 1)));
}

.mb-60 {
  margin-bottom: calc((3.75rem * var(--responsive-50)) * (var(--mb-60, 1)));
}

.mb-64 {
  margin-bottom: calc((4rem * var(--responsive-50)) * (var(--mb-64, 1)));
}

.mb-68 {
  margin-bottom: calc((4.25rem * var(--responsive-50)) * (var(--mb-68, 1)));
}

.mb-72 {
  margin-bottom: calc((4.5rem * var(--responsive-50)) * (var(--mb-72, 1)));
}

.mb-76 {
  margin-bottom: calc((4.75rem * var(--responsive-50)) * (var(--mb-76, 1)));
}

.mb-80 {
  margin-bottom: calc((5rem * var(--responsive-40)) * (var(--mb-80, 1)));
}

.mb-84 {
  margin-bottom: calc((5.25rem * var(--responsive-40)) * (var(--mb-84, 1)));
}

.mb-88 {
  margin-bottom: calc((5.5rem * var(--responsive-40)) * (var(--mb-88, 1)));
}

.mb-92 {
  margin-bottom: calc((5.75rem * var(--responsive-40)) * (var(--mb-92, 1)));
}

.mb-96 {
  margin-bottom: calc((6rem * var(--responsive-40)) * (var(--mb-96, 1)));
}

.mb-100 {
  margin-bottom: calc((6.25rem * var(--responsive-30)) * (var(--mb-100, 1)));
}

.mb-120 {
  margin-bottom: calc((7.5rem * var(--responsive-30)) * (var(--mb-120, 1)));
}

.mb-140 {
  margin-bottom: calc((8.75rem * var(--responsive-30)) * (var(--mb-140, 1)));
}

.mb-160 {
  margin-bottom: calc((10rem * var(--responsive-30)) * (var(--mb-160, 1)));
}

.mb-180 {
  margin-bottom: calc((11.25rem * var(--responsive-30)) * (var(--mb-180, 1)));
}

.mb-200 {
  margin-bottom: calc((12.5rem * var(--responsive-30)) * (var(--mb-200, 1)));
}

.mb-220 {
  margin-bottom: calc((13.75rem * var(--responsive-70)) * (var(--mb-220, 1)));
}

.mb-240 {
  margin-bottom: calc((15rem * var(--responsive-70)) * (var(--mb-240, 1)));
}

.mb-260 {
  margin-bottom: calc((16.25rem * var(--responsive-70)) * (var(--mb-260, 1)));
}

.mb-280 {
  margin-bottom: calc((17.5rem * var(--responsive-70)) * (var(--mb-280, 1)));
}

.mb-300 {
  margin-bottom: calc((18.75rem * var(--responsive-70)) * (var(--mb-300, 1)));
}

.mb-320 {
  margin-bottom: calc((20rem * var(--responsive-70)) * (var(--mb-320, 1)));
}

.mb-340 {
  margin-bottom: calc((21.25rem * var(--responsive-70)) * (var(--mb-340, 1)));
}

.mb-360 {
  margin-bottom: calc((22.5rem * var(--responsive-70)) * (var(--mb-360, 1)));
}

.mb-380 {
  margin-bottom: calc((23.75rem * var(--responsive-70)) * (var(--mb-380, 1)));
}

.mb-400 {
  margin-bottom: calc((25rem * var(--responsive-70)) * (var(--mb-400, 1)));
}

/*--------------------------------------------------------------

## Margin Top

--------------------------------------------------------------*/

.mt-0 {
  margin-top: 0px;
}

.mt-1 {
  margin-top: 1px;
}

.mt-2 {
  margin-top: 0.125rem;
}

.mt-4 {
  margin-top: calc((0.25rem * var(--responsive-70)) * (var(--mt-4, 1)));
}

.mt-6 {
  margin-top: calc((0.375rem * var(--responsive-70)) * (var(--mt-6, 1)));
}

.mt-8 {
  margin-top: calc((0.5rem * var(--responsive-70)) * (var(--mt-8, 1)));
}

.mt-10 {
  margin-top: calc((0.625rem * var(--responsive-70)) * (var(--mt-10, 1)));
}

.mt-12 {
  margin-top: calc((0.75rem * var(--responsive-70)) * (var(--mt-12, 1)));
}

.mt-14 {
  margin-top: calc((0.875rem * var(--responsive-70)) * (var(--mt-14, 1)));
}

.mt-16 {
  margin-top: calc((1rem * var(--responsive-70)) * (var(--mt-16, 1)));
}

.mt-18 {
  margin-top: calc((1.125rem * var(--responsive-70)) * (var(--mt-18, 1)));
}

.mt-20 {
  margin-top: calc((1.25rem * var(--responsive-70)) * (var(--mt-20, 1)));
}

.mt-22 {
  margin-top: calc((1.375rem * var(--responsive-70)) * (var(--mt-22, 1)));
}

.mt-24 {
  margin-top: calc((1.5rem * var(--responsive-70)) * (var(--mt-24, 1)));
}

.mt-28 {
  margin-top: calc((1.75rem * var(--responsive-70)) * (var(--mt-28, 1)));
}

.mt-32 {
  margin-top: calc((2rem * var(--responsive-70)) * (var(--mt-32, 1)));
}

.mt-36 {
  margin-top: calc((2.25rem * var(--responsive-70)) * (var(--mt-36, 1)));
}

.mt-40 {
  margin-top: calc((2.5rem * var(--responsive-60)) * (var(--mt-40, 1)));
}

.mt-44 {
  margin-top: calc((2.75rem * var(--responsive-60)) * (var(--mt-44, 1)));
}

.mt-48 {
  margin-top: calc((3rem * var(--responsive-60)) * (var(--mt-48, 1)));
}

.mt-52 {
  margin-top: calc((3.25rem * var(--responsive-60)) * (var(--mt-52, 1)));
}

.mt-56 {
  margin-top: calc((3.5rem * var(--responsive-60)) * (var(--mt-56, 1)));
}

.mt-60 {
  margin-top: calc((3.75rem * var(--responsive-50)) * (var(--mt-60, 1)));
}

.mt-64 {
  margin-top: calc((4rem * var(--responsive-50)) * (var(--mt-64, 1)));
}

.mt-68 {
  margin-top: calc((4.25rem * var(--responsive-50)) * (var(--mt-68, 1)));
}

.mt-72 {
  margin-top: calc((4.5rem * var(--responsive-50)) * (var(--mt-72, 1)));
}

.mt-76 {
  margin-top: calc((4.75rem * var(--responsive-50)) * (var(--mt-76, 1)));
}

.mt-80 {
  margin-top: calc((5rem * var(--responsive-40)) * (var(--mt-80, 1)));
}

.mt-84 {
  margin-top: calc((5.25rem * var(--responsive-40)) * (var(--mt-84, 1)));
}

.mt-88 {
  margin-top: calc((5.5rem * var(--responsive-40)) * (var(--mt-88, 1)));
}

.mt-92 {
  margin-top: calc((5.75rem * var(--responsive-40)) * (var(--mt-92, 1)));
}

.mt-96 {
  margin-top: calc((6rem * var(--responsive-40)) * (var(--mt-96, 1)));
}

.mt-100 {
  margin-top: calc((6.25rem * var(--responsive-30)) * (var(--mt-100, 1)));
}

.mt-120 {
  margin-top: calc((7.5rem * var(--responsive-30)) * (var(--mt-120, 1)));
}

.mt-140 {
  margin-top: calc((8.75rem * var(--responsive-30)) * (var(--mt-140, 1)));
}

.mt-160 {
  margin-top: calc((10rem * var(--responsive-30)) * (var(--mt-160, 1)));
}

.mt-180 {
  margin-top: calc((11.25rem * var(--responsive-30)) * (var(--mt-180, 1)));
}

.mt-200 {
  margin-top: calc((12.5rem * var(--responsive-30)) * (var(--mt-200, 1)));
}

.mt-220 {
  margin-top: calc((13.75rem * var(--responsive-70)) * (var(--mt-220, 1)));
}

.mt-240 {
  margin-top: calc((15rem * var(--responsive-70)) * (var(--mt-240, 1)));
}

.mt-260 {
  margin-top: calc((16.25rem * var(--responsive-70)) * (var(--mt-260, 1)));
}

.mt-280 {
  margin-top: calc((17.5rem * var(--responsive-70)) * (var(--mt-280, 1)));
}

.mt-300 {
  margin-top: calc((18.75rem * var(--responsive-70)) * (var(--mt-300, 1)));
}

.mt-320 {
  margin-top: calc((20rem * var(--responsive-70)) * (var(--mt-320, 1)));
}

.mt-340 {
  margin-top: calc((21.25rem * var(--responsive-70)) * (var(--mt-340, 1)));
}

.mt-360 {
  margin-top: calc((22.5rem * var(--responsive-70)) * (var(--mt-360, 1)));
}

.mt-380 {
  margin-top: calc((23.75rem * var(--responsive-70)) * (var(--mt-380, 1)));
}

.mt-400 {
  margin-top: calc((25rem * var(--responsive-70)) * (var(--mt-400, 1)));
}

/*--------------------------------------------------------------

## Margin Left

--------------------------------------------------------------*/

.ml-0 {
  margin-left: 0px;
}

.ml-1 {
  margin-left: 1px;
}

.ml-2 {
  margin-left: 0.125rem;
}

.ml-4 {
  margin-left: calc((0.25rem * var(--responsive-70)) * (var(--ml-4, 1)));
}

.ml-6 {
  margin-left: calc((0.375rem * var(--responsive-70)) * (var(--ml-6, 1)));
}

.ml-8 {
  margin-left: calc((0.5rem * var(--responsive-70)) * (var(--ml-8, 1)));
}

.ml-10 {
  margin-left: calc((0.625rem * var(--responsive-70)) * (var(--ml-10, 1)));
}

.ml-12 {
  margin-left: calc((0.75rem * var(--responsive-70)) * (var(--ml-12, 1)));
}

.ml-14 {
  margin-left: calc((0.875rem * var(--responsive-70)) * (var(--ml-14, 1)));
}

.ml-16 {
  margin-left: calc((1rem * var(--responsive-70)) * (var(--ml-16, 1)));
}

.ml-18 {
  margin-left: calc((1.125rem * var(--responsive-70)) * (var(--ml-18, 1)));
}

.ml-20 {
  margin-left: calc((1.25rem * var(--responsive-70)) * (var(--ml-20, 1)));
}

.ml-22 {
  margin-left: calc((1.375rem * var(--responsive-70)) * (var(--ml-22, 1)));
}

.ml-24 {
  margin-left: calc((1.5rem * var(--responsive-70)) * (var(--ml-24, 1)));
}

.ml-28 {
  margin-left: calc((1.75rem * var(--responsive-70)) * (var(--ml-28, 1)));
}

.ml-32 {
  margin-left: calc((2rem * var(--responsive-70)) * (var(--ml-32, 1)));
}

.ml-36 {
  margin-left: calc((2.25rem * var(--responsive-70)) * (var(--ml-36, 1)));
}

.ml-40 {
  margin-left: calc((2.5rem * var(--responsive-60)) * (var(--ml-40, 1)));
}

.ml-44 {
  margin-left: calc((2.75rem * var(--responsive-60)) * (var(--ml-44, 1)));
}

.ml-48 {
  margin-left: calc((3rem * var(--responsive-60)) * (var(--ml-48, 1)));
}

.ml-52 {
  margin-left: calc((3.25rem * var(--responsive-60)) * (var(--ml-52, 1)));
}

.ml-56 {
  margin-left: calc((3.5rem * var(--responsive-60)) * (var(--ml-56, 1)));
}

.ml-60 {
  margin-left: calc((3.75rem * var(--responsive-50)) * (var(--ml-60, 1)));
}

.ml-64 {
  margin-left: calc((4rem * var(--responsive-50)) * (var(--ml-64, 1)));
}

.ml-68 {
  margin-left: calc((4.25rem * var(--responsive-50)) * (var(--ml-68, 1)));
}

.ml-72 {
  margin-left: calc((4.5rem * var(--responsive-50)) * (var(--ml-72, 1)));
}

.ml-76 {
  margin-left: calc((4.75rem * var(--responsive-50)) * (var(--ml-76, 1)));
}

.ml-80 {
  margin-left: calc((5rem * var(--responsive-40)) * (var(--ml-80, 1)));
}

.ml-84 {
  margin-left: calc((5.25rem * var(--responsive-40)) * (var(--ml-84, 1)));
}

.ml-88 {
  margin-left: calc((5.5rem * var(--responsive-40)) * (var(--ml-88, 1)));
}

.ml-92 {
  margin-left: calc((5.75rem * var(--responsive-40)) * (var(--ml-92, 1)));
}

.ml-96 {
  margin-left: calc((6rem * var(--responsive-40)) * (var(--ml-96, 1)));
}

.ml-100 {
  margin-left: calc((6.25rem * var(--responsive-30)) * (var(--ml-100, 1)));
}

.ml-120 {
  margin-left: calc((7.5rem * var(--responsive-30)) * (var(--ml-120, 1)));
}

.ml-140 {
  margin-left: calc((8.75rem * var(--responsive-30)) * (var(--ml-140, 1)));
}

.ml-160 {
  margin-left: calc((10rem * var(--responsive-30)) * (var(--ml-160, 1)));
}

.ml-180 {
  margin-left: calc((11.25rem * var(--responsive-30)) * (var(--ml-180, 1)));
}

.ml-200 {
  margin-left: calc((12.5rem * var(--responsive-30)) * (var(--ml-200, 1)));
}

.ml-220 {
  margin-left: calc((13.75rem * var(--responsive-70)) * (var(--ml-220, 1)));
}

.ml-240 {
  margin-left: calc((15rem * var(--responsive-70)) * (var(--ml-240, 1)));
}

.ml-260 {
  margin-left: calc((16.25rem * var(--responsive-70)) * (var(--ml-260, 1)));
}

.ml-280 {
  margin-left: calc((17.5rem * var(--responsive-70)) * (var(--ml-280, 1)));
}

.ml-300 {
  margin-left: calc((18.75rem * var(--responsive-70)) * (var(--ml-300, 1)));
}

.ml-320 {
  margin-left: calc((20rem * var(--responsive-70)) * (var(--ml-320, 1)));
}

.ml-340 {
  margin-left: calc((21.25rem * var(--responsive-70)) * (var(--ml-340, 1)));
}

.ml-360 {
  margin-left: calc((22.5rem * var(--responsive-70)) * (var(--ml-360, 1)));
}

.ml-380 {
  margin-left: calc((23.75rem * var(--responsive-70)) * (var(--ml-380, 1)));
}

.ml-400 {
  margin-left: calc((25rem * var(--responsive-70)) * (var(--ml-400, 1)));
}

/* Bottom Position */
.s\:bottom-4 { bottom: 0.25rem; }
.s\:bottom-6 { bottom: 0.375rem; }
.s\:bottom-8 { bottom: 0.5rem; }
.s\:bottom-10 { bottom: 0.625rem; }
.s\:bottom-12 { bottom: 0.75rem; }
.s\:bottom-14 { bottom: 0.875rem; }
.s\:bottom-16 { bottom: 1rem; }
.s\:bottom-18 { bottom: 1.125rem; }
.s\:bottom-20 { bottom: 1.25rem; }
.s\:bottom-22 { bottom: 1.375rem; }
.s\:bottom-24 { bottom: 1.5rem; }
.s\:bottom-28 { bottom: 1.75rem; }
.s\:bottom-32 { bottom: 2rem; }
.s\:bottom-36 { bottom: 2.25rem; }
.s\:bottom-40 { bottom: 2.5rem; }
.s\:bottom-44 { bottom: 2.75rem; }
.s\:bottom-48 { bottom: 3rem; }
.s\:bottom-52 { bottom: 3.25rem; }
.s\:bottom-56 { bottom: 3.5rem; }
.s\:bottom-60 { bottom: 3.75rem; }
.s\:bottom-64 { bottom: 4rem; }
.s\:bottom-68 { bottom: 4.25rem; }
.s\:bottom-72 { bottom: 4.5rem; }
.s\:bottom-76 { bottom: 4.75rem; }
.s\:bottom-80 { bottom: 5rem; }
.s\:bottom-84 { bottom: 5.25rem; }
.s\:bottom-88 { bottom: 5.5rem; }
.s\:bottom-92 { bottom: 5.75rem; }
.s\:bottom-96 { bottom: 6rem; }
.s\:bottom-100 { bottom: 6.25rem; }
.s\:bottom-120 { bottom: 7.5rem; }
.s\:bottom-140 { bottom: 8.75rem; }
.s\:bottom-160 { bottom: 10rem; }
.s\:bottom-180 { bottom: 11.25rem; }
.s\:bottom-200 { bottom: 12.5rem; }
.s\:bottom-220 { bottom: 13.75rem; }
.s\:bottom-240 { bottom: 15rem; }
.s\:bottom-260 { bottom: 16.25rem; }
.s\:bottom-280 { bottom: 17.5rem; }
.s\:bottom-300 { bottom: 18.75rem; }
.s\:bottom-320 { bottom: 20rem; }
.s\:bottom-340 { bottom: 21.25rem; }
.s\:bottom-360 { bottom: 22.5rem; }
.s\:bottom-380 { bottom: 23.75rem; }
.s\:bottom-400 { bottom: 25rem; }

/* Bottom Percentage Negative */
.s\:bottom--10-100 { bottom: -10%; }
.s\:bottom--20-100 { bottom: -20%; }
.s\:bottom--30-100 { bottom: -30%; }
.s\:bottom--40-100 { bottom: -40%; }
.s\:bottom--50-100 { bottom: -50%; }
.s\:bottom--60-100 { bottom: -60%; }
.s\:bottom--70-100 { bottom: -70%; }
.s\:bottom--80-100 { bottom: -80%; }
.s\:bottom--90-100 { bottom: -90%; }
.s\:bottom--100-100 { bottom: -100%; }

/* Bottom Negative */
.s\:bottom--1 { bottom: -1px; }
.s\:bottom--4 { bottom: -0.25rem; }
.s\:bottom--6 { bottom: -0.375rem; }
.s\:bottom--8 { bottom: -0.5rem; }
.s\:bottom--10 { bottom: -0.625rem; }
.s\:bottom--12 { bottom: -0.75rem; }
.s\:bottom--14 { bottom: -0.875rem; }
.s\:bottom--16 { bottom: -1rem; }
.s\:bottom--18 { bottom: -1.125rem; }
.s\:bottom--20 { bottom: -1.25rem; }
.s\:bottom--22 { bottom: -1.375rem; }
.s\:bottom--24 { bottom: -1.5rem; }
.s\:bottom--28 { bottom: -1.75rem; }
.s\:bottom--32 { bottom: -2rem; }
.s\:bottom--36 { bottom: -2.25rem; }
.s\:bottom--40 { bottom: -2.5rem; }
.s\:bottom--44 { bottom: -2.75rem; }
.s\:bottom--48 { bottom: -3rem; }
.s\:bottom--52 { bottom: -3.25rem; }
.s\:bottom--56 { bottom: -3.5rem; }
.s\:bottom--60 { bottom: -3.75rem; }
.s\:bottom--64 { bottom: -4rem; }
.s\:bottom--68 { bottom: -4.25rem; }
.s\:bottom--72 { bottom: -4.5rem; }
.s\:bottom--76 { bottom: -4.75rem; }
.s\:bottom--80 { bottom: -5rem; }
.s\:bottom--84 { bottom: -5.25rem; }
.s\:bottom--88 { bottom: -5.5rem; }
.s\:bottom--92 { bottom: -5.75rem; }
.s\:bottom--96 { bottom: -6rem; }
.s\:bottom--100 { bottom: -6.25rem; }
.s\:bottom--120 { bottom: -7.5rem; }
.s\:bottom--140 { bottom: -8.75rem; }
.s\:bottom--160 { bottom: -10rem; }
.s\:bottom--180 { bottom: -11.25rem; }
.s\:bottom--200 { bottom: -12.5rem; }
.s\:bottom--220 { bottom: -13.75rem; }
.s\:bottom--240 { bottom: -15rem; }
.s\:bottom--260 { bottom: -16.25rem; }
.s\:bottom--280 { bottom: -17.5rem; }
.s\:bottom--300 { bottom: -18.75rem; }
.s\:bottom--320 { bottom: -20rem; }
.s\:bottom--340 { bottom: -21.25rem; }
.s\:bottom--360 { bottom: -22.5rem; }
.s\:bottom--380 { bottom: -23.75rem; }
.s\:bottom--400 { bottom: -25rem; }

/* Auto Margins */
.s\:m-auto { margin: auto; }
.s\:mx-auto {
  margin-left: auto;
  margin-right: auto;
}
.s\:my-auto {
  margin-top: auto;
  margin-bottom: auto;
}
.s\:ml-auto { margin-left: auto; }
.s\:mr-auto { margin-right: auto; }
.s\:mt-auto { margin-top: auto; }
.s\:mb-auto { margin-bottom: auto; }

/* Margin Bottom */
.s\:mb-0 { margin-bottom: 0px; }
.s\:mb-1 { margin-bottom: 1px; }
.s\:mb-2 { margin-bottom: 0.125rem; }
.s\:mb-4 { margin-bottom: 0.25rem; }
.s\:mb-6 { margin-bottom: 0.375rem; }
.s\:mb-8 { margin-bottom: 0.5rem; }
.s\:mb-10 { margin-bottom: 0.625rem; }
.s\:mb-12 { margin-bottom: 0.75rem; }
.s\:mb-14 { margin-bottom: 0.875rem; }
.s\:mb-16 { margin-bottom: 1rem; }
.s\:mb-18 { margin-bottom: 1.125rem; }
.s\:mb-20 { margin-bottom: 1.25rem; }
.s\:mb-22 { margin-bottom: 1.375rem; }
.s\:mb-24 { margin-bottom: 1.5rem; }
.s\:mb-28 { margin-bottom: 1.75rem; }
.s\:mb-32 { margin-bottom: 2rem; }
.s\:mb-36 { margin-bottom: 2.25rem; }
.s\:mb-40 { margin-bottom: 2.5rem; }
.s\:mb-44 { margin-bottom: 2.75rem; }
.s\:mb-48 { margin-bottom: 3rem; }
.s\:mb-52 { margin-bottom: 3.25rem; }
.s\:mb-56 { margin-bottom: 3.5rem; }
.s\:mb-60 { margin-bottom: 3.75rem; }
.s\:mb-64 { margin-bottom: 4rem; }
.s\:mb-68 { margin-bottom: 4.25rem; }
.s\:mb-72 { margin-bottom: 4.5rem; }
.s\:mb-76 { margin-bottom: 4.75rem; }
.s\:mb-80 { margin-bottom: 5rem; }
.s\:mb-84 { margin-bottom: 5.25rem; }
.s\:mb-88 { margin-bottom: 5.5rem; }
.s\:mb-92 { margin-bottom: 5.75rem; }
.s\:mb-96 { margin-bottom: 6rem; }
.s\:mb-100 { margin-bottom: 6.25rem; }
.s\:mb-120 { margin-bottom: 7.5rem; }
.s\:mb-140 { margin-bottom: 8.75rem; }
.s\:mb-160 { margin-bottom: 10rem; }
.s\:mb-180 { margin-bottom: 11.25rem; }
.s\:mb-200 { margin-bottom: 12.5rem; }
.s\:mb-220 { margin-bottom: 13.75rem; }
.s\:mb-240 { margin-bottom: 15rem; }
.s\:mb-260 { margin-bottom: 16.25rem; }
.s\:mb-280 { margin-bottom: 17.5rem; }
.s\:mb-300 { margin-bottom: 18.75rem; }
.s\:mb-320 { margin-bottom: 20rem; }
.s\:mb-340 { margin-bottom: 21.25rem; }
.s\:mb-360 { margin-bottom: 22.5rem; }
.s\:mb-380 { margin-bottom: 23.75rem; }
.s\:mb-400 { margin-bottom: 25rem; }

/* Margin Top */
.s\:mt-0 { margin-top: 0px; }
.s\:mt-1 { margin-top: 1px; }
.s\:mt-2 { margin-top: 0.125rem; }
.s\:mt-4 { margin-top: 0.25rem; }
.s\:mt-6 { margin-top: 0.375rem; }
.s\:mt-8 { margin-top: 0.5rem; }
.s\:mt-10 { margin-top: 0.625rem; }
.s\:mt-12 { margin-top: 0.75rem; }
.s\:mt-14 { margin-top: 0.875rem; }
.s\:mt-16 { margin-top: 1rem; }
.s\:mt-18 { margin-top: 1.125rem; }
.s\:mt-20 { margin-top: 1.25rem; }
.s\:mt-22 { margin-top: 1.375rem; }
.s\:mt-24 { margin-top: 1.5rem; }
.s\:mt-28 { margin-top: 1.75rem; }
.s\:mt-32 { margin-top: 2rem; }
.s\:mt-36 { margin-top: 2.25rem; }
.s\:mt-40 { margin-top: 2.5rem; }
.s\:mt-44 { margin-top: 2.75rem; }
.s\:mt-48 { margin-top: 3rem; }
.s\:mt-52 { margin-top: 3.25rem; }
.s\:mt-56 { margin-top: 3.5rem; }
.s\:mt-60 { margin-top: 3.75rem; }
.s\:mt-64 { margin-top: 4rem; }
.s\:mt-68 { margin-top: 4.25rem; }
.s\:mt-72 { margin-top: 4.5rem; }
.s\:mt-76 { margin-top: 4.75rem; }
.s\:mt-80 { margin-top: 5rem; }
.s\:mt-84 { margin-top: 5.25rem; }
.s\:mt-88 { margin-top: 5.5rem; }
.s\:mt-92 { margin-top: 5.75rem; }
.s\:mt-96 { margin-top: 6rem; }
.s\:mt-100 { margin-top: 6.25rem; }
.s\:mt-120 { margin-top: 7.5rem; }
.s\:mt-140 { margin-top: 8.75rem; }
.s\:mt-160 { margin-top: 10rem; }
.s\:mt-180 { margin-top: 11.25rem; }
.s\:mt-200 { margin-top: 12.5rem; }
.s\:mt-220 { margin-top: 13.75rem; }
.s\:mt-240 { margin-top: 15rem; }
.s\:mt-260 { margin-top: 16.25rem; }
.s\:mt-280 { margin-top: 17.5rem; }
.s\:mt-300 { margin-top: 18.75rem; }
.s\:mt-320 { margin-top: 20rem; }
.s\:mt-340 { margin-top: 21.25rem; }
.s\:mt-360 { margin-top: 22.5rem; }
.s\:mt-380 { margin-top: 23.75rem; }
.s\:mt-400 { margin-top: 25rem; }

/* Margin Left */
.s\:ml-0 { margin-left: 0px; }
.s\:ml-1 { margin-left: 1px; }
.s\:ml-2 { margin-left: 0.125rem; }
.s\:ml-4 { margin-left: 0.25rem; }
.s\:ml-6 { margin-left: 0.375rem; }
.s\:ml-8 { margin-left: 0.5rem; }
.s\:ml-10 { margin-left: 0.625rem; }
.s\:ml-12 { margin-left: 0.75rem; }
.s\:ml-14 { margin-left: 0.875rem; }
.s\:ml-16 { margin-left: 1rem; }
.s\:ml-18 { margin-left: 1.125rem; }
.s\:ml-20 { margin-left: 1.25rem; }
.s\:ml-22 { margin-left: 1.375rem; }
.s\:ml-24 { margin-left: 1.5rem; }
.s\:ml-28 { margin-left: 1.75rem; }
.s\:ml-32 { margin-left: 2rem; }
.s\:ml-36 { margin-left: 2.25rem; }
.s\:ml-40 { margin-left: 2.5rem; }
.s\:ml-44 { margin-left: 2.75rem; }
.s\:ml-48 { margin-left: 3rem; }
.s\:ml-52 { margin-left: 3.25rem; }
.s\:ml-56 { margin-left: 3.5rem; }
.s\:ml-60 { margin-left: 3.75rem; }
.s\:ml-64 { margin-left: 4rem; }
.s\:ml-68 { margin-left: 4.25rem; }
.s\:ml-72 { margin-left: 4.5rem; }
.s\:ml-76 { margin-left: 4.75rem; }
.s\:ml-80 { margin-left: 5rem; }
.s\:ml-84 { margin-left: 5.25rem; }
.s\:ml-88 { margin-left: 5.5rem; }
.s\:ml-92 { margin-left: 5.75rem; }
.s\:ml-96 { margin-left: 6rem; }
.s\:ml-100 { margin-left: 6.25rem; }
.s\:ml-120 { margin-left: 7.5rem; }
.s\:ml-140 { margin-left: 8.75rem; }
.s\:ml-160 { margin-left: 10rem; }
.s\:ml-180 { margin-left: 11.25rem; }
.s\:ml-200 { margin-left: 12.5rem; }
.s\:ml-220 { margin-left: 13.75rem; }
.s\:ml-240 { margin-left: 15rem; }
.s\:ml-260 { margin-left: 16.25rem; }
.s\:ml-280 { margin-left: 17.5rem; }
.s\:ml-300 { margin-left: 18.75rem; }
.s\:ml-320 { margin-left: 20rem; }
.s\:ml-340 { margin-left: 21.25rem; }
.s\:ml-360 { margin-left: 22.5rem; }
.s\:ml-380 { margin-left: 23.75rem; }
.s\:ml-400 { margin-left: 25rem; }

/*--------------------------------------------------------------

## Margin Right

--------------------------------------------------------------*/

.mr-0 {
  margin-right: 0px;
}

.mr-1 {
  margin-right: 1px;
}

.mr-2 {
  margin-right: 0.125rem;
}

.mr-4 {
  margin-right: calc((0.25rem * var(--responsive-70)) * (var(--mr-4, 1)));
}

.mr-6 {
  margin-right: calc((0.375rem * var(--responsive-70)) * (var(--mr-6, 1)));
}

.mr-8 {
  margin-right: calc((0.5rem * var(--responsive-70)) * (var(--mr-8, 1)));
}

.mr-10 {
  margin-right: calc((0.625rem * var(--responsive-70)) * (var(--mr-10, 1)));
}

.mr-12 {
  margin-right: calc((0.75rem * var(--responsive-70)) * (var(--mr-12, 1)));
}

.mr-14 {
  margin-right: calc((0.875rem * var(--responsive-70)) * (var(--mr-14, 1)));
}

.mr-16 {
  margin-right: calc((1rem * var(--responsive-70)) * (var(--mr-16, 1)));
}

.mr-18 {
  margin-right: calc((1.125rem * var(--responsive-70)) * (var(--mr-18, 1)));
}

.mr-20 {
  margin-right: calc((1.25rem * var(--responsive-70)) * (var(--mr-20, 1)));
}

.mr-22 {
  margin-right: calc((1.375rem * var(--responsive-70)) * (var(--mr-22, 1)));
}

.mr-24 {
  margin-right: calc((1.5rem * var(--responsive-70)) * (var(--mr-24, 1)));
}

.mr-28 {
  margin-right: calc((1.75rem * var(--responsive-70)) * (var(--mr-28, 1)));
}

.mr-32 {
  margin-right: calc((2rem * var(--responsive-70)) * (var(--mr-32, 1)));
}

.mr-36 {
  margin-right: calc((2.25rem * var(--responsive-70)) * (var(--mr-36, 1)));
}

.mr-40 {
  margin-right: calc((2.5rem * var(--responsive-60)) * (var(--mr-40, 1)));
}

.mr-44 {
  margin-right: calc((2.75rem * var(--responsive-60)) * (var(--mr-44, 1)));
}

.mr-48 {
  margin-right: calc((3rem * var(--responsive-60)) * (var(--mr-48, 1)));
}

.mr-52 {
  margin-right: calc((3.25rem * var(--responsive-60)) * (var(--mr-52, 1)));
}

.mr-56 {
  margin-right: calc((3.5rem * var(--responsive-60)) * (var(--mr-56, 1)));
}

.mr-60 {
  margin-right: calc((3.75rem * var(--responsive-50)) * (var(--mr-60, 1)));
}

.mr-64 {
  margin-right: calc((4rem * var(--responsive-50)) * (var(--mr-64, 1)));
}

.mr-68 {
  margin-right: calc((4.25rem * var(--responsive-50)) * (var(--mr-68, 1)));
}

.mr-72 {
  margin-right: calc((4.5rem * var(--responsive-50)) * (var(--mr-72, 1)));
}

.mr-76 {
  margin-right: calc((4.75rem * var(--responsive-50)) * (var(--mr-76, 1)));
}

.mr-80 {
  margin-right: calc((5rem * var(--responsive-40)) * (var(--mr-80, 1)));
}

.mr-84 {
  margin-right: calc((5.25rem * var(--responsive-40)) * (var(--mr-84, 1)));
}

.mr-88 {
  margin-right: calc((5.5rem * var(--responsive-40)) * (var(--mr-88, 1)));
}

.mr-92 {
  margin-right: calc((5.75rem * var(--responsive-40)) * (var(--mr-92, 1)));
}

.mr-96 {
  margin-right: calc((6rem * var(--responsive-40)) * (var(--mr-96, 1)));
}

.mr-100 {
  margin-right: calc((6.25rem * var(--responsive-30)) * (var(--mr-100, 1)));
}

.mr-120 {
  margin-right: calc((7.5rem * var(--responsive-30)) * (var(--mr-120, 1)));
}

.mr-140 {
  margin-right: calc((8.75rem * var(--responsive-30)) * (var(--mr-140, 1)));
}

.mr-160 {
  margin-right: calc((10rem * var(--responsive-30)) * (var(--mr-160, 1)));
}

.mr-180 {
  margin-right: calc((11.25rem * var(--responsive-30)) * (var(--mr-180, 1)));
}

.mr-200 {
  margin-right: calc((12.5rem * var(--responsive-30)) * (var(--mr-200, 1)));
}

.mr-220 {
  margin-right: calc((13.75rem * var(--responsive-70)) * (var(--mr-220, 1)));
}

.mr-240 {
  margin-right: calc((15rem * var(--responsive-70)) * (var(--mr-240, 1)));
}

.mr-260 {
  margin-right: calc((16.25rem * var(--responsive-70)) * (var(--mr-260, 1)));
}

.mr-280 {
  margin-right: calc((17.5rem * var(--responsive-70)) * (var(--mr-280, 1)));
}

.mr-300 {
  margin-right: calc((18.75rem * var(--responsive-70)) * (var(--mr-300, 1)));
}

.mr-320 {
  margin-right: calc((20rem * var(--responsive-70)) * (var(--mr-320, 1)));
}

.mr-340 {
  margin-right: calc((21.25rem * var(--responsive-70)) * (var(--mr-340, 1)));
}

.mr-360 {
  margin-right: calc((22.5rem * var(--responsive-70)) * (var(--mr-360, 1)));
}

.mr-380 {
  margin-right: calc((23.75rem * var(--responsive-70)) * (var(--mr-380, 1)));
}

.mr-400 {
  margin-right: calc((25rem * var(--responsive-70)) * (var(--mr-400, 1)));
}

/*--------------------------------------------------------------

## Margin Y (Top and Bottom)

--------------------------------------------------------------*/

.my-0 {
  margin-top: 0px;
  margin-bottom: 0px;
}

.my-1 {
  margin-top: 1px;
  margin-bottom: 1px;
}

.my-2 {
  margin-top: 0.125rem;
  margin-bottom: 0.125rem;
}

.my-4 {
  margin-top: calc((0.25rem * var(--responsive-70)) * (var(--my-4, 1)));
  margin-bottom: calc((0.25rem * var(--responsive-70)) * (var(--my-4, 1)));
}

.my-6 {
  margin-top: calc((0.375rem * var(--responsive-70)) * (var(--my-6, 1)));
  margin-bottom: calc((0.375rem * var(--responsive-70)) * (var(--my-6, 1)));
}

.my-8 {
  margin-top: calc((0.5rem * var(--responsive-70)) * (var(--my-8, 1)));
  margin-bottom: calc((0.5rem * var(--responsive-70)) * (var(--my-8, 1)));
}

.my-10 {
  margin-top: calc((0.625rem * var(--responsive-70)) * (var(--my-10, 1)));
  margin-bottom: calc((0.625rem * var(--responsive-70)) * (var(--my-10, 1)));
}

.my-12 {
  margin-top: calc((0.75rem * var(--responsive-70)) * (var(--my-12, 1)));
  margin-bottom: calc((0.75rem * var(--responsive-70)) * (var(--my-12, 1)));
}

.my-14 {
  margin-top: calc((0.875rem * var(--responsive-70)) * (var(--my-14, 1)));
  margin-bottom: calc((0.875rem * var(--responsive-70)) * (var(--my-14, 1)));
}

.my-16 {
  margin-top: calc((1rem * var(--responsive-70)) * (var(--my-16, 1)));
  margin-bottom: calc((1rem * var(--responsive-70)) * (var(--my-16, 1)));
}

.my-18 {
  margin-top: calc((1.125rem * var(--responsive-70)) * (var(--my-18, 1)));
  margin-bottom: calc((1.125rem * var(--responsive-70)) * (var(--my-18, 1)));
}

.my-20 {
  margin-top: calc((1.25rem * var(--responsive-70)) * (var(--my-20, 1)));
  margin-bottom: calc((1.25rem * var(--responsive-70)) * (var(--my-20, 1)));
}

.my-22 {
  margin-top: calc((1.375rem * var(--responsive-70)) * (var(--my-22, 1)));
  margin-bottom: calc((1.375rem * var(--responsive-70)) * (var(--my-22, 1)));
}

.my-24 {
  margin-top: calc((1.5rem * var(--responsive-70)) * (var(--my-24, 1)));
  margin-bottom: calc((1.5rem * var(--responsive-70)) * (var(--my-24, 1)));
}

.my-28 {
  margin-top: calc((1.75rem * var(--responsive-70)) * (var(--my-28, 1)));
  margin-bottom: calc((1.75rem * var(--responsive-70)) * (var(--my-28, 1)));
}

.my-32 {
  margin-top: calc((2rem * var(--responsive-70)) * (var(--my-32, 1)));
  margin-bottom: calc((2rem * var(--responsive-70)) * (var(--my-32, 1)));
}

.my-36 {
  margin-top: calc((2.25rem * var(--responsive-70)) * (var(--my-36, 1)));
  margin-bottom: calc((2.25rem * var(--responsive-70)) * (var(--my-36, 1)));
}

.my-40 {
  margin-top: calc((2.5rem * var(--responsive-60)) * (var(--my-40, 1)));
  margin-bottom: calc((2.5rem * var(--responsive-60)) * (var(--my-40, 1)));
}

.my-44 {
  margin-top: calc((2.75rem * var(--responsive-60)) * (var(--my-44, 1)));
  margin-bottom: calc((2.75rem * var(--responsive-60)) * (var(--my-44, 1)));
}

.my-48 {
  margin-top: calc((3rem * var(--responsive-60)) * (var(--my-48, 1)));
  margin-bottom: calc((3rem * var(--responsive-60)) * (var(--my-48, 1)));
}

.my-52 {
  margin-top: calc((3.25rem * var(--responsive-60)) * (var(--my-52, 1)));
  margin-bottom: calc((3.25rem * var(--responsive-60)) * (var(--my-52, 1)));
}

.my-56 {
  margin-top: calc((3.5rem * var(--responsive-60)) * (var(--my-56, 1)));
  margin-bottom: calc((3.5rem * var(--responsive-60)) * (var(--my-56, 1)));
}

.my-60 {
  margin-top: calc((3.75rem * var(--responsive-50)) * (var(--my-60, 1)));
  margin-bottom: calc((3.75rem * var(--responsive-50)) * (var(--my-60, 1)));
}

.my-64 {
  margin-top: calc((4rem * var(--responsive-50)) * (var(--my-64, 1)));
  margin-bottom: calc((4rem * var(--responsive-50)) * (var(--my-64, 1)));
}

.my-68 {
  margin-top: calc((4.25rem * var(--responsive-50)) * (var(--my-68, 1)));
  margin-bottom: calc((4.25rem * var(--responsive-50)) * (var(--my-68, 1)));
}

.my-72 {
  margin-top: calc((4.5rem * var(--responsive-50)) * (var(--my-72, 1)));
  margin-bottom: calc((4.5rem * var(--responsive-50)) * (var(--my-72, 1)));
}

.my-76 {
  margin-top: calc((4.75rem * var(--responsive-50)) * (var(--my-76, 1)));
  margin-bottom: calc((4.75rem * var(--responsive-50)) * (var(--my-76, 1)));
}

.my-80 {
  margin-top: calc((5rem * var(--responsive-40)) * (var(--my-80, 1)));
  margin-bottom: calc((5rem * var(--responsive-40)) * (var(--my-80, 1)));
}

.my-84 {
  margin-top: calc((5.25rem * var(--responsive-40)) * (var(--my-84, 1)));
  margin-bottom: calc((5.25rem * var(--responsive-40)) * (var(--my-84, 1)));
}

.my-88 {
  margin-top: calc((5.5rem * var(--responsive-40)) * (var(--my-88, 1)));
  margin-bottom: calc((5.5rem * var(--responsive-40)) * (var(--my-88, 1)));
}

.my-92 {
  margin-top: calc((5.75rem * var(--responsive-40)) * (var(--my-92, 1)));
  margin-bottom: calc((5.75rem * var(--responsive-40)) * (var(--my-92, 1)));
}

.my-96 {
  margin-top: calc((6rem * var(--responsive-40)) * (var(--my-96, 1)));
  margin-bottom: calc((6rem * var(--responsive-40)) * (var(--my-96, 1)));
}

.my-100 {
  margin-top: calc((6.25rem * var(--responsive-30)) * (var(--my-100, 1)));
  margin-bottom: calc((6.25rem * var(--responsive-30)) * (var(--my-100, 1)));
}

.my-120 {
  margin-top: calc((7.5rem * var(--responsive-30)) * (var(--my-120, 1)));
  margin-bottom: calc((7.5rem * var(--responsive-30)) * (var(--my-120, 1)));
}

.my-140 {
  margin-top: calc((8.75rem * var(--responsive-30)) * (var(--my-140, 1)));
  margin-bottom: calc((8.75rem * var(--responsive-30)) * (var(--my-140, 1)));
}

.my-160 {
  margin-top: calc((10rem * var(--responsive-30)) * (var(--my-160, 1)));
  margin-bottom: calc((10rem * var(--responsive-30)) * (var(--my-160, 1)));
}

.my-180 {
  margin-top: calc((11.25rem * var(--responsive-30)) * (var(--my-180, 1)));
  margin-bottom: calc((11.25rem * var(--responsive-30)) * (var(--my-180, 1)));
}

.my-200 {
  margin-top: calc((12.5rem * var(--responsive-30)) * (var(--my-200, 1)));
  margin-bottom: calc((12.5rem * var(--responsive-30)) * (var(--my-200, 1)));
}

.my-220 {
  margin-top: calc((13.75rem * var(--responsive-70)) * (var(--my-220, 1)));
  margin-bottom: calc((13.75rem * var(--responsive-70)) * (var(--my-220, 1)));
}

.my-240 {
  margin-top: calc((15rem * var(--responsive-70)) * (var(--my-240, 1)));
  margin-bottom: calc((15rem * var(--responsive-70)) * (var(--my-240, 1)));
}

.my-260 {
  margin-top: calc((16.25rem * var(--responsive-70)) * (var(--my-260, 1)));
  margin-bottom: calc((16.25rem * var(--responsive-70)) * (var(--my-260, 1)));
}

.my-280 {
  margin-top: calc((17.5rem * var(--responsive-70)) * (var(--my-280, 1)));
  margin-bottom: calc((17.5rem * var(--responsive-70)) * (var(--my-280, 1)));
}

.my-300 {
  margin-top: calc((18.75rem * var(--responsive-70)) * (var(--my-300, 1)));
  margin-bottom: calc((18.75rem * var(--responsive-70)) * (var(--my-300, 1)));
}

.my-320 {
  margin-top: calc((20rem * var(--responsive-70)) * (var(--my-320, 1)));
  margin-bottom: calc((20rem * var(--responsive-70)) * (var(--my-320, 1)));
}

.my-340 {
  margin-top: calc((21.25rem * var(--responsive-70)) * (var(--my-340, 1)));
  margin-bottom: calc((21.25rem * var(--responsive-70)) * (var(--my-340, 1)));
}

.my-360 {
  margin-top: calc((22.5rem * var(--responsive-70)) * (var(--my-360, 1)));
  margin-bottom: calc((22.5rem * var(--responsive-70)) * (var(--my-360, 1)));
}

.my-380 {
  margin-top: calc((23.75rem * var(--responsive-70)) * (var(--my-380, 1)));
  margin-bottom: calc((23.75rem * var(--responsive-70)) * (var(--my-380, 1)));
}

.my-400 {
  margin-top: calc((25rem * var(--responsive-70)) * (var(--my-400, 1)));
  margin-bottom: calc((25rem * var(--responsive-70)) * (var(--my-400, 1)));
}

/*--------------------------------------------------------------

## Margin X (Left and Right)

--------------------------------------------------------------*/

.mx-0 {
  margin-left: 0px;
  margin-right: 0px;
}

.mx-1 {
  margin-left: 1px;
  margin-right: 1px;
}

.mx-2 {
  margin-left: 0.125rem;
  margin-right: 0.125rem;
}

.mx-4 {
  margin-left: calc((0.25rem * var(--responsive-70)) * (var(--mx-4, 1)));
  margin-right: calc((0.25rem * var(--responsive-70)) * (var(--mx-4, 1)));
}

.mx-6 {
  margin-left: calc((0.375rem * var(--responsive-70)) * (var(--mx-6, 1)));
  margin-right: calc((0.375rem * var(--responsive-70)) * (var(--mx-6, 1)));
}

.mx-8 {
  margin-left: calc((0.5rem * var(--responsive-70)) * (var(--mx-8, 1)));
  margin-right: calc((0.5rem * var(--responsive-70)) * (var(--mx-8, 1)));
}

.mx-10 {
  margin-left: calc((0.625rem * var(--responsive-70)) * (var(--mx-10, 1)));
  margin-right: calc((0.625rem * var(--responsive-70)) * (var(--mx-10, 1)));
}

.mx-12 {
  margin-left: calc((0.75rem * var(--responsive-70)) * (var(--mx-12, 1)));
  margin-right: calc((0.75rem * var(--responsive-70)) * (var(--mx-12, 1)));
}

.mx-14 {
  margin-left: calc((0.875rem * var(--responsive-70)) * (var(--mx-14, 1)));
  margin-right: calc((0.875rem * var(--responsive-70)) * (var(--mx-14, 1)));
}

.mx-16 {
  margin-left: calc((1rem * var(--responsive-70)) * (var(--mx-16, 1)));
  margin-right: calc((1rem * var(--responsive-70)) * (var(--mx-16, 1)));
}

.mx-18 {
  margin-left: calc((1.125rem * var(--responsive-70)) * (var(--mx-18, 1)));
  margin-right: calc((1.125rem * var(--responsive-70)) * (var(--mx-18, 1)));
}

.mx-20 {
  margin-left: calc((1.25rem * var(--responsive-70)) * (var(--mx-20, 1)));
  margin-right: calc((1.25rem * var(--responsive-70)) * (var(--mx-20, 1)));
}

.mx-22 {
  margin-left: calc((1.375rem * var(--responsive-70)) * (var(--mx-22, 1)));
  margin-right: calc((1.375rem * var(--responsive-70)) * (var(--mx-22, 1)));
}

.mx-24 {
  margin-left: calc((1.5rem * var(--responsive-70)) * (var(--mx-24, 1)));
  margin-right: calc((1.5rem * var(--responsive-70)) * (var(--mx-24, 1)));
}

.mx-28 {
  margin-left: calc((1.75rem * var(--responsive-70)) * (var(--mx-28, 1)));
  margin-right: calc((1.75rem * var(--responsive-70)) * (var(--mx-28, 1)));
}

.mx-32 {
  margin-left: calc((2rem * var(--responsive-70)) * (var(--mx-32, 1)));
  margin-right: calc((2rem * var(--responsive-70)) * (var(--mx-32, 1)));
}

.mx-36 {
  margin-left: calc((2.25rem * var(--responsive-70)) * (var(--mx-36, 1)));
  margin-right: calc((2.25rem * var(--responsive-70)) * (var(--mx-36, 1)));
}

.mx-40 {
  margin-left: calc((2.5rem * var(--responsive-60)) * (var(--mx-40, 1)));
  margin-right: calc((2.5rem * var(--responsive-60)) * (var(--mx-40, 1)));
}

.mx-44 {
  margin-left: calc((2.75rem * var(--responsive-60)) * (var(--mx-44, 1)));
  margin-right: calc((2.75rem * var(--responsive-60)) * (var(--mx-44, 1)));
}

.mx-48 {
  margin-left: calc((3rem * var(--responsive-60)) * (var(--mx-48, 1)));
  margin-right: calc((3rem * var(--responsive-60)) * (var(--mx-48, 1)));
}

.mx-52 {
  margin-left: calc((3.25rem * var(--responsive-60)) * (var(--mx-52, 1)));
  margin-right: calc((3.25rem * var(--responsive-60)) * (var(--mx-52, 1)));
}

.mx-56 {
  margin-left: calc((3.5rem * var(--responsive-60)) * (var(--mx-56, 1)));
  margin-right: calc((3.5rem * var(--responsive-60)) * (var(--mx-56, 1)));
}

.mx-60 {
  margin-left: calc((3.75rem * var(--responsive-50)) * (var(--mx-60, 1)));
  margin-right: calc((3.75rem * var(--responsive-50)) * (var(--mx-60, 1)));
}

.mx-64 {
  margin-left: calc((4rem * var(--responsive-50)) * (var(--mx-64, 1)));
  margin-right: calc((4rem * var(--responsive-50)) * (var(--mx-64, 1)));
}

.mx-68 {
  margin-left: calc((4.25rem * var(--responsive-50)) * (var(--mx-68, 1)));
  margin-right: calc((4.25rem * var(--responsive-50)) * (var(--mx-68, 1)));
}

.mx-72 {
  margin-left: calc((4.5rem * var(--responsive-50)) * (var(--mx-72, 1)));
  margin-right: calc((4.5rem * var(--responsive-50)) * (var(--mx-72, 1)));
}

.mx-76 {
  margin-left: calc((4.75rem * var(--responsive-50)) * (var(--mx-76, 1)));
  margin-right: calc((4.75rem * var(--responsive-50)) * (var(--mx-76, 1)));
}

.mx-80 {
  margin-left: calc((5rem * var(--responsive-40)) * (var(--mx-80, 1)));
  margin-right: calc((5rem * var(--responsive-40)) * (var(--mx-80, 1)));
}

.mx-84 {
  margin-left: calc((5.25rem * var(--responsive-40)) * (var(--mx-84, 1)));
  margin-right: calc((5.25rem * var(--responsive-40)) * (var(--mx-84, 1)));
}

.mx-88 {
  margin-left: calc((5.5rem * var(--responsive-40)) * (var(--mx-88, 1)));
  margin-right: calc((5.5rem * var(--responsive-40)) * (var(--mx-88, 1)));
}

.mx-92 {
  margin-left: calc((5.75rem * var(--responsive-40)) * (var(--mx-92, 1)));
  margin-right: calc((5.75rem * var(--responsive-40)) * (var(--mx-92, 1)));
}

.mx-96 {
  margin-left: calc((6rem * var(--responsive-40)) * (var(--mx-96, 1)));
  margin-right: calc((6rem * var(--responsive-40)) * (var(--mx-96, 1)));
}

.mx-100 {
  margin-left: calc((6.25rem * var(--responsive-30)) * (var(--mx-100, 1)));
  margin-right: calc((6.25rem * var(--responsive-30)) * (var(--mx-100, 1)));
}

.mx-120 {
  margin-left: calc((7.5rem * var(--responsive-30)) * (var(--mx-120, 1)));
  margin-right: calc((7.5rem * var(--responsive-30)) * (var(--mx-120, 1)));
}

.mx-140 {
  margin-left: calc((8.75rem * var(--responsive-30)) * (var(--mx-140, 1)));
  margin-right: calc((8.75rem * var(--responsive-30)) * (var(--mx-140, 1)));
}

.mx-160 {
  margin-left: calc((10rem * var(--responsive-30)) * (var(--mx-160, 1)));
  margin-right: calc((10rem * var(--responsive-30)) * (var(--mx-160, 1)));
}

.mx-180 {
  margin-left: calc((11.25rem * var(--responsive-30)) * (var(--mx-180, 1)));
  margin-right: calc((11.25rem * var(--responsive-30)) * (var(--mx-180, 1)));
}

.mx-200 {
  margin-left: calc((12.5rem * var(--responsive-30)) * (var(--mx-200, 1)));
  margin-right: calc((12.5rem * var(--responsive-30)) * (var(--mx-200, 1)));
}

.mx-220 {
  margin-left: calc((13.75rem * var(--responsive-70)) * (var(--mx-220, 1)));
  margin-right: calc((13.75rem * var(--responsive-70)) * (var(--mx-220, 1)));
}

.mx-240 {
  margin-left: calc((15rem * var(--responsive-70)) * (var(--mx-240, 1)));
  margin-right: calc((15rem * var(--responsive-70)) * (var(--mx-240, 1)));
}

.mx-260 {
  margin-left: calc((16.25rem * var(--responsive-70)) * (var(--mx-260, 1)));
  margin-right: calc((16.25rem * var(--responsive-70)) * (var(--mx-260, 1)));
}

.mx-280 {
  margin-left: calc((17.5rem * var(--responsive-70)) * (var(--mx-280, 1)));
  margin-right: calc((17.5rem * var(--responsive-70)) * (var(--mx-280, 1)));
}

.mx-300 {
  margin-left: calc((18.75rem * var(--responsive-70)) * (var(--mx-300, 1)));
  margin-right: calc((18.75rem * var(--responsive-70)) * (var(--mx-300, 1)));
}

.mx-320 {
  margin-left: calc((20rem * var(--responsive-70)) * (var(--mx-320, 1)));
  margin-right: calc((20rem * var(--responsive-70)) * (var(--mx-320, 1)));
}

.mx-340 {
  margin-left: calc((21.25rem * var(--responsive-70)) * (var(--mx-340, 1)));
  margin-right: calc((21.25rem * var(--responsive-70)) * (var(--mx-340, 1)));
}

.mx-360 {
  margin-left: calc((22.5rem * var(--responsive-70)) * (var(--mx-360, 1)));
  margin-right: calc((22.5rem * var(--responsive-70)) * (var(--mx-360, 1)));
}

.mx-380 {
  margin-left: calc((23.75rem * var(--responsive-70)) * (var(--mx-380, 1)));
  margin-right: calc((23.75rem * var(--responsive-70)) * (var(--mx-380, 1)));
}

.mx-400 {
  margin-left: calc((25rem * var(--responsive-70)) * (var(--mx-400, 1)));
  margin-right: calc((25rem * var(--responsive-70)) * (var(--mx-400, 1)));
}

/*--------------------------------------------------------------

## Negative Margin Bottom

--------------------------------------------------------------*/

.mb--1 {
  margin-bottom: -1px;
}

.mb--2 {
  margin-bottom: -0.125rem;
}

.mb--4 {
  margin-bottom: calc((-0.25rem * var(--responsive-70)) * (var(--mb--4, 1)));
}

.mb--6 {
  margin-bottom: calc((-0.375rem * var(--responsive-70)) * (var(--mb--6, 1)));
}

.mb--8 {
  margin-bottom: calc((-0.5rem * var(--responsive-70)) * (var(--mb--8, 1)));
}

.mb--10 {
  margin-bottom: calc((-0.625rem * var(--responsive-70)) * (var(--mb--10, 1)));
}

.mb--12 {
  margin-bottom: calc((-0.75rem * var(--responsive-70)) * (var(--mb--12, 1)));
}

.mb--14 {
  margin-bottom: calc((-0.875rem * var(--responsive-70)) * (var(--mb--14, 1)));
}

.mb--16 {
  margin-bottom: calc((-1rem * var(--responsive-70)) * (var(--mb--16, 1)));
}

.mb--18 {
  margin-bottom: calc((-1.125rem * var(--responsive-70)) * (var(--mb--18, 1)));
}

.mb--20 {
  margin-bottom: calc((-1.25rem * var(--responsive-70)) * (var(--mb--20, 1)));
}

.mb--22 {
  margin-bottom: calc((-1.375rem * var(--responsive-70)) * (var(--mb--22, 1)));
}

.mb--24 {
  margin-bottom: calc((-1.5rem * var(--responsive-70)) * (var(--mb--24, 1)));
}

.mb--28 {
  margin-bottom: calc((-1.75rem * var(--responsive-70)) * (var(--mb--28, 1)));
}

.mb--32 {
  margin-bottom: calc((-2rem * var(--responsive-70)) * (var(--mb--32, 1)));
}

.mb--36 {
  margin-bottom: calc((-2.25rem * var(--responsive-70)) * (var(--mb--36, 1)));
}

.mb--40 {
  margin-bottom: calc((-2.5rem * var(--responsive-60)) * (var(--mb--40, 1)));
}

.mb--44 {
  margin-bottom: calc((-2.75rem * var(--responsive-60)) * (var(--mb--44, 1)));
}

.mb--48 {
  margin-bottom: calc((-3rem * var(--responsive-60)) * (var(--mb--48, 1)));
}

.mb--52 {
  margin-bottom: calc((-3.25rem * var(--responsive-60)) * (var(--mb--52, 1)));
}

.mb--56 {
  margin-bottom: calc((-3.5rem * var(--responsive-60)) * (var(--mb--56, 1)));
}

.mb--60 {
  margin-bottom: calc((-3.75rem * var(--responsive-50)) * (var(--mb--60, 1)));
}

.mb--64 {
  margin-bottom: calc((-4rem * var(--responsive-50)) * (var(--mb--64, 1)));
}

.mb--68 {
  margin-bottom: calc((-4.25rem * var(--responsive-50)) * (var(--mb--68, 1)));
}

.mb--72 {
  margin-bottom: calc((-4.5rem * var(--responsive-50)) * (var(--mb--72, 1)));
}

.mb--76 {
  margin-bottom: calc((-4.75rem * var(--responsive-50)) * (var(--mb--76, 1)));
}

.mb--80 {
  margin-bottom: calc((-5rem * var(--responsive-40)) * (var(--mb--80, 1)));
}

.mb--84 {
  margin-bottom: calc((-5.25rem * var(--responsive-40)) * (var(--mb--84, 1)));
}

.mb--88 {
  margin-bottom: calc((-5.5rem * var(--responsive-40)) * (var(--mb--88, 1)));
}

.mb--92 {
  margin-bottom: calc((-5.75rem * var(--responsive-40)) * (var(--mb--92, 1)));
}

.mb--96 {
  margin-bottom: calc((-6rem * var(--responsive-40)) * (var(--mb--96, 1)));
}

.mb--100 {
  margin-bottom: calc((-6.25rem * var(--responsive-30)) * (var(--mb--100, 1)));
}

.mb--120 {
  margin-bottom: calc((-7.5rem * var(--responsive-30)) * (var(--mb--120, 1)));
}

.mb--140 {
  margin-bottom: calc((-8.75rem * var(--responsive-30)) * (var(--mb--140, 1)));
}

.mb--160 {
  margin-bottom: calc((-10rem * var(--responsive-30)) * (var(--mb--160, 1)));
}

.mb--180 {
  margin-bottom: calc((-11.25rem * var(--responsive-30)) * (var(--mb--180, 1)));
}

.mb--200 {
  margin-bottom: calc((-12.5rem * var(--responsive-30)) * (var(--mb--200, 1)));
}

.mb--220 {
  margin-bottom: calc((-13.75rem * var(--responsive-70)) * (var(--mb--220, 1)));
}

.mb--240 {
  margin-bottom: calc((-15rem * var(--responsive-70)) * (var(--mb--240, 1)));
}

.mb--260 {
  margin-bottom: calc((-16.25rem * var(--responsive-70)) * (var(--mb--260, 1)));
}

.mb--280 {
  margin-bottom: calc((-17.5rem * var(--responsive-70)) * (var(--mb--280, 1)));
}

.mb--300 {
  margin-bottom: calc((-18.75rem * var(--responsive-70)) * (var(--mb--300, 1)));
}

.mb--320 {
  margin-bottom: calc((-20rem * var(--responsive-70)) * (var(--mb--320, 1)));
}

.mb--340 {
  margin-bottom: calc((-21.25rem * var(--responsive-70)) * (var(--mb--340, 1)));
}

.mb--360 {
  margin-bottom: calc((-22.5rem * var(--responsive-70)) * (var(--mb--360, 1)));
}

.mb--380 {
  margin-bottom: calc((-23.75rem * var(--responsive-70)) * (var(--mb--380, 1)));
}

.mb--400 {
  margin-bottom: calc((-25rem * var(--responsive-70)) * (var(--mb--400, 1)));
}




/*--------------------------------------------------------------

## Negative Margin all

--------------------------------------------------------------*/

.m--20 {
  margin: calc((-1.25rem * var(--responsive-70)) * (var(--m--20, 1)));
}


.m--40 {
  margin: calc((-2.5rem * var(--responsive-60)) * (var(--m--40, 1)));
}

.m--60 {
  margin: calc((-3.75rem * var(--responsive-50)) * (var(--m--60, 1)));
}



/*--------------------------------------------------------------

## Negative Margin Top

--------------------------------------------------------------*/

.mt--0 {
  margin-top: 0px;
}

.mt--1 {
  margin-top: -1px;
}

.mt--2 {
  margin-top: -0.125rem;
}

.mt--4 {
  margin-top: calc((-0.25rem * var(--responsive-70)) * (var(--mt--4, 1)));
}

.mt--6 {
  margin-top: calc((-0.375rem * var(--responsive-70)) * (var(--mt--6, 1)));
}

.mt--8 {
  margin-top: calc((-0.5rem * var(--responsive-70)) * (var(--mt--8, 1)));
}

.mt--10 {
  margin-top: calc((-0.625rem * var(--responsive-70)) * (var(--mt--10, 1)));
}

.mt--12 {
  margin-top: calc((-0.75rem * var(--responsive-70)) * (var(--mt--12, 1)));
}

.mt--14 {
  margin-top: calc((-0.875rem * var(--responsive-70)) * (var(--mt--14, 1)));
}

.mt--16 {
  margin-top: calc((-1rem * var(--responsive-70)) * (var(--mt--16, 1)));
}

.mt--18 {
  margin-top: calc((-1.125rem * var(--responsive-70)) * (var(--mt--18, 1)));
}

.mt--20 {
  margin-top: calc((-1.25rem * var(--responsive-70)) * (var(--mt--20, 1)));
}

.mt--22 {
  margin-top: calc((-1.375rem * var(--responsive-70)) * (var(--mt--22, 1)));
}

.mt--24 {
  margin-top: calc((-1.5rem * var(--responsive-70)) * (var(--mt--24, 1)));
}

.mt--28 {
  margin-top: calc((-1.75rem * var(--responsive-70)) * (var(--mt--28, 1)));
}

.mt--32 {
  margin-top: calc((-2rem * var(--responsive-70)) * (var(--mt--32, 1)));
}

.mt--36 {
  margin-top: calc((-2.25rem * var(--responsive-70)) * (var(--mt--36, 1)));
}

.mt--40 {
  margin-top: calc((-2.5rem * var(--responsive-60)) * (var(--mt--40, 1)));
}

.mt--44 {
  margin-top: calc((-2.75rem * var(--responsive-60)) * (var(--mt--44, 1)));
}

.mt--48 {
  margin-top: calc((-3rem * var(--responsive-60)) * (var(--mt--48, 1)));
}

.mt--52 {
  margin-top: calc((-3.25rem * var(--responsive-60)) * (var(--mt--52, 1)));
}

.mt--56 {
  margin-top: calc((-3.5rem * var(--responsive-60)) * (var(--mt--56, 1)));
}

.mt--60 {
  margin-top: calc((-3.75rem * var(--responsive-50)) * (var(--mt--60, 1)));
}

.mt--64 {
  margin-top: calc((-4rem * var(--responsive-50)) * (var(--mt--64, 1)));
}

.mt--68 {
  margin-top: calc((-4.25rem * var(--responsive-50)) * (var(--mt--68, 1)));
}

.mt--72 {
  margin-top: calc((-4.5rem * var(--responsive-50)) * (var(--mt--72, 1)));
}

.mt--76 {
  margin-top: calc((-4.75rem * var(--responsive-50)) * (var(--mt--76, 1)));
}

.mt--80 {
  margin-top: calc((-5rem * var(--responsive-40)) * (var(--mt--80, 1)));
}

.mt--84 {
  margin-top: calc((-5.25rem * var(--responsive-40)) * (var(--mt--84, 1)));
}

.mt--88 {
  margin-top: calc((-5.5rem * var(--responsive-40)) * (var(--mt--88, 1)));
}

.mt--92 {
  margin-top: calc((-5.75rem * var(--responsive-40)) * (var(--mt--92, 1)));
}

.mt--96 {
  margin-top: calc((-6rem * var(--responsive-40)) * (var(--mt--96, 1)));
}

.mt--100 {
  margin-top: calc((-6.25rem * var(--responsive-30)) * (var(--mt--100, 1)));
}

.mt--120 {
  margin-top: calc((-7.5rem * var(--responsive-30)) * (var(--mt--120, 1)));
}

.mt--140 {
  margin-top: calc((-8.75rem * var(--responsive-30)) * (var(--mt--140, 1)));
}

.mt--160 {
  margin-top: calc((-10rem * var(--responsive-30)) * (var(--mt--160, 1)));
}

.mt--180 {
  margin-top: calc((-11.25rem * var(--responsive-30)) * (var(--mt--180, 1)));
}

.mt--200 {
  margin-top: calc((-12.5rem * var(--responsive-30)) * (var(--mt--200, 1)));
}

.mt--220 {
  margin-top: calc((-13.75rem * var(--responsive-70)) * (var(--mt--220, 1)));
}

.mt--240 {
  margin-top: calc((-15rem * var(--responsive-70)) * (var(--mt--240, 1)));
}

.mt--260 {
  margin-top: calc((-16.25rem * var(--responsive-70)) * (var(--mt--260, 1)));
}

.mt--280 {
  margin-top: calc((-17.5rem * var(--responsive-70)) * (var(--mt--280, 1)));
}

.mt--300 {
  margin-top: calc((-18.75rem * var(--responsive-70)) * (var(--mt--300, 1)));
}

.mt--320 {
  margin-top: calc((-20rem * var(--responsive-70)) * (var(--mt--320, 1)));
}

.mt--340 {
  margin-top: calc((-21.25rem * var(--responsive-70)) * (var(--mt--340, 1)));
}

.mt--360 {
  margin-top: calc((-22.5rem * var(--responsive-70)) * (var(--mt--360, 1)));
}

.mt--380 {
  margin-top: calc((-23.75rem * var(--responsive-70)) * (var(--mt--380, 1)));
}

.mt--400 {
  margin-top: calc((-25rem * var(--responsive-70)) * (var(--mt--400, 1)));
}

/* Margin Right */
.s\:mr-0 { margin-right: 0px; }
.s\:mr-1 { margin-right: 1px; }
.s\:mr-2 { margin-right: 0.125rem; }
.s\:mr-4 { margin-right: 0.25rem; }
.s\:mr-6 { margin-right: 0.375rem; }
.s\:mr-8 { margin-right: 0.5rem; }
.s\:mr-10 { margin-right: 0.625rem; }
.s\:mr-12 { margin-right: 0.75rem; }
.s\:mr-14 { margin-right: 0.875rem; }
.s\:mr-16 { margin-right: 1rem; }
.s\:mr-18 { margin-right: 1.125rem; }
.s\:mr-20 { margin-right: 1.25rem; }
.s\:mr-22 { margin-right: 1.375rem; }
.s\:mr-24 { margin-right: 1.5rem; }
.s\:mr-28 { margin-right: 1.75rem; }
.s\:mr-32 { margin-right: 2rem; }
.s\:mr-36 { margin-right: 2.25rem; }
.s\:mr-40 { margin-right: 2.5rem; }
.s\:mr-44 { margin-right: 2.75rem; }
.s\:mr-48 { margin-right: 3rem; }
.s\:mr-52 { margin-right: 3.25rem; }
.s\:mr-56 { margin-right: 3.5rem; }
.s\:mr-60 { margin-right: 3.75rem; }
.s\:mr-64 { margin-right: 4rem; }
.s\:mr-68 { margin-right: 4.25rem; }
.s\:mr-72 { margin-right: 4.5rem; }
.s\:mr-76 { margin-right: 4.75rem; }
.s\:mr-80 { margin-right: 5rem; }
.s\:mr-84 { margin-right: 5.25rem; }
.s\:mr-88 { margin-right: 5.5rem; }
.s\:mr-92 { margin-right: 5.75rem; }
.s\:mr-96 { margin-right: 6rem; }
.s\:mr-100 { margin-right: 6.25rem; }
.s\:mr-120 { margin-right: 7.5rem; }
.s\:mr-140 { margin-right: 8.75rem; }
.s\:mr-160 { margin-right: 10rem; }
.s\:mr-180 { margin-right: 11.25rem; }
.s\:mr-200 { margin-right: 12.5rem; }
.s\:mr-220 { margin-right: 13.75rem; }
.s\:mr-240 { margin-right: 15rem; }
.s\:mr-260 { margin-right: 16.25rem; }
.s\:mr-280 { margin-right: 17.5rem; }
.s\:mr-300 { margin-right: 18.75rem; }
.s\:mr-320 { margin-right: 20rem; }
.s\:mr-340 { margin-right: 21.25rem; }
.s\:mr-360 { margin-right: 22.5rem; }
.s\:mr-380 { margin-right: 23.75rem; }
.s\:mr-400 { margin-right: 25rem; }

/* Margin Y (Top & Bottom) */
.s\:my-0 {
  margin-top: 0px;
  margin-bottom: 0px;
}
.s\:my-1 {
  margin-top: 1px;
  margin-bottom: 1px;
}
.s\:my-2 {
  margin-top: 0.125rem;
  margin-bottom: 0.125rem;
}
.s\:my-4 {
  margin-top: 0.25rem;
  margin-bottom: 0.25rem;
}
.s\:my-6 {
  margin-top: 0.375rem;
  margin-bottom: 0.375rem;
}
.s\:my-8 {
  margin-top: 0.5rem;
  margin-bottom: 0.5rem;
}
.s\:my-10 {
  margin-top: 0.625rem;
  margin-bottom: 0.625rem;
}
.s\:my-12 {
  margin-top: 0.75rem;
  margin-bottom: 0.75rem;
}
.s\:my-14 {
  margin-top: 0.875rem;
  margin-bottom: 0.875rem;
}
.s\:my-16 {
  margin-top: 1rem;
  margin-bottom: 1rem;
}
.s\:my-18 {
  margin-top: 1.125rem;
  margin-bottom: 1.125rem;
}
.s\:my-20 {
  margin-top: 1.25rem;
  margin-bottom: 1.25rem;
}
.s\:my-22 {
  margin-top: 1.375rem;
  margin-bottom: 1.375rem;
}
.s\:my-24 {
  margin-top: 1.5rem;
  margin-bottom: 1.5rem;
}
.s\:my-28 {
  margin-top: 1.75rem;
  margin-bottom: 1.75rem;
}
.s\:my-32 {
  margin-top: 2rem;
  margin-bottom: 2rem;
}
.s\:my-36 {
  margin-top: 2.25rem;
  margin-bottom: 2.25rem;
}
.s\:my-40 {
  margin-top: 2.5rem;
  margin-bottom: 2.5rem;
}
.s\:my-44 {
  margin-top: 2.75rem;
  margin-bottom: 2.75rem;
}
.s\:my-48 {
  margin-top: 3rem;
  margin-bottom: 3rem;
}
.s\:my-52 {
  margin-top: 3.25rem;
  margin-bottom: 3.25rem;
}
.s\:my-56 {
  margin-top: 3.5rem;
  margin-bottom: 3.5rem;
}
.s\:my-60 {
  margin-top: 3.75rem;
  margin-bottom: 3.75rem;
}
.s\:my-64 {
  margin-top: 4rem;
  margin-bottom: 4rem;
}
.s\:my-68 {
  margin-top: 4.25rem;
  margin-bottom: 4.25rem;
}
.s\:my-72 {
  margin-top: 4.5rem;
  margin-bottom: 4.5rem;
}
.s\:my-76 {
  margin-top: 4.75rem;
  margin-bottom: 4.75rem;
}
.s\:my-80 {
  margin-top: 5rem;
  margin-bottom: 5rem;
}
.s\:my-84 {
  margin-top: 5.25rem;
  margin-bottom: 5.25rem;
}
.s\:my-88 {
  margin-top: 5.5rem;
  margin-bottom: 5.5rem;
}
.s\:my-92 {
  margin-top: 5.75rem;
  margin-bottom: 5.75rem;
}
.s\:my-96 {
  margin-top: 6rem;
  margin-bottom: 6rem;
}
.s\:my-100 {
  margin-top: 6.25rem;
  margin-bottom: 6.25rem;
}
.s\:my-120 {
  margin-top: 7.5rem;
  margin-bottom: 7.5rem;
}
.s\:my-140 {
  margin-top: 8.75rem;
  margin-bottom: 8.75rem;
}
.s\:my-160 {
  margin-top: 10rem;
  margin-bottom: 10rem;
}
.s\:my-180 {
  margin-top: 11.25rem;
  margin-bottom: 11.25rem;
}
.s\:my-200 {
  margin-top: 12.5rem;
  margin-bottom: 12.5rem;
}
.s\:my-220 {
  margin-top: 13.75rem;
  margin-bottom: 13.75rem;
}
.s\:my-240 {
  margin-top: 15rem;
  margin-bottom: 15rem;
}
.s\:my-260 {
  margin-top: 16.25rem;
  margin-bottom: 16.25rem;
}
.s\:my-280 {
  margin-top: 17.5rem;
  margin-bottom: 17.5rem;
}
.s\:my-300 {
  margin-top: 18.75rem;
  margin-bottom: 18.75rem;
}
.s\:my-320 {
  margin-top: 20rem;
  margin-bottom: 20rem;
}
.s\:my-340 {
  margin-top: 21.25rem;
  margin-bottom: 21.25rem;
}
.s\:my-360 {
  margin-top: 22.5rem;
  margin-bottom: 22.5rem;
}
.s\:my-380 {
  margin-top: 23.75rem;
  margin-bottom: 23.75rem;
}
.s\:my-400 {
  margin-top: 25rem;
  margin-bottom: 25rem;
}

/* Margin X (Left & Right) */
.s\:mx-0 {
  margin-left: 0px;
  margin-right: 0px;
}
.s\:mx-1 {
  margin-left: 1px;
  margin-right: 1px;
}
.s\:mx-2 {
  margin-left: 0.125rem;
  margin-right: 0.125rem;
}
.s\:mx-4 {
  margin-left: 0.25rem;
  margin-right: 0.25rem;
}
.s\:mx-6 {
  margin-left: 0.375rem;
  margin-right: 0.375rem;
}
.s\:mx-8 {
  margin-left: 0.5rem;
  margin-right: 0.5rem;
}
.s\:mx-10 {
  margin-left: 0.625rem;
  margin-right: 0.625rem;
}
.s\:mx-12 {
  margin-left: 0.75rem;
  margin-right: 0.75rem;
}
.s\:mx-14 {
  margin-left: 0.875rem;
  margin-right: 0.875rem;
}
.s\:mx-16 {
  margin-left: 1rem;
  margin-right: 1rem;
}
.s\:mx-18 {
  margin-left: 1.125rem;
  margin-right: 1.125rem;
}
.s\:mx-20 {
  margin-left: 1.25rem;
  margin-right: 1.25rem;
}
.s\:mx-22 {
  margin-left: 1.375rem;
  margin-right: 1.375rem;
}
.s\:mx-24 {
  margin-left: 1.5rem;
  margin-right: 1.5rem;
}
.s\:mx-28 {
  margin-left: 1.75rem;
  margin-right: 1.75rem;
}
.s\:mx-32 {
  margin-left: 2rem;
  margin-right: 2rem;
}
.s\:mx-36 {
  margin-left: 2.25rem;
  margin-right: 2.25rem;
}
.s\:mx-40 {
  margin-left: 2.5rem;
  margin-right: 2.5rem;
}
.s\:mx-44 {
  margin-left: 2.75rem;
  margin-right: 2.75rem;
}
.s\:mx-48 {
  margin-left: 3rem;
  margin-right: 3rem;
}
.s\:mx-52 {
  margin-left: 3.25rem;
  margin-right: 3.25rem;
}
.s\:mx-56 {
  margin-left: 3.5rem;
  margin-right: 3.5rem;
}
.s\:mx-60 {
  margin-left: 3.75rem;
  margin-right: 3.75rem;
}
.s\:mx-64 {
  margin-left: 4rem;
  margin-right: 4rem;
}
.s\:mx-68 {
  margin-left: 4.25rem;
  margin-right: 4.25rem;
}
.s\:mx-72 {
  margin-left: 4.5rem;
  margin-right: 4.5rem;
}
.s\:mx-76 {
  margin-left: 4.75rem;
  margin-right: 4.75rem;
}
.s\:mx-80 {
  margin-left: 5rem;
  margin-right: 5rem;
}
.s\:mx-84 {
  margin-left: 5.25rem;
  margin-right: 5.25rem;
}
.s\:mx-88 {
  margin-left: 5.5rem;
  margin-right: 5.5rem;
}
.s\:mx-92 {
  margin-left: 5.75rem;
  margin-right: 5.75rem;
}
.s\:mx-96 {
  margin-left: 6rem;
  margin-right: 6rem;
}
.s\:mx-100 {
  margin-left: 6.25rem;
  margin-right: 6.25rem;
}
.s\:mx-120 {
  margin-left: 7.5rem;
  margin-right: 7.5rem;
}
.s\:mx-140 {
  margin-left: 8.75rem;
  margin-right: 8.75rem;
}
.s\:mx-160 {
  margin-left: 10rem;
  margin-right: 10rem;
}
.s\:mx-180 {
  margin-left: 11.25rem;
  margin-right: 11.25rem;
}
.s\:mx-200 {
  margin-left: 12.5rem;
  margin-right: 12.5rem;
}
.s\:mx-220 {
  margin-left: 13.75rem;
  margin-right: 13.75rem;
}
.s\:mx-240 {
  margin-left: 15rem;
  margin-right: 15rem;
}
.s\:mx-260 {
  margin-left: 16.25rem;
  margin-right: 16.25rem;
}
.s\:mx-280 {
  margin-left: 17.5rem;
  margin-right: 17.5rem;
}
.s\:mx-300 {
  margin-left: 18.75rem;
  margin-right: 18.75rem;
}
.s\:mx-320 {
  margin-left: 20rem;
  margin-right: 20rem;
}
.s\:mx-340 {
  margin-left: 21.25rem;
  margin-right: 21.25rem;
}
.s\:mx-360 {
  margin-left: 22.5rem;
  margin-right: 22.5rem;
}
.s\:mx-380 {
  margin-left: 23.75rem;
  margin-right: 23.75rem;
}
.s\:mx-400 {
  margin-left: 25rem;
  margin-right: 25rem;
}

/* Negative Margin Bottom */
.s\:mb--1 { margin-bottom: -1px; }
.s\:mb--2 { margin-bottom: -0.125rem; }
.s\:mb--4 { margin-bottom: -0.25rem; }
.s\:mb--6 { margin-bottom: -0.375rem; }
.s\:mb--8 { margin-bottom: -0.5rem; }
.s\:mb--10 { margin-bottom: -0.625rem; }
.s\:mb--12 { margin-bottom: -0.75rem; }
.s\:mb--14 { margin-bottom: -0.875rem; }
.s\:mb--16 { margin-bottom: -1rem; }
.s\:mb--18 { margin-bottom: -1.125rem; }
.s\:mb--20 { margin-bottom: -1.25rem; }
.s\:mb--22 { margin-bottom: -1.375rem; }
.s\:mb--24 { margin-bottom: -1.5rem; }
.s\:mb--28 { margin-bottom: -1.75rem; }
.s\:mb--32 { margin-bottom: -2rem; }
.s\:mb--36 { margin-bottom: -2.25rem; }
.s\:mb--40 { margin-bottom: -2.5rem; }
.s\:mb--44 { margin-bottom: -2.75rem; }
.s\:mb--48 { margin-bottom: -3rem; }
.s\:mb--52 { margin-bottom: -3.25rem; }
.s\:mb--56 { margin-bottom: -3.5rem; }
.s\:mb--60 { margin-bottom: -3.75rem; }
.s\:mb--64 { margin-bottom: -4rem; }
.s\:mb--68 { margin-bottom: -4.25rem; }
.s\:mb--72 { margin-bottom: -4.5rem; }
.s\:mb--76 { margin-bottom: -4.75rem; }
.s\:mb--80 { margin-bottom: -5rem; }
.s\:mb--84 { margin-bottom: -5.25rem; }
.s\:mb--88 { margin-bottom: -5.5rem; }
.s\:mb--92 { margin-bottom: -5.75rem; }
.s\:mb--96 { margin-bottom: -6rem; }
.s\:mb--100 { margin-bottom: -6.25rem; }
.s\:mb--120 { margin-bottom: -7.5rem; }
.s\:mb--140 { margin-bottom: -8.75rem; }
.s\:mb--160 { margin-bottom: -10rem; }
.s\:mb--180 { margin-bottom: -11.25rem; }
.s\:mb--200 { margin-bottom: -12.5rem; }
.s\:mb--220 { margin-bottom: -13.75rem; }
.s\:mb--240 { margin-bottom: -15rem; }
.s\:mb--260 { margin-bottom: -16.25rem; }
.s\:mb--280 { margin-bottom: -17.5rem; }
.s\:mb--300 { margin-bottom: -18.75rem; }
.s\:mb--320 { margin-bottom: -20rem; }
.s\:mb--340 { margin-bottom: -21.25rem; }
.s\:mb--360 { margin-bottom: -22.5rem; }
.s\:mb--380 { margin-bottom: -23.75rem; }
.s\:mb--400 { margin-bottom: -25rem; }

/* Negative Margin Top */
.s\:mt--0 { margin-top: 0px; }
.s\:mt--1 { margin-top: -1px; }
.s\:mt--2 { margin-top: -0.125rem; }
.s\:mt--4 { margin-top: -0.25rem; }
.s\:mt--6 { margin-top: -0.375rem; }
.s\:mt--8 { margin-top: -0.5rem; }
.s\:mt--10 { margin-top: -0.625rem; }
.s\:mt--12 { margin-top: -0.75rem; }
.s\:mt--14 { margin-top: -0.875rem; }
.s\:mt--16 { margin-top: -1rem; }
.s\:mt--18 { margin-top: -1.125rem; }
.s\:mt--20 { margin-top: -1.25rem; }
.s\:mt--22 { margin-top: -1.375rem; }
.s\:mt--24 { margin-top: -1.5rem; }
.s\:mt--28 { margin-top: -1.75rem; }
.s\:mt--32 { margin-top: -2rem; }
.s\:mt--36 { margin-top: -2.25rem; }
.s\:mt--40 { margin-top: -2.5rem; }
.s\:mt--44 { margin-top: -2.75rem; }
.s\:mt--48 { margin-top: -3rem; }
.s\:mt--52 { margin-top: -3.25rem; }
.s\:mt--56 { margin-top: -3.5rem; }
.s\:mt--60 { margin-top: -3.75rem; }
.s\:mt--64 { margin-top: -4rem; }
.s\:mt--68 { margin-top: -4.25rem; }
.s\:mt--72 { margin-top: -4.5rem; }
.s\:mt--76 { margin-top: -4.75rem; }
.s\:mt--80 { margin-top: -5rem; }
.s\:mt--84 { margin-top: -5.25rem; }
.s\:mt--88 { margin-top: -5.5rem; }
.s\:mt--92 { margin-top: -5.75rem; }
.s\:mt--96 { margin-top: -6rem; }
.s\:mt--100 { margin-top: -6.25rem; }
.s\:mt--120 { margin-top: -7.5rem; }
.s\:mt--140 { margin-top: -8.75rem; }
.s\:mt--160 { margin-top: -10rem; }
.s\:mt--180 { margin-top: -11.25rem; }
.s\:mt--200 { margin-top: -12.5rem; }
.s\:mt--220 { margin-top: -13.75rem; }
.s\:mt--240 { margin-top: -15rem; }
.s\:mt--260 { margin-top: -16.25rem; }
.s\:mt--280 { margin-top: -17.5rem; }
.s\:mt--300 { margin-top: -18.75rem; }
.s\:mt--320 { margin-top: -20rem; }
.s\:mt--340 { margin-top: -21.25rem; }
.s\:mt--360 { margin-top: -22.5rem; }
.s\:mt--380 { margin-top: -23.75rem; }
.s\:mt--400 { margin-top: -25rem; }




/*--------------------------------------------------------------

## Negative Margin Left

--------------------------------------------------------------*/

.ml--0 {
  margin-left: 0px;
}

.ml--1 {
  margin-left: -1px;
}

.ml--2 {
  margin-left: -0.125rem;
}

.ml--4 {
  margin-left: calc((-0.25rem * var(--responsive-70)) * (var(--ml--4, 1)));
}

.ml--6 {
  margin-left: calc((-0.375rem * var(--responsive-70)) * (var(--ml--6, 1)));
}

.ml--8 {
  margin-left: calc((-0.5rem * var(--responsive-70)) * (var(--ml--8, 1)));
}

.ml--10 {
  margin-left: calc((-0.625rem * var(--responsive-70)) * (var(--ml--10, 1)));
}

.ml--12 {
  margin-left: calc((-0.75rem * var(--responsive-70)) * (var(--ml--12, 1)));
}

.ml--14 {
  margin-left: calc((-0.875rem * var(--responsive-70)) * (var(--ml--14, 1)));
}

.ml--16 {
  margin-left: calc((-1rem * var(--responsive-70)) * (var(--ml--16, 1)));
}

.ml--18 {
  margin-left: calc((-1.125rem * var(--responsive-70)) * (var(--ml--18, 1)));
}

.ml--20 {
  margin-left: calc((-1.25rem * var(--responsive-70)) * (var(--ml--20, 1)));
}

.ml--22 {
  margin-left: calc((-1.375rem * var(--responsive-70)) * (var(--ml--22, 1)));
}

.ml--24 {
  margin-left: calc((-1.5rem * var(--responsive-70)) * (var(--ml--24, 1)));
}

.ml--28 {
  margin-left: calc((-1.75rem * var(--responsive-70)) * (var(--ml--28, 1)));
}

.ml--32 {
  margin-left: calc((-2rem * var(--responsive-70)) * (var(--ml--32, 1)));
}

.ml--36 {
  margin-left: calc((-2.25rem * var(--responsive-70)) * (var(--ml--36, 1)));
}

.ml--40 {
  margin-left: calc((-2.5rem * var(--responsive-60)) * (var(--ml--40, 1)));
}

.ml--44 {
  margin-left: calc((-2.75rem * var(--responsive-60)) * (var(--ml--44, 1)));
}

.ml--48 {
  margin-left: calc((-3rem * var(--responsive-60)) * (var(--ml--48, 1)));
}

.ml--52 {
  margin-left: calc((-3.25rem * var(--responsive-60)) * (var(--ml--52, 1)));
}

.ml--56 {
  margin-left: calc((-3.5rem * var(--responsive-60)) * (var(--ml--56, 1)));
}

.ml--60 {
  margin-left: calc((-3.75rem * var(--responsive-50)) * (var(--ml--60, 1)));
}

.ml--64 {
  margin-left: calc((-4rem * var(--responsive-50)) * (var(--ml--64, 1)));
}

.ml--68 {
  margin-left: calc((-4.25rem * var(--responsive-50)) * (var(--ml--68, 1)));
}

.ml--72 {
  margin-left: calc((-4.5rem * var(--responsive-50)) * (var(--ml--72, 1)));
}

.ml--76 {
  margin-left: calc((-4.75rem * var(--responsive-50)) * (var(--ml--76, 1)));
}

.ml--80 {
  margin-left: calc((-5rem * var(--responsive-40)) * (var(--ml--80, 1)));
}

.ml--84 {
  margin-left: calc((-5.25rem * var(--responsive-40)) * (var(--ml--84, 1)));
}

.ml--88 {
  margin-left: calc((-5.5rem * var(--responsive-40)) * (var(--ml--88, 1)));
}

.ml--92 {
  margin-left: calc((-5.75rem * var(--responsive-40)) * (var(--ml--92, 1)));
}

.ml--96 {
  margin-left: calc((-6rem * var(--responsive-40)) * (var(--ml--96, 1)));
}

.ml--100 {
  margin-left: calc((-6.25rem * var(--responsive-30)) * (var(--ml--100, 1)));
}

.ml--120 {
  margin-left: calc((-7.5rem * var(--responsive-30)) * (var(--ml--120, 1)));
}

.ml--140 {
  margin-left: calc((-8.75rem * var(--responsive-30)) * (var(--ml--140, 1)));
}

.ml--160 {
  margin-left: calc((-10rem * var(--responsive-30)) * (var(--ml--160, 1)));
}

.ml--180 {
  margin-left: calc((-11.25rem * var(--responsive-30)) * (var(--ml--180, 1)));
}

.ml--200 {
  margin-left: calc((-12.5rem * var(--responsive-30)) * (var(--ml--200, 1)));
}

.ml--220 {
  margin-left: calc((-13.75rem * var(--responsive-70)) * (var(--ml--220, 1)));
}

.ml--240 {
  margin-left: calc((-15rem * var(--responsive-70)) * (var(--ml--240, 1)));
}

.ml--260 {
  margin-left: calc((-16.25rem * var(--responsive-70)) * (var(--ml--260, 1)));
}

.ml--280 {
  margin-left: calc((-17.5rem * var(--responsive-70)) * (var(--ml--280, 1)));
}

.ml--300 {
  margin-left: calc((-18.75rem * var(--responsive-70)) * (var(--ml--300, 1)));
}

.ml--320 {
  margin-left: calc((-20rem * var(--responsive-70)) * (var(--ml--320, 1)));
}

.ml--340 {
  margin-left: calc((-21.25rem * var(--responsive-70)) * (var(--ml--340, 1)));
}

.ml--360 {
  margin-left: calc((-22.5rem * var(--responsive-70)) * (var(--ml--360, 1)));
}

.ml--380 {
  margin-left: calc((-23.75rem * var(--responsive-70)) * (var(--ml--380, 1)));
}

.ml--400 {
  margin-left: calc((-25rem * var(--responsive-70)) * (var(--ml--400, 1)));
}

/*--------------------------------------------------------------

## Negative Margin Right

--------------------------------------------------------------*/

.mr--1 {
  margin-right: -1px;
}

.mr--2 {
  margin-right: -0.125rem;
}

.mr--4 {
  margin-right: calc((-0.25rem * var(--responsive-70)) * (var(--mr--4, 1)));
}

.mr--6 {
  margin-right: calc((-0.375rem * var(--responsive-70)) * (var(--mr--6, 1)));
}

.mr--8 {
  margin-right: calc((-0.5rem * var(--responsive-70)) * (var(--mr--8, 1)));
}

.mr--10 {
  margin-right: calc((-0.625rem * var(--responsive-70)) * (var(--mr--10, 1)));
}

.mr--12 {
  margin-right: calc((-0.75rem * var(--responsive-70)) * (var(--mr--12, 1)));
}

.mr--14 {
  margin-right: calc((-0.875rem * var(--responsive-70)) * (var(--mr--14, 1)));
}

.mr--16 {
  margin-right: calc((-1rem * var(--responsive-70)) * (var(--mr--16, 1)));
}

.mr--18 {
  margin-right: calc((-1.125rem * var(--responsive-70)) * (var(--mr--18, 1)));
}

.mr--20 {
  margin-right: calc((-1.25rem * var(--responsive-70)) * (var(--mr--20, 1)));
}

.mr--22 {
  margin-right: calc((-1.375rem * var(--responsive-70)) * (var(--mr--22, 1)));
}

.mr--24 {
  margin-right: calc((-1.5rem * var(--responsive-70)) * (var(--mr--24, 1)));
}

.mr--28 {
  margin-right: calc((-1.75rem * var(--responsive-70)) * (var(--mr--28, 1)));
}

.mr--32 {
  margin-right: calc((-2rem * var(--responsive-70)) * (var(--mr--32, 1)));
}

.mr--36 {
  margin-right: calc((-2.25rem * var(--responsive-70)) * (var(--mr--36, 1)));
}

.mr--40 {
  margin-right: calc((-2.5rem * var(--responsive-60)) * (var(--mr--40, 1)));
}

.mr--44 {
  margin-right: calc((-2.75rem * var(--responsive-60)) * (var(--mr--44, 1)));
}

.mr--48 {
  margin-right: calc((-3rem * var(--responsive-60)) * (var(--mr--48, 1)));
}

.mr--52 {
  margin-right: calc((-3.25rem * var(--responsive-60)) * (var(--mr--52, 1)));
}

.mr--56 {
  margin-right: calc((-3.5rem * var(--responsive-60)) * (var(--mr--56, 1)));
}

.mr--60 {
  margin-right: calc((-3.75rem * var(--responsive-50)) * (var(--mr--60, 1)));
}

.mr--64 {
  margin-right: calc((-4rem * var(--responsive-50)) * (var(--mr--64, 1)));
}

.mr--68 {
  margin-right: calc((-4.25rem * var(--responsive-50)) * (var(--mr--68, 1)));
}

.mr--72 {
  margin-right: calc((-4.5rem * var(--responsive-50)) * (var(--mr--72, 1)));
}

.mr--76 {
  margin-right: calc((-4.75rem * var(--responsive-50)) * (var(--mr--76, 1)));
}

.mr--80 {
  margin-right: calc((-5rem * var(--responsive-40)) * (var(--mr--80, 1)));
}

.mr--84 {
  margin-right: calc((-5.25rem * var(--responsive-40)) * (var(--mr--84, 1)));
}

.mr--88 {
  margin-right: calc((-5.5rem * var(--responsive-40)) * (var(--mr--88, 1)));
}

.mr--92 {
  margin-right: calc((-5.75rem * var(--responsive-40)) * (var(--mr--92, 1)));
}

.mr--96 {
  margin-right: calc((-6rem * var(--responsive-40)) * (var(--mr--96, 1)));
}

.mr--100 {
  margin-right: calc((-6.25rem * var(--responsive-30)) * (var(--mr--100, 1)));
}

.mr--120 {
  margin-right: calc((-7.5rem * var(--responsive-30)) * (var(--mr--120, 1)));
}

.mr--140 {
  margin-right: calc((-8.75rem * var(--responsive-30)) * (var(--mr--140, 1)));
}

.mr--160 {
  margin-right: calc((-10rem * var(--responsive-30)) * (var(--mr--160, 1)));
}

.mr--180 {
  margin-right: calc((-11.25rem * var(--responsive-30)) * (var(--mr--180, 1)));
}

.mr--200 {
  margin-right: calc((-12.5rem * var(--responsive-30)) * (var(--mr--200, 1)));
}

.mr--220 {
  margin-right: calc((-13.75rem * var(--responsive-70)) * (var(--mr--220, 1)));
}

.mr--240 {
  margin-right: calc((-15rem * var(--responsive-70)) * (var(--mr--240, 1)));
}

.mr--260 {
  margin-right: calc((-16.25rem * var(--responsive-70)) * (var(--mr--260, 1)));
}

.mr--280 {
  margin-right: calc((-17.5rem * var(--responsive-70)) * (var(--mr--280, 1)));
}

.mr--300 {
  margin-right: calc((-18.75rem * var(--responsive-70)) * (var(--mr--300, 1)));
}

.mr--320 {
  margin-right: calc((-20rem * var(--responsive-70)) * (var(--mr--320, 1)));
}

.mr--340 {
  margin-right: calc((-21.25rem * var(--responsive-70)) * (var(--mr--340, 1)));
}

.mr--360 {
  margin-right: calc((-22.5rem * var(--responsive-70)) * (var(--mr--360, 1)));
}

.mr--380 {
  margin-right: calc((-23.75rem * var(--responsive-70)) * (var(--mr--380, 1)));
}

.mr--400 {
  margin-right: calc((-25rem * var(--responsive-70)) * (var(--mr--400, 1)));
}

/*--------------------------------------------------------------

## Negative Margin Y (Top and Bottom)

--------------------------------------------------------------*/

.my--1 {
  margin-top: -1px;
  margin-bottom: -1px;
}

.my--2 {
  margin-top: -0.125rem;
  margin-bottom: -0.125rem;
}

.my--4 {
  margin-top: calc((-0.25rem * var(--responsive-70)) * (var(--my--4, 1)));
  margin-bottom: calc((-0.25rem * var(--responsive-70)) * (var(--my--4, 1)));
}

.my--6 {
  margin-top: calc((-0.375rem * var(--responsive-70)) * (var(--my--6, 1)));
  margin-bottom: calc((-0.375rem * var(--responsive-70)) * (var(--my--6, 1)));
}

.my--8 {
  margin-top: calc((-0.5rem * var(--responsive-70)) * (var(--my--8, 1)));
  margin-bottom: calc((-0.5rem * var(--responsive-70)) * (var(--my--8, 1)));
}

.my--10 {
  margin-top: calc((-0.625rem * var(--responsive-70)) * (var(--my--10, 1)));
  margin-bottom: calc((-0.625rem * var(--responsive-70)) * (var(--my--10, 1)));
}

.my--12 {
  margin-top: calc((-0.75rem * var(--responsive-70)) * (var(--my--12, 1)));
  margin-bottom: calc((-0.75rem * var(--responsive-70)) * (var(--my--12, 1)));
}

.my--14 {
  margin-top: calc((-0.875rem * var(--responsive-70)) * (var(--my--14, 1)));
  margin-bottom: calc((-0.875rem * var(--responsive-70)) * (var(--my--14, 1)));
}

.my--16 {
  margin-top: calc((-1rem * var(--responsive-70)) * (var(--my--16, 1)));
  margin-bottom: calc((-1rem * var(--responsive-70)) * (var(--my--16, 1)));
}

.my--18 {
  margin-top: calc((-1.125rem * var(--responsive-70)) * (var(--my--18, 1)));
  margin-bottom: calc((-1.125rem * var(--responsive-70)) * (var(--my--18, 1)));
}

.my--20 {
  margin-top: calc((-1.25rem * var(--responsive-70)) * (var(--my--20, 1)));
  margin-bottom: calc((-1.25rem * var(--responsive-70)) * (var(--my--20, 1)));
}

.my--22 {
  margin-top: calc((-1.375rem * var(--responsive-70)) * (var(--my--22, 1)));
  margin-bottom: calc((-1.375rem * var(--responsive-70)) * (var(--my--22, 1)));
}

.my--24 {
  margin-top: calc((-1.5rem * var(--responsive-70)) * (var(--my--24, 1)));
  margin-bottom: calc((-1.5rem * var(--responsive-70)) * (var(--my--24, 1)));
}

.my--28 {
  margin-top: calc((-1.75rem * var(--responsive-70)) * (var(--my--28, 1)));
  margin-bottom: calc((-1.75rem * var(--responsive-70)) * (var(--my--28, 1)));
}

.my--32 {
  margin-top: calc((-2rem * var(--responsive-70)) * (var(--my--32, 1)));
  margin-bottom: calc((-2rem * var(--responsive-70)) * (var(--my--32, 1)));
}

.my--36 {
  margin-top: calc((-2.25rem * var(--responsive-70)) * (var(--my--36, 1)));
  margin-bottom: calc((-2.25rem * var(--responsive-70)) * (var(--my--36, 1)));
}

.my--40 {
  margin-top: calc((-2.5rem * var(--responsive-60)) * (var(--my--40, 1)));
  margin-bottom: calc((-2.5rem * var(--responsive-60)) * (var(--my--40, 1)));
}

.my--44 {
  margin-top: calc((-2.75rem * var(--responsive-60)) * (var(--my--44, 1)));
  margin-bottom: calc((-2.75rem * var(--responsive-60)) * (var(--my--44, 1)));
}

.my--48 {
  margin-top: calc((-3rem * var(--responsive-60)) * (var(--my--48, 1)));
  margin-bottom: calc((-3rem * var(--responsive-60)) * (var(--my--48, 1)));
}

.my--52 {
  margin-top: calc((-3.25rem * var(--responsive-60)) * (var(--my--52, 1)));
  margin-bottom: calc((-3.25rem * var(--responsive-60)) * (var(--my--52, 1)));
}

.my--56 {
  margin-top: calc((-3.5rem * var(--responsive-60)) * (var(--my--56, 1)));
  margin-bottom: calc((-3.5rem * var(--responsive-60)) * (var(--my--56, 1)));
}

.my--60 {
  margin-top: calc((-3.75rem * var(--responsive-50)) * (var(--my--60, 1)));
  margin-bottom: calc((-3.75rem * var(--responsive-50)) * (var(--my--60, 1)));
}

.my--64 {
  margin-top: calc((-4rem * var(--responsive-50)) * (var(--my--64, 1)));
  margin-bottom: calc((-4rem * var(--responsive-50)) * (var(--my--64, 1)));
}

.my--68 {
  margin-top: calc((-4.25rem * var(--responsive-50)) * (var(--my--68, 1)));
  margin-bottom: calc((-4.25rem * var(--responsive-50)) * (var(--my--68, 1)));
}

.my--72 {
  margin-top: calc((-4.5rem * var(--responsive-50)) * (var(--my--72, 1)));
  margin-bottom: calc((-4.5rem * var(--responsive-50)) * (var(--my--72, 1)));
}

.my--76 {
  margin-top: calc((-4.75rem * var(--responsive-50)) * (var(--my--76, 1)));
  margin-bottom: calc((-4.75rem * var(--responsive-50)) * (var(--my--76, 1)));
}

.my--80 {
  margin-top: calc((-5rem * var(--responsive-40)) * (var(--my--80, 1)));
  margin-bottom: calc((-5rem * var(--responsive-40)) * (var(--my--80, 1)));
}

.my--84 {
  margin-top: calc((-5.25rem * var(--responsive-40)) * (var(--my--84, 1)));
  margin-bottom: calc((-5.25rem * var(--responsive-40)) * (var(--my--84, 1)));
}

.my--88 {
  margin-top: calc((-5.5rem * var(--responsive-40)) * (var(--my--88, 1)));
  margin-bottom: calc((-5.5rem * var(--responsive-40)) * (var(--my--88, 1)));
}

.my--92 {
  margin-top: calc((-5.75rem * var(--responsive-40)) * (var(--my--92, 1)));
  margin-bottom: calc((-5.75rem * var(--responsive-40)) * (var(--my--92, 1)));
}

.my--96 {
  margin-top: calc((-6rem * var(--responsive-40)) * (var(--my--96, 1)));
  margin-bottom: calc((-6rem * var(--responsive-40)) * (var(--my--96, 1)));
}

.my--100 {
  margin-top: calc((-6.25rem * var(--responsive-30)) * (var(--my--100, 1)));
  margin-bottom: calc((-6.25rem * var(--responsive-30)) * (var(--my--100, 1)));
}

.my--120 {
  margin-top: calc((-7.5rem * var(--responsive-30)) * (var(--my--120, 1)));
  margin-bottom: calc((-7.5rem * var(--responsive-30)) * (var(--my--120, 1)));
}

.my--140 {
  margin-top: calc((-8.75rem * var(--responsive-30)) * (var(--my--140, 1)));
  margin-bottom: calc((-8.75rem * var(--responsive-30)) * (var(--my--140, 1)));
}

.my--160 {
  margin-top: calc((-10rem * var(--responsive-30)) * (var(--my--160, 1)));
  margin-bottom: calc((-10rem * var(--responsive-30)) * (var(--my--160, 1)));
}

.my--180 {
  margin-top: calc((-11.25rem * var(--responsive-30)) * (var(--my--180, 1)));
  margin-bottom: calc((-11.25rem * var(--responsive-30)) * (var(--my--180, 1)));
}

.my--200 {
  margin-top: calc((-12.5rem * var(--responsive-30)) * (var(--my--200, 1)));
  margin-bottom: calc((-12.5rem * var(--responsive-30)) * (var(--my--200, 1)));
}

.my--220 {
  margin-top: calc((-13.75rem * var(--responsive-70)) * (var(--my--220, 1)));
  margin-bottom: calc((-13.75rem * var(--responsive-70)) * (var(--my--220, 1)));
}

.my--240 {
  margin-top: calc((-15rem * var(--responsive-70)) * (var(--my--240, 1)));
  margin-bottom: calc((-15rem * var(--responsive-70)) * (var(--my--240, 1)));
}

.my--260 {
  margin-top: calc((-16.25rem * var(--responsive-70)) * (var(--my--260, 1)));
  margin-bottom: calc((-16.25rem * var(--responsive-70)) * (var(--my--260, 1)));
}

.my--280 {
  margin-top: calc((-17.5rem * var(--responsive-70)) * (var(--my--280, 1)));
  margin-bottom: calc((-17.5rem * var(--responsive-70)) * (var(--my--280, 1)));
}

.my--300 {
  margin-top: calc((-18.75rem * var(--responsive-70)) * (var(--my--300, 1)));
  margin-bottom: calc((-18.75rem * var(--responsive-70)) * (var(--my--300, 1)));
}

.my--320 {
  margin-top: calc((-20rem * var(--responsive-70)) * (var(--my--320, 1)));
  margin-bottom: calc((-20rem * var(--responsive-70)) * (var(--my--320, 1)));
}

.my--340 {
  margin-top: calc((-21.25rem * var(--responsive-70)) * (var(--my--340, 1)));
  margin-bottom: calc((-21.25rem * var(--responsive-70)) * (var(--my--340, 1)));
}

.my--360 {
  margin-top: calc((-22.5rem * var(--responsive-70)) * (var(--my--360, 1)));
  margin-bottom: calc((-22.5rem * var(--responsive-70)) * (var(--my--360, 1)));
}

.my--380 {
  margin-top: calc((-23.75rem * var(--responsive-70)) * (var(--my--380, 1)));
  margin-bottom: calc((-23.75rem * var(--responsive-70)) * (var(--my--380, 1)));
}

.my--400 {
  margin-top: calc((-25rem * var(--responsive-70)) * (var(--my--400, 1)));
  margin-bottom: calc((-25rem * var(--responsive-70)) * (var(--my--400, 1)));
}

/*--------------------------------------------------------------

## Negative Margin X (Left and Right)

--------------------------------------------------------------*/

.mx--0 {
  margin-left: 0px;
  margin-right: 0px;
}

.mx--1 {
  margin-left: -1px;
  margin-right: -1px;
}

.mx--2 {
  margin-left: -0.125rem;
  margin-right: -0.125rem;
}

.mx--4 {
  margin-left: calc((-0.25rem * var(--responsive-70)) * (var(--mx--4, 1)));
  margin-right: calc((-0.25rem * var(--responsive-70)) * (var(--mx--4, 1)));
}

.mx--6 {
  margin-left: calc((-0.375rem * var(--responsive-70)) * (var(--mx--6, 1)));
  margin-right: calc((-0.375rem * var(--responsive-70)) * (var(--mx--6, 1)));
}

.mx--8 {
  margin-left: calc((-0.5rem * var(--responsive-70)) * (var(--mx--8, 1)));
  margin-right: calc((-0.5rem * var(--responsive-70)) * (var(--mx--8, 1)));
}

.mx--10 {
  margin-left: calc((-0.625rem * var(--responsive-70)) * (var(--mx--10, 1)));
  margin-right: calc((-0.625rem * var(--responsive-70)) * (var(--mx--10, 1)));
}

.mx--12 {
  margin-left: calc((-0.75rem * var(--responsive-70)) * (var(--mx--12, 1)));
  margin-right: calc((-0.75rem * var(--responsive-70)) * (var(--mx--12, 1)));
}

.mx--14 {
  margin-left: calc((-0.875rem * var(--responsive-70)) * (var(--mx--14, 1)));
  margin-right: calc((-0.875rem * var(--responsive-70)) * (var(--mx--14, 1)));
}

.mx--16 {
  margin-left: calc((-1rem * var(--responsive-70)) * (var(--mx--16, 1)));
  margin-right: calc((-1rem * var(--responsive-70)) * (var(--mx--16, 1)));
}

.mx--18 {
  margin-left: calc((-1.125rem * var(--responsive-70)) * (var(--mx--18, 1)));
  margin-right: calc((-1.125rem * var(--responsive-70)) * (var(--mx--18, 1)));
}

.mx--20 {
  margin-left: calc((-1.25rem * var(--responsive-70)) * (var(--mx--20, 1)));
  margin-right: calc((-1.25rem * var(--responsive-70)) * (var(--mx--20, 1)));
}

.mx--22 {
  margin-left: calc((-1.375rem * var(--responsive-70)) * (var(--mx--22, 1)));
  margin-right: calc((-1.375rem * var(--responsive-70)) * (var(--mx--22, 1)));
}

.mx--24 {
  margin-left: calc((-1.5rem * var(--responsive-70)) * (var(--mx--24, 1)));
  margin-right: calc((-1.5rem * var(--responsive-70)) * (var(--mx--24, 1)));
}

.mx--28 {
  margin-left: calc((-1.75rem * var(--responsive-70)) * (var(--mx--28, 1)));
  margin-right: calc((-1.75rem * var(--responsive-70)) * (var(--mx--28, 1)));
}

.mx--32 {
  margin-left: calc((-2rem * var(--responsive-70)) * (var(--mx--32, 1)));
  margin-right: calc((-2rem * var(--responsive-70)) * (var(--mx--32, 1)));
}

.mx--36 {
  margin-left: calc((-2.25rem * var(--responsive-70)) * (var(--mx--36, 1)));
  margin-right: calc((-2.25rem * var(--responsive-70)) * (var(--mx--36, 1)));
}

.mx--40 {
  margin-left: calc((-2.5rem * var(--responsive-60)) * (var(--mx--40, 1)));
  margin-right: calc((-2.5rem * var(--responsive-60)) * (var(--mx--40, 1)));
}

.mx--44 {
  margin-left: calc((-2.75rem * var(--responsive-60)) * (var(--mx--44, 1)));
  margin-right: calc((-2.75rem * var(--responsive-60)) * (var(--mx--44, 1)));
}

.mx--48 {
  margin-left: calc((-3rem * var(--responsive-60)) * (var(--mx--48, 1)));
  margin-right: calc((-3rem * var(--responsive-60)) * (var(--mx--48, 1)));
}

.mx--52 {
  margin-left: calc((-3.25rem * var(--responsive-60)) * (var(--mx--52, 1)));
  margin-right: calc((-3.25rem * var(--responsive-60)) * (var(--mx--52, 1)));
}

.mx--56 {
  margin-left: calc((-3.5rem * var(--responsive-60)) * (var(--mx--56, 1)));
  margin-right: calc((-3.5rem * var(--responsive-60)) * (var(--mx--56, 1)));
}

.mx--60 {
  margin-left: calc((-3.75rem * var(--responsive-50)) * (var(--mx--60, 1)));
  margin-right: calc((-3.75rem * var(--responsive-50)) * (var(--mx--60, 1)));
}

.mx--64 {
  margin-left: calc((-4rem * var(--responsive-50)) * (var(--mx--64, 1)));
  margin-right: calc((-4rem * var(--responsive-50)) * (var(--mx--64, 1)));
}

.mx--68 {
  margin-left: calc((-4.25rem * var(--responsive-50)) * (var(--mx--68, 1)));
  margin-right: calc((-4.25rem * var(--responsive-50)) * (var(--mx--68, 1)));
}

.mx--72 {
  margin-left: calc((-4.5rem * var(--responsive-50)) * (var(--mx--72, 1)));
  margin-right: calc((-4.5rem * var(--responsive-50)) * (var(--mx--72, 1)));
}

.mx--76 {
  margin-left: calc((-4.75rem * var(--responsive-50)) * (var(--mx--76, 1)));
  margin-right: calc((-4.75rem * var(--responsive-50)) * (var(--mx--76, 1)));
}

.mx--80 {
  margin-left: calc((-5rem * var(--responsive-40)) * (var(--mx--80, 1)));
  margin-right: calc((-5rem * var(--responsive-40)) * (var(--mx--80, 1)));
}

.mx--84 {
  margin-left: calc((-5.25rem * var(--responsive-40)) * (var(--mx--84, 1)));
  margin-right: calc((-5.25rem * var(--responsive-40)) * (var(--mx--84, 1)));
}

.mx--88 {
  margin-left: calc((-5.5rem * var(--responsive-40)) * (var(--mx--88, 1)));
  margin-right: calc((-5.5rem * var(--responsive-40)) * (var(--mx--88, 1)));
}

.mx--92 {
  margin-left: calc((-5.75rem * var(--responsive-40)) * (var(--mx--92, 1)));
  margin-right: calc((-5.75rem * var(--responsive-40)) * (var(--mx--92, 1)));
}

.mx--96 {
  margin-left: calc((-6rem * var(--responsive-40)) * (var(--mx--96, 1)));
  margin-right: calc((-6rem * var(--responsive-40)) * (var(--mx--96, 1)));
}

.mx--100 {
  margin-left: calc((-6.25rem * var(--responsive-30)) * (var(--mx--100, 1)));
  margin-right: calc((-6.25rem * var(--responsive-30)) * (var(--mx--100, 1)));
}

.mx--120 {
  margin-left: calc((-7.5rem * var(--responsive-30)) * (var(--mx--120, 1)));
  margin-right: calc((-7.5rem * var(--responsive-30)) * (var(--mx--120, 1)));
}

.mx--140 {
  margin-left: calc((-8.75rem * var(--responsive-30)) * (var(--mx--140, 1)));
  margin-right: calc((-8.75rem * var(--responsive-30)) * (var(--mx--140, 1)));
}

.mx--160 {
  margin-left: calc((-10rem * var(--responsive-30)) * (var(--mx--160, 1)));
  margin-right: calc((-10rem * var(--responsive-30)) * (var(--mx--160, 1)));
}

.mx--180 {
  margin-left: calc((-11.25rem * var(--responsive-30)) * (var(--mx--180, 1)));
  margin-right: calc((-11.25rem * var(--responsive-30)) * (var(--mx--180, 1)));
}

.mx--200 {
  margin-left: calc((-12.5rem * var(--responsive-30)) * (var(--mx--200, 1)));
  margin-right: calc((-12.5rem * var(--responsive-30)) * (var(--mx--200, 1)));
}

.mx--220 {
  margin-left: calc((-13.75rem * var(--responsive-70)) * (var(--mx--220, 1)));
  margin-right: calc((-13.75rem * var(--responsive-70)) * (var(--mx--220, 1)));
}

.mx--240 {
  margin-left: calc((-15rem * var(--responsive-70)) * (var(--mx--240, 1)));
  margin-right: calc((-15rem * var(--responsive-70)) * (var(--mx--240, 1)));
}

.mx--260 {
  margin-left: calc((-16.25rem * var(--responsive-70)) * (var(--mx--260, 1)));
  margin-right: calc((-16.25rem * var(--responsive-70)) * (var(--mx--260, 1)));
}

.mx--280 {
  margin-left: calc((-17.5rem * var(--responsive-70)) * (var(--mx--280, 1)));
  margin-right: calc((-17.5rem * var(--responsive-70)) * (var(--mx--280, 1)));
}

.mx--300 {
  margin-left: calc((-18.75rem * var(--responsive-70)) * (var(--mx--300, 1)));
  margin-right: calc((-18.75rem * var(--responsive-70)) * (var(--mx--300, 1)));
}

.mx--320 {
  margin-left: calc((-20rem * var(--responsive-70)) * (var(--mx--320, 1)));
  margin-right: calc((-20rem * var(--responsive-70)) * (var(--mx--320, 1)));
}

.mx--340 {
  margin-left: calc((-21.25rem * var(--responsive-70)) * (var(--mx--340, 1)));
  margin-right: calc((-21.25rem * var(--responsive-70)) * (var(--mx--340, 1)));
}

.mx--360 {
  margin-left: calc((-22.5rem * var(--responsive-70)) * (var(--mx--360, 1)));
  margin-right: calc((-22.5rem * var(--responsive-70)) * (var(--mx--360, 1)));
}

.mx--380 {
  margin-left: calc((-23.75rem * var(--responsive-70)) * (var(--mx--380, 1)));
  margin-right: calc((-23.75rem * var(--responsive-70)) * (var(--mx--380, 1)));
}

.mx--400 {
  margin-left: calc((-25rem * var(--responsive-70)) * (var(--mx--400, 1)));
  margin-right: calc((-25rem * var(--responsive-70)) * (var(--mx--400, 1)));
}

/* Negative Margin Left */
.s\:ml--0 { margin-left: 0px; }
.s\:ml--1 { margin-left: -1px; }
.s\:ml--2 { margin-left: -0.125rem; }
.s\:ml--4 { margin-left: -0.25rem; }
.s\:ml--6 { margin-left: -0.375rem; }
.s\:ml--8 { margin-left: -0.5rem; }
.s\:ml--10 { margin-left: -0.625rem; }
.s\:ml--12 { margin-left: -0.75rem; }
.s\:ml--14 { margin-left: -0.875rem; }
.s\:ml--16 { margin-left: -1rem; }
.s\:ml--18 { margin-left: -1.125rem; }
.s\:ml--20 { margin-left: -1.25rem; }
.s\:ml--22 { margin-left: -1.375rem; }
.s\:ml--24 { margin-left: -1.5rem; }
.s\:ml--28 { margin-left: -1.75rem; }
.s\:ml--32 { margin-left: -2rem; }
.s\:ml--36 { margin-left: -2.25rem; }
.s\:ml--40 { margin-left: -2.5rem; }
.s\:ml--44 { margin-left: -2.75rem; }
.s\:ml--48 { margin-left: -3rem; }
.s\:ml--52 { margin-left: -3.25rem; }
.s\:ml--56 { margin-left: -3.5rem; }
.s\:ml--60 { margin-left: -3.75rem; }
.s\:ml--64 { margin-left: -4rem; }
.s\:ml--68 { margin-left: -4.25rem; }
.s\:ml--72 { margin-left: -4.5rem; }
.s\:ml--76 { margin-left: -4.75rem; }
.s\:ml--80 { margin-left: -5rem; }
.s\:ml--84 { margin-left: -5.25rem; }
.s\:ml--88 { margin-left: -5.5rem; }
.s\:ml--92 { margin-left: -5.75rem; }
.s\:ml--96 { margin-left: -6rem; }
.s\:ml--100 { margin-left: -6.25rem; }
.s\:ml--120 { margin-left: -7.5rem; }
.s\:ml--140 { margin-left: -8.75rem; }
.s\:ml--160 { margin-left: -10rem; }
.s\:ml--180 { margin-left: -11.25rem; }
.s\:ml--200 { margin-left: -12.5rem; }
.s\:ml--220 { margin-left: -13.75rem; }
.s\:ml--240 { margin-left: -15rem; }
.s\:ml--260 { margin-left: -16.25rem; }
.s\:ml--280 { margin-left: -17.5rem; }
.s\:ml--300 { margin-left: -18.75rem; }
.s\:ml--320 { margin-left: -20rem; }
.s\:ml--340 { margin-left: -21.25rem; }
.s\:ml--360 { margin-left: -22.5rem; }
.s\:ml--380 { margin-left: -23.75rem; }
.s\:ml--400 { margin-left: -25rem; }

/* Negative Margin Right */
.s\:mr--1 { margin-right: -1px; }
.s\:mr--2 { margin-right: -0.125rem; }
.s\:mr--4 { margin-right: -0.25rem; }
.s\:mr--6 { margin-right: -0.375rem; }
.s\:mr--8 { margin-right: -0.5rem; }
.s\:mr--10 { margin-right: -0.625rem; }
.s\:mr--12 { margin-right: -0.75rem; }
.s\:mr--14 { margin-right: -0.875rem; }
.s\:mr--16 { margin-right: -1rem; }
.s\:mr--18 { margin-right: -1.125rem; }
.s\:mr--20 { margin-right: -1.25rem; }
.s\:mr--22 { margin-right: -1.375rem; }
.s\:mr--24 { margin-right: -1.5rem; }
.s\:mr--28 { margin-right: -1.75rem; }
.s\:mr--32 { margin-right: -2rem; }
.s\:mr--36 { margin-right: -2.25rem; }
.s\:mr--40 { margin-right: -2.5rem; }
.s\:mr--44 { margin-right: -2.75rem; }
.s\:mr--48 { margin-right: -3rem; }
.s\:mr--52 { margin-right: -3.25rem; }
.s\:mr--56 { margin-right: -3.5rem; }
.s\:mr--60 { margin-right: -3.75rem; }
.s\:mr--64 { margin-right: -4rem; }
.s\:mr--68 { margin-right: -4.25rem; }
.s\:mr--72 { margin-right: -4.5rem; }
.s\:mr--76 { margin-right: -4.75rem; }
.s\:mr--80 { margin-right: -5rem; }
.s\:mr--84 { margin-right: -5.25rem; }
.s\:mr--88 { margin-right: -5.5rem; }
.s\:mr--92 { margin-right: -5.75rem; }
.s\:mr--96 { margin-right: -6rem; }
.s\:mr--100 { margin-right: -6.25rem; }
.s\:mr--120 { margin-right: -7.5rem; }
.s\:mr--140 { margin-right: -8.75rem; }
.s\:mr--160 { margin-right: -10rem; }
.s\:mr--180 { margin-right: -11.25rem; }
.s\:mr--200 { margin-right: -12.5rem; }
.s\:mr--220 { margin-right: -13.75rem; }
.s\:mr--240 { margin-right: -15rem; }
.s\:mr--260 { margin-right: -16.25rem; }
.s\:mr--280 { margin-right: -17.5rem; }
.s\:mr--300 { margin-right: -18.75rem; }
.s\:mr--320 { margin-right: -20rem; }
.s\:mr--340 { margin-right: -21.25rem; }
.s\:mr--360 { margin-right: -22.5rem; }
.s\:mr--380 { margin-right: -23.75rem; }
.s\:mr--400 { margin-right: -25rem; }

/* Negative Margin Y (Top & Bottom) */
.s\:my--1 {
  margin-top: -1px;
  margin-bottom: -1px;
}
.s\:my--2 {
  margin-top: -0.125rem;
  margin-bottom: -0.125rem;
}
.s\:my--4 {
  margin-top: -0.25rem;
  margin-bottom: -0.25rem;
}
.s\:my--6 {
  margin-top: -0.375rem;
  margin-bottom: -0.375rem;
}
.s\:my--8 {
  margin-top: -0.5rem;
  margin-bottom: -0.5rem;
}
.s\:my--10 {
  margin-top: -0.625rem;
  margin-bottom: -0.625rem;
}
.s\:my--12 {
  margin-top: -0.75rem;
  margin-bottom: -0.75rem;
}
.s\:my--14 {
  margin-top: -0.875rem;
  margin-bottom: -0.875rem;
}
.s\:my--16 {
  margin-top: -1rem;
  margin-bottom: -1rem;
}
.s\:my--18 {
  margin-top: -1.125rem;
  margin-bottom: -1.125rem;
}
.s\:my--20 {
  margin-top: -1.25rem;
  margin-bottom: -1.25rem;
}
.s\:my--22 {
  margin-top: -1.375rem;
  margin-bottom: -1.375rem;
}
.s\:my--24 {
  margin-top: -1.5rem;
  margin-bottom: -1.5rem;
}
.s\:my--28 {
  margin-top: -1.75rem;
  margin-bottom: -1.75rem;
}
.s\:my--32 {
  margin-top: -2rem;
  margin-bottom: -2rem;
}
.s\:my--36 {
  margin-top: -2.25rem;
  margin-bottom: -2.25rem;
}
.s\:my--40 {
  margin-top: -2.5rem;
  margin-bottom: -2.5rem;
}
.s\:my--44 {
  margin-top: -2.75rem;
  margin-bottom: -2.75rem;
}
.s\:my--48 {
  margin-top: -3rem;
  margin-bottom: -3rem;
}
.s\:my--52 {
  margin-top: -3.25rem;
  margin-bottom: -3.25rem;
}
.s\:my--56 {
  margin-top: -3.5rem;
  margin-bottom: -3.5rem;
}
.s\:my--60 {
  margin-top: -3.75rem;
  margin-bottom: -3.75rem;
}
.s\:my--64 {
  margin-top: -4rem;
  margin-bottom: -4rem;
}
.s\:my--68 {
  margin-top: -4.25rem;
  margin-bottom: -4.25rem;
}
.s\:my--72 {
  margin-top: -4.5rem;
  margin-bottom: -4.5rem;
}
.s\:my--76 {
  margin-top: -4.75rem;
  margin-bottom: -4.75rem;
}
.s\:my--80 {
  margin-top: -5rem;
  margin-bottom: -5rem;
}
.s\:my--84 {
  margin-top: -5.25rem;
  margin-bottom: -5.25rem;
}
.s\:my--88 {
  margin-top: -5.5rem;
  margin-bottom: -5.5rem;
}
.s\:my--92 {
  margin-top: -5.75rem;
  margin-bottom: -5.75rem;
}
.s\:my--96 {
  margin-top: -6rem;
  margin-bottom: -6rem;
}
.s\:my--100 {
  margin-top: -6.25rem;
  margin-bottom: -6.25rem;
}
.s\:my--120 {
  margin-top: -7.5rem;
  margin-bottom: -7.5rem;
}
.s\:my--140 {
  margin-top: -8.75rem;
  margin-bottom: -8.75rem;
}
.s\:my--160 {
  margin-top: -10rem;
  margin-bottom: -10rem;
}
.s\:my--180 {
  margin-top: -11.25rem;
  margin-bottom: -11.25rem;
}
.s\:my--200 {
  margin-top: -12.5rem;
  margin-bottom: -12.5rem;
}
.s\:my--220 {
  margin-top: -13.75rem;
  margin-bottom: -13.75rem;
}
.s\:my--240 {
  margin-top: -15rem;
  margin-bottom: -15rem;
}
.s\:my--260 {
  margin-top: -16.25rem;
  margin-bottom: -16.25rem;
}
.s\:my--280 {
  margin-top: -17.5rem;
  margin-bottom: -17.5rem;
}
.s\:my--300 {
  margin-top: -18.75rem;
  margin-bottom: -18.75rem;
}
.s\:my--320 {
  margin-top: -20rem;
  margin-bottom: -20rem;
}
.s\:my--340 {
  margin-top: -21.25rem;
  margin-bottom: -21.25rem;
}
.s\:my--360 {
  margin-top: -22.5rem;
  margin-bottom: -22.5rem;
}
.s\:my--380 {
  margin-top: -23.75rem;
  margin-bottom: -23.75rem;
}
.s\:my--400 {
  margin-top: -25rem;
  margin-bottom: -25rem;
}

/* Negative Margin X (Left & Right) */
.s\:mx--0 {
  margin-left: 0px;
  margin-right: 0px;
}
.s\:mx--1 {
  margin-left: -1px;
  margin-right: -1px;
}
.s\:mx--2 {
  margin-left: -0.125rem;
  margin-right: -0.125rem;
}
.s\:mx--4 {
  margin-left: -0.25rem;
  margin-right: -0.25rem;
}
.s\:mx--6 {
  margin-left: -0.375rem;
  margin-right: -0.375rem;
}
.s\:mx--8 {
  margin-left: -0.5rem;
  margin-right: -0.5rem;
}
.s\:mx--10 {
  margin-left: -0.625rem;
  margin-right: -0.625rem;
}
.s\:mx--12 {
  margin-left: -0.75rem;
  margin-right: -0.75rem;
}
.s\:mx--14 {
  margin-left: -0.875rem;
  margin-right: -0.875rem;
}
.s\:mx--16 {
  margin-left: -1rem;
  margin-right: -1rem;
}
.s\:mx--18 {
  margin-left: -1.125rem;
  margin-right: -1.125rem;
}
.s\:mx--20 {
  margin-left: -1.25rem;
  margin-right: -1.25rem;
}
.s\:mx--22 {
  margin-left: -1.375rem;
  margin-right: -1.375rem;
}
.s\:mx--24 {
  margin-left: -1.5rem;
  margin-right: -1.5rem;
}
.s\:mx--28 {
  margin-left: -1.75rem;
  margin-right: -1.75rem;
}
.s\:mx--32 {
  margin-left: -2rem;
  margin-right: -2rem;
}
.s\:mx--36 {
  margin-left: -2.25rem;
  margin-right: -2.25rem;
}
.s\:mx--40 {
  margin-left: -2.5rem;
  margin-right: -2.5rem;
}
.s\:mx--44 {
  margin-left: -2.75rem;
  margin-right: -2.75rem;
}
.s\:mx--48 {
  margin-left: -3rem;
  margin-right: -3rem;
}
.s\:mx--52 {
  margin-left: -3.25rem;
  margin-right: -3.25rem;
}
.s\:mx--56 {
  margin-left: -3.5rem;
  margin-right: -3.5rem;
}
.s\:mx--60 {
  margin-left: -3.75rem;
  margin-right: -3.75rem;
}
.s\:mx--64 {
  margin-left: -4rem;
  margin-right: -4rem;
}
.s\:mx--68 {
  margin-left: -4.25rem;
  margin-right: -4.25rem;
}
.s\:mx--72 {
  margin-left: -4.5rem;
  margin-right: -4.5rem;
}
.s\:mx--76 {
  margin-left: -4.75rem;
  margin-right: -4.75rem;
}
.s\:mx--80 {
  margin-left: -5rem;
  margin-right: -5rem;
}
.s\:mx--84 {
  margin-left: -5.25rem;
  margin-right: -5.25rem;
}
.s\:mx--88 {
  margin-left: -5.5rem;
  margin-right: -5.5rem;
}
.s\:mx--92 {
  margin-left: -5.75rem;
  margin-right: -5.75rem;
}
.s\:mx--96 {
  margin-left: -6rem;
  margin-right: -6rem;
}
.s\:mx--100 {
  margin-left: -6.25rem;
  margin-right: -6.25rem;
}
.s\:mx--120 {
  margin-left: -7.5rem;
  margin-right: -7.5rem;
}
.s\:mx--140 {
  margin-left: -8.75rem;
  margin-right: -8.75rem;
}
.s\:mx--160 {
  margin-left: -10rem;
  margin-right: -10rem;
}
.s\:mx--180 {
  margin-left: -11.25rem;
  margin-right: -11.25rem;
}
.s\:mx--200 {
  margin-left: -12.5rem;
  margin-right: -12.5rem;
}
.s\:mx--220 {
  margin-left: -13.75rem;
  margin-right: -13.75rem;
}
.s\:mx--240 {
  margin-left: -15rem;
  margin-right: -15rem;
}
.s\:mx--260 {
  margin-left: -16.25rem;
  margin-right: -16.25rem;
}
.s\:mx--280 {
  margin-left: -17.5rem;
  margin-right: -17.5rem;
}
.s\:mx--300 {
  margin-left: -18.75rem;
  margin-right: -18.75rem;
}
.s\:mx--320 {
  margin-left: -20rem;
  margin-right: -20rem;
}
.s\:mx--340 {
  margin-left: -21.25rem;
  margin-right: -21.25rem;
}
.s\:mx--360 {
  margin-left: -22.5rem;
  margin-right: -22.5rem;
}
.s\:mx--380 {
  margin-left: -23.75rem;
  margin-right: -23.75rem;
}
.s\:mx--400 {
  margin-left: -25rem;
  margin-right: -25rem;
}

/*--------------------------------------------------------------

## Padding

--------------------------------------------------------------*/

.p-0 {
  padding: 0px;
}

.p-1 {
  padding: 1px;
}

.p-2 {
  padding: 0.125rem;
}

.p-4 {
  padding: calc((0.25rem * var(--responsive-70)) * (var(--p-4, 1)));
}

.p-6 {
  padding: calc((0.375rem * var(--responsive-70)) * (var(--p-6, 1)));
}

.p-8 {
  padding: calc((0.5rem * var(--responsive-70)) * (var(--p-8, 1)));
}

.p-10 {
  padding: calc((0.625rem * var(--responsive-70)) * (var(--p-10, 1)));
}

.p-12 {
  padding: calc((0.75rem * var(--responsive-70)) * (var(--p-12, 1)));
}

.p-14 {
  padding: calc((0.875rem * var(--responsive-70)) * (var(--p-14, 1)));
}

.p-16 {
  padding: calc((1rem * var(--responsive-70)) * (var(--p-16, 1)));
}

.p-18 {
  padding: calc((1.125rem * var(--responsive-70)) * (var(--p-18, 1)));
}

.p-20 {
  padding: calc((1.25rem * var(--responsive-70)) * (var(--p-20, 1)));
}

.p-22 {
  padding: calc((1.375rem * var(--responsive-70)) * (var(--p-22, 1)));
}

.p-24 {
  padding: calc((1.5rem * var(--responsive-70)) * (var(--p-24, 1)));
}

.p-28 {
  padding: calc((1.75rem * var(--responsive-70)) * (var(--p-28, 1)));
}

.p-32 {
  padding: calc((2rem * var(--responsive-70)) * (var(--p-32, 1)));
}

.p-36 {
  padding: calc((2.25rem * var(--responsive-70)) * (var(--p-36, 1)));
}

.p-40 {
  padding: calc((2.5rem * var(--responsive-60)) * (var(--p-40, 1)));
}

.p-44 {
  padding: calc((2.75rem * var(--responsive-60)) * (var(--p-44, 1)));
}

.p-48 {
  padding: calc((3rem * var(--responsive-60)) * (var(--p-48, 1)));
}

.p-52 {
  padding: calc((3.25rem * var(--responsive-60)) * (var(--p-52, 1)));
}

.p-56 {
  padding: calc((3.5rem * var(--responsive-60)) * (var(--p-56, 1)));
}

.p-60 {
  padding: calc((3.75rem * var(--responsive-50)) * (var(--p-60, 1)));
}

.p-64 {
  padding: calc((4rem * var(--responsive-50)) * (var(--p-64, 1)));
}

.p-68 {
  padding: calc((4.25rem * var(--responsive-50)) * (var(--p-68, 1)));
}

.p-72 {
  padding: calc((4.5rem * var(--responsive-50)) * (var(--p-72, 1)));
}

.p-76 {
  padding: calc((4.75rem * var(--responsive-50)) * (var(--p-76, 1)));
}

.p-80 {
  padding: calc((5rem * var(--responsive-40)) * (var(--p-80, 1)));
}

.p-84 {
  padding: calc((5.25rem * var(--responsive-40)) * (var(--p-84, 1)));
}

.p-88 {
  padding: calc((5.5rem * var(--responsive-40)) * (var(--p-88, 1)));
}

.p-92 {
  padding: calc((5.75rem * var(--responsive-40)) * (var(--p-92, 1)));
}

.p-96 {
  padding: calc((6rem * var(--responsive-40)) * (var(--p-96, 1)));
}

.p-100 {
  padding: calc((6.25rem * var(--responsive-30)) * (var(--p-100, 1)));
}

.p-120 {
  padding: calc((7.5rem * var(--responsive-30)) * (var(--p-120, 1)));
}

.p-140 {
  padding: calc((8.75rem * var(--responsive-30)) * (var(--p-140, 1)));
}

.p-160 {
  padding: calc((10rem * var(--responsive-30)) * (var(--p-160, 1)));
}

.p-180 {
  padding: calc((11.25rem * var(--responsive-30)) * (var(--p-180, 1)));
}

.p-200 {
  padding: calc((12.5rem * var(--responsive-30)) * (var(--p-200, 1)));
}

.p-220 {
  padding: calc((13.75rem * var(--responsive-70)) * (var(--p-220, 1)));
}

.p-240 {
  padding: calc((15rem * var(--responsive-70)) * (var(--p-240, 1)));
}

.p-260 {
  padding: calc((16.25rem * var(--responsive-70)) * (var(--p-260, 1)));
}

.p-280 {
  padding: calc((17.5rem * var(--responsive-70)) * (var(--p-280, 1)));
}

.p-300 {
  padding: calc((18.75rem * var(--responsive-70)) * (var(--p-300, 1)));
}

.p-320 {
  padding: calc((20rem * var(--responsive-70)) * (var(--p-320, 1)));
}

.p-340 {
  padding: calc((21.25rem * var(--responsive-70)) * (var(--p-340, 1)));
}

.p-360 {
  padding: calc((22.5rem * var(--responsive-70)) * (var(--p-360, 1)));
}

.p-380 {
  padding: calc((23.75rem * var(--responsive-70)) * (var(--p-380, 1)));
}

.p-400 {
  padding: calc((25rem * var(--responsive-70)) * (var(--p-400, 1)));
}

.pt-0 {
  padding-top: 0px;
}

.pt-1 {
  padding-top: 1px;
}

.pt-2 {
  padding-top: 0.125rem;
}

.pt-4 {
  padding-top: calc((0.25rem * var(--responsive-70)) * (var(--pt-4, 1)));
}

.pt-6 {
  padding-top: calc((0.375rem * var(--responsive-70)) * (var(--pt-6, 1)));
}

.pt-8 {
  padding-top: calc((0.5rem * var(--responsive-70)) * (var(--pt-8, 1)));
}

.pt-10 {
  padding-top: calc((0.625rem * var(--responsive-70)) * (var(--pt-10, 1)));
}

.pt-12 {
  padding-top: calc((0.75rem * var(--responsive-70)) * (var(--pt-12, 1)));
}

.pt-14 {
  padding-top: calc((0.875rem * var(--responsive-70)) * (var(--pt-14, 1)));
}

.pt-16 {
  padding-top: calc((1rem * var(--responsive-70)) * (var(--pt-16, 1)));
}

.pt-18 {
  padding-top: calc((1.125rem * var(--responsive-70)) * (var(--pt-18, 1)));
}

.pt-20 {
  padding-top: calc((1.25rem * var(--responsive-70)) * (var(--pt-20, 1)));
}

.pt-22 {
  padding-top: calc((1.375rem * var(--responsive-70)) * (var(--pt-22, 1)));
}

.pt-24 {
  padding-top: calc((1.5rem * var(--responsive-70)) * (var(--pt-24, 1)));
}

.pt-28 {
  padding-top: calc((1.75rem * var(--responsive-70)) * (var(--pt-28, 1)));
}

.pt-32 {
  padding-top: calc((2rem * var(--responsive-70)) * (var(--pt-32, 1)));
}

.pt-36 {
  padding-top: calc((2.25rem * var(--responsive-70)) * (var(--pt-36, 1)));
}

.pt-40 {
  padding-top: calc((2.5rem * var(--responsive-60)) * (var(--pt-40, 1)));
}

.pt-44 {
  padding-top: calc((2.75rem * var(--responsive-60)) * (var(--pt-44, 1)));
}

.pt-48 {
  padding-top: calc((3rem * var(--responsive-60)) * (var(--pt-48, 1)));
}

.pt-52 {
  padding-top: calc((3.25rem * var(--responsive-60)) * (var(--pt-52, 1)));
}

.pt-56 {
  padding-top: calc((3.5rem * var(--responsive-60)) * (var(--pt-56, 1)));
}

.pt-60 {
  padding-top: calc((3.75rem * var(--responsive-50)) * (var(--pt-60, 1)));
}

.pt-64 {
  padding-top: calc((4rem * var(--responsive-50)) * (var(--pt-64, 1)));
}

.pt-68 {
  padding-top: calc((4.25rem * var(--responsive-50)) * (var(--pt-68, 1)));
}

.pt-72 {
  padding-top: calc((4.5rem * var(--responsive-50)) * (var(--pt-72, 1)));
}

.pt-76 {
  padding-top: calc((4.75rem * var(--responsive-50)) * (var(--pt-76, 1)));
}

.pt-80 {
  padding-top: calc((5rem * var(--responsive-40)) * (var(--pt-80, 1)));
}

.pt-84 {
  padding-top: calc((5.25rem * var(--responsive-40)) * (var(--pt-84, 1)));
}

.pt-88 {
  padding-top: calc((5.5rem * var(--responsive-40)) * (var(--pt-88, 1)));
}

.pt-92 {
  padding-top: calc((5.75rem * var(--responsive-40)) * (var(--pt-92, 1)));
}

.pt-96 {
  padding-top: calc((6rem * var(--responsive-40)) * (var(--pt-96, 1)));
}

.pt-100 {
  padding-top: calc((6.25rem * var(--responsive-30)) * (var(--pt-100, 1)));
}

.pt-120 {
  padding-top: calc((7.5rem * var(--responsive-30)) * (var(--pt-120, 1)));
}

.pt-140 {
  padding-top: calc((8.75rem * var(--responsive-30)) * (var(--pt-140, 1)));
}

.pt-160 {
  padding-top: calc((10rem * var(--responsive-30)) * (var(--pt-160, 1)));
}

.pt-180 {
  padding-top: calc((11.25rem * var(--responsive-30)) * (var(--pt-180, 1)));
}

.pt-200 {
  padding-top: calc((12.5rem * var(--responsive-30)) * (var(--pt-200, 1)));
}

.pt-220 {
  padding-top: calc((13.75rem * var(--responsive-70)) * (var(--pt-220, 1)));
}

.pt-240 {
  padding-top: calc((15rem * var(--responsive-70)) * (var(--pt-240, 1)));
}

.pt-260 {
  padding-top: calc((16.25rem * var(--responsive-70)) * (var(--pt-260, 1)));
}

.pt-280 {
  padding-top: calc((17.5rem * var(--responsive-70)) * (var(--pt-280, 1)));
}

.pt-300 {
  padding-top: calc((18.75rem * var(--responsive-70)) * (var(--pt-300, 1)));
}

.pt-320 {
  padding-top: calc((20rem * var(--responsive-70)) * (var(--pt-320, 1)));
}

.pt-340 {
  padding-top: calc((21.25rem * var(--responsive-70)) * (var(--pt-340, 1)));
}

.pt-360 {
  padding-top: calc((22.5rem * var(--responsive-70)) * (var(--pt-360, 1)));
}

.pt-380 {
  padding-top: calc((23.75rem * var(--responsive-70)) * (var(--pt-380, 1)));
}

.pt-400 {
  padding-top: calc((25rem * var(--responsive-70)) * (var(--pt-400, 1)));
}


.pt-100-100 {
    padding-top: 100%;
  }



.pt-110-100 {
    padding-top: 110%;
  }



.pt-120-100 {
    padding-top: 120%;
  }



.pt-130-100 {
    padding-top: 130%;
  }



.pt-140-100 {
  padding-top: 140%;
}



.pt-150-100 {
  padding-top: 150%;
}

.pb-0 {
  padding-bottom: 0px;
}

.pb-1 {
  padding-bottom: 1px;
}

.pb-2 {
  padding-bottom: 0.125rem;
}

.pb-4 {
  padding-bottom: calc((0.25rem * var(--responsive-70)) * (var(--pb-4, 1)));
}

.pb-6 {
  padding-bottom: calc((0.375rem * var(--responsive-70)) * (var(--pb-6, 1)));
}

.pb-8 {
  padding-bottom: calc((0.5rem * var(--responsive-70)) * (var(--pb-8, 1)));
}

.pb-10 {
  padding-bottom: calc((0.625rem * var(--responsive-70)) * (var(--pb-10, 1)));
}

.pb-12 {
  padding-bottom: calc((0.75rem * var(--responsive-70)) * (var(--pb-12, 1)));
}

.pb-14 {
  padding-bottom: calc((0.875rem * var(--responsive-70)) * (var(--pb-14, 1)));
}

.pb-16 {
  padding-bottom: calc((1rem * var(--responsive-70)) * (var(--pb-16, 1)));
}

.pb-18 {
  padding-bottom: calc((1.125rem * var(--responsive-70)) * (var(--pb-18, 1)));
}

.pb-20 {
  padding-bottom: calc((1.25rem * var(--responsive-70)) * (var(--pb-20, 1)));
}

.pb-22 {
  padding-bottom: calc((1.375rem * var(--responsive-70)) * (var(--pb-22, 1)));
}

.pb-24 {
  padding-bottom: calc((1.5rem * var(--responsive-70)) * (var(--pb-24, 1)));
}

.pb-28 {
  padding-bottom: calc((1.75rem * var(--responsive-70)) * (var(--pb-28, 1)));
}

.pb-32 {
  padding-bottom: calc((2rem * var(--responsive-70)) * (var(--pb-32, 1)));
}

.pb-36 {
  padding-bottom: calc((2.25rem * var(--responsive-70)) * (var(--pb-36, 1)));
}

.pb-40 {
  padding-bottom: calc((2.5rem * var(--responsive-60)) * (var(--pb-40, 1)));
}

.pb-44 {
  padding-bottom: calc((2.75rem * var(--responsive-60)) * (var(--pb-44, 1)));
}

.pb-48 {
  padding-bottom: calc((3rem * var(--responsive-60)) * (var(--pb-48, 1)));
}

.pb-52 {
  padding-bottom: calc((3.25rem * var(--responsive-60)) * (var(--pb-52, 1)));
}

.pb-56 {
  padding-bottom: calc((3.5rem * var(--responsive-60)) * (var(--pb-56, 1)));
}

.pb-60 {
  padding-bottom: calc((3.75rem * var(--responsive-50)) * (var(--pb-60, 1)));
}

.pb-64 {
  padding-bottom: calc((4rem * var(--responsive-50)) * (var(--pb-64, 1)));
}

.pb-68 {
  padding-bottom: calc((4.25rem * var(--responsive-50)) * (var(--pb-68, 1)));
}

.pb-72 {
  padding-bottom: calc((4.5rem * var(--responsive-50)) * (var(--pb-72, 1)));
}

.pb-76 {
  padding-bottom: calc((4.75rem * var(--responsive-50)) * (var(--pb-76, 1)));
}

.pb-80 {
  padding-bottom: calc((5rem * var(--responsive-40)) * (var(--pb-80, 1)));
}

.pb-84 {
  padding-bottom: calc((5.25rem * var(--responsive-40)) * (var(--pb-84, 1)));
}

.pb-88 {
  padding-bottom: calc((5.5rem * var(--responsive-40)) * (var(--pb-88, 1)));
}

.pb-92 {
  padding-bottom: calc((5.75rem * var(--responsive-40)) * (var(--pb-92, 1)));
}

.pb-96 {
  padding-bottom: calc((6rem * var(--responsive-40)) * (var(--pb-96, 1)));
}

.pb-100 {
  padding-bottom: calc((6.25rem * var(--responsive-30)) * (var(--pb-100, 1)));
}

.pb-120 {
  padding-bottom: calc((7.5rem * var(--responsive-30)) * (var(--pb-120, 1)));
}

.pb-140 {
  padding-bottom: calc((8.75rem * var(--responsive-30)) * (var(--pb-140, 1)));
}

.pb-160 {
  padding-bottom: calc((10rem * var(--responsive-30)) * (var(--pb-160, 1)));
}

.pb-180 {
  padding-bottom: calc((11.25rem * var(--responsive-30)) * (var(--pb-180, 1)));
}

.pb-200 {
  padding-bottom: calc((12.5rem * var(--responsive-30)) * (var(--pb-200, 1)));
}

.pb-220 {
  padding-bottom: calc((13.75rem * var(--responsive-70)) * (var(--pb-220, 1)));
}

.pb-240 {
  padding-bottom: calc((15rem * var(--responsive-70)) * (var(--pb-240, 1)));
}

.pb-260 {
  padding-bottom: calc((16.25rem * var(--responsive-70)) * (var(--pb-260, 1)));
}

.pb-280 {
  padding-bottom: calc((17.5rem * var(--responsive-70)) * (var(--pb-280, 1)));
}

.pb-300 {
  padding-bottom: calc((18.75rem * var(--responsive-70)) * (var(--pb-300, 1)));
}

.pb-320 {
  padding-bottom: calc((20rem * var(--responsive-70)) * (var(--pb-320, 1)));
}

.pb-340 {
  padding-bottom: calc((21.25rem * var(--responsive-70)) * (var(--pb-340, 1)));
}

.pb-360 {
  padding-bottom: calc((22.5rem * var(--responsive-70)) * (var(--pb-360, 1)));
}

.pb-380 {
  padding-bottom: calc((23.75rem * var(--responsive-70)) * (var(--pb-380, 1)));
}

.pb-400 {
  padding-bottom: calc((25rem * var(--responsive-70)) * (var(--pb-400, 1)));
}

.pl-0 {
  padding-left: 0px;
}

.pl-1 {
  padding-left: 1px;
}

.pl-2 {
  padding-left: 0.125rem;
}

.pl-4 {
  padding-left: calc((0.25rem * var(--responsive-70)) * (var(--pl-4, 1)));
}

.pl-6 {
  padding-left: calc((0.375rem * var(--responsive-70)) * (var(--pl-6, 1)));
}

.pl-8 {
  padding-left: calc((0.5rem * var(--responsive-70)) * (var(--pl-8, 1)));
}

.pl-10 {
  padding-left: calc((0.625rem * var(--responsive-70)) * (var(--pl-10, 1)));
}

.pl-12 {
  padding-left: calc((0.75rem * var(--responsive-70)) * (var(--pl-12, 1)));
}

.pl-14 {
  padding-left: calc((0.875rem * var(--responsive-70)) * (var(--pl-14, 1)));
}

.pl-16 {
  padding-left: calc((1rem * var(--responsive-70)) * (var(--pl-16, 1)));
}

.pl-18 {
  padding-left: calc((1.125rem * var(--responsive-70)) * (var(--pl-18, 1)));
}

.pl-20 {
  padding-left: calc((1.25rem * var(--responsive-70)) * (var(--pl-20, 1)));
}

.pl-22 {
  padding-left: calc((1.375rem * var(--responsive-70)) * (var(--pl-22, 1)));
}

.pl-24 {
  padding-left: calc((1.5rem * var(--responsive-70)) * (var(--pl-24, 1)));
}

.pl-28 {
  padding-left: calc((1.75rem * var(--responsive-70)) * (var(--pl-28, 1)));
}

.pl-32 {
  padding-left: calc((2rem * var(--responsive-70)) * (var(--pl-32, 1)));
}

.pl-36 {
  padding-left: calc((2.25rem * var(--responsive-70)) * (var(--pl-36, 1)));
}

.pl-40 {
  padding-left: calc((2.5rem * var(--responsive-60)) * (var(--pl-40, 1)));
}

.pl-44 {
  padding-left: calc((2.75rem * var(--responsive-60)) * (var(--pl-44, 1)));
}

.pl-48 {
  padding-left: calc((3rem * var(--responsive-60)) * (var(--pl-48, 1)));
}

.pl-52 {
  padding-left: calc((3.25rem * var(--responsive-60)) * (var(--pl-52, 1)));
}

.pl-56 {
  padding-left: calc((3.5rem * var(--responsive-60)) * (var(--pl-56, 1)));
}

.pl-60 {
  padding-left: calc((3.75rem * var(--responsive-50)) * (var(--pl-60, 1)));
}

.pl-64 {
  padding-left: calc((4rem * var(--responsive-50)) * (var(--pl-64, 1)));
}

.pl-68 {
  padding-left: calc((4.25rem * var(--responsive-50)) * (var(--pl-68, 1)));
}

.pl-72 {
  padding-left: calc((4.5rem * var(--responsive-50)) * (var(--pl-72, 1)));
}

.pl-76 {
  padding-left: calc((4.75rem * var(--responsive-50)) * (var(--pl-76, 1)));
}

.pl-80 {
  padding-left: calc((5rem * var(--responsive-40)) * (var(--pl-80, 1)));
}

.pl-84 {
  padding-left: calc((5.25rem * var(--responsive-40)) * (var(--pl-84, 1)));
}

.pl-88 {
  padding-left: calc((5.5rem * var(--responsive-40)) * (var(--pl-88, 1)));
}

.pl-92 {
  padding-left: calc((5.75rem * var(--responsive-40)) * (var(--pl-92, 1)));
}

.pl-96 {
  padding-left: calc((6rem * var(--responsive-40)) * (var(--pl-96, 1)));
}

.pl-100 {
  padding-left: calc((6.25rem * var(--responsive-30)) * (var(--pl-100, 1)));
}

.pl-120 {
  padding-left: calc((7.5rem * var(--responsive-30)) * (var(--pl-120, 1)));
}

.pl-140 {
  padding-left: calc((8.75rem * var(--responsive-30)) * (var(--pl-140, 1)));
}

.pl-160 {
  padding-left: calc((10rem * var(--responsive-30)) * (var(--pl-160, 1)));
}

.pl-180 {
  padding-left: calc((11.25rem * var(--responsive-30)) * (var(--pl-180, 1)));
}

.pl-200 {
  padding-left: calc((12.5rem * var(--responsive-30)) * (var(--pl-200, 1)));
}

.pl-220 {
  padding-left: calc((13.75rem * var(--responsive-70)) * (var(--pl-220, 1)));
}

.pl-240 {
  padding-left: calc((15rem * var(--responsive-70)) * (var(--pl-240, 1)));
}

.pl-260 {
  padding-left: calc((16.25rem * var(--responsive-70)) * (var(--pl-260, 1)));
}

.pl-280 {
  padding-left: calc((17.5rem * var(--responsive-70)) * (var(--pl-280, 1)));
}

.pl-300 {
  padding-left: calc((18.75rem * var(--responsive-70)) * (var(--pl-300, 1)));
}

.pl-320 {
  padding-left: calc((20rem * var(--responsive-70)) * (var(--pl-320, 1)));
}

.pl-340 {
  padding-left: calc((21.25rem * var(--responsive-70)) * (var(--pl-340, 1)));
}

.pl-360 {
  padding-left: calc((22.5rem * var(--responsive-70)) * (var(--pl-360, 1)));
}

.pl-380 {
  padding-left: calc((23.75rem * var(--responsive-70)) * (var(--pl-380, 1)));
}

.pl-400 {
  padding-left: calc((25rem * var(--responsive-70)) * (var(--pl-400, 1)));
}

.pr-0 {
  padding-right: 0px;
}

.pr-1 {
  padding-right: 1px;
}

.pr-2 {
  padding-right: 0.125rem;
}

.pr-4 {
  padding-right: calc((0.25rem * var(--responsive-70)) * (var(--pr-4, 1)));
}

.pr-6 {
  padding-right: calc((0.375rem * var(--responsive-70)) * (var(--pr-6, 1)));
}

.pr-8 {
  padding-right: calc((0.5rem * var(--responsive-70)) * (var(--pr-8, 1)));
}

.pr-10 {
  padding-right: calc((0.625rem * var(--responsive-70)) * (var(--pr-10, 1)));
}

.pr-12 {
  padding-right: calc((0.75rem * var(--responsive-70)) * (var(--pr-12, 1)));
}

.pr-14 {
  padding-right: calc((0.875rem * var(--responsive-70)) * (var(--pr-14, 1)));
}

.pr-16 {
  padding-right: calc((1rem * var(--responsive-70)) * (var(--pr-16, 1)));
}

.pr-18 {
  padding-right: calc((1.125rem * var(--responsive-70)) * (var(--pr-18, 1)));
}

.pr-20 {
  padding-right: calc((1.25rem * var(--responsive-70)) * (var(--pr-20, 1)));
}

.pr-22 {
  padding-right: calc((1.375rem * var(--responsive-70)) * (var(--pr-22, 1)));
}

.pr-24 {
  padding-right: calc((1.5rem * var(--responsive-70)) * (var(--pr-24, 1)));
}

.pr-28 {
  padding-right: calc((1.75rem * var(--responsive-70)) * (var(--pr-28, 1)));
}

.pr-32 {
  padding-right: calc((2rem * var(--responsive-70)) * (var(--pr-32, 1)));
}

.pr-36 {
  padding-right: calc((2.25rem * var(--responsive-70)) * (var(--pr-36, 1)));
}

.pr-40 {
  padding-right: calc((2.5rem * var(--responsive-60)) * (var(--pr-40, 1)));
}

.pr-44 {
  padding-right: calc((2.75rem * var(--responsive-60)) * (var(--pr-44, 1)));
}

.pr-48 {
  padding-right: calc((3rem * var(--responsive-60)) * (var(--pr-48, 1)));
}

.pr-52 {
  padding-right: calc((3.25rem * var(--responsive-60)) * (var(--pr-52, 1)));
}

.pr-56 {
  padding-right: calc((3.5rem * var(--responsive-60)) * (var(--pr-56, 1)));
}

.pr-60 {
  padding-right: calc((3.75rem * var(--responsive-50)) * (var(--pr-60, 1)));
}

.pr-64 {
  padding-right: calc((4rem * var(--responsive-50)) * (var(--pr-64, 1)));
}

.pr-68 {
  padding-right: calc((4.25rem * var(--responsive-50)) * (var(--pr-68, 1)));
}

.pr-72 {
  padding-right: calc((4.5rem * var(--responsive-50)) * (var(--pr-72, 1)));
}

.pr-76 {
  padding-right: calc((4.75rem * var(--responsive-50)) * (var(--pr-76, 1)));
}

.pr-80 {
  padding-right: calc((5rem * var(--responsive-40)) * (var(--pr-80, 1)));
}

.pr-84 {
  padding-right: calc((5.25rem * var(--responsive-40)) * (var(--pr-84, 1)));
}

.pr-88 {
  padding-right: calc((5.5rem * var(--responsive-40)) * (var(--pr-88, 1)));
}

.pr-92 {
  padding-right: calc((5.75rem * var(--responsive-40)) * (var(--pr-92, 1)));
}

.pr-96 {
  padding-right: calc((6rem * var(--responsive-40)) * (var(--pr-96, 1)));
}

.pr-100 {
  padding-right: calc((6.25rem * var(--responsive-30)) * (var(--pr-100, 1)));
}

.pr-120 {
  padding-right: calc((7.5rem * var(--responsive-30)) * (var(--pr-120, 1)));
}

.pr-140 {
  padding-right: calc((8.75rem * var(--responsive-30)) * (var(--pr-140, 1)));
}

.pr-160 {
  padding-right: calc((10rem * var(--responsive-30)) * (var(--pr-160, 1)));
}

.pr-180 {
  padding-right: calc((11.25rem * var(--responsive-30)) * (var(--pr-180, 1)));
}

.pr-200 {
  padding-right: calc((12.5rem * var(--responsive-30)) * (var(--pr-200, 1)));
}

.pr-220 {
  padding-right: calc((13.75rem * var(--responsive-70)) * (var(--pr-220, 1)));
}

.pr-240 {
  padding-right: calc((15rem * var(--responsive-70)) * (var(--pr-240, 1)));
}

.pr-260 {
  padding-right: calc((16.25rem * var(--responsive-70)) * (var(--pr-260, 1)));
}

.pr-280 {
  padding-right: calc((17.5rem * var(--responsive-70)) * (var(--pr-280, 1)));
}

.pr-300 {
  padding-right: calc((18.75rem * var(--responsive-70)) * (var(--pr-300, 1)));
}

.pr-320 {
  padding-right: calc((20rem * var(--responsive-70)) * (var(--pr-320, 1)));
}

.pr-340 {
  padding-right: calc((21.25rem * var(--responsive-70)) * (var(--pr-340, 1)));
}

.pr-360 {
  padding-right: calc((22.5rem * var(--responsive-70)) * (var(--pr-360, 1)));
}

.pr-380 {
  padding-right: calc((23.75rem * var(--responsive-70)) * (var(--pr-380, 1)));
}

.pr-400 {
  padding-right: calc((25rem * var(--responsive-70)) * (var(--pr-400, 1)));
}

.px-0 {
  padding-left: 0px;
  padding-right: 0px;
}

.px-1 {
  padding-left: 1px;
  padding-right: 1px;
}

.px-2 {
  padding-left: 0.125rem;
  padding-right: 0.125rem;
}

.px-4 {
  padding-left: calc((0.25rem * var(--responsive-70)) * (var(--px-4, 1)));
  padding-right: calc((0.25rem * var(--responsive-70)) * (var(--px-4, 1)));
}

.px-6 {
  padding-left: calc((0.375rem * var(--responsive-70)) * (var(--px-6, 1)));
  padding-right: calc((0.375rem * var(--responsive-70)) * (var(--px-6, 1)));
}

.px-8 {
  padding-left: calc((0.5rem * var(--responsive-70)) * (var(--px-8, 1)));
  padding-right: calc((0.5rem * var(--responsive-70)) * (var(--px-8, 1)));
}

.px-10 {
  padding-left: calc((0.625rem * var(--responsive-70)) * (var(--px-10, 1)));
  padding-right: calc((0.625rem * var(--responsive-70)) * (var(--px-10, 1)));
}

.px-12 {
  padding-left: calc((0.75rem * var(--responsive-70)) * (var(--px-12, 1)));
  padding-right: calc((0.75rem * var(--responsive-70)) * (var(--px-12, 1)));
}

.px-14 {
  padding-left: calc((0.875rem * var(--responsive-70)) * (var(--px-14, 1)));
  padding-right: calc((0.875rem * var(--responsive-70)) * (var(--px-14, 1)));
}

.px-16 {
  padding-left: calc((1rem * var(--responsive-70)) * (var(--px-16, 1)));
  padding-right: calc((1rem * var(--responsive-70)) * (var(--px-16, 1)));
}

.px-18 {
  padding-left: calc((1.125rem * var(--responsive-70)) * (var(--px-18, 1)));
  padding-right: calc((1.125rem * var(--responsive-70)) * (var(--px-18, 1)));
}

.px-20 {
  padding-left: calc((1.25rem * var(--responsive-70)) * (var(--px-20, 1)));
  padding-right: calc((1.25rem * var(--responsive-70)) * (var(--px-20, 1)));
}

.px-22 {
  padding-left: calc((1.375rem * var(--responsive-70)) * (var(--px-22, 1)));
  padding-right: calc((1.375rem * var(--responsive-70)) * (var(--px-22, 1)));
}

.px-24 {
  padding-left: calc((1.5rem * var(--responsive-70)) * (var(--px-24, 1)));
  padding-right: calc((1.5rem * var(--responsive-70)) * (var(--px-24, 1)));
}

.px-28 {
  padding-left: calc((1.75rem * var(--responsive-70)) * (var(--px-28, 1)));
  padding-right: calc((1.75rem * var(--responsive-70)) * (var(--px-28, 1)));
}

.px-32 {
  padding-left: calc((2rem * var(--responsive-70)) * (var(--px-32, 1)));
  padding-right: calc((2rem * var(--responsive-70)) * (var(--px-32, 1)));
}

.px-36 {
  padding-left: calc((2.25rem * var(--responsive-70)) * (var(--px-36, 1)));
  padding-right: calc((2.25rem * var(--responsive-70)) * (var(--px-36, 1)));
}

.px-40 {
  padding-left: calc((2.5rem * var(--responsive-60)) * (var(--px-40, 1)));
  padding-right: calc((2.5rem * var(--responsive-60)) * (var(--px-40, 1)));
}

.px-44 {
  padding-left: calc((2.75rem * var(--responsive-60)) * (var(--px-44, 1)));
  padding-right: calc((2.75rem * var(--responsive-60)) * (var(--px-44, 1)));
}

.px-48 {
  padding-left: calc((3rem * var(--responsive-60)) * (var(--px-48, 1)));
  padding-right: calc((3rem * var(--responsive-60)) * (var(--px-48, 1)));
}

.px-52 {
  padding-left: calc((3.25rem * var(--responsive-60)) * (var(--px-52, 1)));
  padding-right: calc((3.25rem * var(--responsive-60)) * (var(--px-52, 1)));
}

.px-56 {
  padding-left: calc((3.5rem * var(--responsive-60)) * (var(--px-56, 1)));
  padding-right: calc((3.5rem * var(--responsive-60)) * (var(--px-56, 1)));
}

.px-60 {
  padding-left: calc((3.75rem * var(--responsive-50)) * (var(--px-60, 1)));
  padding-right: calc((3.75rem * var(--responsive-50)) * (var(--px-60, 1)));
}

.px-64 {
  padding-left: calc((4rem * var(--responsive-50)) * (var(--px-64, 1)));
  padding-right: calc((4rem * var(--responsive-50)) * (var(--px-64, 1)));
}

.px-68 {
  padding-left: calc((4.25rem * var(--responsive-50)) * (var(--px-68, 1)));
  padding-right: calc((4.25rem * var(--responsive-50)) * (var(--px-68, 1)));
}

.px-72 {
  padding-left: calc((4.5rem * var(--responsive-50)) * (var(--px-72, 1)));
  padding-right: calc((4.5rem * var(--responsive-50)) * (var(--px-72, 1)));
}

.px-76 {
  padding-left: calc((4.75rem * var(--responsive-50)) * (var(--px-76, 1)));
  padding-right: calc((4.75rem * var(--responsive-50)) * (var(--px-76, 1)));
}

.px-80 {
  padding-left: calc((5rem * var(--responsive-40)) * (var(--px-80, 1)));
  padding-right: calc((5rem * var(--responsive-40)) * (var(--px-80, 1)));
}

.px-84 {
  padding-left: calc((5.25rem * var(--responsive-40)) * (var(--px-84, 1)));
  padding-right: calc((5.25rem * var(--responsive-40)) * (var(--px-84, 1)));
}

.px-88 {
  padding-left: calc((5.5rem * var(--responsive-40)) * (var(--px-88, 1)));
  padding-right: calc((5.5rem * var(--responsive-40)) * (var(--px-88, 1)));
}

.px-92 {
  padding-left: calc((5.75rem * var(--responsive-40)) * (var(--px-92, 1)));
  padding-right: calc((5.75rem * var(--responsive-40)) * (var(--px-92, 1)));
}

.px-96 {
  padding-left: calc((6rem * var(--responsive-40)) * (var(--px-96, 1)));
  padding-right: calc((6rem * var(--responsive-40)) * (var(--px-96, 1)));
}

.px-100 {
  padding-left: calc((6.25rem * var(--responsive-30)) * (var(--px-100, 1)));
  padding-right: calc((6.25rem * var(--responsive-30)) * (var(--px-100, 1)));
}

.px-120 {
  padding-left: calc((7.5rem * var(--responsive-30)) * (var(--px-120, 1)));
  padding-right: calc((7.5rem * var(--responsive-30)) * (var(--px-120, 1)));
}

.px-140 {
  padding-left: calc((8.75rem * var(--responsive-30)) * (var(--px-140, 1)));
  padding-right: calc((8.75rem * var(--responsive-30)) * (var(--px-140, 1)));
}

.px-160 {
  padding-left: calc((10rem * var(--responsive-30)) * (var(--px-160, 1)));
  padding-right: calc((10rem * var(--responsive-30)) * (var(--px-160, 1)));
}

.px-180 {
  padding-left: calc((11.25rem * var(--responsive-30)) * (var(--px-180, 1)));
  padding-right: calc((11.25rem * var(--responsive-30)) * (var(--px-180, 1)));
}

.px-200 {
  padding-left: calc((12.5rem * var(--responsive-30)) * (var(--px-200, 1)));
  padding-right: calc((12.5rem * var(--responsive-30)) * (var(--px-200, 1)));
}

.px-220 {
  padding-left: calc((13.75rem * var(--responsive-70)) * (var(--px-220, 1)));
  padding-right: calc((13.75rem * var(--responsive-70)) * (var(--px-220, 1)));
}

.px-240 {
  padding-left: calc((15rem * var(--responsive-70)) * (var(--px-240, 1)));
  padding-right: calc((15rem * var(--responsive-70)) * (var(--px-240, 1)));
}

.px-260 {
  padding-left: calc((16.25rem * var(--responsive-70)) * (var(--px-260, 1)));
  padding-right: calc((16.25rem * var(--responsive-70)) * (var(--px-260, 1)));
}

.px-280 {
  padding-left: calc((17.5rem * var(--responsive-70)) * (var(--px-280, 1)));
  padding-right: calc((17.5rem * var(--responsive-70)) * (var(--px-280, 1)));
}

.px-300 {
  padding-left: calc((18.75rem * var(--responsive-70)) * (var(--px-300, 1)));
  padding-right: calc((18.75rem * var(--responsive-70)) * (var(--px-300, 1)));
}

.px-320 {
  padding-left: calc((20rem * var(--responsive-70)) * (var(--px-320, 1)));
  padding-right: calc((20rem * var(--responsive-70)) * (var(--px-320, 1)));
}

.px-340 {
  padding-left: calc((21.25rem * var(--responsive-70)) * (var(--px-340, 1)));
  padding-right: calc((21.25rem * var(--responsive-70)) * (var(--px-340, 1)));
}

.px-360 {
  padding-left: calc((22.5rem * var(--responsive-70)) * (var(--px-360, 1)));
  padding-right: calc((22.5rem * var(--responsive-70)) * (var(--px-360, 1)));
}

.px-380 {
  padding-left: calc((23.75rem * var(--responsive-70)) * (var(--px-380, 1)));
  padding-right: calc((23.75rem * var(--responsive-70)) * (var(--px-380, 1)));
}

.px-400 {
  padding-left: calc((25rem * var(--responsive-70)) * (var(--px-400, 1)));
  padding-right: calc((25rem * var(--responsive-70)) * (var(--px-400, 1)));
}


.py-0 {
  padding-top: 0px;
  padding-bottom: 0px;
}

.py-1 {
  padding-top: 1px;
  padding-bottom: 1px;
}

.py-2 {
  padding-top: 0.125rem;
  padding-bottom: 0.125rem;
}

.py-4 {
  padding-top: calc((0.25rem * var(--responsive-70)) * (var(--py-4, 1)));
  padding-bottom: calc((0.25rem * var(--responsive-70)) * (var(--py-4, 1)));
}

.py-6 {
  padding-top: calc((0.375rem * var(--responsive-70)) * (var(--py-6, 1)));
  padding-bottom: calc((0.375rem * var(--responsive-70)) * (var(--py-6, 1)));
}

.py-8 {
  padding-top: calc((0.5rem * var(--responsive-70)) * (var(--py-8, 1)));
  padding-bottom: calc((0.5rem * var(--responsive-70)) * (var(--py-8, 1)));
}

.py-10 {
  padding-top: calc((0.625rem * var(--responsive-70)) * (var(--py-10, 1)));
  padding-bottom: calc((0.625rem * var(--responsive-70)) * (var(--py-10, 1)));
}

.py-12 {
  padding-top: calc((0.75rem * var(--responsive-70)) * (var(--py-12, 1)));
  padding-bottom: calc((0.75rem * var(--responsive-70)) * (var(--py-12, 1)));
}

.py-14 {
  padding-top: calc((0.875rem * var(--responsive-70)) * (var(--py-14, 1)));
  padding-bottom: calc((0.875rem * var(--responsive-70)) * (var(--py-14, 1)));
}

.py-16 {
  padding-top: calc((1rem * var(--responsive-70)) * (var(--py-16, 1)));
  padding-bottom: calc((1rem * var(--responsive-70)) * (var(--py-16, 1)));
}

.py-18 {
  padding-top: calc((1.125rem * var(--responsive-70)) * (var(--py-18, 1)));
  padding-bottom: calc((1.125rem * var(--responsive-70)) * (var(--py-18, 1)));
}

.py-20 {
  padding-top: calc((1.25rem * var(--responsive-70)) * (var(--py-20, 1)));
  padding-bottom: calc((1.25rem * var(--responsive-70)) * (var(--py-20, 1)));
}

.py-22 {
  padding-top: calc((1.375rem * var(--responsive-70)) * (var(--py-22, 1)));
  padding-bottom: calc((1.375rem * var(--responsive-70)) * (var(--py-22, 1)));
}

.py-24 {
  padding-top: calc((1.5rem * var(--responsive-70)) * (var(--py-24, 1)));
  padding-bottom: calc((1.5rem * var(--responsive-70)) * (var(--py-24, 1)));
}

.py-28 {
  padding-top: calc((1.75rem * var(--responsive-70)) * (var(--py-28, 1)));
  padding-bottom: calc((1.75rem * var(--responsive-70)) * (var(--py-28, 1)));
}

.py-32 {
  padding-top: calc((2rem * var(--responsive-70)) * (var(--py-32, 1)));
  padding-bottom: calc((2rem * var(--responsive-70)) * (var(--py-32, 1)));
}

.py-36 {
  padding-top: calc((2.25rem * var(--responsive-70)) * (var(--py-36, 1)));
  padding-bottom: calc((2.25rem * var(--responsive-70)) * (var(--py-36, 1)));
}

.py-40 {
  padding-top: calc((2.5rem * var(--responsive-60)) * (var(--py-40, 1)));
  padding-bottom: calc((2.5rem * var(--responsive-60)) * (var(--py-40, 1)));
}
 
.py-44 {
  padding-top: calc((2.75rem * var(--responsive-60)) * (var(--py-44, 1)));
  padding-bottom: calc((2.75rem * var(--responsive-60)) * (var(--py-44, 1)));
}

.py-48 {
  padding-top: calc((3rem * var(--responsive-60)) * (var(--py-48, 1)));
  padding-bottom: calc((3rem * var(--responsive-60)) * (var(--py-48, 1)));
}

.py-52 {
  padding-top: calc((3.25rem * var(--responsive-60)) * (var(--py-52, 1)));
  padding-bottom: calc((3.25rem * var(--responsive-60)) * (var(--py-52, 1)));
}

.py-56 {
  padding-top: calc((3.5rem * var(--responsive-60)) * (var(--py-56, 1)));
  padding-bottom: calc((3.5rem * var(--responsive-60)) * (var(--py-56, 1)));
}

.py-60 {
  padding-top: calc((3.75rem * var(--responsive-50)) * (var(--py-60, 1)));
  padding-bottom: calc((3.75rem * var(--responsive-50)) * (var(--py-60, 1)));
}

.py-64 {
  padding-top: calc((4rem * var(--responsive-50)) * (var(--py-64, 1)));
  padding-bottom: calc((4rem * var(--responsive-50)) * (var(--py-64, 1)));
}

.py-68 {
  padding-top: calc((4.25rem * var(--responsive-50)) * (var(--py-68, 1)));
  padding-bottom: calc((4.25rem * var(--responsive-50)) * (var(--py-68, 1)));
}

.py-72 {
  padding-top: calc((4.5rem * var(--responsive-50)) * (var(--py-72, 1)));
  padding-bottom: calc((4.5rem * var(--responsive-50)) * (var(--py-72, 1)));
}

.py-76 {
  padding-top: calc((4.75rem * var(--responsive-50)) * (var(--py-76, 1)));
  padding-bottom: calc((4.75rem * var(--responsive-50)) * (var(--py-76, 1)));
}

.py-80 {
  padding-top: calc((5rem * var(--responsive-40)) * (var(--py-80, 1)));
  padding-bottom: calc((5rem * var(--responsive-40)) * (var(--py-80, 1)));
}

.py-84 {
  padding-top: calc((5.25rem * var(--responsive-40)) * (var(--py-84, 1)));
  padding-bottom: calc((5.25rem * var(--responsive-40)) * (var(--py-84, 1)));
}

.py-88 {
  padding-top: calc((5.5rem * var(--responsive-40)) * (var(--py-88, 1)));
  padding-bottom: calc((5.5rem * var(--responsive-40)) * (var(--py-88, 1)));
}

.py-92 {
  padding-top: calc((5.75rem * var(--responsive-40)) * (var(--py-92, 1)));
  padding-bottom: calc((5.75rem * var(--responsive-40)) * (var(--py-92, 1)));
}

.py-96 {
  padding-top: calc((6rem * var(--responsive-40)) * (var(--py-96, 1)));
  padding-bottom: calc((6rem * var(--responsive-40)) * (var(--py-96, 1)));
}

.py-100 {
  padding-top: calc((6.25rem * var(--responsive-30)) * (var(--py-100, 1)));
  padding-bottom: calc((6.25rem * var(--responsive-30)) * (var(--py-100, 1)));
}

.py-120 {
  padding-top: calc((7.5rem * var(--responsive-30)) * (var(--py-120, 1)));
  padding-bottom: calc((7.5rem * var(--responsive-30)) * (var(--py-120, 1)));
}

.py-140 {
  padding-top: calc((8.75rem * var(--responsive-30)) * (var(--py-140, 1)));
  padding-bottom: calc((8.75rem * var(--responsive-30)) * (var(--py-140, 1)));
}

.py-160 {
  padding-top: calc((10rem * var(--responsive-30)) * (var(--py-160, 1)));
  padding-bottom: calc((10rem * var(--responsive-30)) * (var(--py-160, 1)));
}

.py-180 {
  padding-top: calc((11.25rem * var(--responsive-30)) * (var(--py-180, 1)));
  padding-bottom: calc((11.25rem * var(--responsive-30)) * (var(--py-180, 1)));
}

.py-200 {
  padding-top: calc((12.5rem * var(--responsive-30)) * (var(--py-200, 1)));
  padding-bottom: calc((12.5rem * var(--responsive-30)) * (var(--py-200, 1)));
}

.py-220 {
  padding-top: calc((13.75rem * var(--responsive-70)) * (var(--py-220, 1)));
  padding-bottom: calc((13.75rem * var(--responsive-70)) * (var(--py-220, 1)));
}

.py-240 {
  padding-top: calc((15rem * var(--responsive-70)) * (var(--py-240, 1)));
  padding-bottom: calc((15rem * var(--responsive-70)) * (var(--py-240, 1)));
}

.py-260 {
  padding-top: calc((16.25rem * var(--responsive-70)) * (var(--py-260, 1)));
  padding-bottom: calc((16.25rem * var(--responsive-70)) * (var(--py-260, 1)));
}

.py-280 {
  padding-top: calc((17.5rem * var(--responsive-70)) * (var(--py-280, 1)));
  padding-bottom: calc((17.5rem * var(--responsive-70)) * (var(--py-280, 1)));
}

.py-300 {
  padding-top: calc((18.75rem * var(--responsive-70)) * (var(--py-300, 1)));
  padding-bottom: calc((18.75rem * var(--responsive-70)) * (var(--py-300, 1)));
}

.py-320 {
  padding-top: calc((20rem * var(--responsive-70)) * (var(--py-320, 1)));
  padding-bottom: calc((20rem * var(--responsive-70)) * (var(--py-320, 1)));
}

.py-340 {
  padding-top: calc((21.25rem * var(--responsive-70)) * (var(--py-340, 1)));
  padding-bottom: calc((21.25rem * var(--responsive-70)) * (var(--py-340, 1)));
}

.py-360 {
  padding-top: calc((22.5rem * var(--responsive-70)) * (var(--py-360, 1)));
  padding-bottom: calc((22.5rem * var(--responsive-70)) * (var(--py-360, 1)));
}

.py-380 {
  padding-top: calc((23.75rem * var(--responsive-70)) * (var(--py-380, 1)));
  padding-bottom: calc((23.75rem * var(--responsive-70)) * (var(--py-380, 1)));
}

.py-400 {
  padding-top: calc((25rem * var(--responsive-70)) * (var(--py-400, 1)));
  padding-bottom: calc((25rem * var(--responsive-70)) * (var(--py-400, 1)));
}



/* Padding All Sides */
.s\:p-4 { padding: 0.25rem; }
.s\:p-6 { padding: 0.375rem; }
.s\:p-8 { padding: 0.5rem; }
.s\:p-10 { padding: 0.625rem; }
.s\:p-12 { padding: 0.75rem; }
.s\:p-14 { padding: 0.875rem; }
.s\:p-16 { padding: 1rem; }
.s\:p-18 { padding: 1.125rem; }
.s\:p-20 { padding: 1.25rem; }
.s\:p-22 { padding: 1.375rem; }
.s\:p-24 { padding: 1.5rem; }
.s\:p-28 { padding: 1.75rem; }
.s\:p-32 { padding: 2rem; }
.s\:p-36 { padding: 2.25rem; }
.s\:p-40 { padding: 2.5rem; }
.s\:p-44 { padding: 2.75rem; }
.s\:p-48 { padding: 3rem; }
.s\:p-52 { padding: 3.25rem; }
.s\:p-56 { padding: 3.5rem; }
.s\:p-60 { padding: 3.75rem; }
.s\:p-64 { padding: 4rem; }
.s\:p-68 { padding: 4.25rem; }
.s\:p-72 { padding: 4.5rem; }
.s\:p-76 { padding: 4.75rem; }
.s\:p-80 { padding: 5rem; }
.s\:p-84 { padding: 5.25rem; }
.s\:p-88 { padding: 5.5rem; }
.s\:p-92 { padding: 5.75rem; }
.s\:p-96 { padding: 6rem; }
.s\:p-100 { padding: 6.25rem; }
.s\:p-120 { padding: 7.5rem; }
.s\:p-140 { padding: 8.75rem; }
.s\:p-160 { padding: 10rem; }
.s\:p-180 { padding: 11.25rem; }
.s\:p-200 { padding: 12.5rem; }
.s\:p-220 { padding: 13.75rem; }
.s\:p-240 { padding: 15rem; }
.s\:p-260 { padding: 16.25rem; }
.s\:p-280 { padding: 17.5rem; }
.s\:p-300 { padding: 18.75rem; }
.s\:p-320 { padding: 20rem; }
.s\:p-340 { padding: 21.25rem; }
.s\:p-360 { padding: 22.5rem; }
.s\:p-380 { padding: 23.75rem; }
.s\:p-400 { padding: 25rem; }

/* Padding Top */
.s\:pt-4 { padding-top: 0.25rem; }
.s\:pt-6 { padding-top: 0.375rem; }
.s\:pt-8 { padding-top: 0.5rem; }
.s\:pt-10 { padding-top: 0.625rem; }
.s\:pt-12 { padding-top: 0.75rem; }
.s\:pt-14 { padding-top: 0.875rem; }
.s\:pt-16 { padding-top: 1rem; }
.s\:pt-18 { padding-top: 1.125rem; }
.s\:pt-20 { padding-top: 1.25rem; }
.s\:pt-22 { padding-top: 1.375rem; }
.s\:pt-24 { padding-top: 1.5rem; }
.s\:pt-28 { padding-top: 1.75rem; }
.s\:pt-32 { padding-top: 2rem; }
.s\:pt-36 { padding-top: 2.25rem; }
.s\:pt-40 { padding-top: 2.5rem; }
.s\:pt-44 { padding-top: 2.75rem; }
.s\:pt-48 { padding-top: 3rem; }
.s\:pt-52 { padding-top: 3.25rem; }
.s\:pt-56 { padding-top: 3.5rem; }
.s\:pt-60 { padding-top: 3.75rem; }
.s\:pt-64 { padding-top: 4rem; }
.s\:pt-68 { padding-top: 4.25rem; }
.s\:pt-72 { padding-top: 4.5rem; }
.s\:pt-76 { padding-top: 4.75rem; }
.s\:pt-80 { padding-top: 5rem; }
.s\:pt-84 { padding-top: 5.25rem; }
.s\:pt-88 { padding-top: 5.5rem; }
.s\:pt-92 { padding-top: 5.75rem; }
.s\:pt-96 { padding-top: 6rem; }
.s\:pt-100 { padding-top: 6.25rem; }
.s\:pt-120 { padding-top: 7.5rem; }
.s\:pt-140 { padding-top: 8.75rem; }
.s\:pt-160 { padding-top: 10rem; }
.s\:pt-180 { padding-top: 11.25rem; }
.s\:pt-200 { padding-top: 12.5rem; }
.s\:pt-220 { padding-top: 13.75rem; }
.s\:pt-240 { padding-top: 15rem; }
.s\:pt-260 { padding-top: 16.25rem; }
.s\:pt-280 { padding-top: 17.5rem; }
.s\:pt-300 { padding-top: 18.75rem; }
.s\:pt-320 { padding-top: 20rem; }
.s\:pt-340 { padding-top: 21.25rem; }
.s\:pt-360 { padding-top: 22.5rem; }
.s\:pt-380 { padding-top: 23.75rem; }
.s\:pt-400 { padding-top: 25rem; }

/* Padding Bottom */
.s\:pb-4 { padding-bottom: 0.25rem; }
.s\:pb-6 { padding-bottom: 0.375rem; }
.s\:pb-8 { padding-bottom: 0.5rem; }
.s\:pb-10 { padding-bottom: 0.625rem; }
.s\:pb-12 { padding-bottom: 0.75rem; }
.s\:pb-14 { padding-bottom: 0.875rem; }
.s\:pb-16 { padding-bottom: 1rem; }
.s\:pb-18 { padding-bottom: 1.125rem; }
.s\:pb-20 { padding-bottom: 1.25rem; }
.s\:pb-22 { padding-bottom: 1.375rem; }
.s\:pb-24 { padding-bottom: 1.5rem; }
.s\:pb-28 { padding-bottom: 1.75rem; }
.s\:pb-32 { padding-bottom: 2rem; }
.s\:pb-36 { padding-bottom: 2.25rem; }
.s\:pb-40 { padding-bottom: 2.5rem; }
.s\:pb-44 { padding-bottom: 2.75rem; }
.s\:pb-48 { padding-bottom: 3rem; }
.s\:pb-52 { padding-bottom: 3.25rem; }
.s\:pb-56 { padding-bottom: 3.5rem; }
.s\:pb-60 { padding-bottom: 3.75rem; }
.s\:pb-64 { padding-bottom: 4rem; }
.s\:pb-68 { padding-bottom: 4.25rem; }
.s\:pb-72 { padding-bottom: 4.5rem; }
.s\:pb-76 { padding-bottom: 4.75rem; }
.s\:pb-80 { padding-bottom: 5rem; }
.s\:pb-84 { padding-bottom: 5.25rem; }
.s\:pb-88 { padding-bottom: 5.5rem; }
.s\:pb-92 { padding-bottom: 5.75rem; }
.s\:pb-96 { padding-bottom: 6rem; }
.s\:pb-100 { padding-bottom: 6.25rem; }
.s\:pb-120 { padding-bottom: 7.5rem; }
.s\:pb-140 { padding-bottom: 8.75rem; }
.s\:pb-160 { padding-bottom: 10rem; }
.s\:pb-180 { padding-bottom: 11.25rem; }
.s\:pb-200 { padding-bottom: 12.5rem; }
.s\:pb-220 { padding-bottom: 13.75rem; }
.s\:pb-240 { padding-bottom: 15rem; }
.s\:pb-260 { padding-bottom: 16.25rem; }
.s\:pb-280 { padding-bottom: 17.5rem; }
.s\:pb-300 { padding-bottom: 18.75rem; }
.s\:pb-320 { padding-bottom: 20rem; }
.s\:pb-340 { padding-bottom: 21.25rem; }
.s\:pb-360 { padding-bottom: 22.5rem; }
.s\:pb-380 { padding-bottom: 23.75rem; }
.s\:pb-400 { padding-bottom: 25rem; }

/* Padding Left */
.s\:pl-4 { padding-left: 0.25rem; }
.s\:pl-6 { padding-left: 0.375rem; }
.s\:pl-8 { padding-left: 0.5rem; }
.s\:pl-10 { padding-left: 0.625rem; }
.s\:pl-12 { padding-left: 0.75rem; }
.s\:pl-14 { padding-left: 0.875rem; }
.s\:pl-16 { padding-left: 1rem; }
.s\:pl-18 { padding-left: 1.125rem; }
.s\:pl-20 { padding-left: 1.25rem; }
.s\:pl-22 { padding-left: 1.375rem; }
.s\:pl-24 { padding-left: 1.5rem; }
.s\:pl-28 { padding-left: 1.75rem; }
.s\:pl-32 { padding-left: 2rem; }
.s\:pl-36 { padding-left: 2.25rem; }
.s\:pl-40 { padding-left: 2.5rem; }
.s\:pl-44 { padding-left: 2.75rem; }
.s\:pl-48 { padding-left: 3rem; }
.s\:pl-52 { padding-left: 3.25rem; }
.s\:pl-56 { padding-left: 3.5rem; }
.s\:pl-60 { padding-left: 3.75rem; }
.s\:pl-64 { padding-left: 4rem; }
.s\:pl-68 { padding-left: 4.25rem; }
.s\:pl-72 { padding-left: 4.5rem; }
.s\:pl-76 { padding-left: 4.75rem; }
.s\:pl-80 { padding-left: 5rem; }
.s\:pl-84 { padding-left: 5.25rem; }
.s\:pl-88 { padding-left: 5.5rem; }
.s\:pl-92 { padding-left: 5.75rem; }
.s\:pl-96 { padding-left: 6rem; }
.s\:pl-100 { padding-left: 6.25rem; }
.s\:pl-120 { padding-left: 7.5rem; }
.s\:pl-140 { padding-left: 8.75rem; }
.s\:pl-160 { padding-left: 10rem; }
.s\:pl-180 { padding-left: 11.25rem; }
.s\:pl-200 { padding-left: 12.5rem; }
.s\:pl-220 { padding-left: 13.75rem; }
.s\:pl-240 { padding-left: 15rem; }
.s\:pl-260 { padding-left: 16.25rem; }
.s\:pl-280 { padding-left: 17.5rem; }
.s\:pl-300 { padding-left: 18.75rem; }
.s\:pl-320 { padding-left: 20rem; }
.s\:pl-340 { padding-left: 21.25rem; }
.s\:pl-360 { padding-left: 22.5rem; }
.s\:pl-380 { padding-left: 23.75rem; }
.s\:pl-400 { padding-left: 25rem; }

/* Padding Right */
.s\:pr-4 { padding-right: 0.25rem; }
.s\:pr-6 { padding-right: 0.375rem; }
.s\:pr-8 { padding-right: 0.5rem; }
.s\:pr-10 { padding-right: 0.625rem; }
.s\:pr-12 { padding-right: 0.75rem; }
.s\:pr-14 { padding-right: 0.875rem; }
.s\:pr-16 { padding-right: 1rem; }
.s\:pr-18 { padding-right: 1.125rem; }
.s\:pr-20 { padding-right: 1.25rem; }
.s\:pr-22 { padding-right: 1.375rem; }
.s\:pr-24 { padding-right: 1.5rem; }
.s\:pr-28 { padding-right: 1.75rem; }
.s\:pr-32 { padding-right: 2rem; }
.s\:pr-36 { padding-right: 2.25rem; }
.s\:pr-40 { padding-right: 2.5rem; }
.s\:pr-44 { padding-right: 2.75rem; }
.s\:pr-48 { padding-right: 3rem; }
.s\:pr-52 { padding-right: 3.25rem; }
.s\:pr-56 { padding-right: 3.5rem; }
.s\:pr-60 { padding-right: 3.75rem; }
.s\:pr-64 { padding-right: 4rem; }
.s\:pr-68 { padding-right: 4.25rem; }
.s\:pr-72 { padding-right: 4.5rem; }
.s\:pr-76 { padding-right: 4.75rem; }
.s\:pr-80 { padding-right: 5rem; }
.s\:pr-84 { padding-right: 5.25rem; }
.s\:pr-88 { padding-right: 5.5rem; }
.s\:pr-92 { padding-right: 5.75rem; }
.s\:pr-96 { padding-right: 6rem; }
.s\:pr-100 { padding-right: 6.25rem; }
.s\:pr-120 { padding-right: 7.5rem; }
.s\:pr-140 { padding-right: 8.75rem; }
.s\:pr-160 { padding-right: 10rem; }
.s\:pr-180 { padding-right: 11.25rem; }
.s\:pr-200 { padding-right: 12.5rem; }
.s\:pr-220 { padding-right: 13.75rem; }
.s\:pr-240 { padding-right: 15rem; }
.s\:pr-260 { padding-right: 16.25rem; }
.s\:pr-280 { padding-right: 17.5rem; }
.s\:pr-300 { padding-right: 18.75rem; }
.s\:pr-320 { padding-right: 20rem; }
.s\:pr-340 { padding-right: 21.25rem; }
.s\:pr-360 { padding-right: 22.5rem; }
.s\:pr-380 { padding-right: 23.75rem; }
.s\:pr-400 { padding-right: 25rem; }

/* Padding X (Left & Right) */
.s\:px-4 {
  padding-left: 0.25rem;
  padding-right: 0.25rem;
}
.s\:px-6 {
  padding-left: 0.375rem;
  padding-right: 0.375rem;
}
.s\:px-8 {
  padding-left: 0.5rem;
  padding-right: 0.5rem;
}
.s\:px-10 {
  padding-left: 0.625rem;
  padding-right: 0.625rem;
}
.s\:px-12 {
  padding-left: 0.75rem;
  padding-right: 0.75rem;
}
.s\:px-14 {
  padding-left: 0.875rem;
  padding-right: 0.875rem;
}
.s\:px-16 {
  padding-left: 1rem;
  padding-right: 1rem;
}
.s\:px-18 {
  padding-left: 1.125rem;
  padding-right: 1.125rem;
}
.s\:px-20 {
  padding-left: 1.25rem;
  padding-right: 1.25rem;
}
.s\:px-22 {
  padding-left: 1.375rem;
  padding-right: 1.375rem;
}
.s\:px-24 {
  padding-left: 1.5rem;
  padding-right: 1.5rem;
}
.s\:px-28 {
  padding-left: 1.75rem;
  padding-right: 1.75rem;
}
.s\:px-32 {
  padding-left: 2rem;
  padding-right: 2rem;
}
.s\:px-36 {
  padding-left: 2.25rem;
  padding-right: 2.25rem;
}
.s\:px-40 {
  padding-left: 2.5rem;
  padding-right: 2.5rem;
}
.s\:px-44 {
  padding-left: 2.75rem;
  padding-right: 2.75rem;
}
.s\:px-48 {
  padding-left: 3rem;
  padding-right: 3rem;
}
.s\:px-52 {
  padding-left: 3.25rem;
  padding-right: 3.25rem;
}
.s\:px-56 {
  padding-left: 3.5rem;
  padding-right: 3.5rem;
}
.s\:px-60 {
  padding-left: 3.75rem;
  padding-right: 3.75rem;
}
.s\:px-64 {
  padding-left: 4rem;
  padding-right: 4rem;
}
.s\:px-68 {
  padding-left: 4.25rem;
  padding-right: 4.25rem;
}
.s\:px-72 {
  padding-left: 4.5rem;
  padding-right: 4.5rem;
}
.s\:px-76 {
  padding-left: 4.75rem;
  padding-right: 4.75rem;
}
.s\:px-80 {
  padding-left: 5rem;
  padding-right: 5rem;
}
.s\:px-84 {
  padding-left: 5.25rem;
  padding-right: 5.25rem;
}
.s\:px-88 {
  padding-left: 5.5rem;
  padding-right: 5.5rem;
}
.s\:px-92 {
  padding-left: 5.75rem;
  padding-right: 5.75rem;
}
.s\:px-96 {
  padding-left: 6rem;
  padding-right: 6rem;
}
.s\:px-100 {
  padding-left: 6.25rem;
  padding-right: 6.25rem;
}
.s\:px-120 {
  padding-left: 7.5rem;
  padding-right: 7.5rem;
}
.s\:px-140 {
  padding-left: 8.75rem;
  padding-right: 8.75rem;
}
.s\:px-160 {
  padding-left: 10rem;
  padding-right: 10rem;
}
.s\:px-180 {
  padding-left: 11.25rem;
  padding-right: 11.25rem;
}
.s\:px-200 {
  padding-left: 12.5rem;
  padding-right: 12.5rem;
}
.s\:px-220 {
  padding-left: 13.75rem;
  padding-right: 13.75rem;
}
.s\:px-240 {
  padding-left: 15rem;
  padding-right: 15rem;
}
.s\:px-260 {
  padding-left: 16.25rem;
  padding-right: 16.25rem;
}
.s\:px-280 {
  padding-left: 17.5rem;
  padding-right: 17.5rem;
}
.s\:px-300 {
  padding-left: 18.75rem;
  padding-right: 18.75rem;
}
.s\:px-320 {
  padding-left: 20rem;
  padding-right: 20rem;
}
.s\:px-340 {
  padding-left: 21.25rem;
  padding-right: 21.25rem;
}
.s\:px-360 {
  padding-left: 22.5rem;
  padding-right: 22.5rem;
}
.s\:px-380 {
  padding-left: 23.75rem;
  padding-right: 23.75rem;
}
.s\:px-400 {
  padding-left: 25rem;
  padding-right: 25rem;
}

/* Padding Y (Top & Bottom) */
.s\:py-4 {
  padding-top: 0.25rem;
  padding-bottom: 0.25rem;
}
.s\:py-6 {
  padding-top: 0.375rem;
  padding-bottom: 0.375rem;
}
.s\:py-8 {
  padding-top: 0.5rem;
  padding-bottom: 0.5rem;
}
.s\:py-10 {
  padding-top: 0.625rem;
  padding-bottom: 0.625rem;
}
.s\:py-12 {
  padding-top: 0.75rem;
  padding-bottom: 0.75rem;
}
.s\:py-14 {
  padding-top: 0.875rem;
  padding-bottom: 0.875rem;
}
.s\:py-16 {
  padding-top: 1rem;
  padding-bottom: 1rem;
}
.s\:py-18 {
  padding-top: 1.125rem;
  padding-bottom: 1.125rem;
}
.s\:py-20 {
  padding-top: 1.25rem;
  padding-bottom: 1.25rem;
}
.s\:py-22 {
  padding-top: 1.375rem;
  padding-bottom: 1.375rem;
}
.s\:py-24 {
  padding-top: 1.5rem;
  padding-bottom: 1.5rem;
}
.s\:py-28 {
  padding-top: 1.75rem;
  padding-bottom: 1.75rem;
}
.s\:py-32 {
  padding-top: 2rem;
  padding-bottom: 2rem;
}
.s\:py-36 {
  padding-top: 2.25rem;
  padding-bottom: 2.25rem;
}
.s\:py-40 {
  padding-top: 2.5rem;
  padding-bottom: 2.5rem;
}
.s\:py-44 {
  padding-top: 2.75rem;
  padding-bottom: 2.75rem;
}
.s\:py-48 {
  padding-top: 3rem;
  padding-bottom: 3rem;
}
.s\:py-52 {
  padding-top: 3.25rem;
  padding-bottom: 3.25rem;
}
.s\:py-56 {
  padding-top: 3.5rem;
  padding-bottom: 3.5rem;
}
.s\:py-60 {
  padding-top: 3.75rem;
  padding-bottom: 3.75rem;
}
.s\:py-64 {
  padding-top: 4rem;
  padding-bottom: 4rem;
}
.s\:py-68 {
  padding-top: 4.25rem;
  padding-bottom: 4.25rem;
}
.s\:py-72 {
  padding-top: 4.5rem;
  padding-bottom: 4.5rem;
}
.s\:py-76 {
  padding-top: 4.75rem;
  padding-bottom: 4.75rem;
}
.s\:py-80 {
  padding-top: 5rem;
  padding-bottom: 5rem;
}
.s\:py-84 {
  padding-top: 5.25rem;
  padding-bottom: 5.25rem;
}
.s\:py-88 {
  padding-top: 5.5rem;
  padding-bottom: 5.5rem;
}
.s\:py-92 {
  padding-top: 5.75rem;
  padding-bottom: 5.75rem;
}
.s\:py-96 {
  padding-top: 6rem;
  padding-bottom: 6rem;
}
.s\:py-100 {
  padding-top: 6.25rem;
  padding-bottom: 6.25rem;
}
.s\:py-120 {
  padding-top: 7.5rem;
  padding-bottom: 7.5rem;
}
.s\:py-140 {
  padding-top: 8.75rem;
  padding-bottom: 8.75rem;
}
.s\:py-160 {
  padding-top: 10rem;
  padding-bottom: 10rem;
}
.s\:py-180 {
  padding-top: 11.25rem;
  padding-bottom: 11.25rem;
}
.s\:py-200 {
  padding-top: 12.5rem;
  padding-bottom: 12.5rem;
}
.s\:py-220 {
  padding-top: 13.75rem;
  padding-bottom: 13.75rem;
}
.s\:py-240 {
  padding-top: 15rem;
  padding-bottom: 15rem;
}
.s\:py-260 {
  padding-top: 16.25rem;
  padding-bottom: 16.25rem;
}
.s\:py-280 {
  padding-top: 17.5rem;
  padding-bottom: 17.5rem;
}
.s\:py-300 {
  padding-top: 18.75rem;
  padding-bottom: 18.75rem;
}
.s\:py-320 {
  padding-top: 20rem;
  padding-bottom: 20rem;
}
.s\:py-340 {
  padding-top: 21.25rem;
  padding-bottom: 21.25rem;
}
.s\:py-360 {
  padding-top: 22.5rem;
  padding-bottom: 22.5rem;
}
.s\:py-380 {
  padding-top: 23.75rem;
  padding-bottom: 23.75rem;
}
.s\:py-400 {
  padding-top: 25rem;
  padding-bottom: 25rem;
}





/*--------------------------------------------------------------

## Height

--------------------------------------------------------------*/

.h-auto {
  height: auto;
}

.h-10-100 {
  height: 10%;
}

.h-20-100 {
  height: 20%;
}

.h-30-100 {
  height: 30%;
}

.h-40-100 {
  height: 40%;
}

.h-50-100 {
  height: 50%;
}

.h-60-100 {
  height: 60%;
}

.h-70-100 {
  height: 70%;
}

.h-80-100 {
  height: 80%;
}

.h-90-100 {
  height: 90%;
}

.h-100-100 {
  height: 100%;
}

.h-110-100 {
  height: 110%;
}

.h-120-100 {
  height: 120%;
}

.h-130-100 {
  height: 130%;
}

.h-140-100 {
  height: 140%;
}

.h-100vh {
  height: 100vh;
}

.h-100lvh {
  height: 100lvh;
}

.h-100svh {
  height: 100svh;
}

.h-100dvh {
  height: 100dvh;
}

.h-min {
  height: min-content;
}

.h-max {
  height: max-content;
}

.h-fit {
  height: fit-content;
}

.h-0 {
  height: 0;
}

.h-1 {
  height: 1px;
}

.h-2 {
  height: 2px;
}

.h-4 {
  height: calc((0.25rem * var(--responsive-70)) * (var(--h-4, 1)));
}

.h-6 {
  height: calc((0.375rem * var(--responsive-70)) * (var(--h-6, 1)));
}

.h-8 {
  height: calc((0.5rem * var(--responsive-70)) * (var(--h-8, 1)));
}

.h-10 {
  height: calc((0.625rem * var(--responsive-70)) * (var(--h-10, 1)));
}

.h-12 {
  height: calc((0.75rem * var(--responsive-70)) * (var(--h-12, 1)));
}

.h-14 {
  height: calc((0.875rem * var(--responsive-70)) * (var(--h-14, 1)));
}

.h-16 {
  height: calc((1rem * var(--responsive-70)) * (var(--h-16, 1)));
}

.h-18 {
  height: calc((1.125rem * var(--responsive-70)) * (var(--h-18, 1)));
}

.h-20 {
  height: calc((1.25rem * var(--responsive-70)) * (var(--h-20, 1)));
}

.h-22 {
  height: calc((1.375rem * var(--responsive-70)) * (var(--h-22, 1)));
}

.h-24 {
  height: calc((1.5rem * var(--responsive-70)) * (var(--h-24, 1)));
}

.h-28 {
  height: calc((1.75rem * var(--responsive-70)) * (var(--h-28, 1)));
}

.h-32 {
  height: calc((2rem * var(--responsive-70)) * (var(--h-32, 1)));
}

.h-36 {
  height: calc((2.25rem * var(--responsive-70)) * (var(--h-36, 1)));
}

.h-40 {
  height: calc((2.5rem * var(--responsive-60)) * (var(--h-40, 1)));
}

.h-44 {
  height: calc((2.75rem * var(--responsive-60)) * (var(--h-44, 1)));
}

.h-48 {
  height: calc((3rem * var(--responsive-60)) * (var(--h-48, 1)));
}

.h-52 {
  height: calc((3.25rem * var(--responsive-60)) * (var(--h-52, 1)));
}

.h-56 {
  height: calc((3.5rem * var(--responsive-60)) * (var(--h-56, 1)));
}

.h-60 {
  height: calc((3.75rem * var(--responsive-50)) * (var(--h-60, 1)));
}

.h-64 {
  height: calc((4rem * var(--responsive-50)) * (var(--h-64, 1)));
}

.h-68 {
  height: calc((4.25rem * var(--responsive-50)) * (var(--h-68, 1)));
}

.h-72 {
  height: calc((4.5rem * var(--responsive-50)) * (var(--h-72, 1)));
}

.h-76 {
  height: calc((4.75rem * var(--responsive-50)) * (var(--h-76, 1)));
}

.h-80 {
  height: calc((5rem * var(--responsive-40)) * (var(--h-80, 1)));
}

.h-84 {
  height: calc((5.25rem * var(--responsive-40)) * (var(--h-84, 1)));
}

.h-88 {
  height: calc((5.5rem * var(--responsive-40)) * (var(--h-88, 1)));
}

.h-92 {
  height: calc((5.75rem * var(--responsive-40)) * (var(--h-92, 1)));
}

.h-96 {
  height: calc((6rem * var(--responsive-40)) * (var(--h-96, 1)));
}

.h-100 {
  height: calc((6.25rem * var(--responsive-30)) * (var(--h-100, 1)));
}

.h-120 {
  height: calc((7.5rem * var(--responsive-30)) * (var(--h-120, 1)));
}

.h-140 {
  height: calc((8.75rem * var(--responsive-30)) * (var(--h-140, 1)));
}

.h-160 {
  height: calc((10rem * var(--responsive-30)) * (var(--h-160, 1)));
}

.h-180 {
  height: calc((11.25rem * var(--responsive-30)) * (var(--h-180, 1)));
}
.h-200 {
  height: calc((12.5rem * var(--responsive-30)) * (var(--h-200, 1)));
} 

.h-220 {
  height: calc((13.75rem * var(--responsive-70)) * (var(--h-220, 1)));
}

.h-240 {
  height: calc((15rem * var(--responsive-70)) * (var(--h-240, 1)));
}

.h-260 {
  height: calc((16.25rem * var(--responsive-70)) * (var(--h-260, 1)));
}

.h-280 {
  height: calc((17.5rem * var(--responsive-70)) * (var(--h-280, 1)));
}

.h-300 {
  height: calc((18.75rem * var(--responsive-70)) * (var(--h-300, 1)));
}

.h-320 {
  height: calc((20rem * var(--responsive-70)) * (var(--h-320, 1)));
}

.h-340 {
  height: calc((21.25rem * var(--responsive-70)) * (var(--h-340, 1)));
}

.h-360 {
  height: calc((22.5rem * var(--responsive-70)) * (var(--h-360, 1)));
}

.h-380 {
  height: calc((23.75rem * var(--responsive-70)) * (var(--h-380, 1)));
}

.h-400 {
  height: calc((25rem * var(--responsive-70)) * (var(--h-400, 1)));
}

.h-420 {
  height: calc((26.25rem * var(--responsive-70)) * (var(--h-420, 1)));
}

.h-440 {
  height: calc((27.5rem * var(--responsive-70)) * (var(--h-440, 1)));
}

.h-460 {
  height: calc((28.75rem * var(--responsive-70)) * (var(--h-460, 1)));
}

.h-480 {
  height: calc((30rem * var(--responsive-70)) * (var(--h-480, 1)));
}

.h-500 {
  height: calc((31.25rem * var(--responsive-70)) * (var(--h-500, 1)));
}

.h-520 {
  height: calc((32.5rem * var(--responsive-70)) * (var(--h-520, 1)));
}

.h-540 {
  height: calc((33.75rem * var(--responsive-70)) * (var(--h-540, 1)));
}

.h-560 {
  height: calc((35rem * var(--responsive-70)) * (var(--h-560, 1)));
}

.h-580 {
  height: calc((36.25rem * var(--responsive-70)) * (var(--h-580, 1)));
}

.h-600 {
  height: calc((37.5rem * var(--responsive-70)) * (var(--h-600, 1)));
}

.h-620 {
  height: calc((38.75rem * var(--responsive-70)) * (var(--h-620, 1)));
}

.h-640 {
  height: calc((40rem * var(--responsive-70)) * (var(--h-640, 1)));
}

.h-660 {
  height: calc((41.25rem * var(--responsive-70)) * (var(--h-660, 1)));
}

.h-680 {
  height: calc((42.5rem * var(--responsive-70)) * (var(--h-680, 1)));
}

.h-700 {
  height: calc((43.75rem * var(--responsive-70)) * (var(--h-700, 1)));
}

.h-720 {
  height: calc((45rem * var(--responsive-70)) * (var(--h-720, 1)));
}

.h-740 {
  height: calc((46.25rem * var(--responsive-70)) * (var(--h-740, 1)));
}

.h-760 {
  height: calc((47.5rem * var(--responsive-70)) * (var(--h-760, 1)));
}

.h-780 {
  height: calc((48.75rem * var(--responsive-70)) * (var(--h-780, 1)));
}

.h-800 {
  height: calc((50rem * var(--responsive-70)) * (var(--h-800, 1)));
}

.h-900 {
  height: calc((56.25rem * var(--responsive-70)) * (var(--h-900, 1)));
}

.h-1000 {
    height: calc((62.5rem * var(--responsive-70)) * (var(--h-1000, 1)));
  }

.h-1100 {
  height: calc((68.75rem * var(--responsive-70)) * (var(--h-1100, 1)));
}

.h-1200 { 
  height: calc((75rem * var(--responsive-70)) * (var(--h-1200, 1)));
}

.s\:h-4 {
  height: 0.25rem;
}

.s\:h-6 {
  height: 0.375rem;
}

.s\:h-8 {
  height: 0.5rem;
}

.s\:h-10 {
  height: 0.625rem;
}

.s\:h-12 {
  height: 0.75rem;
}

.s\:h-14 {
  height: 0.875rem;
}

.s\:h-16 {
  height: 1rem;
}

.s\:h-18 {
  height: 1.125rem;
}

.s\:h-20 {
  height: 1.25rem;
}

.s\:h-22 {
  height: 1.375rem;
}

.s\:h-24 {
  height: 1.5rem;
}

.s\:h-28 {
  height: 1.75rem;
}

.s\:h-32 {
  height: 2rem;
}

.s\:h-36 {
  height: 2.25rem;
}

.s\:h-40 {
  height: 2.5rem;
}

.s\:h-44 {
  height: 2.75rem;
}

.s\:h-48 {
  height: 3rem;
}

.s\:h-52 {
  height: 3.25rem;
}

.s\:h-56 {
  height: 3.5rem;
}

.s\:h-60 {
  height: 3.75rem;
}

.s\:h-64 {
  height: 4rem;
}

.s\:h-68 {
  height: 4.25rem;
}

.s\:h-72 {
  height: 4.5rem;
}

.s\:h-76 {
  height: 4.75rem;
}

.s\:h-80 {
  height: 5rem;
}

.s\:h-84 {
  height: 5.25rem;
}

.s\:h-88 {
  height: 5.5rem;
}

.s\:h-92 {
  height: 5.75rem;
}

.s\:h-96 {
  height: 6rem;
}

.s\:h-100 {
  height: 6.25rem;
}

.s\:h-120 {
  height: 7.5rem;
}

.s\:h-140 {
  height: 8.75rem;
}

.s\:h-160 {
  height: 10rem;
}

.s\:h-180 {
  height: 11.25rem;
}
.s\:h-200 {
  height: 12.5rem;
}

.s\:h-220 {
  height: 13.75rem;
}

.s\:h-240 {
  height: 15rem;
}

.s\:h-260 {
  height: 16.25rem;
}

.s\:h-280 {
  height: 17.5rem;
}

.s\:h-300 {
  height: 18.75rem;
}

.s\:h-320 {
  height: 20rem;
}

.s\:h-340 {
  height: 21.25rem;
}

.s\:h-360 {
  height: 22.5rem;
}

.s\:h-380 {
  height: 23.75rem;
}

.s\:h-400 {
  height: 25rem;
}

.s\:h-420 {
  height: 26.25rem;
}

.s\:h-440 {
  height: 27.5rem;
}

.s\:h-460 {
  height: 28.75rem;
}

.s\:h-480 {
  height: 30rem;
}

.s\:h-500 {
  height: 31.25rem;
}

.s\:h-520 {
  height: 32.5rem;
}

.s\:h-540 {
  height: 33.75rem;
}

.s\:h-560 {
  height: 35rem;
}

.s\:h-580 {
  height: 36.25rem;
}

.s\:h-600 {
  height: 37.5rem;
}

.s\:h-620 {
  height: 38.75rem;
}

.s\:h-640 {
  height: 40rem;
}

.s\:h-660 {
  height: 41.25rem;
}

.s\:h-680 {
  height: 42.5rem;
}

.s\:h-700 {
  height: 43.75rem;
}

.s\:h-720 {
  height: 45rem;
}

.s\:h-740 {
  height: 46.25rem;
}

.s\:h-760 {
  height: 47.5rem;
}

.s\:h-780 {
  height: 48.75rem;
}

.s\:h-800 {
  height: 50rem;
}

.s\:h-900 {
  height: 56.25rem;
}

.s\:h-1000 {
  height: 62.5rem;
}

.s\:h-1100 {
  height: 68.75rem;
}

.s\:h-1200 {
  height: 75rem;
}



/*--------------------------------------------------------------

## Width

--------------------------------------------------------------*/

.w-auto {
  width: auto;
}

.w-5-100 {
  width: 5%;
}

.w-10-100 {
  width: 10%;
}

.w-15-100 {
  width: 15%;
}

.w-20-100 {
  width: 20%;
}

.w-25-100 {
  width: 25%;
}

.w-30-100 {
  width: 30%;
}

.w-35-100 {
  width: 35%;
}

.w-40-100 {
  width: 40%;
}

.w-45-100 {
  width: 45%;
}

.w-50-100 {
  width: 50%;
}

.w-55-100 {
  width: 55%;
}

.w-60-100 {
  width: 60%;
}

.w-65-100 {
  width: 65%;
}

.w-70-100 {
  width: 70%;
}

.w-75-100 {
  width: 75%;
}

.w-80-100 {
  width: 80%;
}

.w-85-100 {
  width: 85%;
}

.w-90-100 {
  width: 90%;
}

.w-95-100 {
  width: 95%;
}

.w-100-100 {
  width: 100%;
}

.w-110-100 {
  width: 110%;
}

.w-120-100 {
  width: 120%;
}

.w-130-100 {
  width: 130%;
}

.w-140-100 {
  width: 140%;
}

.w-50vw {
  width: 50vw;
}

.w-100vw {
  width: 100vw;
}

.w-100svw {
  width: 100svw;
}

.w-100lvw {
  width: 100lvw;
}

.w-100dvw {
  width: 100dvw;
}

.w-min {
  width: min-content;
}

.w-max {
  width: max-content;
}

.w-fit {
  width: fit-content;
}

.w-0 {
  width: 0;
}

.w-1 {
  width: 1px;
}


.w-2 {
  width: 2px;
}

.w-4 {
  width: calc((0.25rem * var(--responsive-70)) * (var(--w-4, 1)));
}

.w-6 {
  width: calc((0.375rem * var(--responsive-70)) * (var(--w-6, 1)));
}

.w-8 {
  width: calc((0.5rem * var(--responsive-70)) * (var(--w-8, 1)));
}

.w-10 {
  width: calc((0.625rem * var(--responsive-70)) * (var(--w-10, 1)));
}

.w-12 {
  width: calc((0.75rem * var(--responsive-70)) * (var(--w-12, 1)));
}

.w-14 {
  width: calc((0.875rem * var(--responsive-70)) * (var(--w-14, 1)));
}

.w-16 {
  width: calc((1rem * var(--responsive-70)) * (var(--w-16, 1)));
}

.w-18 {
  width: calc((1.125rem * var(--responsive-70)) * (var(--w-18, 1)));
}

.w-20 {
  width: calc((1.25rem * var(--responsive-70)) * (var(--w-20, 1)));
}

.w-22 {
  width: calc((1.375rem * var(--responsive-70)) * (var(--w-22, 1)));
}

.w-24 {
  width: calc((1.5rem * var(--responsive-70)) * (var(--w-24, 1)));
}

.w-28 {
  width: calc((1.75rem * var(--responsive-70)) * (var(--w-28, 1)));
}

.w-32 {
  width: calc((2rem * var(--responsive-70)) * (var(--w-32, 1)));
}

.w-36 {
  width: calc((2.25rem * var(--responsive-70)) * (var(--w-36, 1)));
}

.w-40 {
  width: calc((2.5rem * var(--responsive-60)) * (var(--w-40, 1)));
}

.w-44 {
  width: calc((2.75rem * var(--responsive-60)) * (var(--w-44, 1)));
}

.w-48 {
  width: calc((3rem * var(--responsive-60)) * (var(--w-48, 1)));
}

.w-52 {
  width: calc((3.25rem * var(--responsive-60)) * (var(--w-52, 1)));
}

.w-56 {
  width: calc((3.5rem * var(--responsive-60)) * (var(--w-56, 1)));
}

.w-60 {
  width: calc((3.75rem * var(--responsive-50)) * (var(--w-60, 1)));
}

.w-64 {
  width: calc((4rem * var(--responsive-50)) * (var(--w-64, 1)));
}

.w-68 {
  width: calc((4.25rem * var(--responsive-50)) * (var(--w-68, 1)));
}

.w-72 {
  width: calc((4.5rem * var(--responsive-50)) * (var(--w-72, 1)));
}

.w-76 {
  width: calc((4.75rem * var(--responsive-50)) * (var(--w-76, 1)));
}

.w-80 {
  width: calc((5rem * var(--responsive-40)) * (var(--w-80, 1)));
}

.w-84 {
  width: calc((5.25rem * var(--responsive-40)) * (var(--w-84, 1)));
}

.w-88 {
  width: calc((5.5rem * var(--responsive-40)) * (var(--w-88, 1)));
}

.w-92 {
  width: calc((5.75rem * var(--responsive-40)) * (var(--w-92, 1)));
}

.w-96 {
  width: calc((6rem * var(--responsive-40)) * (var(--w-96, 1)));
}

.w-100 {
  width: calc((6.25rem * var(--responsive-30)) * (var(--w-100, 1)));
}

.w-120 {
  width: calc((7.5rem * var(--responsive-30)) * (var(--w-120, 1)));
}

.w-140 {
  width: calc((8.75rem * var(--responsive-30)) * (var(--w-140, 1)));
}

.w-160 {
  width: calc((10rem * var(--responsive-30)) * (var(--w-160, 1)));
}

.w-180 {
  width: calc((11.25rem * var(--responsive-30)) * (var(--w-180, 1)));
}
.w-200 {
  width: calc((12.5rem * var(--responsive-30)) * (var(--w-200, 1)));
}

.w-220 {
  width: calc((13.75rem * var(--responsive-70)) * (var(--w-220, 1)));
}

.w-240 {
  width: calc((15rem * var(--responsive-70)) * (var(--w-240, 1)));
}

.w-260 {
  width: calc((16.25rem * var(--responsive-70)) * (var(--w-260, 1)));
}

.w-280 {
  width: calc((17.5rem * var(--responsive-70)) * (var(--w-280, 1)));
}

.w-300 {
  width: calc((18.75rem * var(--responsive-70)) * (var(--w-300, 1)));
}

.w-320 {
  width: calc((20rem * var(--responsive-70)) * (var(--w-320, 1)));
}

.w-340 {
  width: calc((21.25rem * var(--responsive-70)) * (var(--w-340, 1)));
}

.w-360 {
  width: calc((22.5rem * var(--responsive-70)) * (var(--w-360, 1)));
}

.w-380 {
  width: calc((23.75rem * var(--responsive-70)) * (var(--w-380, 1)));
}

.w-400 {
  width: calc((25rem * var(--responsive-70)) * (var(--w-400, 1)));
}

.w-420 {
  width: calc((26.25rem * var(--responsive-70)) * (var(--w-420, 1)));
}

.w-440 {
  width: calc((27.5rem * var(--responsive-70)) * (var(--w-440, 1)));
}

.w-460 {
  width: calc((28.75rem * var(--responsive-70)) * (var(--w-460, 1)));
}

.w-480 {
  width: calc((30rem * var(--responsive-70)) * (var(--w-480, 1)));
}

.w-500 {
  width: calc((31.25rem * var(--responsive-70)) * (var(--w-500, 1)));
}

.w-520 {
  width: calc((32.5rem * var(--responsive-70)) * (var(--w-520, 1)));
}

.w-540 {
  width: calc((33.75rem * var(--responsive-70)) * (var(--w-540, 1)));
}

.w-560 {
  width: calc((35rem * var(--responsive-70)) * (var(--w-560, 1)));
}

.w-580 {
  width: calc((36.25rem * var(--responsive-70)) * (var(--w-580, 1)));
}

.w-600 {
  width: calc((37.5rem * var(--responsive-70)) * (var(--w-600, 1)));
}

.w-620 {
  width: calc((38.75rem * var(--responsive-70)) * (var(--w-620, 1)));
}

.w-640 {
  width: calc((40rem * var(--responsive-70)) * (var(--w-640, 1)));
}

.w-660 {
  width: calc((41.25rem * var(--responsive-70)) * (var(--w-660, 1)));
}

.w-680 {
  width: calc((42.5rem * var(--responsive-70)) * (var(--w-680, 1)));
}

.w-700 {
  width: calc((43.75rem * var(--responsive-70)) * (var(--w-700, 1)));
}

.w-720 {
  width: calc((45rem * var(--responsive-70)) * (var(--w-720, 1)));
}

.w-740 {
  width: calc((46.25rem * var(--responsive-70)) * (var(--w-740, 1)));
}

.w-760 {
  width: calc((47.5rem * var(--responsive-70)) * (var(--w-760, 1)));
}

.w-780 {
  width: calc((48.75rem * var(--responsive-70)) * (var(--w-780, 1)));
}

.w-800 {
  width: calc((50rem * var(--responsive-70)) * (var(--w-800, 1)));
}

.w-900 {
  width: calc((56.25rem * var(--responsive-70)) * (var(--w-900, 1)));
}

.w-1000 {
  width: calc((62.5rem * var(--responsive-70)) * (var(--w-1000, 1)));
}

.w-1100 {
  width: calc((68.75rem * var(--responsive-70)) * (var(--w-1100, 1)));
}

.w-1200 {
  width: calc((75rem * var(--responsive-70)) * (var(--w-1200, 1)));
}



.s\:w-4 { width: 0.25rem; }
.s\:w-6 { width: 0.375rem; }
.s\:w-8 { width: 0.5rem; }
.s\:w-10 { width: 0.625rem; }
.s\:w-12 { width: 0.75rem; }
.s\:w-14 { width: 0.875rem; }
.s\:w-16 { width: 1rem; }
.s\:w-18 { width: 1.125rem; }
.s\:w-20 { width: 1.25rem; }
.s\:w-22 { width: 1.375rem; }
.s\:w-24 { width: 1.5rem; }
.s\:w-28 { width: 1.75rem; }
.s\:w-32 { width: 2rem; }
.s\:w-36 { width: 2.25rem; }
.s\:w-40 { width: 2.5rem; }
.s\:w-44 { width: 2.75rem; }
.s\:w-48 { width: 3rem; }
.s\:w-52 { width: 3.25rem; }
.s\:w-56 { width: 3.5rem; }
.s\:w-60 { width: 3.75rem; }
.s\:w-64 { width: 4rem; }
.s\:w-68 { width: 4.25rem; }
.s\:w-72 { width: 4.5rem; }
.s\:w-76 { width: 4.75rem; }
.s\:w-80 { width: 5rem; }
.s\:w-84 { width: 5.25rem; }
.s\:w-88 { width: 5.5rem; }
.s\:w-92 { width: 5.75rem; }
.s\:w-96 { width: 6rem; }
.s\:w-100 { width: 6.25rem; }
.s\:w-120 { width: 7.5rem; }
.s\:w-140 { width: 8.75rem; }
.s\:w-160 { width: 10rem; }
.s\:w-180 { width: 11.25rem; }
.s\:w-200 { width: 12.5rem; }
.s\:w-220 { width: 13.75rem; }
.s\:w-240 { width: 15rem; }
.s\:w-260 { width: 16.25rem; }
.s\:w-280 { width: 17.5rem; }
.s\:w-300 { width: 18.75rem; }
.s\:w-320 { width: 20rem; }
.s\:w-340 { width: 21.25rem; }
.s\:w-360 { width: 22.5rem; }
.s\:w-380 { width: 23.75rem; }
.s\:w-400 { width: 25rem; }
.s\:w-420 { width: 26.25rem; }
.s\:w-440 { width: 27.5rem; }
.s\:w-460 { width: 28.75rem; }
.s\:w-480 { width: 30rem; }
.s\:w-500 { width: 31.25rem; }
.s\:w-520 { width: 32.5rem; }
.s\:w-540 { width: 33.75rem; }
.s\:w-560 { width: 35rem; }
.s\:w-580 { width: 36.25rem; }
.s\:w-600 { width: 37.5rem; }
.s\:w-620 { width: 38.75rem; }
.s\:w-640 { width: 40rem; }
.s\:w-660 { width: 41.25rem; }
.s\:w-680 { width: 42.5rem; }
.s\:w-700 { width: 43.75rem; }
.s\:w-720 { width: 45rem; }
.s\:w-740 { width: 46.25rem; }
.s\:w-760 { width: 47.5rem; }
.s\:w-780 { width: 48.75rem; }
.s\:w-800 { width: 50rem; }
.s\:w-900 { width: 56.25rem; }
.s\:w-1000 { width: 62.5rem; }
.s\:w-1100 { width: 68.75rem; }
.s\:w-1200 { width: 75rem; }


/*--------------------------------------------------------------

## Max width

--------------------------------------------------------------*/

.max-w-10-100 {
  max-width: 10%;
}

.max-w-15-100 {
  max-width: 15%;
}

.max-w-20-100 {
  max-width: 20%;
}

.max-w-25-100 {
  max-width: 25%;
}

.max-w-30-100 {
  max-width: 30%;
}

.max-w-35-100 {
  max-width: 35%;
}

.max-w-40-100 {
  max-width: 40%;
}

.max-w-45-100 {
  max-width: 45%;
}

.max-w-50-100 {
  max-width: 50%;
}

.max-w-55-100 {
  max-width: 55%;
}

.max-w-60-100 {
  max-width: 60%;
}

.max-w-65-100 {
  max-width: 65%;
}

.max-w-70-100 {
  max-width: 70%;
}

.max-w-75-100 {
  max-width: 75%;
}

.max-w-80-100 {
  max-width: 80%;
}

.max-w-85-100 {
  max-width: 85%;
}

.max-w-90-100 {
  max-width: 90%;
}

.max-w-95-100 {
  max-width: 95%;
}
 
.max-w-100-100 {
  max-width: 100%;
}
 
.max-w-0 {
  max-width: 0;
}

.max-w-1 {
  max-width: 1px;
}

.max-w-4 {
  max-width: calc((0.25rem * var(--responsive-70)) * (var(--max-w-4, 1)));
}

.max-w-6 {
  max-width: calc((0.375rem * var(--responsive-70)) * (var(--max-w-6, 1)));
}

.max-w-8 {
  max-width: calc((0.5rem * var(--responsive-70)) * (var(--max-w-8, 1)));
}

.max-w-10 {
  max-width: calc((0.625rem * var(--responsive-70)) * (var(--max-w-10, 1)));
}

.max-w-12 {
  max-width: calc((0.75rem * var(--responsive-70)) * (var(--max-w-12, 1)));
}

.max-w-14 {
  max-width: calc((0.875rem * var(--responsive-70)) * (var(--max-w-14, 1)));
}

.max-w-16 {
  max-width: calc((1rem * var(--responsive-70)) * (var(--max-w-16, 1)));
}

.max-w-18 {
  max-width: calc((1.125rem * var(--responsive-70)) * (var(--max-w-18, 1)));
}

.max-w-20 {
  max-width: calc((1.25rem * var(--responsive-70)) * (var(--max-w-20, 1)));
}

.max-w-22 {
  max-width: calc((1.375rem * var(--responsive-70)) * (var(--max-w-22, 1)));
}

.max-w-24 {
  max-width: calc((1.5rem * var(--responsive-70)) * (var(--max-w-24, 1)));
}

.max-w-28 {
  max-width: calc((1.75rem * var(--responsive-70)) * (var(--max-w-28, 1)));
}

.max-w-32 {
  max-width: calc((2rem * var(--responsive-70)) * (var(--max-w-32, 1)));
}

.max-w-36 {
  max-width: calc((2.25rem * var(--responsive-70)) * (var(--max-w-36, 1)));
}

.max-w-40 {
  max-width: calc((2.5rem * var(--responsive-60)) * (var(--max-w-40, 1)));
}

.max-w-44 {
  max-width: calc((2.75rem * var(--responsive-60)) * (var(--max-w-44, 1)));
}

.max-w-48 {
  max-width: calc((3rem * var(--responsive-60)) * (var(--max-w-48, 1)));
}

.max-w-52 {
  max-width: calc((3.25rem * var(--responsive-60)) * (var(--max-w-52, 1)));
}

.max-w-56 {
  max-width: calc((3.5rem * var(--responsive-60)) * (var(--max-w-56, 1)));
}

.max-w-60 {
  max-width: calc((3.75rem * var(--responsive-50)) * (var(--max-w-60, 1)));
}

.max-w-64 {
  max-width: calc((4rem * var(--responsive-50)) * (var(--max-w-64, 1)));
}

.max-w-68 {
  max-width: calc((4.25rem * var(--responsive-50)) * (var(--max-w-68, 1)));
}

.max-w-72 {
  max-width: calc((4.5rem * var(--responsive-50)) * (var(--max-w-72, 1)));
}

.max-w-76 {
  max-width: calc((4.75rem * var(--responsive-50)) * (var(--max-w-76, 1)));
}

.max-w-80 {
  max-width: calc((5rem * var(--responsive-40)) * (var(--max-w-80, 1)));
}

.max-w-84 {
  max-width: calc((5.25rem * var(--responsive-40)) * (var(--max-w-84, 1)));
}

.max-w-88 {
  max-width: calc((5.5rem * var(--responsive-40)) * (var(--max-w-88, 1)));
}

.max-w-92 {
  max-width: calc((5.75rem * var(--responsive-40)) * (var(--max-w-92, 1)));
}

.max-w-96 {
  max-width: calc((6rem * var(--responsive-40)) * (var(--max-w-96, 1)));
}

.max-w-100 {
  max-width: calc((6.25rem * var(--responsive-30)) * (var(--max-w-100, 1)));
}

.max-w-120 {
  max-width: calc((7.5rem * var(--responsive-30)) * (var(--max-w-120, 1)));
}

.max-w-140 {
  max-width: calc((8.75rem * var(--responsive-30)) * (var(--max-w-140, 1)));
}

.max-w-160 {
  max-width: calc((10rem * var(--responsive-30)) * (var(--max-w-160, 1)));
}

.max-w-180 {
  max-width: calc((11.25rem * var(--responsive-30)) * (var(--max-w-180, 1)));
}

.max-w-200 {
  max-width: calc((12.5rem * var(--responsive-30)) * (var(--max-w-200, 1)));
}

.max-w-220 {
  max-width: calc((13.75rem * var(--responsive-70)) * (var(--max-w-220, 1)));
}

.max-w-240 {
  max-width: calc((15rem * var(--responsive-70)) * (var(--max-w-240, 1)));
}

.max-w-260 {
  max-width: calc((16.25rem * var(--responsive-70)) * (var(--max-w-260, 1)));
}

.max-w-280 {
  max-width: calc((17.5rem * var(--responsive-70)) * (var(--max-w-280, 1)));
}

.max-w-300 {
  max-width: calc((18.75rem * var(--responsive-70)) * (var(--max-w-300, 1)));
}

.max-w-320 {
  max-width: calc((20rem * var(--responsive-70)) * (var(--max-w-320, 1)));
}

.max-w-340 {
  max-width: calc((21.25rem * var(--responsive-70)) * (var(--max-w-340, 1)));
}

.max-w-360 {
  max-width: calc((22.5rem * var(--responsive-70)) * (var(--max-w-360, 1)));
}

.max-w-380 {
  max-width: calc((23.75rem * var(--responsive-70)) * (var(--max-w-380, 1)));
}

.max-w-400 {
  max-width: calc((25rem * var(--responsive-70)) * (var(--max-w-400, 1)));
}

.max-w-420 {
  max-width: calc((26.25rem * var(--responsive-70)) * (var(--max-w-420, 1)));
}

.max-w-440 {
  max-width: calc((27.5rem * var(--responsive-70)) * (var(--max-w-440, 1)));
}

.max-w-460 {
  max-width: calc((28.75rem * var(--responsive-70)) * (var(--max-w-460, 1)));
}

.max-w-480 {
  max-width: calc((30rem * var(--responsive-70)) * (var(--max-w-480, 1)));
}

.max-w-500 {
  max-width: calc((31.25rem * var(--responsive-70)) * (var(--max-w-500, 1)));
}

.max-w-520 {
  max-width: calc((32.5rem * var(--responsive-70)) * (var(--max-w-520, 1)));
}

.max-w-540 {
  max-width: calc((33.75rem * var(--responsive-70)) * (var(--max-w-540, 1)));
}

.max-w-560 {
  max-width: calc((35rem * var(--responsive-70)) * (var(--max-w-560, 1)));
}

.max-w-580 {
  max-width: calc((36.25rem * var(--responsive-70)) * (var(--max-w-580, 1)));
}

.max-w-600 {
  max-width: calc((37.5rem * var(--responsive-70)) * (var(--max-w-600, 1)));
}

.max-w-620 {
  max-width: calc((38.75rem * var(--responsive-70)) * (var(--max-w-620, 1)));
}

.max-w-640 {
  max-width: calc((40rem * var(--responsive-70)) * (var(--max-w-640, 1)));
}

.max-w-660 {
  max-width: calc((41.25rem * var(--responsive-70)) * (var(--max-w-660, 1)));
}

.max-w-680 {
  max-width: calc((42.5rem * var(--responsive-70)) * (var(--max-w-680, 1)));
}

.max-w-700 {
  max-width: calc((43.75rem * var(--responsive-70)) * (var(--max-w-700, 1)));
}

.max-w-720 {
  max-width: calc((45rem * var(--responsive-70)) * (var(--max-w-720, 1)));
}

.max-w-740 {
  max-width: calc((46.25rem * var(--responsive-70)) * (var(--max-w-740, 1)));
}

.max-w-760 {
  max-width: calc((47.5rem * var(--responsive-70)) * (var(--max-w-760, 1)));
}

.max-w-780 {
  max-width: calc((48.75rem * var(--responsive-70)) * (var(--max-w-780, 1)));
}

.max-w-800 {
  max-width: calc((50rem * var(--responsive-70)) * (var(--max-w-800, 1)));
}

.max-w-820 {
  max-width: calc((51.25rem * var(--responsive-70)) * (var(--max-w-820, 1)));
}

.max-w-840 {
  max-width: calc((52.5rem * var(--responsive-70)) * (var(--max-w-840, 1)));
}

.max-w-860 {
  max-width: calc((53.75rem * var(--responsive-70)) * (var(--max-w-860, 1)));
}

.max-w-880 {
  max-width: calc((55rem * var(--responsive-70)) * (var(--max-w-880, 1)));
}

.max-w-900 {
  max-width: calc((56.25rem * var(--responsive-70)) * (var(--max-w-900, 1)));
}

.max-w-920 {
  max-width: calc((57.5rem * var(--responsive-70)) * (var(--max-w-920, 1)));
}

.max-w-940 {
  max-width: calc((58.75rem * var(--responsive-70)) * (var(--max-w-940, 1)));
}
 
.max-w-960 {
  max-width: calc((60rem * var(--responsive-50)) * (var(--max-w-960, 1)));
}

.max-w-980 {
  max-width: calc((61.25rem * var(--responsive-70)) * (var(--max-w-980, 1)));
}

.max-w-1000 {
  max-width: calc((62.5rem * var(--responsive-70)) * (var(--max-w-1000, 1)));
}

.max-w-1100 {
  max-width: calc((66.25rem * var(--responsive-70)) * (var(--max-w-1100, 1)));
}

.max-w-1200 {
  max-width: calc((75rem * var(--responsive-70)) * (var(--max-w-1200, 1)));
}

.max-w-2000 {
    max-width: calc((125rem * var(--responsive-70)) * (var(--max-w-2000, 1)));
  } 

.s\:max-w-4 { max-width: 0.25rem; }
.s\:max-w-6 { max-width: 0.375rem; }
.s\:max-w-8 { max-width: 0.5rem; }
.s\:max-w-10 { max-width: 0.625rem; }
.s\:max-w-12 { max-width: 0.75rem; }
.s\:max-w-14 { max-width: 0.875rem; }
.s\:max-w-16 { max-width: 1rem; }
.s\:max-w-18 { max-width: 1.125rem; }
.s\:max-w-20 { max-width: 1.25rem; }
.s\:max-w-22 { max-width: 1.375rem; }
.s\:max-w-24 { max-width: 1.5rem; }
.s\:max-w-28 { max-width: 1.75rem; }
.s\:max-w-32 { max-width: 2rem; }
.s\:max-w-36 { max-width: 2.25rem; }
.s\:max-w-40 { max-width: 2.5rem; }
.s\:max-w-44 { max-width: 2.75rem; }
.s\:max-w-48 { max-width: 3rem; }
.s\:max-w-52 { max-width: 3.25rem; }
.s\:max-w-56 { max-width: 3.5rem; }
.s\:max-w-60 { max-width: 3.75rem; }
.s\:max-w-64 { max-width: 4rem; }
.s\:max-w-68 { max-width: 4.25rem; }
.s\:max-w-72 { max-width: 4.5rem; }
.s\:max-w-76 { max-width: 4.75rem; }
.s\:max-w-80 { max-width: 5rem; }
.s\:max-w-84 { max-width: 5.25rem; }
.s\:max-w-88 { max-width: 5.5rem; }
.s\:max-w-92 { max-width: 5.75rem; }
.s\:max-w-96 { max-width: 6rem; }
.s\:max-w-100 { max-width: 6.25rem; }
.s\:max-w-120 { max-width: 7.5rem; }
.s\:max-w-140 { max-width: 8.75rem; }
.s\:max-w-160 { max-width: 10rem; }
.s\:max-w-180 { max-width: 11.25rem; }
.s\:max-w-200 { max-width: 12.5rem; }
.s\:max-w-220 { max-width: 13.75rem; }
.s\:max-w-240 { max-width: 15rem; }
.s\:max-w-260 { max-width: 16.25rem; }
.s\:max-w-280 { max-width: 17.5rem; }
.s\:max-w-300 { max-width: 18.75rem; }
.s\:max-w-320 { max-width: 20rem; }
.s\:max-w-340 { max-width: 21.25rem; }
.s\:max-w-360 { max-width: 22.5rem; }
.s\:max-w-380 { max-width: 23.75rem; }
.s\:max-w-400 { max-width: 25rem; }
.s\:max-w-420 { max-width: 26.25rem; }
.s\:max-w-440 { max-width: 27.5rem; }
.s\:max-w-460 { max-width: 28.75rem; }
.s\:max-w-480 { max-width: 30rem; }
.s\:max-w-500 { max-width: 31.25rem; }
.s\:max-w-520 { max-width: 32.5rem; }
.s\:max-w-540 { max-width: 33.75rem; }
.s\:max-w-560 { max-width: 35rem; }
.s\:max-w-580 { max-width: 36.25rem; }
.s\:max-w-600 { max-width: 37.5rem; }
.s\:max-w-620 { max-width: 38.75rem; }
.s\:max-w-640 { max-width: 40rem; }
.s\:max-w-660 { max-width: 41.25rem; }
.s\:max-w-680 { max-width: 42.5rem; }
.s\:max-w-700 { max-width: 43.75rem; }
.s\:max-w-720 { max-width: 45rem; }
.s\:max-w-740 { max-width: 46.25rem; }
.s\:max-w-760 { max-width: 47.5rem; }
.s\:max-w-780 { max-width: 48.75rem; }
.s\:max-w-800 { max-width: 50rem; }




/*--------------------------------------------------------------

## Min width

--------------------------------------------------------------*/


.min-w-10-100 {
  min-width: 10%;
}

.min-w-15-100 {
  min-width: 15%;
}

.min-w-20-100 {
  min-width: 20%;
}

.min-w-25-100 {
  min-width: 25%;
}

.min-w-30-100 {
  min-width: 30%;
}

.min-w-35-100 {
  min-width: 35%;
}

.min-w-40-100 {
  min-width: 40%;
}

.min-w-45-100 {
  min-width: 45%;
}

.min-w-50-100 {
  min-width: 50%;
}

.min-w-55-100 {
  min-width: 55%;
}

.min-w-60-100 {
  min-width: 60%;
}

.min-w-65-100 {
  min-width: 65%;
}

.min-w-70-100 {
  min-width: 70%;
}

.min-w-75-100 {
  min-width: 75%;
}

.min-w-80-100 {
  min-width: 80%;
}

.min-w-85-100 {
  min-width: 85%;
}

.min-w-90-100 {
  min-width: 90%;
}

.min-w-95-100 {
  min-width: 95%;
}

.min-w-100-100 {
  min-width: 100%;
}

.min-w-0 {
  min-width: 0;
}

.min-w-1 {
  min-width: 1px;
}

.min-w-4 {
  min-width: 0.25rem;
}

.min-w-6 {
  min-width: 0.375rem;
}

.min-w-8 {
  min-width: 0.5rem;
}

.min-w-10 {
  min-width: 0.625rem;
}

.min-w-12 {
  min-width: 0.75rem;
}

.min-w-14 {
  min-width: 0.875rem;
}

.min-w-16 {
  min-width: 1rem;
}

.min-w-18 {
  min-width: 1.125rem;
}

.min-w-20 {
  min-width: 1.25rem;
}

.min-w-22 {
  min-width: 1.375rem;
}

.min-w-24 {
  min-width: 1.5rem;
}

.min-w-28 {
  min-width: 1.75rem;
}

.min-w-32 {
  min-width: 2rem;
}

.min-w-36 {
  min-width: 2.25rem;
}

.min-w-40 {
  min-width: 2.5rem;
}

.min-w-44 {
  min-width: 2.75rem;
}

.min-w-48 {
  min-width: 3rem;
}

.min-w-52 {
  min-width: 3.25rem;
}

.min-w-56 {
  min-width: 3.5rem;
}

.min-w-60 {
  min-width: 3.75rem;
}

.min-w-64 {
  min-width: 4rem;
}

.min-w-68 {
  min-width: 4.25rem;
}

.min-w-72 {
  min-width: 4.5rem;
}

.min-w-76 {
  min-width: 4.75rem;
}

.min-w-80 {
  min-width: 5rem;
}

.min-w-84 {
  min-width: 5.25rem;
}

.min-w-88 {
  min-width: 5.5rem;
}

.min-w-92 {
  min-width: 5.75rem;
}

.min-w-96 {
  min-width: 6rem;
}

.min-w-100 {
  min-width: 6.25rem;
}

.min-w-120 {
  min-width: 7.5rem;
}

.min-w-140 {
  min-width: 8.75rem;
}

.min-w-160 {
  min-width: 10rem;
}

.min-w-180 {
  min-width: 11.25rem;
}

.min-w-200 {
  min-width: 12.5rem;
}

.min-w-220 {
  min-width: 13.75rem;
}

.min-w-240 {
  min-width: 15rem;
}

.min-w-260 {
  min-width: 16.25rem;
}

.min-w-280 {
  min-width: 17.5rem;
}

.min-w-300 {
  min-width:18.75rem;
}

.min-w-320 {
  min-width:20rem;
}

.min-w-340 {
  min-width:21.25rem;
}

.min-w-360 {
  min-width:22.5rem;
}

.min-w-380 {
  min-width:23.75rem;
}

.min-w-400 {
  min-width:25rem;
}

.min-w-420 {
  min-width:26.25rem;
}

.min-w-440 {
  min-width:27.5rem;
}

.min-w-460 {
  min-width:28.75rem;
}

.min-w-480 {
  min-width:30rem;
}

.min-w-500 {
  min-width:31.25rem;
}

.min-w-520 {
  min-width:32.5rem;
}

.min-w-540 {
  min-width:33.75rem;
}

.min-w-560 {
  min-width:35rem;
}

.min-w-580 {
  min-width:36.25rem;
}

.min-w-600 {
  min-width:37.5rem;
}

.min-w-620 {
  min-width:38.75rem;
}

.min-w-640 {
  min-width:40rem;
}

.min-w-660 {
  min-width:41.25rem;
}

.min-w-680 {
  min-width:42.5rem;
}

.min-w-700 {
  min-width:43.75rem;
}

.min-w-720 {
  min-width:45rem;
}

.min-w-740 {
  min-width:46.25rem;
}

.min-w-760 {
  min-width:47.5rem;
}

.min-w-780 {
  min-width:48.75rem;
}

.min-w-800 {
  min-width:50rem;
}

.min-w-820 {
  min-width:51.25rem;
}

.min-w-840 {
  min-width:52.5rem;
}

.min-w-860 {
  min-width:53.75rem;
}

.min-w-880 {
  min-width:55rem;
}

.min-w-900 {
  min-width:56.25rem;
}

.min-w-920 {
  min-width:57.5rem;
}

.min-w-940 {
  min-width:58.75rem;
}

.min-w-960 {
  min-width:60rem;
}

.min-w-980 {
  min-width:61.25rem;
}

.min-w-1000 {
  min-width:62.5rem;
}



/*--------------------------------------------------------------

## Min height

--------------------------------------------------------------*/

.min-h-100vh {
  min-height:100vh;
}


.min-h-10-100 {
  min-height: 10%;
}

.min-h-15-100 {
  min-height: 15%;
}

.min-h-20-100 {
  min-height: 20%;
}

.min-h-25-100 {
  min-height: 25%;
}

.min-h-30-100 {
  min-height: 30%;
}

.min-h-35-100 {
  min-height: 35%;
}

.min-h-40-100 {
  min-height: 40%;
}

.min-h-45-100 {
  min-height: 45%;
}

.min-h-50-100 {
  min-height: 50%;
}

.min-h-55-100 {
  min-height: 55%;
}

.min-h-60-100 {
  min-height: 60%;
}

.min-h-65-100 {
  min-height: 65%;
}

.min-h-70-100 {
  min-height: 70%;
}

.min-h-75-100 {
  min-height: 75%;
}

.min-h-80-100 {
  min-height: 80%;
}

.min-h-85-100 {
  min-height: 85%;
}

.min-h-90-100 {
  min-height: 90%;
}

.min-h-95-100 {
  min-height: 95%;
}

.min-h-100-100 {
  min-height: 100%;
}

.min-h-0 {
  min-height: 0;
}

.min-h-1 {
  min-height: 1px;
}

.min-h-4 {
  min-height: 0.25rem;
}

.min-h-6 {
  min-height: 0.375rem;
}

.min-h-8 {
  min-height: 0.5rem;
}

.min-h-10 {
  min-height: 0.625rem;
}

.min-h-12 {
  min-height: 0.75rem;
}

.min-h-14 {
  min-height: 0.875rem;
}

.min-h-16 {
  min-height: 1rem;
}

.min-h-18 {
  min-height: 1.125rem;
}

.min-h-20 {
  min-height: 1.25rem;
}

.min-h-22 {
  min-height: 1.375rem;
}

.min-h-24 {
  min-height: 1.5rem;
}

.min-h-28 {
  min-height: 1.75rem;
}

.min-h-32 {
  min-height: 2rem;
}

.min-h-36 {
  min-height: 2.25rem;
}

.min-h-40 {
  min-height: 2.5rem;
}

.min-h-44 {
  min-height: 2.75rem;
}

.min-h-48 {
  min-height: 3rem;
}

.min-h-52 {
  min-height: 3.25rem;
}

.min-h-56 {
  min-height: 3.5rem;
}

.min-h-60 {
  min-height: 3.75rem;
}

.min-h-64 {
  min-height: 4rem;
}

.min-h-68 {
  min-height: 4.25rem;
}

.min-h-72 {
  min-height: 4.5rem;
}

.min-h-76 {
  min-height: 4.75rem;
}

.min-h-80 {
  min-height: 5rem;
}

.min-h-84 {
  min-height: 5.25rem;
}

.min-h-88 {
  min-height: 5.5rem;
}

.min-h-92 {
  min-height: 5.75rem;
}

.min-h-96 {
  min-height: 6rem;
}

.min-h-100 {
  min-height: 6.25rem;
}

.min-h-120 {
  min-height: 7.5rem;
}

.min-h-140 {
  min-height: 8.75rem;
}

.min-h-160 {
  min-height: 10rem;
}

.min-h-180 {
  min-height: 11.25rem;
}

.min-h-200 {
  min-height: 12.5rem;
}

.min-h-220 {
  min-height: 13.75rem;
}

.min-h-240 {
  min-height: 15rem;
}

.min-h-260 {
  min-height: 16.25rem;
}

.min-h-280 {
  min-height: 17.5rem;
}


.min-h-300 {
  min-height:18.75rem;
}

.min-h-320 {
  min-height:20rem;
}

.min-h-340 {
  min-height:21.25rem;
}

.min-h-360 {
  min-height:22.5rem;
}

.min-h-380 {
  min-height:23.75rem;
}

.min-h-400 {
  min-height:25rem;
}

.min-h-420 {
  min-height:26.25rem;
}

.min-h-440 {
  min-height:27.5rem;
}

.min-h-460 {
  min-height:28.75rem;
}

.min-h-480 {
  min-height:30rem;
}

.min-h-500 {
  min-height:31.25rem;
}

.min-h-520 {
  min-height:32.5rem;
}

.min-h-540 {
  min-height:33.75rem;
}

.min-h-560 {
  min-height:35rem;
}

.min-h-580 {
  min-height:36.25rem;
}

.min-h-600 {
  min-height:37.5rem;
}

.min-h-620 {
  min-height:38.75rem;
}

.min-h-640 {
  min-height:40rem;
}

.min-h-660 {
  min-height:41.25rem;
}

.min-h-680 {
  min-height:42.5rem;
}

.min-h-700 {
  min-height:43.75rem;
}

.min-h-720 {
  min-height:45rem;
}

.min-h-740 {
  min-height:46.25rem;
}

.min-h-760 {
  min-height:47.5rem;
}

.min-h-780 {
  min-height:48.75rem;
}

.min-h-800 {
  min-height:50rem;
}

.min-h-820 {
  min-height:51.25rem;
}

.min-h-840 {
  min-height:52.5rem;
}

.min-h-860 {
  min-height:53.75rem;
}

.min-h-880 {
  min-height:55rem;
}

.min-h-900 {
  min-height:56.25rem;
}

.min-h-920 {
  min-height:57.5rem;
}

.min-h-940 {
  min-height:58.75rem;
}

.min-h-960 {
  min-height:60rem;
}

.min-h-980 {
  min-height:61.25rem;
}

.min-h-1000 {
  min-height:62.5rem;
}


/*--------------------------------------------------------------

## Max height

--------------------------------------------------------------*/

.max-h-10-100 {
  max-height: 10%;
}

.max-h-15-100 {
  max-height: 15%;
}

.max-h-20-100 {
  max-height: 20%;
}

.max-h-25-100 {
  max-height: 25%;
}

.max-h-30-100 {
  max-height: 30%;
}

.max-h-35-100 {
  max-height: 35%;
}

.max-h-40-100 {
  max-height: 40%;
}

.max-h-45-100 {
  max-height: 45%;
}

.max-h-50-100 {
  max-height: 50%;
}

.max-h-55-100 {
  max-height: 55%;
}

.max-h-60-100 {
  max-height: 60%;
}

.max-h-65-100 {
  max-height: 65%;
}

.max-h-70-100 {
  max-height: 70%;
}

.max-h-75-100 {
  max-height: 75%;
}

.max-h-80-100 {
  max-height: 80%;
}

.max-h-85-100 {
  max-height: 85%;
}

.max-h-90-100 {
  max-height: 90%;
}

.max-h-95-100 {
  max-height: 95%;
}

.max-h-100-100 {
  max-height: 100%;
}

.max-h-0 {
  max-height: 0;
}

.max-h-1 {
  max-height: 1px;
}

.max-h-4 {
  max-height: 0.25rem;
}

.max-h-6 {
  max-height: 0.375rem;
}

.max-h-8 {
  max-height: 0.5rem;
}

.max-h-10 {
  max-height: 0.625rem;
}

.max-h-12 {
  max-height: 0.75rem;
}

.max-h-14 {
  max-height: 0.875rem;
}

.max-h-16 {
  max-height: 1rem;
}

.max-h-18 {
  max-height: 1.125rem;
}

.max-h-20 {
  max-height: 1.25rem;
}

.max-h-22 {
  max-height: 1.375rem;
}

.max-h-24 {
  max-height: 1.5rem;
}

.max-h-28 {
  max-height: 1.75rem;
}

.max-h-32 {
  max-height: 2rem;
}

.max-h-36 {
  max-height: 2.25rem;
}

.max-h-40 {
  max-height: 2.5rem;
}

.max-h-44 {
  max-height: 2.75rem;
}

.max-h-48 {
  max-height: 3rem;
}

.max-h-52 {
  max-height: 3.25rem;
}

.max-h-56 {
  max-height: 3.5rem;
}

.max-h-60 {
  max-height: 3.75rem;
}

.max-h-64 {
  max-height: 4rem;
}

.max-h-68 {
  max-height: 4.25rem;
}

.max-h-72 {
  max-height: 4.5rem;
}

.max-h-76 {
  max-height: 4.75rem;
}

.max-h-80 {
  max-height: 5rem;
}

.max-h-84 {
  max-height: 5.25rem;
}

.max-h-88 {
  max-height: 5.5rem;
}

.max-h-92 {
  max-height: 5.75rem;
}

.max-h-96 {
  max-height: 6rem;
}

.max-h-100 {
  max-height: 6.25rem;
}

.max-h-120 {
  max-height: 7.5rem;
}

.max-h-140 {
  max-height: 8.75rem;
}

.max-h-160 {
  max-height: 10rem;
}

.max-h-180 {
  max-height: 11.25rem;
}

.max-h-200 {
  max-height: 12.5rem;
}

.max-h-220 {
  max-height: 13.75rem;
}

.max-h-240 {
  max-height: 15rem;
}

.max-h-260 {
  max-height: 16.25rem;
}

.max-h-280 {
  max-height: 17.5rem;
}


.max-h-300 {
  max-height:18.75rem;
}

.max-h-320 {
  max-height:20rem;
}

.max-h-340 {
  max-height:21.25rem;
}

.max-h-360 {
  max-height:22.5rem;
}

.max-h-380 {
  max-height:23.75rem;
}

.max-h-400 {
  max-height:25rem;
}

.max-h-420 {
  max-height:26.25rem;
}

.max-h-440 {
  max-height:27.5rem;
}

.max-h-460 {
  max-height:28.75rem;
}

.max-h-480 {
  max-height:30rem;
}

.max-h-500 {
  max-height:31.25rem;
}

.max-h-520 {
  max-height:32.5rem;
}

.max-h-540 {
  max-height:33.75rem;
}

.max-h-560 {
  max-height:35rem;
}

.max-h-580 {
  max-height:36.25rem;
}

.max-h-600 {
  max-height:37.5rem;
}

.max-h-620 {
  max-height:38.75rem;
}

.max-h-640 {
  max-height:40rem;
}

.max-h-660 {
  max-height:41.25rem;
}

.max-h-680 {
  max-height:42.5rem;
}

.max-h-700 {
  max-height:43.75rem;
}

.max-h-720 {
  max-height:45rem;
}

.max-h-740 {
  max-height:46.25rem;
}

.max-h-760 {
  max-height:47.5rem;
}

.max-h-780 {
  max-height:48.75rem;
}

.max-h-800 {
  max-height:50rem;
}

.max-h-820 {
  max-height:51.25rem;
}

.max-h-840 {
  max-height:52.5rem;
}

.max-h-860 {
  max-height:53.75rem;
}

.max-h-880 {
  max-height:55rem;
}

.max-h-900 {
  max-height:56.25rem;
}

.max-h-920 {
  max-height:57.5rem;
}

.max-h-940 {
  max-height:58.75rem;
}

.max-h-960 {
  max-height:60rem;
}

.max-h-980 {
  max-height:61.25rem;
}

.max-h-1000 {
  max-height:62.5rem;
}

/*--------------------------------------------------------------

## Font size

--------------------------------------------------------------*/

.text-8 {
  font-size: calc((0.5rem * var(--responsive-100, 1)) * (var(--text-8, 1)));
}

.text-10 {
  font-size: calc((0.625rem * var(--responsive-100, 1)) * (var(--text-10, 1)));
}

.text-12 {
  font-size: calc((0.75rem * var(--responsive-100, 1)) * (var(--text-12, 1)));
}

.text-14 {
  font-size: calc((0.875rem * var(--responsive-100, 1)) * (var(--text-14, 1)));
}

.text-16 {
  font-size: max(
    calc((1rem * var(--responsive-80, 1)) * (var(--text-16, 1))),
    0.875rem * (var(--text-16-min, 1))
  );
}

.text-18 {
  font-size: max(
    calc((1.125rem * var(--responsive-60, 1)) * (var(--text-18, 1))),
    1rem * (var(--text-18-min, 1))
  );
}

.text-20 {
  font-size: max(
    calc((1.25rem * var(--responsive-60, 1)) * (var(--text-20, 1))),
    1rem * (var(--text-20-min, 1))
  );
}

.text-22 {
  font-size: max(
    calc((1.375rem * var(--responsive-50, 1)) * (var(--text-22, 1))),
    1rem * (var(--text-22-min, 1))
  );
}

.text-24 {
  font-size: max(
    calc((1.5rem * var(--responsive-50, 1)) * (var(--text-24, 1))),
    1.125rem * (var(--text-24-min, 1))
  );
}

.text-28 {
  font-size: max(
    calc((1.75rem * var(--responsive-50, 1)) * (var(--text-28, 1))),
    1.25rem * (var(--text-28-min, 1))
  );
}

.text-36 {
  font-size: max(
    calc((2.25rem * var(--responsive-40, 1)) * (var(--text-36, 1))),
    1.375rem * (var(--text-36-min, 1))
  );
}

.text-48 {
  font-size: max(
    calc((3rem * var(--responsive-30, 1)) * (var(--text-48, 1))),
    1.5rem * (var(--text-48-min, 1))
  );
}

.text-60 {
  font-size: max(
    calc((3.75rem * var(--responsive-30, 1)) * (var(--text-60, 1))),
    1.625rem * (var(--text-60-min, 1))
  );
}

.text-72 {
  font-size: max(
    calc((4.5rem * var(--responsive-30, 1)) * (var(--text-72, 1))),
    1.75rem * (var(--text-72-min, 1))
  );
}

.text-96 {
  font-size: max(
    calc((6rem * var(--responsive-20, 1)) * (var(--text-96, 1))),
    2rem * (var(--text-96-min, 1))
  );
}

.text-128 {
  font-size: max(
    calc((8rem * var(--responsive-40, 1)) * (var(--text-128, 1))),
    2.25rem * (var(--text-128-min, 1))
  );
}



.s\:text-8 { font-size: 0.5rem; }
.s\:text-10 { font-size: 0.625rem; }
.s\:text-12 { font-size: 0.75rem; }
.s\:text-14 { font-size: 0.875rem; }
.s\:text-16 { font-size: 1rem; }
.s\:text-18 { font-size: 1.125rem; }
.s\:text-20 { font-size: 1.25rem; }
.s\:text-22 { font-size: 1.375rem; }
.s\:text-24 { font-size: 1.5rem; }
.s\:text-28 { font-size: 1.75rem; }
.s\:text-36 { font-size: 2.25rem; }
.s\:text-48 { font-size: 3rem; }
.s\:text-60 { font-size: 3.75rem; }
.s\:text-72 { font-size: 4.5rem; }
.s\:text-96 { font-size: 6rem; }
.s\:text-128 { font-size: 8rem; }

@media (max-width:360px) {



  
  :root {
    --text-16-min: 0.9;
    --text-18-min: 0.9;
    --text-20-min: 0.9;
    --text-22-min: 0.9;
    --text-24-min: 0.9;
    --text-36-min: 0.9;
    --text-48-min: 0.9;
    --text-60-min: 0.9;
    --text-72-min: 0.9;
    --text-96-min: 0.9;
    --text-128-min: 0.9;
  }


}
 
/*--------------------------------------------------------------

## Letter spacing

--------------------------------------------------------------*/

.spacing-0 {
  letter-spacing: 0;
}

.spacing-2 {
  letter-spacing: calc(0.01875rem * var(--spacing-2, 1));
}

.spacing-4 {
  letter-spacing: calc(0.0375rem * var(--spacing-4, 1));
}

.spacing-6 {
  letter-spacing: calc(0.0625rem * var(--spacing-6, 1));
}

.spacing-12 {
  letter-spacing: calc((0.125rem * var(--responsive-80)) * (var(--spacing-12, 1)));
}

.spacing-18 {
  letter-spacing: calc((0.1875rem * var(--responsive-60)) * (var(--spacing-18, 1)));
}

/*--------------------------------------------------------------

## Text transform

--------------------------------------------------------------*/

.uppercase {
  text-transform: uppercase;
}

.lowercase {
  text-transform: lowercase;
}

.capitalize {
  text-transform: capitalize;
}

.italic {
  font-style: italic;
}

/*--------------------------------------------------------------

## Text wrap

--------------------------------------------------------------*/

.no-wrap {
  white-space: nowrap;
}

/*--------------------------------------------------------------

## Leading

--------------------------------------------------------------*/

.leading-0 {
  line-height: 0em;
}

.leading-08 {
  line-height: calc(0.8em * var(--leading-08, 1));
}

.leading-09 {
  line-height: calc(0.9em * var(--leading-09, 1));
}

.leading-10 {
  line-height: calc(1em * var(--leading-1, 1));
}

.leading-11 {
  line-height: calc(1.1em * var(--leading-11, 1));
}

.leading-12 {
  line-height: calc(1.2em * var(--leading-12, 1));
}

.leading-13 {
  line-height: calc(1.3em * var(--leading-13, 1));
}

.leading-14 {
  line-height: calc(1.4em * var(--leading-14, 1));
}

.leading-15 {
  line-height: calc(1.5em * var(--leading-15, 1));
}



@media (max-width: 1120px) {
  
.leading-14 {
  line-height: calc(1.4em * var(--leading-14, 1));
}

.leading-15 {
  line-height: calc(1.4em * var(--leading-15, 1));
}
  
  
}

@media (max-width: 480px) {
  
  
.leading-14 {
  line-height: calc(1.4em * var(--leading-14, 1));
}

.leading-15 {
  line-height: calc(1.4em * var(--leading-15, 1));
}
  
  
}



/*--------------------------------------------------------------

## Text decoration

--------------------------------------------------------------*/

.underline {
  text-decoration: underline;
}

.line-through {
  text-decoration: line-through;
}

/*--------------------------------------------------------------

## Text Align

--------------------------------------------------------------*/

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.text-center {
  text-align: center;
}

.text-justify {
  text-align: justify;
}

/*--------------------------------------------------------------

## Text color

--------------------------------------------------------------*/

.text-white {
  color: rgb(255, 255, 255);
}

.text-inherit {
  color: inherit;
}

.text-transparent {
  color: transparent;
}

.text-black {
  color: rgb(0, 0, 0);
  color:rgb(32,41,56);
}

.text-gray {
  color: rgb(121, 139, 151);
  color: rgb(122, 131, 141);
  color:rgb(121, 139, 151);
  color:rgb(99, 108, 116);
}

.text-green {
  color: rgb(47, 176, 35);
}

.text-red {
  color: rgb(239, 68, 68);
}

/*--------------------------------------------------------------

## Vertical align

--------------------------------------------------------------*/

.align-baseline {
  vertical-align: baseline;
}

.align-top {
  vertical-align: top;
}

.align-middle {
  vertical-align: middle;
}

.align-text-top {
  vertical-align: text-top;
}

.align-text-bottom {
  vertical-align: text-bottom;
}

.align-sub {
  vertical-align: sub;
}

.align-super {
  vertical-align: super;
}

/*--------------------------------------------------------------

## Opacity

--------------------------------------------------------------*/

.opacity-0 {
  opacity: 0;
}

.opacity-5 {
  opacity: 0.05;
}

.opacity-10 {
  opacity: 0.10;
}

.opacity-15 {
  opacity: 0.15;
}

.opacity-20 {
  opacity: 0.20;
}

.opacity-25 {
  opacity: 0.25;
}

.opacity-30 {
  opacity: 0.30;
}

.opacity-35 {
  opacity: 0.35;
}

.opacity-40 {
  opacity: 0.40;
}

.opacity-45 {
  opacity: 0.45;
}

.opacity-50 {
  opacity: 0.50;
}

.opacity-55 {
  opacity: 0.55;
}

.opacity-60 {
  opacity: 0.60;
}

.opacity-65 {
  opacity: 0.65;
}

.opacity-70 {
  opacity: 0.70;
}

.opacity-75 {
  opacity: 0.75;
}

.opacity-80 {
  opacity: 0.80;
}

.opacity-85 {
  opacity: 0.85;
}

.opacity-90 {
  opacity: 0.90;
}

.opacity-95 {
  opacity: 0.95;
}

.opacity-100 {
  opacity: 1;
}

/*--------------------------------------------------------------

## Border radius

--------------------------------------------------------------*/

.rounded-0 {
  border-radius: 0;
}

.rounded-2 {
  border-radius: calc(0.125rem * var(--rounded-2, 1));
}

.rounded-4 {
  border-radius: calc(0.25rem * var(--rounded-4, 1));
}

.rounded-6 {
  border-radius: calc(0.375rem * var(--rounded-6, 1));
}

.rounded-8 {
  border-radius: calc((0.5rem * var(--responsive-70)) * (var(--rounded-8, 1)));
}

.rounded-12 {
  border-radius: calc((0.75rem * var(--responsive-70)) * (var(--rounded-12, 1)));
}

.rounded-16 {
  border-radius: calc((1rem * var(--responsive-60)) * (var(--rounded-16, 1)));
}

.rounded-24 {
  border-radius: calc((1.5rem * var(--responsive-70)) * (var(--rounded-24, 1)));
}



/* Right side (top-right + bottom-right) */
.rounded-right-2 {
  border-top-right-radius: calc(0.125rem * var(--rounded-right-2, 1));
  border-bottom-right-radius: calc(0.125rem * var(--rounded-right-2, 1));
}
.rounded-right-4 {
  border-top-right-radius: calc(0.25rem * var(--rounded-right-4, 1));
  border-bottom-right-radius: calc(0.25rem * var(--rounded-right-4, 1));
}
.rounded-right-6 {
  border-top-right-radius: calc(0.375rem * var(--rounded-right-6, 1));
  border-bottom-right-radius: calc(0.375rem * var(--rounded-right-6, 1));
}
.rounded-right-8 {
  border-top-right-radius: calc((0.5rem * var(--responsive-70)) * (var(--rounded-right-8, 1)));
  border-bottom-right-radius: calc((0.5rem * var(--responsive-70)) * (var(--rounded-right-8, 1)));
}
.rounded-right-12 {
  border-top-right-radius: calc((0.75rem * var(--responsive-70)) * (var(--rounded-right-12, 1)));
  border-bottom-right-radius: calc((0.75rem * var(--responsive-70)) * (var(--rounded-right-12, 1)));
}
.rounded-right-16 {
  border-top-right-radius: calc((1rem * var(--responsive-60)) * (var(--rounded-right-16, 1)));
  border-bottom-right-radius: calc((1rem * var(--responsive-60)) * (var(--rounded-right-16, 1)));
}
.rounded-right-24 {
  border-top-right-radius: calc((1.5rem * var(--responsive-70)) * (var(--rounded-right-24, 1)));
  border-bottom-right-radius: calc((1.5rem * var(--responsive-70)) * (var(--rounded-right-24, 1)));
}

/* Left side (top-left + bottom-left) */
.rounded-left-2 {
  border-top-left-radius: calc(0.125rem * var(--rounded-left-2, 1));
  border-bottom-left-radius: calc(0.125rem * var(--rounded-left-2, 1));
}
.rounded-left-4 {
  border-top-left-radius: calc(0.25rem * var(--rounded-left-4, 1));
  border-bottom-left-radius: calc(0.25rem * var(--rounded-left-4, 1));
}
.rounded-left-6 {
  border-top-left-radius: calc(0.375rem * var(--rounded-left-6, 1));
  border-bottom-left-radius: calc(0.375rem * var(--rounded-left-6, 1));
}
.rounded-left-8 {
  border-top-left-radius: calc((0.5rem * var(--responsive-70)) * (var(--rounded-left-8, 1)));
  border-bottom-left-radius: calc((0.5rem * var(--responsive-70)) * (var(--rounded-left-8, 1)));
}
.rounded-left-12 {
  border-top-left-radius: calc((0.75rem * var(--responsive-70)) * (var(--rounded-left-12, 1)));
  border-bottom-left-radius: calc((0.75rem * var(--responsive-70)) * (var(--roundedl-12, 1)));
}
.rounded-left-16 {
  border-top-left-radius: calc((1rem * var(--responsive-60)) * (var(--rounded-left-16, 1)));
  border-bottom-left-radius: calc((1rem * var(--responsive-60)) * (var(--rounded-left-16, 1)));
}
.rounded-left-24 {
  border-top-left-radius: calc((1.5rem * var(--responsive-70)) * (var(--rounded-left-24, 1)));
  border-bottom-left-radius: calc((1.5rem * var(--responsive-70)) * (var(--rounded-left-24, 1)));
}

/* Top side (top-left + top-right) */
.rounded-top-2 {
  border-top-left-radius: calc(0.125rem * var(--rounded-top-2, 1));
  border-top-right-radius: calc(0.125rem * var(--rounded-top-2, 1));
}
.rounded-top-4 {
  border-top-left-radius: calc(0.25rem * var(--rounded-top-4, 1));
  border-top-right-radius: calc(0.25rem * var(--rounded-top-4, 1));
}
.rounded-top-6 {
  border-top-left-radius: calc(0.375rem * var(--rounded-top-6, 1));
  border-top-right-radius: calc(0.375rem * var(--rounded-top-6, 1));
}
.rounded-top-8 {
  border-top-left-radius: calc((0.5rem * var(--responsive-70)) * (var(--rounded-top-8, 1)));
  border-top-right-radius: calc((0.5rem * var(--responsive-70)) * (var(--rounded-top-8, 1)));
}
.rounded-top-12 {
  border-top-left-radius: calc((0.75rem * var(--responsive-70)) * (var(--rounded-top-12, 1)));
  border-top-right-radius: calc((0.75rem * var(--responsive-70)) * (var(--rounded-top-12, 1)));
}
.rounded-top-16 {
  border-top-left-radius: calc((1rem * var(--responsive-60)) * (var(--rounded-top-16, 1)));
  border-top-right-radius: calc((1rem * var(--responsive-60)) * (var(--rounded-top-16, 1)));
}
.rounded-top-24 {
  border-top-left-radius: calc((1.5rem * var(--responsive-70)) * (var(--rounded-top-24, 1)));
  border-top-right-radius: calc((1.5rem * var(--responsive-70)) * (var(--rounded-top-24, 1)));
}

/* Bottom side (bottom-left + bottom-right) */
.rounded-bottom-2 {
  border-bottom-left-radius: calc(0.125rem * var(--rounded-bottom-2, 1));
  border-bottom-right-radius: calc(0.125rem * var(--rounded-bottom-2, 1));
}
.rounded-bottom-4 {
  border-bottom-left-radius: calc(0.25rem * var(--rounded-bottom-4, 1));
  border-bottom-right-radius: calc(0.25rem * var(--rounded-bottom-4, 1));
}
.rounded-bottom-6 {
  border-bottom-left-radius: calc(0.375rem * var(--rounded-bottom-6, 1));
  border-bottom-right-radius: calc(0.375rem * var(--rounded-bottom-6, 1));
}
.rounded-bottom-8 {
  border-bottom-left-radius: calc((0.5rem * var(--responsive-70)) * (var(--rounded-bottom-8, 1)));
  border-bottom-right-radius: calc((0.5rem * var(--responsive-70)) * (var(--rounded-bottom-8, 1)));
}
.rounded-bottom-12 {
  border-bottom-left-radius: calc((0.75rem * var(--responsive-70)) * (var(--rounded-bottom-12, 1)));
  border-bottom-right-radius: calc((0.75rem * var(--responsive-70)) * (var(--rounded-bottom-12, 1)));
}
.rounded-bottom-16 {
  border-bottom-left-radius: calc((1rem * var(--responsive-60)) * (var(--rounded-bottom-16, 1)));
  border-bottom-right-radius: calc((1rem * var(--responsive-60)) * (var(--rounded-bottom-16, 1)));
}
.rounded-bottom-24 {
  border-bottom-left-radius: calc((1.5rem * var(--responsive-70)) * (var(--rounded-bottom-24, 1)));
  border-bottom-right-radius: calc((1.5rem * var(--responsive-70)) * (var(--rounded-bottom-24, 1)));
}


/* Top-right only */
.rounded-top-right-2 {
  border-top-right-radius: calc(0.125rem * var(--rounded-top-right-2, 1));
}
.rounded-top-right-4 {
  border-top-right-radius: calc(0.25rem * var(--rounded-top-right-4, 1));
}
.rounded-top-right-6 {
  border-top-right-radius: calc(0.375rem * var(--rounded-top-right-6, 1));
}
.rounded-top-right-8 {
  border-top-right-radius: calc((0.5rem * var(--responsive-70)) * (var(--rounded-top-right-8, 1)));
}
.rounded-top-right-12 {
  border-top-right-radius: calc((0.75rem * var(--responsive-70)) * (var(--rounded-top-right-12, 1)));
}
.rounded-top-right-16 {
  border-top-right-radius: calc((1rem * var(--responsive-60)) * (var(--rounded-top-right-16, 1)));
}
.rounded-top-right-24 {
  border-top-right-radius: calc((1.5rem * var(--responsive-70)) * (var(--rounded-top-right-24, 1)));
}



/* Top-left (TL) */
.rounded-top-left-2 {
  border-top-left-radius: calc(0.125rem * var(--rounded-top-left-2, 1));
}
.rounded-top-left-4 {
  border-top-left-radius: calc(0.25rem * var(--rounded-top-left-4, 1));
}
.rounded-top-left-6 {
  border-top-left-radius: calc(0.375rem * var(--rounded-top-left-6, 1));
}
.rounded-top-left-8 {
  border-top-left-radius: calc((0.5rem * var(--responsive-70)) * (var(--rounded-top-left-8, 1)));
}
.rounded-top-left-12 {
  border-top-left-radius: calc((0.75rem * var(--responsive-70)) * (var(--rounded-top-left-12, 1)));
}
.rounded-top-left-16 {
  border-top-left-radius: calc((1rem * var(--responsive-60)) * (var(--rounded-top-left-16, 1)));
}
.rounded-top-left-24 {
  border-top-left-radius: calc((1.5rem * var(--responsive-70)) * (var(--rounded-top-left-24, 1)));
}

/* Bottom-right (BR) */
.rounded-bottom-right-2 {
  border-bottom-right-radius: calc(0.125rem * var(--rounded-bottom-right-2, 1));
}
.rounded-bottom-right-4 {
  border-bottom-right-radius: calc(0.25rem * var(--rounded-bottom-right-4, 1));
}
.rounded-bottom-right-6 {
  border-bottom-right-radius: calc(0.375rem * var(--rounded-bottom-right-6, 1));
}
.rounded-bottom-right-8 {
  border-bottom-right-radius: calc((0.5rem * var(--responsive-70)) * (var(--rounded-bottom-right-8, 1)));
}
.rounded-bottom-right-12 {
  border-bottom-right-radius: calc((0.75rem * var(--responsive-70)) * (var(--rounded-bottom-right-12, 1)));
}
.rounded-bottom-right-16 {
  border-bottom-right-radius: calc((1rem * var(--responsive-60)) * (var(--rounded-bottom-right-16, 1)));
}
.rounded-bottom-right-24 {
  border-bottom-right-radius: calc((1.5rem * var(--responsive-70)) * (var(--rounded-bottom-right-24, 1)));
}

/* Bottom-left (BL) */
.rounded-bottom-left-2 {
  border-bottom-left-radius: calc(0.125rem * var(--roundedbottom-left-2, 1));
}
.rounded-bottom-left-4 {
  border-bottom-left-radius: calc(0.25rem * var(--rounded-bottom-left-4, 1));
}
.rounded-bottom-left-6 {
  border-bottom-left-radius: calc(0.375rem * var(--rounded-bottom-left-6, 1));
}
.rounded-bottom-left-8 {
  border-bottom-left-radius: calc((0.5rem * var(--responsive-70)) * (var(--rounded-bottom-left-8, 1)));
}
.rounded-bottom-left-12 {
  border-bottom-left-radius: calc((0.75rem * var(--responsive-70)) * (var(--rounded-bottom-left-12, 1)));
}
.rounded-bottom-left-16 {
  border-bottom-left-radius: calc((1rem * var(--responsive-60)) * (var(--rounded-bottom-left-16, 1)));
}
.rounded-bottom-left-24 {
  border-bottom-left-radius: calc((1.5rem * var(--responsive-70)) * (var(--rounded-bottom-left-24, 1)));
}

.rounded-full {
  border-radius: 9999px;
}

.s\:rounded-2 { border-radius: 0.125rem; }
.s\:rounded-4 { border-radius: 0.25rem; }
.s\:rounded-6 { border-radius: 0.375rem; }
.s\:rounded-8 { border-radius: 0.5rem; }
.s\:rounded-12 { border-radius: 0.75rem; }
.s\:rounded-16 { border-radius: 1rem; }
.s\:rounded-24 { border-radius: 1.5rem; }



/*--------------------------------------------------------------

## Border width

--------------------------------------------------------------*/

.border-0 {
  border-width: 0;
}

.border-1 {
  border-width: 1px;
}

.border-2 {
  border-width: 2px;
}

.border-3 {
  border-width: 3px;
}

.border-4 {
  border-width: 4px;
}

.border-6 {
  border-width: calc((6px * var(--responsive-70)) * (var(--border-6, 1)));
}

.border-8 {
  border-width: calc((8px * var(--responsive-60)) * (var(--border-8, 1)));
}

.border-top-1 {
  border-top-width: 1px;
}

.border-top-2 {
  border-top-width: 2px;
}

.border-top-3 {
  border-top-width: 3px;
}

.border-top-4 {
  border-top-width: 4px;
}

.border-top-6 {
  border-top-width: calc((6px * var(--responsive-70)) * (var(--border-top-6, 1)));
}

.border-top-8 {
  border-top-width: calc((8px * var(--responsive-60)) * (var(--border-top-8, 1)));
}

.border-left-1 {
  border-left-width: 1px;
}

.border-left-2 {
  border-left-width: 2px;
}

.border-left-3 {
  border-left-width: 3px;
}

.border-left-4 {
  border-left-width: 4px;
}

.border-left-6 {
  border-left-width: calc((6px * var(--responsive-70)) * (var(--border-left-6, 1)));
}

.border-left-8 {
  border-left-width: calc((8px * var(--responsive-60)) * (var(--border-left-8, 1)));
}

.border-right-1 {
  border-right-width: 1px;
}

.border-right-2 {
  border-right-width: 2px;
}

.border-right-3 {
  border-right-width: 3px;
}

.border-right-4 {
  border-right-width: 4px;
}

.border-right-6 {
  border-right-width: calc((6px * var(--responsive-70)) * (var(--border-right-6, 1)));
}

.border-right-8 {
  border-right-width: calc((8px * var(--responsive-60)) * (var(--border-right-8, 1)));
}

.border-bottom-1 {
  border-bottom-width: 1px;
}

.border-bottom-2 {
  border-bottom-width: 2px;
}

.border-bottom-3 {
  border-bottom-width: 3px;
}

.border-bottom-4 {
  border-bottom-width: 4px;
}

.border-bottom-6 {
  border-bottom-width: calc((6px * var(--responsive-70)) * (var(--border-bottom-6, 1)));
}

.border-bottom-8 {
  border-bottom-width: calc((8px * var(--responsive-60)) * (var(--border-bottom-8, 1)));
}


.border-y-1 {
  border-bottom-width: 1px;
  border-top-width: 1px;
}

.border-y-2 {
  border-bottom-width: 2px;
  border-top-width: 2px;
}

.border-y-3 {
  border-bottom-width: 3px;
  border-top-width: 3px;
}

.border-y-4 {
  border-bottom-width: 4px;
  border-top-width: 4px;
}

.border-y-6 {
  border-bottom-width: calc((6px * var(--responsive-70)) * (var(--border-y-6, 1)));
  border-top-width: calc((6px * var(--responsive-70)) * (var(--border-y-6, 1)));
}

.border-y-8 {
  border-bottom-width: calc((8px * var(--responsive-60)) * (var(--border-y-8, 1)));
  border-top-width: calc((8px * var(--responsive-60)) * (var(--border-y-8, 1)));
}


.border-x-1 {
  border-right-width: 1px;
  border-left-width: 1px;
}

.border-x-2 {
  border-right-width: 2px;
  border-left-width: 2px;
}

.border-x-3 {
  border-right-width: 3px;
  border-left-width: 3px;
}

.border-x-4 {
  border-right-width: 4px;
  border-left-width: 4px;
}

.border-x-6 {
  border-right-width: calc((6px * var(--responsive-70)) * (var(--border-x-6, 1)));
  border-left-width: calc((6px * var(--responsive-70)) * (var(--border-x-6, 1)));
}

.border-x-8 {
  border-bottom-width: calc((8px * var(--responsive-60)) * (var(--border-x-8, 1)));
  border-left-width: calc((8px * var(--responsive-60)) * (var(--border-x-8, 1)));
}


/* Border Width All Sides */
.s\:border-3 { border-width: 3px; }
.s\:border-4 { border-width: 4px; }
.s\:border-6 { border-width: 6px; }
.s\:border-8 { border-width: 8px; }

/* Border Top Width */
.s\:border-top-1 { border-top-width: 1px; }
.s\:border-top-2 { border-top-width: 2px; }
.s\:border-top-3 { border-top-width: 3px; }
.s\:border-top-4 { border-top-width: 4px; }
.s\:border-top-6 { border-top-width: 6px; }
.s\:border-top-8 { border-top-width: 8px; }

/* Border Left Width */
.s\:border-left-1 { border-left-width: 1px; }
.s\:border-left-2 { border-left-width: 2px; }
.s\:border-left-3 { border-left-width: 3px; }
.s\:border-left-4 { border-left-width: 4px; }
.s\:border-left-6 { border-left-width: 6px; }
.s\:border-left-8 { border-left-width: 8px; }

/* Border Right Width */
.s\:border-right-1 { border-right-width: 1px; }
.s\:border-right-2 { border-right-width: 2px; }
.s\:border-right-3 { border-right-width: 3px; }
.s\:border-right-4 { border-right-width: 4px; }
.s\:border-right-6 { border-right-width: 6px; }
.s\:border-right-8 { border-right-width: 8px; }

/* Border Bottom Width */
.s\:border-bottom-1 { border-bottom-width: 1px; }
.s\:border-bottom-2 { border-bottom-width: 2px; }
.s\:border-bottom-3 { border-bottom-width: 3px; }
.s\:border-bottom-4 { border-bottom-width: 4px; }
.s\:border-bottom-6 { border-bottom-width: 6px; }
.s\:border-bottom-8 { border-bottom-width: 8px; }

/* Border Bottom Width */
.s\:border-y-1 {
  border-bottom-width: 1px;
  border-top-width: 1px;
}

.s\:border-y-2 {
  border-bottom-width: 2px;
  border-top-width: 2px;
}

.s\:border-y-3 {
  border-bottom-width: 3px;
  border-top-width: 3px;
}

.s\:border-y-4 {
  border-bottom-width: 4px;
  border-top-width: 4px;
}

.s\:border-y-6 {
  border-bottom-width: 6px;
  border-top-width: 6px;
}

.s\:border-y-8 {
  border-bottom-width: 8px;
  border-top-width: 8px;
}


/* Border Bottom Width */
.s\:border-x-1 {
  border-right-width: 1px;
  border-left-width: 1px;
}

.s\:border-x-2 {
  border-right-width: 2px;
  border-left-width: 2px;
}

.s\:border-x-3 {
  border-right-width: 3px;
  border-left-width: 3px;
}

.s\:border-x-4 {
  border-right-width: 4px;
  border-left-width: 4px;
}

.s\:border-x-6 {
  border-right-width: 6px;
  border-left-width: 6px;
}

.s\:border-x-8 {
  border-right-width: 8px;
  border-left-width: 8px;
}


/*--------------------------------------------------------------

## Border style

--------------------------------------------------------------*/


.border-solid {
  border-style: solid
}

.border-dashed {
  border-style: dashed
}

.border-dotted {
  border-style: dotted
}

.border-double {
  border-style: double
}

.border-hidden {
  border-style: hidden
}

.border-none {
  border-style: none
}

/*--------------------------------------------------------------

## Border opacity

--------------------------------------------------------------*/

.border-opacity-5 {
  --border-opacity: 0.05;
}

.border-opacity-10 {
  --border-opacity: 0.1;
}

.border-opacity-15 {
  --border-opacity: 0.15;
}

.border-opacity-20 {
  --border-opacity: 0.2;
}

.border-opacity-25 {
  --border-opacity: 0.25;
}

.border-opacity-30 {
  --border-opacity: 0.3;
}

.border-opacity-35 {
  --border-opacity: 0.35;
}

.border-opacity-40 {
  --border-opacity: 0.4;
}

.border-opacity-45 {
  --border-opacity: 0.45;
}

.border-opacity-50 {
  --border-opacity: 0.5;
}

.border-opacity-55 {
  --border-opacity: 0.55;
}

.border-opacity-60 {
  --border-opacity: 0.6;
}

.border-opacity-65 {
  --border-opacity: 0.65;
}

.border-opacity-70 {
  --border-opacity: 0.7;
}

.border-opacity-75 {
  --border-opacity: 0.75;
}

.border-opacity-80 {
  --border-opacity: 0.8;
}

.border-opacity-85 {
  --border-opacity: 0.85;
}

.border-opacity-90 {
  --border-opacity: 0.9;
}

.border-opacity-95 {
  --border-opacity: 0.95;
}

.border-opacity-100 {
  --border-opacity: 1;
}


/*--------------------------------------------------------------

## Border color

--------------------------------------------------------------*/

.border-transparent {
  border-color: transparent;
}

.border-white {
  border-color: rgba(255, 255, 255, var(--border-opacity, 1));
}

.border-black {
  border-color: rgba(0, 0, 0, var(--border-opacity, 1));
}

.border-gray {
  border-color: rgba(228, 235, 240, var(--border-opacity, 1));
}

.border-dark-gray {
  border-color: rgba(121, 139, 151, var(--border-opacity, 1));
}

.border-red {
  border-color: rgba(239, 68, 68, var(--border-opacity, 1));
}

.border-green {
  border-color: rgba(74, 222, 128, var(--border-opacity, 1));
}

.border-yellow {
  border-color: rgba(253, 224, 71, var(--border-opacity, 1));
}

.border-teal {
  border-color: rgba(94, 234, 212, var(--border-opacity, 1));
}

.border-cyan {
  border-color: rgba(103, 232, 249, var(--border-opacity, 1));
}

.border-sky {
  border-color: rgba(125, 211, 252, var(--border-opacity, 1));
}

.border-indigo {
  border-color: rgba(129, 140, 248, var(--border-opacity, 1));
}

.border-violet {
  border-color: rgba(167, 139, 250, var(--border-opacity, 1));
}

.border-pink {
  border-color: rgba(249, 168, 212, var(--border-opacity, 1));
}

.border-orange {
  border-color: rgba(251, 146, 60, var(--border-opacity, 1));
}

.border-blue {
  border-color: rgba(59, 130, 246, var(--border-opacity, 1));
}

.border-purple {
  border-color: rgba(192, 132, 252, var(--border-opacity, 1));
}

.border-fuchsia {
  border-color: rgba(244, 114, 182, var(--border-opacity, 1));
}

.border-rose {
  border-color: rgba(251, 113, 133, var(--border-opacity, 1));
}

.border-amber {
  border-color: rgba(245, 158, 11, var(--border-opacity, 1));
}

.border-lime {
  border-color: rgba(163, 230, 53, var(--border-opacity, 1));
}

.border-emerald {
  border-color: rgba(16, 185, 129, var(--border-opacity, 1));
}

.border-brown {
  border-color: rgba(121, 85, 72, var(--border-opacity, 1));
}

.border-slate {
  border-color: rgba(100, 116, 139, var(--border-opacity, 1));
}

.border-zinc {
  border-color: rgba(113, 113, 122, var(--border-opacity, 1));
}

.border-stone {
  border-color: rgba(120, 113, 108, var(--border-opacity, 1));
}



/*--------------------------------------------------------------

## Divide width

--------------------------------------------------------------*/


.divide-x-1 > :not([hidden]) ~ :not([hidden]) {
    border-right-width: 0px;
    border-left-width: 1px;
}


.divide-x-2 > :not([hidden]) ~ :not([hidden]) {
    border-right-width: 0px;
    border-left-width: 2px;
}


.divide-x-3 > :not([hidden]) ~ :not([hidden]) {
    border-right-width: 0px;
    border-left-width: 3px;
}


.divide-x-4 > :not([hidden]) ~ :not([hidden]) {
    border-right-width: 0px;
    border-left-width: 4px;
}

.divide-y-1 >:not([hidden])~:not([hidden]) {
    border-top-width: 2px;
    border-bottom-width: 0px;
}

.divide-y-2 >:not([hidden])~:not([hidden]) {
    border-top-width: 2px;
    border-bottom-width: 0px;
}

.divide-y-3 >:not([hidden])~:not([hidden]) {
    border-top-width: 3px;
    border-bottom-width: 0px;
}

.divide-y-4 >:not([hidden])~:not([hidden]) {
    border-top-width: 4px;
    border-bottom-width: 0px;
}


/*--------------------------------------------------------------

## Divide color

--------------------------------------------------------------*/


.divide-gray >:not([hidden])~:not([hidden]) {
    border-color: rgba(228, 235, 240, var(--divide-opacity, 1));
}

.divide-white >:not([hidden])~:not([hidden]) {
  border-color: rgba(255, 255, 255, var(--divide-opacity, 1));
}


/*--------------------------------------------------------------

## Divide style

--------------------------------------------------------------*/

.divide-solid >:not([hidden])~:not([hidden]) {
    border-style:solid;
}

/*--------------------------------------------------------------

## Divide opacity

--------------------------------------------------------------*/


.divide-opacity-10 >:not([hidden])~:not([hidden]) {
  --divide-opacity:0.1;
}

.divide-opacity-20 >:not([hidden])~:not([hidden]) {
  --divide-opacity:0.2;
}

.divide-opacity-30 >:not([hidden])~:not([hidden]) {
  --divide-opacity:0.3;
}

.divide-opacity-40 >:not([hidden])~:not([hidden]) {
  --divide-opacity:0.4;
}

.divide-opacity-50 >:not([hidden])~:not([hidden]) {
  --divide-opacity:0.5;
}

.divide-opacity-60 >:not([hidden])~:not([hidden]) {
  --divide-opacity:0.6;
}

.divide-opacity-70 >:not([hidden])~:not([hidden]) {
  --divide-opacity:0.7;
}

.divide-opacity-80 >:not([hidden])~:not([hidden]) {
  --divide-opacity:0.8;
}

.divide-opacity-90 >:not([hidden])~:not([hidden]) {
  --divide-opacity:0.9;
}

.divide-opacity-100 >:not([hidden])~:not([hidden]) {
  --divide-opacity:1;
}


/*--------------------------------------------------------------

## Image

--------------------------------------------------------------*/

.img-cover {
  object-fit:cover;
  inset:0;
  height:100%;
  width:100%;
  position:absolute;
}

.img-contain {
  object-fit:contain;
  inset:0;
  height:100%;
  width:100%;
  position:absolute;
}


/*--------------------------------------------------------------

## Object

--------------------------------------------------------------*/

.object-cover {
  object-fit: cover;
}

.object-contain {
  object-fit: contain;
}

.object-fill {
  object-fit: fill;
}

.object-none {
  object-fit: none;
}

.object-scale {
  object-fit: scale-down;
}

.object-center {
  object-position: center;
}

.object-left {
  object-position: left;
}

.object-right {
  object-position: right;
}

.object-top {
  object-position: top;
}

.object-bottom {
  object-position: bottom;
}

.object-left-center {
  object-position: left center;
}

.object-right-center {
  object-position: right center;
}

.object-left-bottom {
  object-position: left bottom;
}

.object-right-bottom {
  object-position: right bottom;
}

.object-left-top {
  object-position: left top;
}

.object-right-top {
  object-position: right top;
}

.object-left-bottom {
  object-position: left bottom;
}

.object-right-bottom {
  object-position: right bottom;
}

/*--------------------------------------------------------------

## Background position

--------------------------------------------------------------*/

.bg-left {
  background-position: left;
}

.bg-right {
  background-position: right;
}

.bg-center {
  background-position: center;
}

.bg-top {
  background-position: top;
}

.bg-bottom {
  background-position: bottom;
}

.bg-left-top {
  background-position: left top;
}

.bg-right-top {
  background-position: right top;
}

.bg-left-bottom {
  background-position: left bottom;
}

.bg-right-bottom {
  background-position: right bottom;
}

/*--------------------------------------------------------------

## Background repeat

--------------------------------------------------------------*/

.bg-repeat {
  background-repeat: repeat;
}

.bg-norepeat {
  background-repeat: no-repeat;
}

.bg-repeat-x {
  background-repeat: repeat-x;
}

.bg-repeat-y {
  background-repeat: repeat-y;
}

.bg-repeat-space {
  background-repeat: space;
}

.bg-repeat-round {
  background-repeat: round;
}

/*--------------------------------------------------------------

## Background size

--------------------------------------------------------------*/

.bg-cover {
  background-size: cover;
}

.bg-contain {
  background-size: contain;
}

.bg-auto {
  background-size: auto;
}

/*--------------------------------------------------------------

## Background attachment

--------------------------------------------------------------*/

.bg-fixed {
  background-attachment: fixed;
}

/*--------------------------------------------------------------

## Background clip

--------------------------------------------------------------*/

.bg-clip-border {
  background-clip: border-box;
}

.bg-clip-content {
  background-clip: content-box;
}

.bg-clip-padding {
  background-clip: padding-box;
}

.bg-clip-text {
  background-clip: text;
}

/*--------------------------------------------------------------

## Background color

--------------------------------------------------------------*/

.bg-opacity-0 {
  --background-opacity: 0;
}

.bg-opacity-10 {
  --background-opacity: 0.1;
}

.bg-opacity-15 {
  --background-opacity: 0.15;
}

.bg-opacity-20 {
  --background-opacity: 0.2;
}

.bg-opacity-25 {
  --background-opacity: 0.15;
}

.bg-opacity-30 {
  --background-opacity: 0.3;
}

.bg-opacity-35 {
  --background-opacity: 0.35;
}

.bg-opacity-40 {
  --background-opacity: 0.4;
}

.bg-opacity-45 {
  --background-opacity: 0.45;
}

.bg-opacity-50 {
  --background-opacity: 0.5;
}

.bg-opacity-55 {
  --background-opacity: 0.55;
}

.bg-opacity-60 {
  --background-opacity: 0.6;
}

.bg-opacity-65 {
  --background-opacity: 0.65;
}

.bg-opacity-70 {
  --background-opacity: 0.7;
}

.bg-opacity-75 {
  --background-opacity: 0.75;
}

.bg-opacity-80 {
  --background-opacity: 0.8;
}

.bg-opacity-85 {
  --background-opacity: 0.85;
}

.bg-opacity-90 {
  --background-opacity: 0.9;
}

.bg-opacity-95 {
  --background-opacity: 0.95;
}

.bg-opacity-1 {
  --background-opacity: 0.1;
}

.bg-transparent {
  background-color: transparent;
}

.bg-white { 
  background-color: rgba(255, 255, 255, var(--background-opacity, 1));
}

.bg-black {
  background-color: rgba(0, 0, 0, var(--background-opacity, 1));
}

.bg-light-gray {
  background-color: rgba(241, 245, 246, var(--background-opacity, 1));
  background-color: rgba(246, 248, 252, var(--background-opacity, 1));
  background-color: rgba(243, 247, 251, var(--background-opacity, 1));
}


.bg-gray {
  background-color: rgba(229, 231, 235, var(--background-opacity, 1));
  background-color: rgba(228, 235, 240, var(--background-opacity, 1));
}

.bg-dark-gray {
  background-color: rgba(48, 56, 66, var(--background-opacity, 1));
}

.bg-dark {
  background-color: rgba(40, 60, 80, var(--background-opacity, 1));
}

.bg-red {
  background-color: rgba(239, 68, 68, var(--background-opacity, 1));
}

.bg-green {
  background-color: rgba(47, 176, 35, var(--background-opacity, 1));
}

.bg-yellow {
  background-color: rgba(253, 224, 71, var(--background-opacity, 1));
}

.bg-teal {
  background-color: rgba(94, 234, 212, var(--background-opacity, 1));
}

.bg-cyan {
  background-color: rgba(103, 232, 249, var(--background-opacity, 1));
}

.bg-sky {
  background-color: rgba(125, 211, 252, var(--background-opacity, 1));
}

.bg-cyan {
  background-color: rgba(103, 232, 249, var(--background-opacity, 1));
}

.bg-indigo {
  background-color: rgba(129, 140, 248, var(--background-opacity, 1));
}

.bg-violet {
  background-color: rgba(167, 139, 250, var(--background-opacity, 1));
}

.bg-pink {
  background-color: rgba(249, 168, 212, var(--background-opacity, 1));
}

.bg-orange {
  background-color: rgba(251, 146, 60, var(--background-opacity, 1));
}

.bg-blue {
  background-color: rgba(59, 130, 246, var(--background-opacity, 1));
}

.bg-purple {
  background-color: rgba(192, 132, 252, var(--background-opacity, 1));
}

.bg-fuchsia {
  background-color: rgba(244, 114, 182, var(--background-opacity, 1));
}

.bg-rose {
  background-color: rgba(251, 113, 133, var(--background-opacity, 1));
}

.bg-amber {
  background-color: rgba(245, 158, 11, var(--background-opacity, 1));
}

.bg-lime {
  background-color: rgba(163, 230, 53, var(--background-opacity, 1));
}

.bg-emerald {
  background-color: rgba(16, 185, 129, var(--background-opacity, 1));
}

.bg-brown {
  background-color: rgba(121, 85, 72, var(--background-opacity, 1));
}

.bg-slate {
  background-color: rgba(100, 116, 139, var(--background-opacity, 1));
}

.bg-zinc {
  background-color: rgba(113, 113, 122, var(--background-opacity, 1));
}

.bg-stone {
  background-color: rgba(120, 113, 108, var(--background-opacity, 1));
}

/*--------------------------------------------------------------

## Cursor

--------------------------------------------------------------*/

.cursor-pointer {
  cursor: pointer;
}

.pointer-none {
  pointer-events: none;
}

/*--------------------------------------------------------------

## Overflow

--------------------------------------------------------------*/

.overflow-auto {
  overflow: auto;
}

.overflow-hidden {
  overflow: hidden;
}

.overflow-x-hidden {
  overflow-x: hidden;
}

.overflow-y-hidden {
  overflow-y: hidden;
}

.overflow-visible {
  overflow: visible;
}

.overflow-scroll {
  overflow: scroll;
}

.overflow-x-scroll {
  overflow-x: scroll;
}

.overflow-y-scroll {
  overflow-y: scroll;
}

/*--------------------------------------------------------------

## Index

--------------------------------------------------------------*/

.z--30 {
  z-index: -30;
}

.z--20 {
  z-index: -20;
}

.z--10 {
  z-index: -10;
}

.z-0 {
  z-index: 0;
}

.z-10 {
  z-index: 10;
}

.z-20 {
  z-index: 20;
}

.z-30 {
  z-index: 30;
}

.z-40 {
  z-index: 40;
}

.z-50 {
  z-index: 50;
}

.z-60 {
  z-index: 60;
}

.z-70 {
  z-index: 70;
}

.z-80 {
  z-index: 80;
}

.z-90 {
  z-index: 90;
}

.z-100 {
  z-index: 100;
}

.z-200 {
  z-index: 200;
}

/*--------------------------------------------------------------

## Shadow

--------------------------------------------------------------*/

.shadow-opacity-1 {
  --shadow-opacity: 1;
}

.shadow-opacity-5 {
  --shadow-opacity: 0.05;
}

.shadow-opacity-10 {
  --shadow-opacity: 0.1;
}

.shadow-opacity-15 {
  --shadow-opacity: 0.15;
}

.shadow-opacity-20 {
  --shadow-opacity: 0.2;
}

.shadow-opacity-25 {
  --shadow-opacity: 0.25;
}

.shadow-opacity-30 {
  --shadow-opacity: 0.3;
}

.shadow-opacity-35 {
  --shadow-opacity: 0.35;
}

.shadow-opacity-40 {
  --shadow-opacity: 0.4;
}

.shadow-opacity-50 {
  --shadow-opacity: 0.5;
}

.shadow-opacity-55 {
  --shadow-opacity: 0.55;
}

.shadow-opacity-60 {
  --shadow-opacity: 0.6;
}

.shadow-opacity-65 {
  --shadow-opacity: 0.65;
}

.shadow-opacity-70 {
  --shadow-opacity: 0.7;
}

.shadow-opacity-75 {
  --shadow-opacity: 0.75;
}

.shadow-opacity-80 {
  --shadow-opacity: 0.8;
}

.shadow-opacity-85 {
  --shadow-opacity: 0.85;
}

.shadow-opacity-90 {
  --shadow-opacity: 0.9;
}

.shadow-opacity-95 {
  --shadow-opacity: 0.95;
}


.shadow-8 {
  box-shadow: 0px 2px 8px rgba(40, 60, 80, var(--shadow-opacity, 0.15));
}

.shadow-12 {
  box-shadow: 0px 5px 12px rgba(40, 60, 80, var(--shadow-opacity, 0.15));
}

.shadow-24 {
  box-shadow: 0px 8px 24px rgba(40, 60, 80, var(--shadow-opacity, 0.15));
}

.shadow-48 {
  box-shadow: 2px 12px 48px rgba(40, 60, 80, var(--shadow-opacity, 0.15));
}

.shadow-72 {
  box-shadow: 4px 16px 72px rgba(40, 60, 80, var(--shadow-opacity, 0.15));
}

.shadow-98 {
  box-shadow: 6px 20px 98px rgba(40, 60, 80, var(--shadow-opacity, 0.15));
}

.drop-shadow-12 {
  filter: drop-shadow(0px 5px 12px rgba(40, 60, 80, var(--shadow-opacity, 0.15)));
}

.drop-shadow-24 {
  filter: drop-shadow(0px 8px 24px rgba(40, 60, 80, var(--shadow-opacity, 0.15)));
}

.drop-shadow-48 {
  filter: drop-shadow(2px 12px 48px rgba(40, 60, 80, var(--shadow-opacity, 0.15)));
}

.drop-shadow-72 {
  filter: drop-shadow(4px 16px 72px rgba(40, 60, 80, var(--shadow-opacity, 0.15)));
}

.drop-shadow-98 {
  filter: drop-shadow(6px 20px 98px rgba(40, 60, 80, var(--shadow-opacity, 0.15)));
}

/*--------------------------------------------------------------

## Filter

--------------------------------------------------------------*/

.blur-4 {
  filter: blur(4px);
}

.blur-8 {
  filter: blur(8px);
}

.blur-12 {
  filter: blur(12px);
}

.blur-16 {
  filter: blur(16px);
}

.blur-24 {
  filter: blur(24px);
}

.blur-40 {
  filter: blur(40px);
}

.grayscale {
  filter: grayscale(100%);
}

/*--------------------------------------------------------------

## Backdrop

--------------------------------------------------------------*/

.bg-blur-4 {
  backdrop-filter: blur(4px);
}

.bg-blur-8 {
  backdrop-filter: blur(8px);
}

.bg-blur-12 {
  backdrop-filter: saturate(100%) blur(12px);
}

.bg-blur-16 {
  backdrop-filter: blur(16px);
}

.bg-blur-24 {
  backdrop-filter: blur(24px);
}

.bg-blur-40 {
  backdrop-filter: blur(40px);
}

.bg-grayscale {
  backdrop-filter: grayscale(100%);
}


/*--------------------------------------------------------------

## Overflow scroll

--------------------------------------------------------------*/

.overflow-contain {
  overscroll-behavior:contain;
}



/*--------------------------------------------------------------

## Reflect

--------------------------------------------------------------*/

.reflect-x {
  transform: scaleX(-1);
}

.reflect-y {
  transform: scaleY(-1);
}


/*--------------------------------------------------------------

## Translate

--------------------------------------------------------------*/

.translate-y--50-100 {
  transform:translateY(-50%);
}


.translate-y-50-100 {
  transform:translateY(50%);
}


/*--------------------------------------------------------------

## Transition

--------------------------------------------------------------*/

.transition-none {
  transition-property: none;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}

.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 250ms;
}


.ease-linear {
  transition-timing-function: linear;
}

.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0.0, 0, 0.2, 1);
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

.duration-75 {
  transition-duration: 75ms;
}

.duration-100 {
  transition-duration: 100ms;
}

.duration-150 {
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-500 {
  transition-duration: 500ms;
}



.delay-75 {
  transition-delay: 75ms;
}

.delay-100 {
  transition-delay: 100ms;
}

.delay-150 {
  transition-delay: 150ms;
}

.delay-200 {
  transition-delay: 200ms;
}

.delay-300 {
  transition-delay: 300ms;
}

.delay-500 {
  transition-delay: 500ms;
}




/*--------------------------------------------------------------

## Appearance

--------------------------------------------------------------*/

.appearance-none {
  appearance: none;
}

.appearance-auto {
  appearance: auto;
}




/*--------------------------------------------------------------

## Animation duration

--------------------------------------------------------------*/


.animation-duration-1000 {
  animation-duration: 1000ms;
}

.animation-duration-1500 {
  animation-duration: 1500ms;
}

.animation-duration-2000 {
  animation-duration: 2000ms;
}

.animation-duration-3000 {
  animation-duration: 3000ms;
}

.animation-duration-4000 {
  animation-duration: 4000ms;
}

.animation-duration-5000 {
  animation-duration: 5000ms;
}


.animation-duration-6000 {
  animation-duration: 6000ms;
}

.animation-duration-7000 {
  animation-duration: 7000ms;
}


.animation-duration-8000 {
  animation-duration: 8000ms;
}

.animation-duration-9000 {
  animation-duration: 9000ms;
}






.animation-duration-10000 {
  animation-duration: 10000ms;
}

.animation-duration-15000 {
  animation-duration: 15000ms;
}



/*--------------------------------------------------------------

## Aspect ratio

--------------------------------------------------------------*/


.ratio-1-1 {
  aspect-ratio: 1 / 1;
}

.ratio-3-2 {
  aspect-ratio: 3 / 2;
}



.ratio-2-3 {
  aspect-ratio: 2 / 3;
}

.ratio-4-3 {
  aspect-ratio: 4 / 3;
}


.ratio-3-4 {
  aspect-ratio: 3 / 4;
}


.ratio-4-5 {
  aspect-ratio: 4 / 5;
}

.ratio-16-9 {
  aspect-ratio: 16 / 9;
}

.ratio-21-9 {
  aspect-ratio: 21 / 9;
}

.ratio-2-1 {
  aspect-ratio: 2 / 1;
}

.ratio-3-1 {
  aspect-ratio: 3 / 1;
}

.ratio-4-1 {
  aspect-ratio: 4 / 1;
}

.ratio-9-16 {
  aspect-ratio: 9 / 16;
}

.ratio-9-21 {
  aspect-ratio: 9 / 21;
}



/*--------------------------------------------------------------

## Transition

--------------------------------------------------------------*/


/*--------------------------------------------------------------

## Transition

--------------------------------------------------------------*/

.transition-none {
  transition-property: none;
}

.transition-all {
  transition-property: all;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke, opacity, box-shadow, transform, filter, backdrop-filter;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-colors {
  transition-property: color, background-color, border-color, text-decoration-color, fill, stroke;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-opacity {
  transition-property: opacity;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-shadow {
  transition-property: box-shadow;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

.transition-transform {
  transition-property: transform;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
  transition-duration: 150ms;
}

/* Duration */
.duration-75 {
  transition-duration: 75ms;
}

.duration-100 {
  transition-duration: 100ms;
}

.duration-150 {
  transition-duration: 150ms;
}

.duration-200 {
  transition-duration: 200ms;
}

.duration-300 {
  transition-duration: 300ms;
}

.duration-500 {
  transition-duration: 500ms;
}

.duration-700 {
  transition-duration: 700ms;
}

.duration-1000 {
  transition-duration: 1000ms;
}

/* Timing Functions */
.ease-linear {
  transition-timing-function: linear;
}

.ease-in {
  transition-timing-function: cubic-bezier(0.4, 0, 1, 1);
}

.ease-out {
  transition-timing-function: cubic-bezier(0, 0, 0.2, 1);
}

.ease-in-out {
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}

/* Delay */
.delay-75 {
  transition-delay: 75ms;
}

.delay-100 {
  transition-delay: 100ms;
}

.delay-150 {
  transition-delay: 150ms;
}

.delay-200 {
  transition-delay: 200ms;
}

.delay-300 {
  transition-delay: 300ms;
}

.delay-500 {
  transition-delay: 500ms;
}

.delay-700 {
  transition-delay: 700ms;
}

.delay-1000 {
  transition-delay: 1000ms;
}

