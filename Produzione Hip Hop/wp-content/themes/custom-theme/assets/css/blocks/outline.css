/*
  Stili per l'animazione dell'accordion gestita via jQuery.
*/

/* Nasconde di default il corpo della riga. jQuery gestirà la visibilità. */
.block-outline .row .body {
  display: none;
}

/* Aggiunge una transizione fluida per la rotazione dell'icona toggle */
.block-outline .row .toggle {
  transition: transform 0.3s ease-in-out;
}

/* Quando l'icona ha la classe .active (aggiunta da jQuery), ruota di 45 gradi. */
.block-outline .row .toggle.active {
  transform: rotate(45deg);
} 