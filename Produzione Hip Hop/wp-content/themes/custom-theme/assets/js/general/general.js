
/*--------------------------------------------------------------
## Auto - Lazy Loading Optimization
--------------------------------------------------------------*/

// Function to handle lazy loading
function lazyLoad(element) {
  var src = element.attr("data-src");
  var srcset = element.attr("data-srcset");
  element.attr("src", src);
  element.attr("srcset", srcset);
} 

// Function to check if an element is within 500px of the viewport
function isNearViewport(element) {
  var windowHeight = jQuery(window).height();
  var windowScroll = jQuery(window).scrollTop();
  var elementTop = element.offset().top;
  var threshold = 500;

  return elementTop <= windowScroll + windowHeight + threshold;
}

// Function to handle lazy loading for elements with the ".lazy" class
function lazyLoadElements() {
  jQuery(".lazy").each(function() {
    var element = jQuery(this);
    if (isNearViewport(element)) {
      lazyLoad(element);
      element.removeClass("lazy");
    }
  });
}

// Debounce function to limit the execution frequency
function debounce(func, wait) {
  var timeout;
  return function() {
    var context = this;
    var args = arguments;
    var later = function() {
      timeout = null;
      func.apply(context, args);
    };
    clearTimeout(timeout);
    timeout = setTimeout(later, wait);
  };
}

// Trigger lazy loading on scroll and resize events with debounce
var lazyLoadDebounced = debounce(lazyLoadElements, 200);
jQuery(window).on("scroll resize", lazyLoadDebounced);

// Initial lazy loading
lazyLoadElements();





/*--------------------------------------------------------------

## Scrollmonitor

--------------------------------------------------------------*/


// Common function to create scroll monitor and handle viewport entry
function createScrollMonitor(selector) {
  var element = jQuery(selector);
  if (element.length) {
    var monitor = scrollMonitor.create(element, 500);
    monitor.enterViewport(function() {
      lazyLoad(selector);
      monitor.destroy();
    });
  }
}

/*--------------------------------------------------------------

## Viewport

--------------------------------------------------------------*/



jQuery.fn.isInViewport = function() {

 var elementTop = jQuery(this).offset().top;

  var elementBottom = elementTop + jQuery(this).outerHeight();

  var viewportTop = jQuery(window).scrollTop();

  var viewportBottom = viewportTop + jQuery(window).height();

  return elementBottom > viewportTop && elementTop < viewportBottom;

};





















/*--------------------------------------------------------------

## Homepage - Social

--------------------------------------------------------------*/
 
// Gestione al caricamento del DOM
document.addEventListener('DOMContentLoaded', () => {
const socialSection = document.querySelector('.home-social');
if (!socialSection) {
    return;
}
const socialButtonsContainer = socialSection.querySelector('.cta .items');
if (!socialButtonsContainer) {
    return;
}

const buttons = Array.from(socialButtonsContainer.querySelectorAll('a.button'));
if (buttons.length === 0) {
    return;
}

let currentIndex = -1; 
let animationTimeoutId = null;
const hoverClassName = 'js-manual-hover';
const animationInterval = 2000; // 2 seconds
let isUserHovering = false;
let lastManuallyAnimatedIndex = -1;

function animateNextButton() {
    if (isUserHovering) return;

    if (lastManuallyAnimatedIndex !== -1 && buttons[lastManuallyAnimatedIndex]) {
        buttons[lastManuallyAnimatedIndex].classList.remove(hoverClassName);
    }

    currentIndex = (currentIndex + 1) % buttons.length;
    
    if (buttons[currentIndex]) {
        buttons[currentIndex].classList.add(hoverClassName);
        lastManuallyAnimatedIndex = currentIndex;
    }

    animationTimeoutId = setTimeout(animateNextButton, animationInterval);
}

function pauseAnimation() {
    isUserHovering = true;
    clearTimeout(animationTimeoutId);
    if (lastManuallyAnimatedIndex !== -1 && buttons[lastManuallyAnimatedIndex]) {
        buttons[lastManuallyAnimatedIndex].classList.remove(hoverClassName);
    }
}

function resumeAnimation(resumeFromIndex) {
    isUserHovering = false;
    clearTimeout(animationTimeoutId); 

    buttons.forEach(btn => btn.classList.remove(hoverClassName));
    
    currentIndex = resumeFromIndex; 
    
    if (buttons[currentIndex]) {
        buttons[currentIndex].classList.add(hoverClassName);
        lastManuallyAnimatedIndex = currentIndex;
    }
    
    animationTimeoutId = setTimeout(animateNextButton, animationInterval);
}

buttons.forEach((button, index) => {
    button.addEventListener('mouseenter', () => {
        pauseAnimation();
    });

    button.addEventListener('mouseleave', () => {
        resumeAnimation(index);
    });
});

if (buttons.length > 0) {
    resumeAnimation(0); 
}

});









