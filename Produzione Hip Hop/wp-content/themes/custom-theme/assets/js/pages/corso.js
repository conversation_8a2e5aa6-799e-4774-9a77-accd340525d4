document.addEventListener('DOMContentLoaded', function () {
    const availabilityContainer = document.querySelector('.availability');
    if (availabilityContainer) {
        const items = availabilityContainer.querySelectorAll('.item');
        if (items.length > 1) {
            const secondItem = items[1];
            const svg = secondItem.querySelector('svg');
            const circle = svg.querySelector('circle');
            const path = svg.querySelector('path');

            setInterval(() => {
                // Toggle item classes for background and opacity
                secondItem.classList.toggle('bg-brand-red');
                secondItem.classList.toggle('bg-brand-red-medium');
                secondItem.classList.toggle('opacity-40');

                // Toggle SVG stroke color
                if (circle.getAttribute('stroke') === 'white') {
                    circle.setAttribute('stroke', '#FF0000');
                    path.setAttribute('stroke', '#FF0000');
                } else {
                    circle.setAttribute('stroke', 'white');
                    path.setAttribute('stroke', 'white');
                }
            }, 1000);
        }
    }
});



const reviewsSlider = document.querySelector('.reviews');
    if (reviewsSlider) {
        new Swiper(reviewsSlider, {
            loop: true,
            autoplay: {
                delay: 5000,
            },
            slidesPerView: 3,
            spaceBetween: 80,
        });
    }

