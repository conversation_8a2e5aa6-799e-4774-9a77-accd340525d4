/*--------------------------------------------------------------

## Scuola - 

--------------------------------------------------------------*/
document.addEventListener('DOMContentLoaded', function() {


  const countUpElement = document.getElementById('count-up-stats');

  if (countUpElement) {
    const finalValueString = countUpElement.textContent.trim();
    const finalValue = parseInt(finalValueString.replace(/\./g, ''), 10);
    
    // Set initial value
    countUpElement.textContent = '4.000.000';

    const countUpObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          animateCountUp(countUpElement, finalValue);
          observer.unobserve(entry.target);
        }
      });
    }, {
      threshold: 0.1 // Start when 10% of the element is visible
    });

    countUpObserver.observe(countUpElement);
  }

  function animateCountUp(element, endValue) {
    const duration = 3000; // 4 seconds
    const startValue = 4000000;
    let startTime = null;

    // Ease-out function: starts fast, slows down at the end.
    function easeOutQuart(t) {
        return 1 - (--t) * t * t * t;
    }

    function step(timestamp) {
      if (!startTime) startTime = timestamp;
      const progress = timestamp - startTime;
      const progressRatio = Math.min(progress / duration, 1);
      const easedProgress = easeOutQuart(progressRatio);
      const currentValue = Math.floor(easedProgress * (endValue - startValue) + startValue);

      // Format number with dots as thousand separators. 'de-DE' uses dots.
      element.textContent = currentValue.toLocaleString('de-DE');

      if (progressRatio < 1) {
        requestAnimationFrame(step);
      } else {
        element.textContent = endValue.toLocaleString('de-DE');
      }
    }
    
    requestAnimationFrame(step);
  }


/*--------------------------------------------------------------

## Scuola - Mission - Swiper

--------------------------------------------------------------*/
  const reviewsSwiper = new Swiper('.reviews-swiper', {
    slidesPerView: "auto",
    centeredSlides: true,
    spaceBetween: 20,
    loop: true,
    autoplay: {
      delay: 4000,
    }
  });

  if (reviewsSwiper) {
    reviewsSwiper.init();
  }







});





















































