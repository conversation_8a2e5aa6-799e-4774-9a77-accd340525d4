/*--------------------------------------------------------------

## Homepage - Objectives
 
--------------------------------------------------------------*/
  
const calculateColumnHeight = () => {
  let totalHeight = 0;
  jQuery(".home-obj .item").each(function() {
    totalHeight += jQuery(this).outerHeight();
  });
  return totalHeight / 2;
};

const defineSlideUpAnimation = (height) => {
  jQuery.keyframe.define([{
    name: 'slide-up-obj',
    '0%': {
      'transform': 'translate3d(0, 0, 0)'
    },
    '100%': {
      'transform': `translate3d(0, -${height}px, 0)`
    }
  }]);
};

const columnHeight = calculateColumnHeight();
defineSlideUpAnimation(columnHeight);







/*--------------------------------------------------------------

## Homepage - Equalizer

--------------------------------------------------------------*/
// Configurazione globale di base per tutti gli equalizzatori
const globalEqConfig = {
  "dotSize": 3,         // Questi valori provengono dalla tua config, ma potrebbero non essere usati
  "dotSpacing": 6,      // direttamente se gli stili CSS sono fissi come hai mostrato.
  "defaultDotColor": "#d9d9d9", // Comunque, li teniamo per la logica interna.
  "highlightDotColor": "#194DD4",
  "numColumns": 30,
  "dotsPerColumn": 20,
  "animationSpeed": 80,
  "smoothingFactor": 0.2,
  "decayFactor": 0.96,
  "gravity": 0,
  "targetUpdateInterval": 400
};

// Oggetto per memorizzare i dati di ogni istanza di equalizzatore
const equalizerInstances = {};

/**
* Inizializza o reinizializza lo stato logico delle colonne per una specifica istanza.
* @param {object} instance - L'oggetto istanza dell'equalizzatore.
*/
function initializeInstanceColumnStates(instance) {
  instance.columnStates = [];
  for (let i = 0; i < instance.config.numColumns; i++) {
      instance.columnStates.push({
          currentHeight: 0,
          targetHeight: Math.random() * instance.config.dotsPerColumn,
          timeToNextTarget: Math.random() * instance.config.targetUpdateInterval
      });
  }
}

/**
* Crea la struttura HTML (colonne e pallini) per una specifica istanza.
* @param {object} instance - L'oggetto istanza dell'equalizzatore.
*/
function createInstanceEqualizerStructure(instance) {
  if (!instance.containerElement) {
      console.error(`Elemento contenitore non trovato per l'istanza: ${instance.id}`);
      return;
  }
  instance.containerElement.innerHTML = ''; // Pulisce contenuto precedente
  for (let i = 0; i < instance.config.numColumns; i++) {
      const column = document.createElement('div');
      column.classList.add(instance.columnClassName); // Es: 'equalizer-column-1'
      for (let j = 0; j < instance.config.dotsPerColumn; j++) {
          const dot = document.createElement('div');
          dot.classList.add(instance.dotClassName); // Es: 'equalizer-dot-1'
          column.appendChild(dot);
      }
      instance.containerElement.appendChild(column);
  }
}

/**
* Aggiorna l'aspetto visivo per una specifica istanza.
* @param {object} instance - L'oggetto istanza dell'equalizzatore.
*/
function updateInstanceEqualizerEffect(instance) {
  if (!instance.containerElement || !instance.columnStates) return;
  const timeElapsed = instance.config.animationSpeed;

  instance.columnStates.forEach((colState, colIndex) => {
      colState.timeToNextTarget -= timeElapsed;
      if (colState.timeToNextTarget <= 0) {
          colState.targetHeight = Math.random() * instance.config.dotsPerColumn;
          colState.timeToNextTarget = instance.config.targetUpdateInterval + (Math.random() * 100 - 50);
      }
      const difference = colState.targetHeight - colState.currentHeight;
      colState.currentHeight += difference * instance.config.smoothingFactor;
      if (instance.config.decayFactor > 0 && instance.config.decayFactor < 1) {
          colState.currentHeight *= instance.config.decayFactor;
      }
      if (instance.config.gravity > 0) {
          colState.currentHeight -= instance.config.gravity;
      }
      colState.currentHeight = Math.max(0, Math.min(colState.currentHeight, instance.config.dotsPerColumn));
      
      const columnElements = instance.containerElement.getElementsByClassName(instance.columnClassName);
      if (columnElements[colIndex]) {
          const dots = columnElements[colIndex].getElementsByClassName(instance.dotClassName);
          const activeDotsCount = Math.round(colState.currentHeight);
          for (let k = 0; k < dots.length; k++) {
              dots[k].classList.toggle('highlight', k < activeDotsCount);
          }
      }
  });
}

/**
* Spegne tutti i pallini per una specifica istanza.
* @param {object} instance - L'oggetto istanza dell'equalizzatore.
*/
function turnOffInstanceDots(instance) {
  if (!instance.containerElement) return;
  const columnElements = instance.containerElement.getElementsByClassName(instance.columnClassName);
  for (let i = 0; i < columnElements.length; i++) {
      const dots = columnElements[i].getElementsByClassName(instance.dotClassName);
      for (let j = 0; j < dots.length; j++) {
          dots[j].classList.remove('highlight');
      }
  }
}

// Gestione al caricamento del DOM
document.addEventListener('DOMContentLoaded', () => {
  // Trova tutti gli elementi che attivano l'hover.
  // Cerca elementi la cui classe contiene "equalizer-" e termina con "-hover".
  const hoverActivators = document.querySelectorAll('[class*="equalizer-"][class*="-hover"]');

  hoverActivators.forEach(hoverElement => {
      let instanceIndex = null;
      // Itera sulle classi dell'elemento hover per trovare quella che definisce l'indice
      hoverElement.classList.forEach(cls => {
          const match = cls.match(/^equalizer-(\d+)-hover$/); // Es: "equalizer-1-hover"
          if (match && match[1]) {
              instanceIndex = match[1];
          }
      });

      if (!instanceIndex) {
          console.warn("Impossibile determinare l'indice dell'istanza per l'elemento hover:", hoverElement);
          return; // Salta questo elemento se non troviamo l'indice
      }

      const containerId = `equalizer-container-${instanceIndex}`;
      const containerElement = document.getElementById(containerId);

      if (!containerElement) {
          console.warn(`Contenitore equalizer #${containerId} non trovato per l'elemento hover:`, hoverElement);
          return; // Salta se il contenitore corrispondente non esiste
      }

      // Crea e memorizza i dati per questa istanza di equalizzatore
      const instanceId = containerId; // Usiamo l'ID del container come ID univoco dell'istanza
      equalizerInstances[instanceId] = {
          id: instanceId,
          config: JSON.parse(JSON.stringify(globalEqConfig)), // Copia della config globale
          columnStates: [],
          animationIntervalId: null,
          hoverElement: hoverElement,
          containerElement: containerElement,
          // Nomi delle classi CSS specifici per questa istanza
          columnClassName: `equalizer-column-${instanceIndex}`, 
          dotClassName: `equalizer-dot-${instanceIndex}`
      };

      const currentInstance = equalizerInstances[instanceId];

      createInstanceEqualizerStructure(currentInstance); // Crea la struttura HTML
      turnOffInstanceDots(currentInstance);           // Assicura che parta spento

      // Aggiunge gli event listener all'elemento hover specifico
      hoverElement.addEventListener('mouseenter', () => {
          if (!currentInstance.animationIntervalId) { // Avvia solo se non già attivo
              initializeInstanceColumnStates(currentInstance); // Prepara stato "fresco"
              updateInstanceEqualizerEffect(currentInstance);  // Primo frame immediato
              currentInstance.animationIntervalId = setInterval(() => {
                  updateInstanceEqualizerEffect(currentInstance);
              }, currentInstance.config.animationSpeed);
          }
      });

      hoverElement.addEventListener('mouseleave', () => {
          if (currentInstance.animationIntervalId) {
              clearInterval(currentInstance.animationIntervalId); // Ferma animazione
              currentInstance.animationIntervalId = null;
          }
          // Commenta la riga seguente se vuoi che l'equalizzatore si "congeli" all'ultimo stato
          turnOffInstanceDots(currentInstance); // Spegne i pallini
      });
  });

});





/*--------------------------------------------------------------

## Homepage - Social

--------------------------------------------------------------*/
 





































