


/*
jQuery(document).ready(function() {
    jQuery(document).on("focus", "form .fluent-phone input", function() {
        var fieldPhone = jQuery(this).closest(".fluent-phone");
        if (fieldPhone.length) {
            fieldPhone.addClass("focused");
        }
    });

    jQuery(document).on("blur", ".fluent-phone input", function() {
        var fieldPhone = jQuery(this).closest(".fluent-phone");
        if (fieldPhone.length) {
            var val = "";
            if (typeof iti !== "undefined" && iti !== null) {
                val = iti.getNumber().trim();
            } else {
                val = jQuery(this).val().trim();
            }
            if (val) {
                fieldPhone.addClass("focused");
            } else {
                fieldPhone.removeClass("focused");
            }
        }
    });
});
*/



/*--------------------------------------------------------------

## Fluentform - Label on focus

--------------------------------------------------------------*/

function check_labels_fluentform(){
  
    jQuery(".fluentform input").each(function(){
      
      if(jQuery(this).val()) {
        jQuery(this).parent().parent().addClass("focus");
      }
      
    });
    
  }
  
  
    check_labels_fluentform();
    
  jQuery(".fluentform input").blur(function() {
    if(!jQuery(this).val()){
          jQuery(this).parent().parent().removeClass("focus");
      }
        })
        .focus(function() {
          jQuery(this).parent().parent().addClass("focus")
        });


/*--------------------------------------------------------------

## Fluentform - Birthdate Field

--------------------------------------------------------------*/

jQuery(document).ready(function($) {
    const daySelect = $('#ff_1_birthdate_day');
    const monthSelect = $('#ff_1_birthdate_month');
    const yearSelect = $('#ff_1_birthdate_year');
    const hiddenBirthdateInput = $('input[name="birthdate"]');

    if (!daySelect.length || !monthSelect.length || !yearSelect.length || !hiddenBirthdateInput.length) {
        // Elements not found, do nothing.
        return;
    }

    const monthMap = {
        'Gennaio': '01', 'Febbraio': '02', 'Marzo': '03', 'Aprile': '04', 'Maggio': '05',
        'Giugno': '06', 'GIugno': '06', // Handling typo
        'Luglio': '07', 'Agosto': '08', 'Settembre': '09', 'Ottobre': '10', 'Novembre': '11', 'Dicembre': '12'
    };

    function isLeapYear(year) {
        return (year % 4 === 0 && year % 100 !== 0) || (year % 400 === 0);
    }

    function getDaysInMonth(month, year) {
        switch (month) {
            case 'Aprile':
            case 'Giugno':
            case 'GIugno':
            case 'Settembre':
            case 'Novembre':
                return 30;
            case 'Febbraio':
                return isLeapYear(parseInt(year, 10)) ? 29 : 28;
            default:
                return 31;
        }
    }

    function updateHiddenBirthdate() {
        const day = String(daySelect.val()).padStart(2, '0');
        const monthName = monthSelect.val();
        const month = monthMap[monthName];
        const year = yearSelect.val();

        if (day && month && year) {
            const fullDate = `${day}/${month}/${year}`;
            hiddenBirthdateInput.val(fullDate);
        }
    }

    function updateDays() {
        const selectedMonth = monthSelect.val();
        const selectedYear = yearSelect.val();
        const daysInMonth = getDaysInMonth(selectedMonth, selectedYear);
        const currentSelectedDay = parseInt(daySelect.val(), 10);

        daySelect.empty();

        for (let i = 1; i <= daysInMonth; i++) {
            daySelect.append($('<option>', { value: i, text: i }));
        }

        if (currentSelectedDay <= daysInMonth) {
            daySelect.val(currentSelectedDay);
        } else {
            daySelect.val(daysInMonth);
        }
        
        updateHiddenBirthdate(); // Call after updating days
    }

    // Add event listeners
    daySelect.on('change', updateHiddenBirthdate);
    monthSelect.on('change', updateDays);
    yearSelect.on('change', updateDays);

    // Initial call
    if (daySelect.length && monthSelect.length && yearSelect.length) {
        updateDays();
    }
});
