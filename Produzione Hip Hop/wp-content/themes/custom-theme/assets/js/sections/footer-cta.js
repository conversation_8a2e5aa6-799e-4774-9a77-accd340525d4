(function($){
    $(document).ready(function(){

    /***************************************************************
     * 1) RIFERIMENTI PRINCIPALI
     ***************************************************************/
    var $form = $("form.footer_contact_form");
    if (!$form.length) return;

    var $errorsBox = $form.find(".errors");

    // Campi del form
    var $fullNameField = $form.find('[field="full_name"]');
    var $emailField    = $form.find('[field="email"]'); // HTML uses field="email" for the div wrapping user_email input
    var $phoneField    = $form.find('[field="phone"]');
    var $messageField  = $form.find('[field="message"]');
    var $privacyCheckboxWrapper = $form.find('[option="privacy_footer"]');


    /***************************************************************
     * 2) Utility errori
     ***************************************************************/
    function clearAllErrors() {
        $errorsBox.empty().hide();
        $form.removeClass("has-errors");
        $form.find(".field").removeClass("error");
        $privacyCheckboxWrapper.removeClass("error"); 
    }

    function showFieldError(fieldName, message) {
        var $fieldElement = $form.find('[field="' + fieldName + '"]');
        if (fieldName === "privacy_footer") {
            $fieldElement = $privacyCheckboxWrapper;
        }
        $fieldElement.addClass("error");
        $errorsBox.append(
            '<div class="error-message error-for-field-' + fieldName + '">' + message + '</div>'
        );
    }

    function showGeneralError(message) {
        $errorsBox.append(
            '<div class="error-message general-error">' + message + '</div>'
        );
    }

    function displayErrorsContainer() {
        $errorsBox.show();
        $form.addClass("has-errors");
        // Scroll to errors if needed
        $('html, body').animate({
            scrollTop: $errorsBox.offset().top - 100 
        }, 500);
    }

    /***************************************************************
     * 3) Inizializzazione intl-tel-input (telefono con bandierine)
     ***************************************************************/
    var iti = null;
    if ($phoneField.length) {
        var phoneInputEl = $phoneField.find('input[type="tel"]')[0];
        if (phoneInputEl) {
            iti = window.intlTelInput(phoneInputEl, {
                initialCountry:   "it",
                separateDialCode: true,
                nationalMode:     false,
                autoHideDialCode: false,
                formatOnDisplay:  true,
                preferredCountries: ["it", "ch", "sm", "gb", "us", "de", "fr", "es"]
            });

            $(phoneInputEl).on("keypress", function(e){
                var char = String.fromCharCode(e.which);
                if (!/[0-9\s\-\(\)\+]/.test(char)) {
                    e.preventDefault();
                }
            });
        }
    }

    /***************************************************************
     * 4) SUBMIT form => AJAX
     ***************************************************************/
    $form.on("submit", function(e){
        e.preventDefault();
        clearAllErrors();

        var errors = [];

        // Leggi i valori dei campi
        var fullNameVal = $fullNameField.find('input').val().trim();
        var emailVal    = $emailField.find('input').val().trim();
        var phoneVal    = "";
        if (iti) {
            phoneVal = iti.getNumber().trim(); // Gets number with country code
        } else if ($phoneField.length) {
            phoneVal = $phoneField.find('input[type="tel"]').val().trim();
        }
        var messageVal  = $messageField.find('textarea').val().trim();
        var privacyChecked = $privacyCheckboxWrapper.find('input').is(':checked');

        // Validazioni base
        var emailRegex = /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/;

        if (!fullNameVal) {
            errors.push({ field: "full_name", message: "Inserisci nome e cognome." });
        }
        if (!emailVal) {
            errors.push({ field: "email", message: "Inserisci la tua e-mail." });
        } else if (!emailRegex.test(emailVal)) {
            errors.push({ field: "email", message: "Inserisci un\'e-mail valida." });
        }
        if (!phoneVal) {
            errors.push({ field: "phone", message: "Inserisci il numero di telefono." });
        } else if (iti && !iti.isValidNumber() && phoneVal !== "" ) { // Check validity only if iti is present and phone is not empty after trim
             errors.push({ field: "phone", message: "Numero di telefono non valido." });
        }
        if (!messageVal) {
            errors.push({ field: "message", message: "Inserisci il tuo messaggio." });
        }
        if (!privacyChecked) {
            errors.push({ field: "privacy_footer", message: "Devi accettare l'informativa sulla privacy." });
        }

        // Se errori, li mostro e stop
        if (errors.length > 0) {
            errors.forEach(function(err){
                showFieldError(err.field, err.message);
            });
            displayErrorsContainer();
            return;
        }

        // Disabilita il bottone di submit per prevenire doppi click
        var $submitButton = $form.find('button[type="submit"]');
        var originalButtonText = $submitButton.text();
        $submitButton.prop('disabled', true).text('Invio in corso...');


        // Preparo i dati per AJAX
        // Assicurati che footer_form_ajax_object sia localizzato da WordPress
        var postData = {
            action:         'footer_contact_form_action',
            security:       footer_form_ajax_object.security, // da wp_localize_script
            full_name:      fullNameVal,
            user_email:     emailVal,
            phone:          phoneVal,
            message:        messageVal,
            privacy_footer: privacyChecked ? 'yes' : 'no'
        };

        // Chiamata AJAX
        $.ajax({
            url: footer_form_ajax_object.ajaxurl, // da wp_localize_script
            type: "POST",
            data: postData,
            dataType: "json",
            success: function(response) {
                if (response.success === true) {
                    $form[0].reset(); // Resetta il form
                    if (iti) {
                        iti.setCountry("it"); // Resetta intl-tel-input al paese iniziale
                    }
                    // Mostra un messaggio di successo globale o reindirizza
                    // Per ora, lo mettiamo nel box errori con stile diverso
                    $errorsBox.html('<div class="success-message">' + (response.message || 'Messaggio inviato con successo!') + '</div>').show();
                     $('html, body').animate({
                        scrollTop: $form.offset().top - 100
                    }, 500);
                    setTimeout(function(){
                        $errorsBox.empty().hide();
                    }, 5000); // Nascondi messaggio dopo 5 secondi

                } else {
                    if (response.errors && response.errors.length) {
                        response.errors.forEach(function(msg){
                            // Se l'errore ha un campo specifico, usa showFieldError
                            if (typeof msg === 'object' && msg.field) {
                                showFieldError(msg.field, msg.message);
                            } else {
                                showGeneralError(msg); // Messaggio di errore generico
                            }
                        });
                    } else {
                        showGeneralError(response.message || "Si è verificato un errore. Riprova.");
                    }
                    displayErrorsContainer();
                }
            },
            error: function(jqXHR, textStatus, errorThrown){
                var errorMsg = "Errore di comunicazione col server (" + textStatus + "). Riprova.";
                if (jqXHR.responseJSON && jqXHR.responseJSON.message) {
                    errorMsg = jqXHR.responseJSON.message;
                } else if (jqXHR.responseText) {
                    try {
                        var errResponse = JSON.parse(jqXHR.responseText);
                        if (errResponse.message) errorMsg = errResponse.message;
                    } catch (e) { /* non fa niente se non è JSON */ }
                }
                showGeneralError(errorMsg);
                displayErrorsContainer();
            },
            complete: function() {
                // Riabilita il bottone di submit
                $submitButton.prop('disabled', false).text(originalButtonText);
            }
        });
    });

    /***************************************************************
     * 5) Rimuove errori su focus/change
     ***************************************************************/
    $form.on("focus", ".field input, .field textarea", function(){
        var $field = $(this).closest(".field");
        var fieldName = $field.attr("field");
        if ($field.hasClass("error")) {
            $field.removeClass("error");
            $errorsBox.find('.error-for-field-' + fieldName).remove();
        }
        if ($errorsBox.find(".error-message").length === 0) {
            clearAllErrors();
        }
    });

    $form.on("change", '[option="privacy_footer"] input', function(){
        var $option = $(this).closest('[option]');
        var fieldName = $option.attr("option"); 
        if ($option.hasClass("error")) {
            $option.removeClass("error");
            $errorsBox.find('.error-for-field-' + fieldName).remove();
        }
        if ($errorsBox.find(".error-message").length === 0) {
            clearAllErrors();
        }
    });

}); // end doc.ready

})(jQuery);
