



jQuery(document).ready(function() {
    jQuery(document).on("focus", "form .field.phone input", function() {
        var fieldPhone = jQuery(this).closest(".field.phone");
        if (fieldPhone.length) {
            fieldPhone.addClass("focused");
        }
    });

    jQuery(document).on("blur", ".field.phone input", function() {
        var fieldPhone = jQuery(this).closest(".field.phone");
        if (fieldPhone.length) {
            var val = "";
            if (typeof iti !== "undefined" && iti !== null) {
                val = iti.getNumber().trim();
            } else {
                val = jQuery(this).val().trim();
            }
            if (val) {
                fieldPhone.addClass("focused");
            } else {
                fieldPhone.removeClass("focused");
            }
        }
    });
});
