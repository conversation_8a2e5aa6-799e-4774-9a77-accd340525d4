<?php
/**
 * footer-form.php
 * Gestisce la logica AJAX per il modulo di contatto nel footer.
 * - Valida i dati inviati.
 * - Invia un'email all'amministratore.
 * - Invia un'email di conferma all'utente.
 */

// Funzione principale per gestire la richiesta AJAX
function handle_footer_contact_form() {
    // Verifica nonce per la sicurezza
    check_ajax_referer('footer-contact-nonce', 'security');

    $errors = [];
    $response_data = [
        'success' => false,
        'message' => '',
        'errors'  => []
    ];

    // Leggi e sanifica i dati inviati
    $full_name = isset($_POST['full_name']) ? sanitize_text_field(wp_unslash($_POST['full_name'])) : '';
    $email     = isset($_POST['user_email']) ? sanitize_email(wp_unslash($_POST['user_email'])) : '';
    $phone     = isset($_POST['phone']) ? sanitize_text_field(wp_unslash($_POST['phone'])) : '';
    $message   = isset($_POST['message']) ? sanitize_textarea_field(wp_unslash($_POST['message'])) : '';
    $privacy   = isset($_POST['privacy_footer']) && $_POST['privacy_footer'] === 'yes';

    // Validazione dei campi
    if (empty($full_name)) {
        $errors[] = ['field' => 'full_name', 'message' => 'Il campo Nome e Cognome è obbligatorio.'];
    }
    if (empty($email)) {
        $errors[] = ['field' => 'email', 'message' => 'Il campo E-mail è obbligatorio.'];
    } elseif (!is_email($email)) {
        $errors[] = ['field' => 'email', 'message' => 'Inserisci un indirizzo e-mail valido.'];
    }
    if (empty($phone)) {
        $errors[] = ['field' => 'phone', 'message' => 'Il campo Telefono è obbligatorio.'];
    }
    if (empty($message)) {
        $errors[] = ['field' => 'message', 'message' => 'Il campo Messaggio è obbligatorio.'];
    }
    if (!$privacy) {
        $errors[] = ['field' => 'privacy_footer', 'message' => "Devi accettare l'informativa sulla privacy."];
    }

    if (!empty($errors)) {
        $response_data['errors'] = $errors;
        $response_data['message'] = 'Per favore, correggi gli errori nel modulo.';
        wp_send_json($response_data);
        return;
    }

    $admin_email_subject = "Nuovo messaggio dal form contatti footer - " . esc_html($full_name);
    $admin_email_template_path = get_stylesheet_directory() . '/inc/e-mails/footer-form-admin.php';
    $admin_email_body = '';

    if (file_exists($admin_email_template_path)) {
        ob_start();
        $template_vars = [
            'full_name' => $full_name,
            'email'     => $email,
            'phone'     => $phone,
            'message'   => nl2br(esc_html($message)),
        ];
        extract($template_vars);
        include $admin_email_template_path;
        $admin_email_body = ob_get_clean();
    } else {
        $admin_email_body  = "<p>Nuovo messaggio ricevuto:</p>";
        $admin_email_body .= "<p><strong>Nome e Cognome:</strong> " . esc_html($full_name) . "</p>";
        $admin_email_body .= "<p><strong>Email:</strong> " . esc_html($email) . "</p>";
        $admin_email_body .= "<p><strong>Telefono:</strong> " . esc_html($phone) . "</p>";
        $admin_email_body .= "<p><strong>Messaggio:</strong><br>" . nl2br(esc_html($message)) . "</p>";
    }

    $admin_to = get_option('admin_email'); 
    $headers = ['Content-Type: text/html; charset=UTF-8'];
    $headers[] = 'Reply-To: ' . esc_html($full_name) . ' <' . esc_html($email) . '>';

    $admin_mail_sent = wp_mail($admin_to, $admin_email_subject, $admin_email_body, $headers);

    $user_email_subject = "Grazie per averci contattato, " . esc_html($full_name) . "!";
    $user_email_template_path = get_stylesheet_directory() . '/inc/e-mails/footer-form-user.php';
    $user_email_body = '';

    if (file_exists($user_email_template_path)) {
        ob_start();
        $template_vars_user = [
            'full_name' => $full_name,
        ];
        extract($template_vars_user);
        include $user_email_template_path;
        $user_email_body = ob_get_clean();
    } else {
        $user_email_body = "<p>Ciao " . esc_html($full_name) . ",</p><p>Grazie per averci contattato. Abbiamo ricevuto il tuo messaggio e ti risponderemo il prima possibile.</p><p>Cordiali saluti,<br>Il Team</p>";
    }

    $user_mail_sent = wp_mail($email, $user_email_subject, $user_email_body, $headers);

    if ($admin_mail_sent && $user_mail_sent) {
        $response_data['success'] = true;
        $response_data['message'] = 'Messaggio inviato con successo! Ti abbiamo inviato una mail di conferma.';
    } elseif ($admin_mail_sent) {
        $response_data['success'] = true;
        $response_data['message'] = 'Messaggio inviato con successo! Non è stato possibile inviare la mail di conferma all\'utente.';
    } else {
        $response_data['message'] = 'Si è verificato un errore durante l\'invio del messaggio. Riprova più tardi.';
    }

    wp_send_json($response_data);
}

add_action('wp_ajax_footer_contact_form_action', 'handle_footer_contact_form');
add_action('wp_ajax_nopriv_footer_contact_form_action', 'handle_footer_contact_form');

?>
