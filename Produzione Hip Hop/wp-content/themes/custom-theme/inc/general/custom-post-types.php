<?php
/**
 * Register a custom post type for Corsi.
 */
function corsi_custom_post_type() {

	$labels = array(
		'name'                  => _x( 'Corsi', 'Post Type General Name', 'custom-theme' ),
		'singular_name'         => _x( 'Corso', 'Post Type Singular Name', 'custom-theme' ),
		'menu_name'             => __( 'Corsi', 'custom-theme' ),
		'name_admin_bar'        => __( 'Corso', 'custom-theme' ),
		'archives'              => __( 'Archivio Corsi', 'custom-theme' ),
		'attributes'            => __( 'Attributi Corso', 'custom-theme' ),
		'parent_item_colon'     => __( 'Corso Genitore:', 'custom-theme' ),
		'all_items'             => __( 'Tutti i Corsi', 'custom-theme' ),
		'add_new_item'          => __( 'Aggiungi Nuovo Corso', 'custom-theme' ),
		'add_new'               => __( 'Aggiungi Nuovo', 'custom-theme' ),
		'new_item'              => __( 'Nuovo Corso', 'custom-theme' ),
		'edit_item'             => __( 'Modifica Corso', 'custom-theme' ),
		'update_item'           => __( 'Aggiorna Corso', 'custom-theme' ),
		'view_item'             => __( 'Visualizza Corso', 'custom-theme' ),
		'view_items'            => __( 'Visualizza Corsi', 'custom-theme' ),
		'search_items'          => __( 'Cerca Corso', 'custom-theme' ),
		'not_found'             => __( 'Non trovato', 'custom-theme' ),
		'not_found_in_trash'    => __( 'Non trovato nel Cestino', 'custom-theme' ),
		'featured_image'        => __( 'Immagine in Evidenza', 'custom-theme' ),
		'set_featured_image'    => __( 'Imposta immagine in evidenza', 'custom-theme' ),
		'remove_featured_image' => __( 'Rimuovi immagine in evidenza', 'custom-theme' ),
		'use_featured_image'    => __( 'Usa come immagine in evidenza', 'custom-theme' ),
		'insert_into_item'      => __( 'Inserisci nel Corso', 'custom-theme' ),
		'uploaded_to_this_item' => __( 'Caricato in questo Corso', 'custom-theme' ),
		'items_list'            => __( 'Elenco Corsi', 'custom-theme' ),
		'items_list_navigation' => __( 'Navigazione elenco Corsi', 'custom-theme' ),
		'filter_items_list'     => __( 'Filtra elenco Corsi', 'custom-theme' ),
	);
	$args   = array(
		'label'               => __( 'Corso', 'custom-theme' ), // Nome singolare del post type.
		'description'         => __( 'Post Type per i corsi del sito', 'custom-theme' ), // Descrizione del post type.
		'labels'              => $labels, // Array delle etichette per il post type.
		'supports'            => array( 'title', 'editor', 'thumbnail', 'excerpt', 'custom-fields', 'page-attributes', 'author', 'revisions' ), // Funzionalità supportate (aggiunto 'revisions').
		'taxonomies'          => array( 'category', 'post_tag' ), // Tassonomie associate al post type (es. categorie, tag).
		'hierarchical'        => false, // Se il post type è gerarchico (come le pagine).
		'public'              => true, // Se il post type è visibile pubblicamente.
		'show_ui'             => true, // Se mostrare l'interfaccia utente nel backend.
		'show_in_menu'        => true, // Se mostrare il post type nel menu di amministrazione.
		'menu_position'       => 5, // Posizione nel menu di amministrazione.
		'menu_icon'           => 'dashicons-welcome-learn-more', // Icona per il menu.
		'show_in_admin_bar'   => true, // Se mostrare il post type nella barra di amministrazione.
		'show_in_nav_menus'   => true, // Se il post type può essere selezionato nei menu di navigazione.
		'can_export'          => true, // Se il post type può essere esportato.
		'has_archive'         => true, // Se abilitare un archivio per il post type.
		'exclude_from_search' => false, // Se escludere i post di questo tipo dalle ricerche del sito.
		'publicly_queryable'  => true, // Se le query possono essere eseguite sul frontend.
		'capability_type'     => 'post', // Tipo di capacità per i permessi (es. 'post', 'page').
		'show_in_rest'        => true, // Abilita l'API REST e l'editor Gutenberg.
	);
	register_post_type( 'corsi', $args );

}
add_action( 'init', 'corsi_custom_post_type', 0 );
