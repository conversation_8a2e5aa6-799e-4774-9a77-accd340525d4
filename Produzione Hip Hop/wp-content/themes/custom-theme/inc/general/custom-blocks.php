<?php

/**
 * Gets the color class name from the 'corso_colore' custom field of the current post.
 *
 * @return string The color class name or a default 'blue' if not found.
 */
function get_current_corso_color_class_name() {
    // Se la funzione di Meta Box non esiste o non siamo in un CPT 'corsi', restituisce un default.
    if ( ! function_exists( 'rwmb_get_value' ) || 'corsi' !== get_post_type() ) {
        return 'blue';
    }

    // Prova a ottenere il colore per il post corrente.
    $color = rwmb_get_value( 'corso_colore', [ 'object_id' => get_the_ID() ] );

    // Se il colore non è impostato, restituisce un default.
    return $color ?: 'blue';
}

/**
 * Fetches video data (source, ID, hash, thumbnail) from a video URL.
 * It handles both Vimeo and YouTube URLs and caches the result.
 *
 * @param string $video_url The full URL of the video.
 * @return array An array containing video data, or empty if failed.
 */
function custom_theme_get_video_data($video_url) {
    if (empty($video_url) || !is_string($video_url)) {
        return ['source' => '', 'video_id' => '', 'hash' => '', 'thumbnail_url' => ''];
    }

    $transient_key = 'video_data_' . md5($video_url);
    $cached_data = get_transient($transient_key);

    if ($cached_data !== false) {
        return $cached_data;
    }

    $result = ['source' => '', 'video_id' => '', 'hash' => '', 'thumbnail_url' => ''];
    
    // Check for YouTube
    if (preg_match('/(youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([a-zA-Z0-9_-]+)/', $video_url, $matches)) {
        $result['source'] = 'youtube';
        $result['video_id'] = $matches[2];
        // Get the highest quality thumbnail available by default.
        $result['thumbnail_url'] = 'https://i.ytimg.com/vi/' . $result['video_id'] . '/maxresdefault.jpg';
        
        // Check if the high-res thumbnail exists, otherwise fall back to standard quality.
        $response = wp_remote_head($result['thumbnail_url']);
        if (is_wp_error($response) || wp_remote_retrieve_response_code($response) !== 200) {
            $result['thumbnail_url'] = 'https://i.ytimg.com/vi/' . $result['video_id'] . '/hqdefault.jpg';
        }

    // Check for Vimeo
    } elseif (preg_match('/vimeo\.com\/(\d+)(?:\/(\w+))?/', $video_url, $matches)) {
        $result['source'] = 'vimeo';
        $result['video_id'] = $matches[1];
        $result['hash'] = $matches[2] ?? '';

        // Fetch thumbnail from Vimeo oEmbed API for better quality
        $api_url = 'https://vimeo.com/api/oembed.json?url=' . rawurlencode($video_url) . '&width=1920';
        $response = wp_remote_get($api_url);

        if (!is_wp_error($response) && wp_remote_retrieve_response_code($response) === 200) {
            $data = json_decode(wp_remote_retrieve_body($response), true);
            $result['thumbnail_url'] = $data['thumbnail_url_with_play_button'] ?? $data['thumbnail_url'] ?? '';
        }
    }
 
    set_transient($transient_key, $result, WEEK_IN_SECONDS); // Cache for one week
    return $result;
}


/*--------------------------------------------------------------
## Gutenberg Blocks
--------------------------------------------------------------*/


/**
 * Register block fields.
 *
 * @param array $meta_boxes
 *
 * @return array
 */
function custom_theme_register_block_fields( $meta_boxes ) {
	$meta_boxes[] = [
		'title'           => 'Pretitle',
		'id'              => 'pretitle',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'text',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'pretitle', 'testo' ],
		'render_template' => 'blocks/pretitle/pretitle.php',
		'fields'  => [
			[
				'name' => 'Testo',
				'id'   => 'text',
				'type' => 'text',
				'std'  => 'Testo pretitle',
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Subtitle',
		'id'              => 'subtitle',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'editor-paragraph',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'sottotitolo', 'testo' ],
		'render_template' => 'blocks/subtitle/subtitle.php',
		'fields'  => [
			[
				'name' => 'Testo',
				'id'   => 'text',
				'type' => 'textarea',
				'std'  => 'Testo del sottotitolo',
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Author',
		'id'              => 'author',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'admin-users',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'autore', 'profilo', 'docente' ],
		'render_template' => 'blocks/author/author.php',
		'fields'  => [
			[
				'name' => 'Foto Profilo',
				'id'   => 'photo',
				'type' => 'single_image',
			],
			[
				'name' => 'Pretitolo',
				'id'   => 'pretitle',
				'type' => 'text',
				'std'  => 'Docente del corso',
			],
			[
				'name' => 'Nome e Cognome',
				'id'   => 'name',
				'type' => 'text',
				'std'  => 'Nome Cognome',
			],
			[
				'name' => 'Ruolo',
				'id'   => 'role',
				'type' => 'text',
				'std'  => 'Produttore e tecnico del suono',
			],
			[
				'name'        => 'Biografia',
				'id'          => 'bio',
				'type'        => 'textarea',
				'placeholder' => 'Breve biografia dell\'autore...',
				'maxlength'   => 240,
			],
			[
				'name' => 'Instagram URL',
				'id'   => 'social_instagram',
				'type' => 'url',
			],
			[
				'name' => 'TikTok URL',
				'id'   => 'social_tiktok',
				'type' => 'url',
			],
			[
				'name' => 'Facebook URL',
				'id'   => 'social_facebook',
				'type' => 'url',
			],
			[
				'name' => 'YouTube URL',
				'id'   => 'social_youtube',
				'type' => 'url',
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Loghi',
		'id'              => 'loghi',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'images-alt2',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'loghi', 'slider', 'carosello', 'swiper' ],
		'render_template' => 'blocks/loghi/loghi.php',
		'fields'  => [
			[
				'name'             => 'Loghi',
				'id'               => 'logos',
				'type'             => 'image_advanced',
				'max_file_uploads' => 20,
				'desc'             => 'Carica fino a 20 immagini per i loghi. Puoi riordinarle tramite drag & drop.',
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Stats',
		'id'              => 'stats',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'chart-bar',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'statistiche', 'numeri', 'dati' ],
		'render_template' => 'blocks/stats/stats.php',
		'fields'   => [
			[
				'id'         => 'stats',
				'type'       => 'group',
				'clone'      => true,
				'sort_clone' => true,
				'max_clone'  => 3,
				'add_button' => '+ Aggiungi statistica',
				'fields'     => [
					[
						'name' => 'Valore',
						'id'   => 'value',
						'type' => 'text',
					],
					[
						'name'        => 'Unità',
						'id'          => 'unit',
						'type'        => 'text',
						'placeholder' => '+, €, etc.',
					],
					[
						'name' => 'Etichetta',
						'id'   => 'label',
						'type' => 'text',
					],
				],
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Stats Text',
		'id'              => 'stats-text',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'align-left',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'statistiche', 'testo', 'dati' ],
		'render_template' => 'blocks/stats-text/stats-text.php',
		'fields'   => [
			[
				'name' => 'Valore',
				'id'   => 'value',
				'type' => 'text',
			],
			[
				'name'        => 'Unità',
				'id'          => 'unit',
				'type'        => 'text',
				'placeholder' => '+, €, etc.',
			],
			[
				'name' => 'Etichetta',
				'id'   => 'label',
				'type' => 'text',
			],
			[
				'name' => 'Testo laterale',
				'id'   => 'side_text',
				'type' => 'textarea',
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Stats Icon',
		'id'              => 'stats-icon',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'star-filled',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'stat', 'icona', 'svg' ],
		'supports'        => [
			'anchor' => true,
			'align'  => ['wide', 'full']
		],
		'render_template' => 'blocks/stats-icon/stats-icon.php',
		'fields'   => [
			[
				'id'         => 'boxes',
				'type'       => 'group',
				'clone'      => true,
				'sort_clone' => true,
				'max_clone'  => 2,
				'add_button' => '+ Aggiungi Box',
				'fields'     => [
					[
						'name' => 'Valore',
						'id'   => 'value',
						'type' => 'text',
					],
					[
						'name'        => 'Unità',
						'id'          => 'unit',
						'type'        => 'text',
						'placeholder' => '+, €, etc.',
					],

					[
						'name' => 'Etichetta',
						'id'   => 'label',
						'type' => 'text',
					],
					[
						'name' => 'Icona',
						'id'   => 'icon_image',
						'type' => 'single_image',
						'desc' => 'Carica un\'icona in formato JPG o PNG.',
					],
				],
			],
		],
	];
 
	$meta_boxes[] = [
		'title'           => 'Block Outline',
		'id'              => 'outline',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'editor-ul',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'accordion', 'faq', 'lista', 'outline' ],
		'supports'        => [
			'anchor' => true,
			'align'  => ['wide', 'full']
		],
		'render_template' => 'blocks/outline/outline.php',
		'fields'   => [
			[
				'id'         => 'rows',
				'type'       => 'group',
				'clone'      => true,
				'sort_clone' => true,
				'add_button' => '+ Aggiungi Riga',
				'fields'     => [
					[
						'name' => 'Titolo',
						'id'   => 'title',
						'type' => 'text',
					],
					[
						'name'    => 'Contenuto',
						'id'      => 'body',
						'type'    => 'textarea',
					],
				],
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block CTA',
		'id'              => 'cta',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'megaphone',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'cta', 'call to action', 'bottone', 'brochure' ],
		'supports'        => [
			'anchor' => true,
			'align'  => ['wide', 'full']
		],
		'render_template' => 'blocks/cta/cta.php',
		'fields'  => [
			[
				'name' => 'Icona (PNG)',
				'id'   => 'icon',
				'type' => 'single_image',
				'desc' => 'Carica un\'icona in formato PNG (verrà mostrata con altezza 48px).',
			],
			[
				'name' => 'Titolo',
				'id'   => 'title',
				'type' => 'text',
			],
			[
				'name' => 'Testo',
				'id'   => 'text',
				'type' => 'textarea',
			],
			[
				'name' => 'Testo del bottone',
				'id'   => 'button_text',
				'type' => 'text',
			],
			[
				'name' => 'URL del bottone',
				'id'   => 'button_url',
				'type' => 'url',
			],
			[
				'name' => 'Immagine laterale',
				'id'   => 'side_image',
				'type' => 'single_image',
			],
			[
				'name' => 'Immagine di background (Opzionale)',
				'id'   => 'background_image',
				'type' => 'single_image',
				'desc' => 'Carica un\'immagine di background che apparirà nella parte destra del blocco.',
			],
		],
	]; 

	$meta_boxes[] = [
		'title'           => 'Block Box Image',
    'id'              => 'box-image',
    'type'            => 'block',
    'context'         => 'normal',
    'icon'            => 'format-image',
    'category'        => 'custom-blocks',
    'keywords'        => [ 'box', 'image', 'icon', 'text' ],
    'render_template' => 'blocks/box-image/box-image.php',
    'fields'  => [
			[
				'name' => 'Icona (Immagine)',
				'id'   => 'icon',
				'type' => 'single_image',
				'desc' => 'Carica un\'icona in formato PNG o JPG (verrà mostrata con altezza 48px).',
			],
			[
				'name' => 'Titolo',
				'id'   => 'title',
				'type' => 'text',
			],
			[
				'name' => 'Testo',
				'id'   => 'text',
				'type' => 'textarea',
			],
			[
				'name' => 'Immagine Laterale',
				'id'   => 'side_image',
				'type' => 'single_image',
			],
		],
	];
	$meta_boxes[] = [
        'title'           => 'Block Video',
        'id'              => 'video',
        'type'            => 'block',
        'context'         => 'normal',
        'icon'            => 'video-alt3',
        'category'        => 'custom-blocks',
        'keywords'        => [ 'video', 'player', 'vimeo', 'youtube' ],
        'render_template' => 'blocks/video/video.php',
        'fields'  => [
			[
				'name' => 'Video URL',
				'id'   => 'video_url',
				'type' => 'url',
				'desc' => 'Incolla l\'URL del video da Vimeo (es. https://vimeo.com/ID/HASH).',
			],
			[
				'name'    => 'Aspect Ratio',
				'id'      => 'aspect_ratio',
				'type'    => 'select',
				'std'     => 'ratio-16-9',
				'options' => [
                    'ratio-16-9' => '16:9',
                    'ratio-4-3'  => '4:3',
                    'ratio-1-1'  => '1:1',
                    'ratio-3-2'  => '3:2',
                    'ratio-2-3'  => '2:3',
                    'ratio-3-4'  => '3:4',
                    'ratio-4-5'  => '4:5',
                    'ratio-21-9' => '21:9',
                    'ratio-2-1'  => '2:1',
                    'ratio-3-1'  => '3:1',
                    'ratio-4-1'  => '4:1',
                    'ratio-9-16' => '9:16',
                    'ratio-9-21' => '9:21',
				],
			],
			[
				'name' => 'Titolo Video (Opzionale)',
				'id'   => 'video_title',
				'type' => 'text',
				'desc' => 'Titolo da mostrare sotto al video.',
			],
			[
				'name' => 'Autore Video (Opzionale)',
				'id'   => 'video_author',
				'type' => 'text',
				'desc' => 'Nome dell\'autore o descrizione da mostrare sotto al video.',
			],
			[
				'name' => 'Numero Visualizzazioni (Opzionale)',
				'id'   => 'video_views_number',
				'type' => 'text',
				'desc' => 'Numero di visualizzazioni (es. 1M, 500K, 1000).',
			],
			[
				'name' => 'Etichetta Visualizzazioni (Opzionale)',
				'id'   => 'video_views_unit',
				'type' => 'text',
				'std'  => 'Visualizzazioni',
				'desc' => 'Etichetta per le visualizzazioni (es. Visualizzazioni, Views, etc.).',
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Box Row',
		'id'              => 'box-row',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'align-left',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'box', 'row', 'icon', 'text', 'horizontal' ],
		'render_template' => 'blocks/box-row/box-row.php',
		'fields'  => [
			[
				'name' => 'Icona (Immagine)',
				'id'   => 'icon',
				'type' => 'single_image',
				'desc' => 'Carica un\'icona in formato PNG o JPG (verrà mostrata con altezza 48px).',
			],
			[
				'name' => 'Titolo',
				'id'   => 'title',
				'type' => 'text',
			],
			[
				'name' => 'Testo',
				'id'   => 'text',
				'type' => 'wysiwyg',
				'options' => [
					'textarea_rows' => 4,
					'teeny' => true,
					'media_buttons' => false,
				],
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Box Column',
		'id'              => 'box-column',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'align-center',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'box', 'column', 'icon', 'text', 'vertical' ],
		'render_template' => 'blocks/box-column/box-column.php',
		'fields'  => [
			[
				'name' => 'Icona (Immagine)',
				'id'   => 'icon',
				'type' => 'single_image',
				'desc' => 'Carica un\'icona in formato PNG o JPG (verrà mostrata con altezza 48px).',
			],
			[
				'name' => 'Titolo',
				'id'   => 'title',
				'type' => 'text',
			],
			[
				'name' => 'Testo',
				'id'   => 'text',
				'type' => 'wysiwyg',
				'options' => [
					'textarea_rows' => 4,
					'teeny' => true,
					'media_buttons' => false,
				],
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Icon Text',
		'id'              => 'icon-text',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'format-chat',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'icon', 'text', 'simple' ],
		'render_template' => 'blocks/icon-text/icon-text.php',
		'fields'  => [
			[
				'name' => 'Icona (Immagine)',
				'id'   => 'icon',
				'type' => 'single_image',
				'desc' => 'Carica un\'icona in formato PNG o JPG (verrà mostrata con altezza 48px).',
			],
			[
				'name' => 'Testo',
				'id'   => 'text',
				'type' => 'wysiwyg',
				'options' => [
					'textarea_rows' => 3,
					'teeny' => true,
					'media_buttons' => false,
				],
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Gallery Slider',
		'id'              => 'gallery-slider',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'format-gallery',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'gallery', 'slider', 'images', 'swiper', 'lightbox' ],
		'render_template' => 'blocks/gallery-slider/gallery-slider.php',
		'fields'  => [
			[
				'name'             => 'Immagini',
				'id'               => 'images',
				'type'             => 'image_advanced',
				'max_file_uploads' => 20,
				'desc'             => 'Carica fino a 20 immagini per la galleria. Puoi riordinarle tramite drag & drop.',
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Quote',
		'id'              => 'quote',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'format-quote',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'quote', 'citazione', 'testimonianza', 'author' ],
		'render_template' => 'blocks/quote/quote.php',
		'fields'  => [
			[
				'name' => 'Testo della citazione',
				'id'   => 'text',
				'type' => 'wysiwyg',
				'options' => [
					'textarea_rows' => 4,
					'teeny' => true,
					'media_buttons' => false,
				],
			],
			[
				'name' => 'Foto autore',
				'id'   => 'author_photo',
				'type' => 'single_image',
				'desc' => 'Carica la foto dell\'autore della citazione.',
			],
			[
				'name' => 'Nome autore',
				'id'   => 'author_name',
				'type' => 'text',
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block FAQ',
		'id'              => 'faq',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'editor-help',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'faq', 'domande', 'risposte', 'accordion' ],
		'render_template' => 'blocks/faq/faq.php',
		'fields'   => [
			[
				'id'         => 'rows',
				'type'       => 'group',
				'clone'      => true,
				'sort_clone' => true,
				'add_button' => '+ Aggiungi Domanda',
				'fields'     => [
					[
						'name' => 'Domanda',
						'id'   => 'question',
						'type' => 'text',
					],
					[
						'name'    => 'Risposta',
						'id'      => 'answer',
						'type'    => 'wysiwyg',
						'options' => [
							'textarea_rows' => 4,
							'teeny' => true,
							'media_buttons' => false,
						],
					],
				],
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Button',
		'id'              => 'button',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'button',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'button', 'link', 'cta', 'bottone' ],
		'render_template' => 'blocks/button/button.php',
		'fields'  => [
			[
				'name' => 'Testo del bottone',
				'id'   => 'text',
				'type' => 'text',
				'std'  => 'Clicca qui',
			],
			[
				'name' => 'URL',
				'id'   => 'url',
				'type' => 'url',
			],
			[
				'name'    => 'Apri in nuova finestra',
				'id'      => 'target_blank',
				'type'    => 'checkbox',
				'std'     => 0,
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Video Reviews',
		'id'              => 'video-reviews',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'video-alt3',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'video', 'reviews', 'testimonials', 'slider', 'vimeo' ],
		'render_template' => 'blocks/video-reviews/video-reviews.php',
		'fields'   => [
			[
				'id'         => 'videos',
				'type'       => 'group',
				'clone'      => true,
				'sort_clone' => true,
				'max_clone'  => 20,
				'add_button' => '+ Aggiungi Video Review',
				'fields'     => [
					[
						'name' => 'Video URL (Vimeo)',
						'id'   => 'video_url',
						'type' => 'url',
						'desc' => 'Incolla l\'URL del video da Vimeo (es. https://vimeo.com/ID/HASH). L\'immagine di anteprima verrà caricata automaticamente.',
					],
					[
						'name'    => 'Aspect Ratio Video',
						'id'      => 'aspect_ratio',
						'type'    => 'select',
						'std'     => 'ratio-4-5',
						'options' => [
							'ratio-16-9' => '16:9',
							'ratio-4-3'  => '4:3',
							'ratio-1-1'  => '1:1',
							'ratio-3-2'  => '3:2',
							'ratio-2-3'  => '2:3',
							'ratio-3-4'  => '3:4',
							'ratio-4-5'  => '4:5',
							'ratio-21-9' => '21:9',
							'ratio-2-1'  => '2:1',
							'ratio-9-16' => '9:16',
						],
					],
					[
						'name' => 'Nome autore',
						'id'   => 'author_name',
						'type' => 'text',
					],
					[
						'name' => 'Foto autore',
						'id'   => 'author_photo',
						'type' => 'single_image',
						'desc' => 'Carica la foto dell\'autore del video.',
					],
					[
						'name'    => 'Testo review',
						'id'      => 'review_text',
						'type'    => 'textarea',
						'desc'    => 'Breve testo della testimonianza.',
					],
				],
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Image Reviews',
		'id'              => 'image-reviews',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'format-gallery',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'image', 'reviews', 'gallery', 'slider', 'swiper' ],
		'render_template' => 'blocks/image-reviews/image-reviews.php',
		'fields'  => [
			[
				'name'             => 'Immagini Reviews',
				'id'               => 'images',
				'type'             => 'image_advanced',
				'max_file_uploads' => 20,
				'desc'             => 'Carica fino a 20 immagini per lo slider delle reviews. Puoi riordinarle tramite drag & drop.',
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Box Reviews',
		'id'              => 'box-reviews',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'star-filled',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'box', 'reviews', 'testimonials', 'slider', 'swiper', 'stars' ],
		'render_template' => 'blocks/box-reviews/box-reviews.php',
		'fields'   => [
			[
				'id'         => 'reviews',
				'type'       => 'group',
				'clone'      => true,
				'sort_clone' => true,
				'max_clone'  => 20,
				'add_button' => '+ Aggiungi Review',
				'fields'     => [
					[
						'name' => 'Numero di stelle',
						'id'   => 'stars',
						'type' => 'select',
						'std'  => '5',
						'options' => [
							'1' => '1 stella',
							'2' => '2 stelle',
							'3' => '3 stelle',
							'4' => '4 stelle',
							'5' => '5 stelle',
						],
					],
					[
						'name'    => 'Testo della recensione',
						'id'      => 'text',
						'type'    => 'textarea',
						'desc'    => 'Testo della testimonianza/recensione.',
					],
					[
						'name' => 'Foto autore',
						'id'   => 'author_photo',
						'type' => 'single_image',
						'desc' => 'Carica la foto dell\'autore della recensione.',
					],
					[
						'name' => 'Nome autore',
						'id'   => 'author_name',
						'type' => 'text',
					],
					[
						'name' => 'Ruolo autore',
						'id'   => 'author_role',
						'type' => 'text',
						'desc' => 'Es: Produttore musicale, Student, etc.',
					],
				],
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Video Slider',
		'id'              => 'video-slider',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'video-alt3',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'video', 'slider', 'swiper', 'vimeo', 'youtube', 'carousel' ],
		'render_template' => 'blocks/video-slider/video-slider.php',
		'fields'   => [
			[
				'id'         => 'videos',
				'type'       => 'group',
				'clone'      => true,
				'sort_clone' => true,
				'max_clone'  => 20,
				'add_button' => '+ Aggiungi Video',
				'fields'     => [
					[
						'name' => 'Video URL',
						'id'   => 'video_url',
						'type' => 'url',
						'desc' => 'Incolla l\'URL del video da Vimeo o YouTube.',
					],
					[
						'name'    => 'Aspect Ratio',
						'id'      => 'aspect_ratio',
						'type'    => 'select',
						'std'     => 'ratio-16-9',
						'options' => [
							'ratio-16-9' => '16:9',
							'ratio-4-3'  => '4:3',
							'ratio-1-1'  => '1:1',
							'ratio-3-2'  => '3:2',
							'ratio-2-3'  => '2:3',
							'ratio-3-4'  => '3:4',
							'ratio-4-5'  => '4:5',
							'ratio-21-9' => '21:9',
							'ratio-2-1'  => '2:1',
							'ratio-9-16' => '9:16',
						],
					],
					[
						'name' => 'Titolo Video (Opzionale)',
						'id'   => 'video_title',
						'type' => 'text',
						'desc' => 'Titolo da mostrare sotto al video.',
					],
					[
						'name' => 'Autore Video (Opzionale)',
						'id'   => 'video_author',
						'type' => 'text',
						'desc' => 'Nome dell\'autore o descrizione da mostrare sotto al video.',
					],
					[
						'name' => 'Numero Visualizzazioni (Opzionale)',
						'id'   => 'video_views_number',
						'type' => 'text',
						'desc' => 'Numero di visualizzazioni (es. 1M, 500K, 1000).',
					],
					[
						'name' => 'Etichetta Visualizzazioni (Opzionale)',
						'id'   => 'video_views_unit',
						'type' => 'text',
						'std'  => 'Visualizzazioni',
						'desc' => 'Etichetta per le visualizzazioni (es. Visualizzazioni, Views, etc.).',
					],
				],
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Line Text',
		'id'              => 'line-text',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'editor-quote',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'line', 'text', 'border', 'quote', 'testimonial' ],
		'render_template' => 'blocks/line-text/line-text.php',
		'fields'  => [
			[
				'name' => 'Titolo',
				'id'   => 'title',
				'type' => 'text',
				'desc' => 'Nome o titolo da mostrare.',
			],
			[
				'name' => 'Testo',
				'id'   => 'text',
				'type' => 'wysiwyg',
				'options' => [
					'textarea_rows' => 4,
					'teeny' => true,
					'media_buttons' => false,
				],
				'desc' => 'Contenuto del testo da mostrare.',
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Gallery',
		'id'              => 'gallery',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'format-gallery',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'gallery', 'grid', 'images', 'lightbox' ],
		'render_template' => 'blocks/gallery/gallery.php',
		'fields'  => [
			[
				'name'             => 'Immagini',
				'id'               => 'images',
				'type'             => 'image_advanced',
				'max_file_uploads' => 20,
				'desc'             => 'Carica fino a 20 immagini per la galleria. Puoi riordinarle tramite drag & drop.',
			],
			[
				'name'    => 'Aspect Ratio',
				'id'      => 'aspect_ratio',
				'type'    => 'select',
				'std'     => 'ratio-3-4',
				'options' => [
					'ratio-3-4' => 'Verticale (3:4)',
					'ratio-1-1' => 'Quadrato (1:1)',
					'ratio-4-3' => 'Orizzontale (4:3)',
				],
			],
			[
				'name'    => 'Numero di Colonne',
				'id'      => 'columns',
				'type'    => 'select',
				'std'     => '3',
				'options' => [
					'1' => '1 Colonna',
					'2' => '2 Colonne',
					'3' => '3 Colonne',
				],
			],
			[
				'name' => 'Abilita Lightbox (Opzionale)',
				'id'   => 'enable_lightbox',
				'type' => 'checkbox',
				'std'  => 0,
				'desc' => 'Attiva il lightbox per visualizzare le immagini a schermo intero.',
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Roadmap',
		'id'              => 'roadmap',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'admin-site-alt3',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'roadmap', 'timeline', 'steps', 'process' ],
		'render_template' => 'blocks/roadmap/roadmap.php',
		'fields'   => [
			[
				'id'         => 'items',
				'type'       => 'group',
				'clone'      => true,
				'sort_clone' => true,
				'max_clone'  => 20,
				'add_button' => '+ Aggiungi Step',
				'fields'     => [
					[
						'name'    => 'Testo',
						'id'      => 'text',
						'type'    => 'wysiwyg',
						'options' => [
							'textarea_rows' => 4,
							'teeny' => true,
							'media_buttons' => false,
						],
						'desc'    => 'Contenuto del testo per questo step.',
					],
				],
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Checklist',
		'id'              => 'checklist',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'yes-alt',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'checklist', 'check', 'lista', 'spunta' ],
		'render_template' => 'blocks/checklist/checklist.php',
		'fields'   => [
			[
				'id'         => 'items',
				'type'       => 'group',
				'clone'      => true,
				'sort_clone' => true,
				'max_clone'  => 20,
				'add_button' => '+ Aggiungi Item',
				'fields'     => [
					[
						'name'    => 'Testo',
						'id'      => 'text',
						'type'    => 'wysiwyg',
						'options' => [
							'textarea_rows' => 3,
							'teeny' => true,
							'media_buttons' => false,
						],
						'desc'    => 'Contenuto del testo per questo item della checklist.',
					],
				],
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Ticket',
		'id'              => 'ticket',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'tickets-alt',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'ticket', 'offerta', 'sconto', 'promo' ],
		'render_template' => 'blocks/ticket/ticket.php',
		'fields'  => [
			[
				'name' => 'Valore',
				'id'   => 'value',
				'type' => 'text',
				'desc' => 'Valore numerico del ticket (es. 50)',
			],
			[
				'name' => 'Unità',
				'id'   => 'unit',
				'type' => 'text',
				'desc' => 'Unità di misura (es. %, €, etc.)',
			],
			[
				'name' => 'Etichetta',
				'id'   => 'label',
				'type' => 'text',
				'desc' => 'Titolo/etichetta del ticket',
			],
			[
				'name' => 'Testo laterale',
				'id'   => 'side_text',
				'type' => 'wysiwyg',
				'options' => [
					'textarea_rows' => 4,
					'teeny' => true,
					'media_buttons' => false,
				],
				'desc' => 'Testo descrittivo che appare nella parte destra del ticket',
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Card',
		'id'              => 'card',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'format-aside',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'card', 'scheda', 'content' ],
		'render_template' => 'blocks/card/card.php',
		'fields'  => [
			[
				'name' => 'Immagine di copertina',
				'id'   => 'cover_image',
				'type' => 'single_image',
				'desc' => 'Carica l\'immagine di copertina per la card.',
			],
			[
				'name'    => 'Aspect Ratio',
				'id'      => 'aspect_ratio',
				'type'    => 'select',
				'std'     => 'ratio-16-9',
				'options' => [
					'ratio-16-9' => '16:9',
					'ratio-4-3'  => '4:3',
					'ratio-1-1'  => '1:1',
					'ratio-3-2'  => '3:2',
					'ratio-2-3'  => '2:3',
					'ratio-3-4'  => '3:4',
					'ratio-4-5'  => '4:5',
				],
			],
			[
				'name' => 'Titolo',
				'id'   => 'title',
				'type' => 'text',
				'desc' => 'Titolo principale della card.',
			],
			[
				'name' => 'Testo',
				'id'   => 'text',
				'type' => 'wysiwyg',
				'options' => [
					'textarea_rows' => 4,
					'teeny' => true,
					'media_buttons' => false,
				],
				'desc' => 'Contenuto testuale della card.',
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block List',
		'id'              => 'block-list',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'editor-ul',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'list', 'elenco', 'items' ],
		'render_template' => 'blocks/block-list/block-list.php',
		'fields'   => [
			[
				'id'         => 'items',
				'type'       => 'group',
				'clone'      => true,
				'sort_clone' => true,
				'max_clone'  => 20,
				'add_button' => '+ Aggiungi Item',
				'fields'     => [
					[
						'name'    => 'Tipo di Marcatore',
						'id'      => 'marker_type',
						'type'    => 'select',
						'std'     => 'numbered',
						'options' => [
							'numbered'          => 'Numerato',
							'numbered_bg'       => 'Numerato con sfondo',
							'icon'              => 'Icona personalizzata',
							'bullet'            => 'Punto',
							'check'             => 'Check',
							'cross'             => 'Cross',
							'arrow'             => 'Arrow',
						],
						'desc'    => 'Seleziona il tipo di marcatore da visualizzare',
					],
					[
						'name' => 'Icona (solo per tipo "Icona")',
						'id'   => 'custom_icon',
						'type' => 'single_image',
						'desc' => 'Carica un\'icona personalizzata (PNG o SVG).',
						'visible' => [
							'when'     => [ [ 'marker_type', '=', 'icon' ] ],
							'relation' => 'or'
						],
					],
					[
						'name'    => 'Testo',
						'id'      => 'text',
						'type'    => 'wysiwyg',
						'options' => [
							'textarea_rows' => 4,
							'teeny' => true,
							'media_buttons' => false,
						],
						'desc'    => 'Contenuto del testo per questo item.',
					],
				],
			],
		],
	];

	$meta_boxes[] = [
		'title'           => 'Block Comparison',
		'id'              => 'comparison',
		'type'            => 'block',
		'context'         => 'normal',
		'icon'            => 'table-row-after',
		'category'        => 'custom-blocks',
		'keywords'        => [ 'comparison', 'comparazione', 'tabella', 'confronto' ],
		'render_template' => 'blocks/comparison/comparison.php',
		'fields'   => [
			[
				'name' => 'Titolo Colonna 1',
				'id'   => 'column1_title',
				'type' => 'text',
				'std'  => 'Problema',
				'desc' => 'Titolo per la prima colonna',
			],
			[
				'name' => 'Titolo Colonna 2',
				'id'   => 'column2_title',
				'type' => 'text',
				'std'  => 'Soluzione',
				'desc' => 'Titolo per la seconda colonna',
			],
			[
				'id'         => 'rows',
				'type'       => 'group',
				'clone'      => true,
				'sort_clone' => true,
				'max_clone'  => 10,
				'add_button' => '+ Aggiungi Riga',
				'fields'     => [
					[
						'name'     => 'Colonna 1',
						'id'       => 'column1',
						'type'     => 'textarea',
						'desc'     => 'Contenuto per la prima colonna (supporta Markdown).',
						'markdown' => true,
					],
					[
						'name'     => 'Colonna 2',
						'id'       => 'column2',
						'type'     => 'textarea',
						'desc'     => 'Contenuto per la seconda colonna (supporta Markdown).',
						'markdown' => true,
					],
				],
			],
		],
	];

	return $meta_boxes;
}
add_filter( 'rwmb_meta_boxes', 'custom_theme_register_block_fields' );
 

/**
 * Enqueue all Meta Box block assets for both frontend and editor
 */
function custom_theme_enqueue_all_mb_block_assets() {
    // Array di configurazione per i blocchi e i loro asset
    $block_assets = [
        'meta-box/loghi' => [
            'dependencies' => [
                'swiper' => [
                    'css' => '/assets/css/vendor/swiper.css',
                    'js' => '/assets/js/vendor/swiper.js'
                ]
            ],
            'css' => '/blocks/loghi/loghi.css',
            'js' => '/blocks/loghi/loghi.js',
            'js_deps' => ['swiper']
        ],
        'meta-box/outline' => [
            'css' => '/blocks/outline/outline.css',
            'js' => '/blocks/outline/outline.js'
        ],
        'meta-box/stats' => [
            'css' => '/blocks/stats/stats.css'
        ],
        'meta-box/stats-icon' => [
            'css' => '/blocks/stats-icon/stats-icon.css'
        ],
        'meta-box/stats-text' => [
            'css' => '/blocks/stats-text/stats-text.css'
        ],
        'meta-box/subtitle' => [
            'css' => '/blocks/subtitle/subtitle.css'
        ],
        'meta-box/pretitle' => [
            'css' => '/blocks/pretitle/pretitle.css'
        ],
        'meta-box/author' => [
            'css' => '/blocks/author/author.css'
        ],
        'meta-box/cta' => [
            'css' => '/blocks/cta/cta.css',
        ],
        'meta-box/box-image' => [
            'css' => '/blocks/box-image/box-image.css'
        ],
        'meta-box/video' => [
            'css' => '/blocks/video/video.css',
            'js' => '/blocks/video/video.js'
        ],
        'meta-box/box-row' => [
            'css' => '/blocks/box-row/box-row.css'
        ],
        'meta-box/box-column' => [
            'css' => '/blocks/box-column/box-column.css'
        ],
        'meta-box/icon-text' => [
            'css' => '/blocks/icon-text/icon-text.css'
        ],
        'meta-box/gallery-slider' => [
            'dependencies' => [
                'swiper' => [
                    'css' => '/assets/css/vendor/swiper.css',
                    'js' => '/assets/js/vendor/swiper.js'
                ]
            ],
            'css' => '/blocks/gallery-slider/gallery-slider.css',
            'js' => '/blocks/gallery-slider/gallery-slider.js',
            'js_deps' => ['swiper']
        ],
        'meta-box/quote' => [
            'css' => '/blocks/quote/quote.css'
        ],
        'meta-box/faq' => [
            'css' => '/blocks/faq/faq.css',
            'js' => '/blocks/faq/faq.js'
        ],
        'meta-box/button' => [
            'css' => '/blocks/button/button.css'
        ],
        'meta-box/video-reviews' => [
            'dependencies' => [
                'swiper' => [
                    'css' => '/assets/css/vendor/swiper.css',
                    'js' => '/assets/js/vendor/swiper.js'
                ]
            ],
            'css' => '/blocks/video-reviews/video-reviews.css',
            'js' => '/blocks/video-reviews/video-reviews.js',
            'js_deps' => ['swiper']
        ],
        'meta-box/image-reviews' => [
            'dependencies' => [
                'swiper' => [
                    'css' => '/assets/css/vendor/swiper.css',
                    'js' => '/assets/js/vendor/swiper.js'
                ]
            ],
            'css' => '/blocks/image-reviews/image-reviews.css',
            'js' => '/blocks/image-reviews/image-reviews.js',
            'js_deps' => ['swiper']
        ],
        'meta-box/box-reviews' => [
            'dependencies' => [
                'swiper' => [
                    'css' => '/assets/css/vendor/swiper.css',
                    'js' => '/assets/js/vendor/swiper.js'
                ]
            ],
            'css' => '/blocks/box-reviews/box-reviews.css',
            'js' => '/blocks/box-reviews/box-reviews.js',
            'js_deps' => ['swiper']
        ],
        'meta-box/video-slider' => [
            'dependencies' => [
                'swiper' => [
                    'css' => '/assets/css/vendor/swiper.css',
                    'js' => '/assets/js/vendor/swiper.js'
                ]
            ],
            'css' => '/blocks/video-slider/video-slider.css',
            'js' => '/blocks/video-slider/video-slider.js',
            'js_deps' => ['swiper']
        ],
        'meta-box/line-text' => [
            'css' => '/blocks/line-text/line-text.css'
        ],
        'meta-box/gallery' => [
            'css' => '/blocks/gallery/gallery.css',
            'js' => '/blocks/gallery/gallery.js'
        ],
        'meta-box/roadmap' => [
            'css' => '/blocks/roadmap/roadmap.css'
        ],
        'meta-box/checklist' => [
            'css' => '/blocks/checklist/checklist.css'
        ],
        'meta-box/ticket' => [
            'css' => '/blocks/ticket/ticket.css'
        ],
        'meta-box/card' => [
            'css' => '/blocks/card/card.css'
        ],
        'meta-box/block-list' => [
            'css' => '/blocks/block-list/block-list.css'
        ],
        'meta-box/comparison' => [
            'css' => '/blocks/comparison/comparison.css'
        ],
    ];
    
    // Determina se stiamo nell'editor o nel frontend
    $is_editor = is_admin() && function_exists('get_current_screen') && get_current_screen() && get_current_screen()->is_block_editor();
    
    if ( $is_editor ) {
        // Nell'editor, carica sempre tutti gli asset
        foreach ( $block_assets as $block_name => $assets ) {
            $block_slug = str_replace( 'meta-box/', '', $block_name );
            
            // Carica prima le dipendenze
            if ( ! empty( $assets['dependencies'] ) ) {
                foreach ( $assets['dependencies'] as $dep_name => $dep_files ) {
                    if ( ! empty( $dep_files['css'] ) && ! wp_style_is( $dep_name, 'enqueued' ) ) {
                        $css_path = get_template_directory() . $dep_files['css'];
                        if ( file_exists( $css_path ) ) {
                            wp_enqueue_style( 
                                $dep_name, 
                                get_template_directory_uri() . $dep_files['css'],
                                [],
                                filemtime( $css_path )
                            );
                        }
                    }
                    if ( ! empty( $dep_files['js'] ) && ! wp_script_is( $dep_name, 'enqueued' ) ) {
                        $js_path = get_template_directory() . $dep_files['js'];
                        if ( file_exists( $js_path ) ) {
                            wp_enqueue_script( 
                                $dep_name, 
                                get_template_directory_uri() . $dep_files['js'],
                                [],
                                filemtime( $js_path ),
                                true
                            );
                        }
                    }
                }
            }
            
            // Carica CSS del blocco
            if ( ! empty( $assets['css'] ) ) {
                $css_path = get_template_directory() . $assets['css'];
                if ( file_exists( $css_path ) ) {
                    wp_enqueue_style( 
                        'block-' . $block_slug, 
                        get_template_directory_uri() . $assets['css'],
                        [],
                        filemtime( $css_path )
                    );
                }
            }
            
            // Carica JS del blocco
            if ( ! empty( $assets['js'] ) ) {
                $js_path = get_template_directory() . $assets['js'];
                if ( file_exists( $js_path ) ) {
                    $js_deps = ! empty( $assets['js_deps'] ) ? $assets['js_deps'] : [];
                    wp_enqueue_script( 
                        'block-' . $block_slug, 
                        get_template_directory_uri() . $assets['js'],
                        $js_deps,
                        filemtime( $js_path ),
                        true
                    );
                }
            }
        }
    } else {
        // Nel frontend, carica solo se i blocchi sono presenti
        $post = get_post();
        if ( ! $post ) {
            return;
        }
        
        foreach ( $block_assets as $block_name => $assets ) {
            if ( has_block( $block_name, $post ) ) {
                $block_slug = str_replace( 'meta-box/', '', $block_name );
                
                // Carica prima le dipendenze
                if ( ! empty( $assets['dependencies'] ) ) {
                    foreach ( $assets['dependencies'] as $dep_name => $dep_files ) {
                        if ( ! empty( $dep_files['css'] ) && ! wp_style_is( $dep_name, 'enqueued' ) ) {
                            $css_path = get_template_directory() . $dep_files['css'];
                            if ( file_exists( $css_path ) ) {
                                wp_enqueue_style( 
                                    $dep_name, 
                                    get_template_directory_uri() . $dep_files['css'],
                                    [],
                                    filemtime( $css_path )
                                );
                            }
                        }
                        if ( ! empty( $dep_files['js'] ) && ! wp_script_is( $dep_name, 'enqueued' ) ) {
                            $js_path = get_template_directory() . $dep_files['js'];
                            if ( file_exists( $js_path ) ) {
                                wp_enqueue_script( 
                                    $dep_name, 
                                    get_template_directory_uri() . $dep_files['js'],
                                    [],
                                    filemtime( $js_path ),
                                    true
                                );
                            }
                        }
                    }
                }
                
                // Carica CSS del blocco
                if ( ! empty( $assets['css'] ) ) {
                    $css_path = get_template_directory() . $assets['css'];
                    if ( file_exists( $css_path ) ) {
                        wp_enqueue_style( 
                            'block-' . $block_slug, 
                            get_template_directory_uri() . $assets['css'],
                            [],
                            filemtime( $css_path )
                        );
                    }
                }
                
                // Carica JS del blocco
                if ( ! empty( $assets['js'] ) ) {
                    $js_path = get_template_directory() . $assets['js'];
                    if ( file_exists( $js_path ) ) {
                        $js_deps = ! empty( $assets['js_deps'] ) ? $assets['js_deps'] : [];
                        wp_enqueue_script( 
                            'block-' . $block_slug, 
                            get_template_directory_uri() . $assets['js'],
                            $js_deps,
                            filemtime( $js_path ),
                            true
                        );
                    }
                }
            }
        }
    }
}
// Hook per frontend e editor
add_action( 'wp_enqueue_scripts', 'custom_theme_enqueue_all_mb_block_assets' );
add_action( 'enqueue_block_editor_assets', 'custom_theme_enqueue_all_mb_block_assets' );

/**
 * Enqueue scripts and styles for the block editor (additional assets)
 */
function custom_theme_enqueue_block_editor_assets() {
    $theme_dir = get_template_directory();
    $theme_uri = get_template_directory_uri();

    // Enqueue Gutenberg styles for editor
    $gutenberg_css_file = '/assets/css/sections/gutenberg.css';
    if ( file_exists( $theme_dir . $gutenberg_css_file ) ) {
        wp_enqueue_style(
            'custom-theme-gutenberg-editor',
            $theme_uri . $gutenberg_css_file,
            [],
            filemtime( $theme_dir . $gutenberg_css_file )
        );
    }

    // Enqueue framework styles
    $css_file = '/assets/css/general/framework.css';
    if ( file_exists( $theme_dir . $css_file ) ) {
        wp_enqueue_style(
            'custom-theme-framework-editor',
            $theme_uri . $css_file,
            [],
            filemtime( $theme_dir . $css_file )
        );
    }

    // Enqueue fonts
    $css_file = '/assets/css/general/fonts.css';
    if ( file_exists( $theme_dir . $css_file ) ) {
        wp_enqueue_style(
            'custom-theme-fonts-editor',
            $theme_uri . $css_file,
            [],
            filemtime( $theme_dir . $css_file )
        );
    }

    // Enqueue customisation styles
    $css_file = '/assets/css/general/customisation.css';
    if ( file_exists( $theme_dir . $css_file ) ) {
        wp_enqueue_style(
            'custom-theme-customisation-editor',
            $theme_uri . $css_file,
            [],
            filemtime( $theme_dir . $css_file )
        );
    }
}
add_action( 'enqueue_block_editor_assets', 'custom_theme_enqueue_block_editor_assets' );

 

/**
 * Get safe block wrapper attributes
 * 
 * Questa funzione gestisce gli attributi del wrapper sia per il frontend
 * (tramite il hook render_block) che per l'editor (tramite get_block_wrapper_attributes)
 * 
 * @param array $extra_attrs Extra attributes to add
 * @param string $block_name Block name for fallback class
 * @return string HTML attributes string
 */
function get_safe_block_wrapper_attributes( $extra_attrs = [], $block_name = '' ) {
    global $mb_current_block_data, $mb_current_block_attrs;
    
    // Se siamo nel rendering tramite hook (frontend e colonne), crea gli attributi manualmente
    if ( isset( $mb_current_block_data ) ) {
        $classes = [];
        
        // Aggiungi le classi extra
        if ( ! empty( $extra_attrs['class'] ) ) {
            $classes[] = $extra_attrs['class'];
        }
        
        // Aggiungi la classe del blocco WordPress standard
        if ( $block_name ) {
            $classes[] = 'wp-block-meta-box-' . $block_name;
        }
        
        // Aggiungi eventuali classi dagli attributi del blocco
        if ( ! empty( $mb_current_block_attrs['className'] ) ) {
            $classes[] = $mb_current_block_attrs['className'];
        }
        
        // Costruisci la stringa degli attributi
        $attrs = [];
        
        // Classi
        if ( ! empty( $classes ) ) {
            $attrs[] = 'class="' . esc_attr( implode( ' ', $classes ) ) . '"';
        }
        
        // ID personalizzato
        if ( ! empty( $mb_current_block_attrs['anchor'] ) ) {
            $attrs[] = 'id="' . esc_attr( $mb_current_block_attrs['anchor'] ) . '"';
        }
        
        // Attributi di allineamento
        if ( ! empty( $mb_current_block_attrs['align'] ) ) {
            $align_class = 'align' . $mb_current_block_attrs['align'];
            $attrs[0] = str_replace( '"', ' ' . esc_attr( $align_class ) . '"', $attrs[0] );
        }
        
        return implode( ' ', $attrs );
    } else {
        // Siamo nell'editor, usa la funzione standard di WordPress
        if ( function_exists( 'get_block_wrapper_attributes' ) ) {
            return get_block_wrapper_attributes( $extra_attrs );
        } else {
            // Fallback per versioni di WordPress più vecchie
            $class = ! empty( $extra_attrs['class'] ) ? $extra_attrs['class'] : '';
            if ( $block_name ) {
                $class .= ' wp-block-meta-box-' . $block_name;
            }
            return 'class="' . esc_attr( trim( $class ) ) . '"';
        }
    }
}

/**
 * Hook globale per intercettare il rendering di tutti i blocchi Meta Box
 * Questo risolve il problema dei blocchi nested (es. dentro le colonne)
 */
function custom_theme_render_meta_box_blocks( $block_content, $block ) {
    // Lista dei blocchi Meta Box da gestire
    $meta_box_blocks = [
        'meta-box/box-image',
        'meta-box/cta', 
        'meta-box/pretitle',
        'meta-box/subtitle',
        'meta-box/author',
        'meta-box/loghi',
        'meta-box/stats',
        'meta-box/stats-text',
        'meta-box/stats-icon',
        'meta-box/outline',
        'meta-box/video',
        'meta-box/box-row',
        'meta-box/box-column',
        'meta-box/icon-text',
        'meta-box/gallery-slider',
        'meta-box/quote',
        'meta-box/faq',
        'meta-box/button',
        'meta-box/video-reviews',
        'meta-box/image-reviews',
        'meta-box/box-reviews',
        'meta-box/video-slider',
        'meta-box/line-text',
        'meta-box/gallery',
        'meta-box/roadmap',
        'meta-box/checklist',
        'meta-box/ticket',
        'meta-box/card',
        'meta-box/block-list',
    ];
    
    // Se non è un blocco Meta Box che gestiamo, restituisci il contenuto originale
    if ( ! in_array( $block['blockName'], $meta_box_blocks ) ) {
        return $block_content;
    }
    
    // Se non ci sono dati, restituisci il contenuto originale
    if ( ! isset( $block['attrs']['data'] ) ) {
        return $block_content;
    }
    
    // Estrai il nome del blocco senza il prefisso
    $block_name = str_replace( 'meta-box/', '', $block['blockName'] );
    
    // Costruisci il percorso del template
    $template_path = get_template_directory() . '/blocks/' . $block_name . '/' . $block_name . '.php';
    
    // Se il template non esiste, restituisci il contenuto originale
    if ( ! file_exists( $template_path ) ) {
        return $block_content;
    }
    
    // Salva le variabili globali attuali (se esistono)
    global $mb_current_block_data, $mb_current_block_attrs;
    $old_block_data = $mb_current_block_data ?? null;
    $old_block_attrs = $mb_current_block_attrs ?? null;
    
    // Imposta i dati globalmente per questo specifico blocco
    $mb_current_block_data = $block['attrs']['data'];
    $mb_current_block_attrs = $block['attrs'];
    
    // Disabilita temporaneamente gli errori per get_block_wrapper_attributes
    $error_level = error_reporting();
    error_reporting( $error_level & ~E_WARNING );
    
    // Cattura l'output del template
    ob_start();
    include $template_path;
    $custom_content = ob_get_clean();
    
    // Ripristina il livello di errore
    error_reporting( $error_level );
    
    // Ripristina le variabili globali precedenti
    $mb_current_block_data = $old_block_data;
    $mb_current_block_attrs = $old_block_attrs;
    
    return $custom_content;
}
add_filter( 'render_block', 'custom_theme_render_meta_box_blocks', 10, 2 );