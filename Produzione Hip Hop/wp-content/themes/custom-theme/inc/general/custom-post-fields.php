


<?php 
add_filter( 'rwmb_meta_boxes', 'corso_fields' );

function corso_fields( $meta_boxes ) {
    $prefix = '';

    $meta_boxes[] = [
        'title'      => __( 'Corsi fields', 'custom-theme' ),
        'id'         => 'corsi-fields',
        'modified'   => 1749538880,
        'post_types' => ['corsi'],
        'closed'     => false,
        'revision'   => false,
        'fields'     => [
            [
                'name'            => __( 'Cover', 'custom-theme' ),
                'id'              => $prefix . 'corso_cover',
                'type'            => 'single_image',
                'image_size'      => 'large',
                'hide_from_rest'  => false,
                'hide_from_front' => false,
            ],
            [
                'name'            => __( 'Durata', 'custom-theme' ),
                'id'              => $prefix . 'corso_durata',
                'type'            => 'text',
                'hide_from_rest'  => false,
                'hide_from_front' => false,
                'limit_type'      => 'character',
            ],
            [
                'name'            => __( 'Breve descrizione', 'custom-theme' ),
                'id'              => $prefix . 'corso_descrizione',
                'type'            => 'text',
                'hide_from_rest'  => false,
                'hide_from_front' => false,
                'limit'           => 140,
                'limit_type'      => 'character',
            ], 
            [
                'name'            => __( 'Icona corso', 'custom-theme' ),
                'id'              => $prefix . 'corso_icona',
                'type'            => 'single_image',
                'hide_from_rest'  => false,
                'hide_from_front' => false,
            ],

            [
                'name'            => __( 'Colore', 'custom-theme' ),
                'id'              => $prefix . 'corso_colore',
                'type'            => 'select',
                'options'         => [
                    'violet'  => __( 'viola', 'custom-theme' ),
'pink'    => __( 'rosa', 'custom-theme' ),
'sky'     => __( 'azzurro', 'custom-theme' ),
'blue'    => __( 'blu', 'custom-theme' ),
'ocean'   => __( 'oceano', 'custom-theme' ),
'red'     => __( 'rosso', 'custom-theme' ),
'green'   => __( 'verde', 'custom-theme' ),
'brown'   => __( 'marrone', 'custom-theme' ),
'yellow'  => __( 'giallo', 'custom-theme' ),
                ],
                'select_all_none' => false,
                'hide_from_rest'  => false,
                'hide_from_front' => false,
            ],
            [
                'name'  => __( 'Boxes', 'custom-theme' ),
                'id'    => $prefix . 'corso_boxes',
                'type'  => 'group',
                'clone' => true,
                'sort_clone' => true,
                'fields' => [
                    [
                        'name' => __( 'Icona', 'custom-theme' ),
                        'id'   => $prefix . 'box_icona',
                        'type' => 'single_image',
                    ],
                    [
                        'name' => __( 'Titolo', 'custom-theme' ),
                        'id'   => $prefix . 'box_titolo',
                        'type' => 'text',
                    ],
                    [
                        'name'    => __( 'Colore', 'custom-theme' ),
                        'id'      => $prefix . 'box_colore',
                        'type'    => 'select',
                        'options' => [
                            'brown'  => __( 'marrone', 'custom-theme' ),
                            'green'  => __( 'verde', 'custom-theme' ),
                            'yellow' => __( 'giallo', 'custom-theme' ),
                            'red'    => __( 'rosso', 'custom-theme' ),
                            'ocean'  => __( 'oceano', 'custom-theme' ),
                            'blue'   => __( 'blu', 'custom-theme' ),
                            'sky'    => __( 'azzurro', 'custom-theme' ),
                            'pink'   => __( 'rosa', 'custom-theme' ),
                            'violet' => __( 'viola', 'custom-theme' ),
                        ],
                    ],
                    [
                        'name' => __( 'Immagine di sfondo', 'custom-theme' ),
                        'id'   => $prefix . 'box_immagine_sfondo',
                        'type' => 'single_image',
                    ],
                    [
                        'name'  => __( 'Checklist', 'custom-theme' ),
                        'id'    => $prefix . 'box_checklist',
                        'type'  => 'group',
                        'clone' => true,
                        'sort_clone' => true,
                        'fields' => [
                            [
                                'name' => __( 'Testo', 'custom-theme' ),
                                'id'   => $prefix . 'checklist_testo',
                                'type' => 'text',
                            ],
                            [
                                'name'    => __( 'Helper modale', 'custom-theme' ),
                                'id'      => $prefix . 'checklist_helper',
                                'type'    => 'wysiwyg',
                                'raw'     => false,
                                'options' => [
                                    'textarea_rows' => 2,
                                    'teeny'         => true,
                                    'media_buttons' => false,
                                ],
                            ],
                        ],
                    ],
                ],
            ],
        ],
    ];

    return $meta_boxes;
}