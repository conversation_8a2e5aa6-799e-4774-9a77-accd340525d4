<?php
/**
 * Block Checklist Template
 * 
 * @package Custom_Theme
 */

// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $items = $mb_current_block_data['items'] ?? [];
} else {
    // Siamo nell'editor, usa mb_get_block_field()
    $items = [];
    if ( function_exists( 'mb_get_block_field' ) ) {
        $items = mb_get_block_field( 'items' );
    }
}

// Se non ci sono items, non mostrare nulla.
if ( empty( $items ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block Checklist</div><div class="components-placeholder__instructions">Aggiungi elementi per creare la checklist.</div></div>';
    }
    return;
}

// Recupera il colore dinamico dal CPT "Corsi"
$color_class_name = get_current_corso_color_class_name();

// Mappa dei colori (come nel single-corsi.php)
$color_map = [
    'blue' => '#1289ED',
    'sky' => '#56CCF2', 
    'pink' => '#E04E9D',
    'violet' => '#A973FF',
    'brown' => '#9D8271',
    'green' => '#25D366',
    'yellow' => '#FFBB44',
    'red' => '#FF4444',
    'ocean' => '#194DD4'
];

// Usa la funzione helper per gli attributi del wrapper
$wrapper_attributes = get_safe_block_wrapper_attributes(
    [ 'class' => 'block-checklist mt-60 mb-60' ],
    'checklist'
);

?>

<div <?php echo $wrapper_attributes; ?>>
    <div class="container">
        <div class="items flex flex-col gap-20">
            <?php foreach ( $items as $item ) : ?>
                <?php 
                $text = $item['text'] ?? '';
                
                if ( empty( $text ) ) {
                    continue;
                }
                ?>
                <div class="item flex items-center gap-40">
                    <div class="icon bg-brand-<?php echo esc_attr( $color_class_name ); ?>-medium rounded-12 h-80 w-80 place-center flex bg-opacity-60 shrink-0">
                        <svg class="h-50-100" width="36" height="36" viewBox="0 0 36 36" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <rect x="1.33978" y="1.33978" width="33.0479" height="33.0479" rx="7.59208" stroke="#192329" stroke-width="2.67956"/>
                            <path d="M11.168 19.6494L15.5225 22.999C16.3181 23.611 17.4617 23.4475 18.0539 22.6371L25.4589 12.5039" stroke="<?php echo esc_attr( $color_map[ $color_class_name ] ); ?>" stroke-width="3.57274" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                    </div>
                    <div class="text font-ave-d text-22 leading-15">
                        <?php echo wp_kses_post( $text ); ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div> 