/* Block Checklist Styles */

.block-checklist .container {
    /* Container styles inherited */
}

.block-checklist .items {
    /* Items container styles */
}

.block-checklist .item {
    /* Individual item styles */
}

.block-checklist .icon {
    /* Icon container styles */
    transition: all 0.3s ease;
}

.block-checklist .icon:hover {
    transform: scale(1.05);
}

.block-checklist .icon svg {
    max-width: none; /* Override any global max-width constraints */
    flex-shrink: 0;
}

.block-checklist .text {
    /* Text content styles */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .block-checklist .items {
        gap: 16px;
    }
    
    .block-checklist .item {
        gap: 32px;
    }
    
    .block-checklist .icon {
        width: 60px;
        height: 60px;
    }
    
    .block-checklist .text {
        font-size: 20px;
        line-height: 1.4;
    }
}

@media (max-width: 480px) {
    .block-checklist .items {
        gap: 12px;
    }
    
    .block-checklist .item {
        gap: 24px;
    }
    
    .block-checklist .icon {
        width: 50px;
        height: 50px;
    }
    
    .block-checklist .text {
        font-size: 18px;
        line-height: 1.4;
    }
} 