/* Box Row Block Styles */

.block-box-row .container {
    /* Container styles are inherited from the HTML structure */
}

.block-box-row .side {
    /* Icon container specific styles if needed */
}

.block-box-row .icon {
    /* Ensure proper icon sizing and positioning */
}

.block-box-row .icon img,
.block-box-row .icon svg {
    max-width: none; /* Override any global max-width constraints */
    flex-shrink: 0;
}

.block-box-row .main {
    /* Content area specific styles if needed */
    flex: 1;
}

.block-box-row .title {
    /* Title specific styles if needed */
}

.block-box-row .text {
    /* Text specific styles if needed */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .block-box-row .container {
        flex-direction: column;
        text-align: center;
        gap: 40px;
    }
    
    .block-box-row .side {
        flex-shrink: initial;
    }
}

@media (max-width: 480px) {
    .block-box-row .container {
        padding: 40px;
        gap: 32px;
    }
} 