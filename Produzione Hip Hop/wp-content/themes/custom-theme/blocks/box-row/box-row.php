<?php
/**
 * Block Box Row Template
 * 
 * @package Custom_Theme
 */

// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $icon_id = $mb_current_block_data['icon'] ?? 0;
    $title = $mb_current_block_data['title'] ?? '';
    $text = $mb_current_block_data['text'] ?? '';
} else {
    // Siamo nell'editor, usa mb_get_block_field()
    $icon_data = mb_get_block_field( 'icon' );
    
    $icon_id = ! empty( $icon_data['ID'] ) ? (int) $icon_data['ID'] : 0;
    $title = mb_get_block_field( 'title' );
    $text = mb_get_block_field( 'text' );
}

// Se tutti i campi sono vuoti, non mostrare nulla.
if ( empty( $icon_id ) && empty( $title ) && empty( $text ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block Box Row</div><div class="components-placeholder__instructions">Aggiungi contenuto per visualizzare il blocco.</div></div>';
    }
    return;
}

// Recupera il colore dinamico dal CPT "Corsi"
$color_class_name = get_current_corso_color_class_name();

// Usa la funzione helper per gli attributi del wrapper
$wrapper_attributes = get_safe_block_wrapper_attributes(
    [ 'class' => 'block-box-row mt-20 mb-20' ],
    'box-row'
);

?>

<div <?php echo $wrapper_attributes; ?>>
    <div class="container flex gap-80 items-center border-2 border-solid border-gray rounded-24 p-60">
        
        <?php if ( $icon_id ) : ?>
            <div class="side shrink-0">
                <div class="icon flex">
                    <?php load_img( class: 'img h-48', high: $icon_id ); ?>
                </div>
            </div>
        <?php endif; ?>

        <div class="main">
            <?php if ( ! empty( $title ) ) : ?>
                <div class="title uppercase font-tt-eb text-16 mb-20 spacing-12 text-brand-<?php echo esc_attr( $color_class_name ); ?>">
                    <?php echo esc_html( $title ); ?>
                </div>
            <?php endif; ?>
            
            <?php if ( ! empty( $text ) ) : ?>
                <div class="text font-ave-m text-20 leading-15 text-gray">
                    <?php echo wp_kses_post( $text ); ?>
                </div>
            <?php endif; ?>
        </div>
        
    </div>
</div> 


