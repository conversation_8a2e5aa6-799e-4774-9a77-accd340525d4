(function ($) {
    'use strict';

    // La funzione che inizializza un singolo player
    function initPlayer(blockContainer) {
        const $container = $(blockContainer);
        const $cover = $container.find('.cover');
        const $player = $container.find('.player');
        const videoId = $container.data('videoId');
        const videoHash = $container.data('videoHash');

        // Se non abbiamo gli elementi necessari o lo script è già stato inizializzato, ci fermiamo.
        if (!$cover.length || !$player.length || !videoId || $cover.data('initialized')) {
            return;
        }

        // Gestione del click
        $cover.on('click', function () {
            // Costruiamo l'URL dell'embed di Vimeo
            let videoSrc = `https://player.vimeo.com/video/${videoId}?autoplay=1&autopause=0&dnt=1`;
            if (videoHash) {
                videoSrc += `&h=${videoHash}`;
            }

            // Creiamo l'iframe
            const $iframe = $('<iframe>', {
                src: videoSrc,
                width: '100%',
                height: '100%',
                frameborder: 0,
                allow: 'autoplay; fullscreen; picture-in-picture',
                allowfullscreen: ''
            });

            // Sostituiamo il placeholder con l'iframe e nascondiamo la copertina
            $player.html($iframe);
            $cover.hide();
        });

        // Marchiamo l'elemento come inizializzato per evitare doppi eventi
        $cover.data('initialized', true);
    }

    // Inizializzazione per il frontend (al caricamento della pagina)
    $(document).ready(function () {
        $('.block-video .vimeo-player-container[data-video-id]').each(function () {
            initPlayer(this);
        });
    });

    // Inizializzazione per l'editor di Gutenberg (al refresh dell'anteprima del blocco)
    $(document).on('mb_blocks_preview/video', function (e) {
        const $blockWrapper = $(e.target);
        const $container = $blockWrapper.find('.vimeo-player-container[data-video-id]');
        if ($container.length) {
            initPlayer($container.get(0));
        }
    });

})(jQuery); 