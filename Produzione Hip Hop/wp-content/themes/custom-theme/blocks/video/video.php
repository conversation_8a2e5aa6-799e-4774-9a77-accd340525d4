<?php
// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $video_url = $mb_current_block_data['video_url'] ?? '';
    $aspect_ratio = $mb_current_block_data['aspect_ratio'] ?? 'ratio-16-9';
    $video_title = $mb_current_block_data['video_title'] ?? '';
    $video_author = $mb_current_block_data['video_author'] ?? '';
    $video_views_number = $mb_current_block_data['video_views_number'] ?? '';
    $video_views_unit = $mb_current_block_data['video_views_unit'] ?? '';
} else {
    // Siamo nell'editor
    $video_url = mb_get_block_field( 'video_url' );
    $aspect_ratio = mb_get_block_field( 'aspect_ratio' ) ?: 'ratio-16-9';
    $video_title = mb_get_block_field( 'video_title' );
    $video_author = mb_get_block_field( 'video_author' );
    $video_views_number = mb_get_block_field( 'video_views_number' );
    $video_views_unit = mb_get_block_field( 'video_views_unit' );
}

// Get video data from our helper function.
$video_data = custom_theme_get_video_data($video_url);

// Usa la funzione helper per gli attributi del wrapper
$wrapper_attrs = get_safe_block_wrapper_attributes(
    [ 'class' => 'block-video mb-80' ],
    'video'
);
?> 

<div <?php echo $wrapper_attrs; ?>>
    <?php if ( ! empty( $video_data['source'] ) ) : ?>
        <div class="container">
            
            <?php // --- Vimeo Player --- ?>
            <?php if ( $video_data['source'] === 'vimeo' ) : ?>
                <div
                    class="vimeo-player-container relative"
                    data-video-id="<?php echo esc_attr( $video_data['video_id'] ); ?>"
                    data-video-hash="<?php echo esc_attr( $video_data['hash'] ); ?>"
                >
                    <div class="player <?php echo esc_attr( $aspect_ratio ); ?> bg-brand-dark rounded-24 overflow-hidden relative shadow-24">
                        <!-- Vimeo player will be loaded here by JavaScript -->
                    </div>
                    <div class="cover absolute inset-0 z-30 rounded-24 overflow-hidden cursor-pointer">
                        <img class="img img-cover" src="<?php echo esc_url( $video_data['thumbnail_url'] ); ?>" alt="Video thumbnail">
                        <div class="play h-120 absolute-center flex place-center z-60 rounded-full w-120">
                            <div class="icon">
                                <svg class="h-50-100" width="51" height="60" viewBox="0 0 51 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M48.0174 27.6454L17.8589 3.836C15.8916 2.28285 13 3.68412 13 6.19065V53.8094C13 56.3159 15.8916 57.7172 17.8589 56.164L48.0174 32.3546C49.5389 31.1535 49.5389 28.8465 48.0174 27.6454Z" fill="white"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                </div>

            <?php // --- YouTube Link --- ?>
            <?php elseif ( $video_data['source'] === 'youtube' ) : ?>
                <a href="<?php echo esc_url( $video_url ); ?>" target="_blank" rel="noopener noreferrer" class="youtube-link relative">
                    <div class="player <?php echo esc_attr( $aspect_ratio ); ?> bg-brand-dark rounded-24 overflow-hidden relative">
                        <img class="img img-cover" src="<?php echo esc_url( $video_data['thumbnail_url'] ); ?>" alt="Video thumbnail">
                    </div>
                    <div class="cover absolute inset-0 z-30 rounded-24 overflow-hidden cursor-pointer">
                        <div class="play h-120 absolute-center flex place-center z-60 rounded-full w-120">
                            <div class="icon">
                            <svg class="h-50-100" width="69" height="52" viewBox="0 0 69 52" fill="none" xmlns="http://www.w3.org/2000/svg">
<path d="M68.6146 11.4358C68.6146 10.8339 67.7121 6.01883 65.6055 3.91223C62.8969 0.90283 59.8875 0.601887 58.3826 0.601887H58.0818C48.7526 -1.78814e-07 34.9092 0 34.6083 0C34.6083 0 20.464 -1.78814e-07 11.1348 0.601887H10.8339C9.32921 0.601887 6.31978 0.90283 3.61132 3.91223C1.50471 6.31978 0.601883 11.1348 0.601883 11.7367C0.601883 12.0377 0 17.4546 0 23.1725V28.2886C0 34.0063 0.601883 39.4235 0.601883 39.7243C0.601883 40.3264 1.50471 45.1412 3.61132 47.2477C6.01883 49.9563 9.02827 50.2572 10.8339 50.5584C11.1348 50.5584 11.4358 50.5584 11.7367 50.5584C17.1537 51.16 33.7055 51.16 34.3074 51.16C34.3074 51.16 48.4518 51.16 57.7809 50.5584H58.0818C59.5867 50.2572 62.5961 49.9563 65.3043 47.2477C67.4109 44.8403 68.3138 40.0252 68.3138 39.4235C68.3138 39.1223 68.9158 33.7055 68.9158 27.9878V22.8716C69.2166 17.4546 68.6146 11.7367 68.6146 11.4358Z" fill="white"/>
<path d="M68.6146 11.4358C68.6146 10.8339 67.7121 6.01883 65.6055 3.91223C62.8969 0.90283 59.8875 0.601887 58.3826 0.601887H58.0818C48.7526 -1.78814e-07 34.9092 0 34.6083 0C34.6083 0 20.464 -1.78814e-07 11.1348 0.601887H10.8339C9.32921 0.601887 6.31978 0.90283 3.61132 3.91223C1.50471 6.31978 0.601883 11.1348 0.601883 11.7367C0.601883 12.0377 0 17.4546 0 23.1725V28.2886C0 34.0063 0.601883 39.4235 0.601883 39.7243C0.601883 40.3264 1.50471 45.1412 3.61132 47.2477C6.01883 49.9563 9.02827 50.2572 10.8339 50.5584C11.1348 50.5584 11.4358 50.5584 11.7367 50.5584C17.1537 51.16 33.7055 51.16 34.3074 51.16C34.3074 51.16 48.4518 51.16 57.7809 50.5584H58.0818C59.5867 50.2572 62.5961 49.9563 65.3043 47.2477C67.4109 44.8403 68.3138 40.0252 68.3138 39.4235C68.3138 39.1223 68.9158 33.7055 68.9158 27.9878V22.8716C69.2166 17.4546 68.6146 11.7367 68.6146 11.4358ZM46.3452 26.182L28.2885 35.812C27.9876 35.812 27.9876 36.1132 27.6867 36.1132C27.3857 36.1132 27.0848 36.1132 27.0848 35.812C26.7838 35.5112 26.4829 35.2103 26.4829 34.6083V15.0471C26.4829 14.4452 26.7838 14.1443 27.0848 13.8433C27.3857 13.5424 27.9876 13.5424 28.5895 13.8433L46.6461 23.4735C47.2477 23.7744 47.5489 24.0754 47.5489 24.6772C47.5489 25.2792 46.9469 25.8809 46.3452 26.182Z" fill="#FF0000"/>
</svg>

                            </div>
                        </div>
                    </div>
                </a>
            <?php endif; ?>

            <?php 
            // Mostra le informazioni del video se almeno uno dei campi è compilato
            $has_video_info = ! empty( $video_title ) || ! empty( $video_author ) || ! empty( $video_views_number );
            if ( $has_video_info ) : 
            ?>
                <div class="info flex flex-col gap-20 mt-40">
                    <?php if ( ! empty( $video_title ) ) : ?>
                        <div class="title uppercase font-tt-eb text-16 spacing-12">
                            <?php echo esc_html( $video_title ); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ( ! empty( $video_author ) ) : ?>
                        <div class="author font-ave-d text-20 text-gray">
                            <?php echo esc_html( $video_author ); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ( ! empty( $video_views_number ) ) : ?>
                        <div class="stats flex items-center gap-12">
                            <div class="icon flex">
                                <svg class="h-14" width="19" height="13" viewBox="0 0 19 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M0 3.33333C0 1.49238 1.49238 0 3.33333 0H15C16.8409 0 18.3333 1.49238 18.3333 3.33333V9.16667C18.3333 11.0076 16.8409 12.5 15 12.5H3.33333C1.49238 12.5 0 11.0076 0 9.16667V3.33333ZM3.33333 1.66667C2.41286 1.66667 1.66667 2.41286 1.66667 3.33333V9.16667C1.66667 10.0872 2.41286 10.8333 3.33333 10.8333H15C15.9205 10.8333 16.6667 10.0872 16.6667 9.16667V3.33333C16.6667 2.41286 15.9205 1.66667 15 1.66667H3.33333Z" fill="#798B97"/>
                                    <path fill-rule="evenodd" clip-rule="evenodd" d="M7.11646 2.59418C7.39207 2.45069 7.72464 2.47245 7.97922 2.65064L12.1459 5.56733C12.3686 5.72325 12.5013 5.97808 12.5013 6.25C12.5013 6.52192 12.3686 6.77675 12.1459 6.93267L7.97922 9.84933C7.72464 10.0276 7.39207 10.0493 7.11646 9.90583C6.84085 9.76233 6.66797 9.47742 6.66797 9.16667V3.33333C6.66797 3.02261 6.84085 2.73768 7.11646 2.59418ZM8.33464 4.93392V7.56608L10.2148 6.25L8.33464 4.93392Z" fill="#798B97"/>
                                </svg>
                            </div>
                            <div class="number font-ave-d text-16 text-gray">
                                <?php echo esc_html( $video_views_number ); ?>
                            </div>
                            <?php if ( ! empty( $video_views_unit ) ) : ?>
                                <div class="unit font-ave-d text-16 text-gray">
                                    <?php echo esc_html( $video_views_unit ); ?>
                                </div>
                            <?php endif; ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>

        </div>
    <?php elseif ( is_admin() && ! isset( $mb_current_block_data ) ) : ?>
        <div class="components-placeholder">
            <div class="components-placeholder__label">Block Video</div>
            <div class="components-placeholder__instructions">
                Inserisci un URL Vimeo o YouTube valido nel campo "Video URL".
            </div>
        </div>
    <?php endif; ?>
</div>