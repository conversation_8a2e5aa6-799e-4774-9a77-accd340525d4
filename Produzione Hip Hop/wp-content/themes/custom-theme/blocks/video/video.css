


.block-video .vimeo-player-container .play {
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(4px);
    transition: all 0.3s ease-in-out;
}


.block-video .cover:before {
    content:"";
    inset:0;
    position:absolute;
    z-index:50;
    background: rgba(40,60,80,0.2);
    transition: all 0.3s ease-in-out;
}

.block-video .cover:hover:before {
    background: rgba(40,60,80,0.4);
}

/* Aspect Ratios */
.ratio-1-1 { aspect-ratio: 1 / 1; }
.ratio-3-2 { aspect-ratio: 3 / 2; }
.ratio-2-3 { aspect-ratio: 2 / 3; }
.ratio-4-3 { aspect-ratio: 4 / 3; }
.ratio-3-4 { aspect-ratio: 3 / 4; }
.ratio-4-5 { aspect-ratio: 4 / 5; }
.ratio-16-9 { aspect-ratio: 16 / 9; }
.ratio-21-9 { aspect-ratio: 21 / 9; }
.ratio-2-1 { aspect-ratio: 2 / 1; }
.ratio-3-1 { aspect-ratio: 3 / 1; }
.ratio-4-1 { aspect-ratio: 4 / 1; }
.ratio-9-16 { aspect-ratio: 9 / 16; }
.ratio-9-21 { aspect-ratio: 9 / 21; } 