<?php
/**
 * Block Box Reviews Template
 * 
 * @package Custom_Theme
 */

// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $reviews = $mb_current_block_data['reviews'] ?? [];
} else {
    // Siamo nell'editor, usa mb_get_block_field()
    $reviews = [];
    if ( function_exists( 'mb_get_block_field' ) ) {
        $reviews = mb_get_block_field( 'reviews' );
    }
}

// Se non ci sono reviews, non mostrare nulla.
if ( empty( $reviews ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block Box Reviews</div><div class="components-placeholder__instructions">Aggiungi recensioni per creare lo slider testimonial.</div></div>';
    }
    return;
}

// Ottieni il colore dinamico dal CPT "Corsi"
$color_class_name = get_current_corso_color_class_name();

// Usa la funzione helper per gli attributi del wrapper
$wrapper_attributes = get_safe_block_wrapper_attributes( 
    [ 'class' => 'block-box-reviews mt-60 mb-60' ], 
    'box-reviews' 
);

// Generate a unique ID for this swiper instance
$swiper_id = 'swiper-' . uniqid();

/**
 * Genera le stelle in base al numero
 */
if ( ! function_exists( 'render_stars' ) ) {
    function render_stars( $star_count ) {
        $star_count = (int) $star_count;
        if ( $star_count < 1 || $star_count > 5 ) {
            $star_count = 5; // Default a 5 stelle
        }
        
        $star_svg = '<path d="M9.82471 16.0737L15.7202 20L14.1557 12.6L19.3643 7.62105L12.5053 6.97895L9.82471 0L7.14409 6.97895L0.285156 7.62105L5.49375 12.6L3.92927 20L9.82471 16.0737Z" fill="#FFBB44"/>';
        
        // Calcola larghezza in base al numero di stelle (ogni stella è circa 22.5 unità)
        $width = $star_count * 22.5;
        
        echo '<svg class="h-20" width="' . esc_attr( $width ) . '" height="20" viewBox="0 0 ' . esc_attr( $width ) . ' 20" fill="none" xmlns="http://www.w3.org/2000/svg">';
        
        for ( $i = 0; $i < $star_count; $i++ ) {
            $offset = $i * 22.5;
            echo '<g transform="translate(' . esc_attr( $offset ) . ',0)">' . $star_svg . '</g>';
        }
        
        echo '</svg>';
    }
}

?>

<div <?php echo $wrapper_attributes; ?>>
    <div class="container relative mx--40">
        <div class="swiper px-40 <?php echo esc_attr( $swiper_id ); ?>">
            <div class="swiper-wrapper">
                <?php foreach ( $reviews as $review ) : ?>
                    <?php
                    // Gestione corretta dell'ID immagine autore
                    if ( isset( $mb_current_block_data ) ) {
                        // Frontend: l'ID potrebbe essere direttamente un numero
                        $author_photo_id = ! empty( $review['author_photo'] ) ? (int) $review['author_photo'] : 0;
                    } else {
                        // Editor: l'ID è in un array
                        $author_photo_id = ! empty( $review['author_photo']['ID'] ) ? (int) $review['author_photo']['ID'] : 0;
                    }
                    
                    $stars = $review['stars'] ?? '5';
                    $text = $review['text'] ?? '';
                    $author_name = $review['author_name'] ?? '';
                    $author_role = $review['author_role'] ?? '';
                    ?>
                    <div class="swiper-slide">
                        <div class="item flex flex-col rounded-24 relative h-100-100">
                            <div class="inner z-40 p-60 rounded-24 h-100-100 flex flex-col">
                                <div class="stars mb-40">
                                    <?php render_stars( $stars ); ?>
                                </div>
                                
                                <?php if ( ! empty( $text ) ) : ?>
                                    <div class="text mb-40 font-ave-m text-20 leading-15 text-gray">
                                        <?php echo wp_kses_post( $text ); ?>
                                    </div>
                                <?php endif; ?>
                                
                                <div class="author mt-auto flex gap-28 items-center">
                                    <?php if ( $author_photo_id ) : ?>
                                        <div class="photo w-80 h-80 rounded-full overflow-hidden relative">
                                            <?php load_img( class: 'img img-cover', high: $author_photo_id ); ?>
                                        </div>
                                    <?php endif; ?>
                                    
                                    <div class="info">
                                        <?php if ( ! empty( $author_name ) ) : ?>
                                            <div class="name font-tt-eb text-16 uppercase spacing-12 mb-12">
                                                <?php echo esc_html( $author_name ); ?>
                                            </div>
                                        <?php endif; ?>
                                        
                                        <?php if ( ! empty( $author_role ) ) : ?>
                                            <div class="role font-ave-d text-18">
                                                <?php echo esc_html( $author_role ); ?>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            
                            <div class="gradients absolute inset-0 z-10 overflow-hidden rounded-24">
                                <div class="radial-gradient-<?php echo esc_attr( $color_class_name ); ?> animate-float-around animate-opacity-70-100 animation-duration-10000 absolute z-40 h-540 w-540 left--340 bottom--340"></div>
                                <div class="radial-gradient-<?php echo esc_attr( $color_class_name ); ?> animate-float-around animate-opacity-70-100 animation-duration-10000 absolute z-40 h-540 w-540 right--140 top--140"></div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>

        <div class="prev absolute-center-y left--40 z-50 cursor-pointer">
            <svg class="h-80" width="78" height="78" viewBox="0 0 78 78" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g filter="url(#filter0_d_555_17971)">
                    <circle cx="30" cy="30" r="30" transform="matrix(-1 0 0 1 69 6)" fill="white"/>
                </g>
                <path d="M43 27L34 36L43 45" stroke="#636C74" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                <defs>
                    <filter id="filter0_d_555_17971" x="0.172311" y="0.114874" width="77.6554" height="77.6554" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="2.94256"/>
                        <feGaussianBlur stdDeviation="4.41384"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0.156863 0 0 0 0 0.235294 0 0 0 0 0.313726 0 0 0 0.15 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_555_17971"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_555_17971" result="shape"/>
                    </filter>
                </defs>
            </svg>
        </div>
        
        <div class="next absolute-center-y right--40 z-50 cursor-pointer">
            <svg class="h-80" width="78" height="78" viewBox="0 0 78 78" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g filter="url(#filter0_d_555_17968)">
                    <circle cx="39" cy="36" r="30" fill="white"/>
                </g>
                <path d="M36 27L45 36L36 45" stroke="#636C74" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                <defs>
                    <filter id="filter0_d_555_17968" x="0.172311" y="0.114874" width="77.6554" height="77.6554" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="2.94256"/>
                        <feGaussianBlur stdDeviation="4.41384"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0.156863 0 0 0 0 0.235294 0 0 0 0 0.313726 0 0 0 0.15 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_555_17968"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_555_17968" result="shape"/>
                    </filter>
                </defs>
            </svg>
        </div>
    </div>
</div> 