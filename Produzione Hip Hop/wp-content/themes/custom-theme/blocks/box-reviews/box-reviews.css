/* Box Reviews Block Styles */

.block-box-reviews .container {
    /* Container styles inherited */
}

.block-box-reviews .swiper-slide {
    height: auto;
}

.block-box-reviews .item {
    background: #CFCFCF;
}

.block-box-reviews .item .inner {
    background: rgba(255, 255, 255, 0.85);
    backdrop-filter: blur(50px);
    min-height: 320px;
}

.block-box-reviews .stars {
    /* Stars styling */
}

.block-box-reviews .text {
    /* Text styling */
}

.block-box-reviews .author {
    /* Author styling */
}

.block-box-reviews .photo {
    /* Photo styling */
}

.block-box-reviews .info {
    /* Info styling */
}

.block-box-reviews .name {
    /* Name styling */
}

.block-box-reviews .role {
    /* Role styling */
}

.block-box-reviews .gradients {
    /* Gradients styling */
}

.block-box-reviews .swiper-button-disabled {
    opacity: 0.3;
    pointer-events: none;
}

/* Navigation buttons */
.block-box-reviews .prev,
.block-box-reviews .next {
    transition: all 0.3s ease;
}

.block-box-reviews .prev:hover,
.block-box-reviews .next:hover {
    transform: scale(1.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .block-box-reviews .container {
        margin-left: -20px;
        margin-right: -20px;
    }
    
    .block-box-reviews .swiper {
        padding: 20px;
    }
    
    .block-box-reviews .prev,
    .block-box-reviews .next {
        transform: scale(0.8);
    }
    
    .block-box-reviews .prev {
        left: -20px;
    }
    
    .block-box-reviews .next {
        right: -20px;
    }
    
    .block-box-reviews .item .inner {
        padding: 40px;
        min-height: 300px;
    }
    
    .block-box-reviews .author {
        flex-direction: column;
        text-align: center;
        gap: 20px;
    }
}

@media (max-width: 480px) {
    .block-box-reviews .prev,
    .block-box-reviews .next {
        transform: scale(0.7);
        display: none; /* Hide navigation on very small screens */
    }
    
    .block-box-reviews .prev {
        left: -10px;
    }
    
    .block-box-reviews .next {
        right: -10px;
    }
    
    .block-box-reviews .container {
        margin-left: -10px;
        margin-right: -10px;
    }
    
    .block-box-reviews .swiper {
        padding: 10px;
    }
    
    .block-box-reviews .item .inner {
        padding: 32px;
        min-height: 280px;
    }
    
    .block-box-reviews .text {
        font-size: 18px;
        line-height: 1.4;
    }
    
    .block-box-reviews .photo {
        width: 60px;
        height: 60px;
    }
    
    .block-box-reviews .name {
        font-size: 14px;
    }
    
    .block-box-reviews .role {
        font-size: 16px;
    }
} 