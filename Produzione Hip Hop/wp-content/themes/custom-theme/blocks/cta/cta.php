<?php
// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $icon_id = $mb_current_block_data['icon'] ?? 0;
    $title = $mb_current_block_data['title'] ?? '';
    $text = $mb_current_block_data['text'] ?? '';
    $button_text = $mb_current_block_data['button_text'] ?? '';
    $button_url = $mb_current_block_data['button_url'] ?? '';
    $side_image_id = $mb_current_block_data['side_image'] ?? 0;
    $background_image_id = $mb_current_block_data['background_image'] ?? 0;
} else {
    // Siamo nell'editor, usa mb_get_block_field()
    $icon_data = mb_get_block_field( 'icon' );
    $side_img_data = mb_get_block_field( 'side_image' );
    $background_img_data = mb_get_block_field( 'background_image' );
    
    $icon_id = ! empty( $icon_data['ID'] ) ? (int) $icon_data['ID'] : 0;
    $title = mb_get_block_field( 'title' );
    $text = mb_get_block_field( 'text' );
    $button_text = mb_get_block_field( 'button_text' );
    $button_url = mb_get_block_field( 'button_url' );
    $side_image_id = ! empty( $side_img_data['ID'] ) ? (int) $side_img_data['ID'] : 0;
    $background_image_id = ! empty( $background_img_data['ID'] ) ? (int) $background_img_data['ID'] : 0;
}

// Se tutti i campi sono vuoti, non mostrare nulla.
if ( empty( $icon_id ) && empty( $title ) && empty( $text ) && empty( $button_text ) && empty( $side_image_id ) && empty( $background_image_id ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block CTA</div><div class="components-placeholder__instructions">Aggiungi contenuto per visualizzare il blocco.</div></div>';
    }
    return;
}

// Recupera il colore dinamico dal CPT "Corsi"
$color_class_name = get_current_corso_color_class_name();

// Mappa dei colori per i gradienti (come nel single-corsi.php)
$color_map_light = [
    'blue' => '#E7F3FD',
    'sky' => '#EEFAFE', 
    'pink' => '#FCEDF5',
    'violet' => '#F6F1FF',
    'brown' => '#F5F2F1',
    'green' => '#E9FBF0',
    'yellow' => '#FFF8EC',
    'red' => '#FFECEC',
    'ocean' => '#E8EDFB'
];

// Usa la funzione helper per gli attributi del wrapper
$wrapper_attributes = get_safe_block_wrapper_attributes(
    [ 'class' => 'block-cta mt-60 mb-60' ],
    'cta'
);

?>

<div <?php echo $wrapper_attributes; ?>>
    <div class="container grid grid-cols-12 gap-80 items-center bg-brand-<?php echo esc_attr( $color_class_name ); ?>-light rounded-24 p-60 relative overflow-hidden">
        <div class="main <?php echo $background_image_id ? 'col-span-12' : 'col-span-7'; ?> relative z-20">

            <?php if ( $icon_id ) : ?>
                <div class="icon flex mb-40">
                    <?php load_img( class: 'img h-48', high: $icon_id ); ?>
                </div>
            <?php endif; ?>

            <?php if ( ! empty( $title ) ) : ?>
                <div class="title uppercase font-tt-eb text-16 mb-20 spacing-12 text-brand-<?php echo esc_attr( $color_class_name ); ?>">
                    <?php echo esc_html( $title ); ?>
                </div>
            <?php endif; ?>

            <?php if ( ! empty( $text ) ) : ?>
                <div class="text font-ave-m text-20 leading-15 text-gray mb-60">
                    <?php echo wp_kses_post( $text ); ?>
                </div>
            <?php endif; ?>

            <?php if ( ! empty( $button_text ) && ! empty( $button_url ) ) : ?>
                <div class="cta">
                    <a href="<?php echo esc_url( $button_url ); ?>" class="btn h-72 min-h-52 w-100-100 rounded-16 text-center border-brand-<?php echo esc_attr( $color_class_name ); ?> border-4 border-solid flex place-center z-20 relative cursor-pointer hover-shadow-<?php echo esc_attr( $color_class_name ); ?> hover-up-2 transition-all duration-300 ease-in-out">
                        <div class="text font-ave-b text-20">
                            <?php echo esc_html( $button_text ); ?>
                        </div>
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <?php if ( $side_image_id && ! $background_image_id ) : ?>
            <div class="side mr--100 col-span-5 relative z-20">
                <div class="visual w-100-100">
                    <?php load_img( class: 'img w-100-100', high: $side_image_id ); ?>
                </div>
            </div>
        <?php endif; ?>



        <?php if ( $background_image_id ) : ?>
            <div class="background absolute top-0 right-0 bottom-0 w-50-100 z-10">
                <svg class="absolute inset-0 z-30" width="540" height="598" viewBox="0 0 540 598" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <rect width="540" height="598" rx="5.69354" fill="url(#paint0_linear_724_699)"/>
                    <defs>
                        <linearGradient id="paint0_linear_724_699" x1="1.50874e-06" y1="299" x2="540" y2="299" gradientUnits="userSpaceOnUse">
                            <stop stop-color="<?php echo esc_attr( $color_map_light[ $color_class_name ] ); ?>"/>
                            <stop offset="1" stop-color="<?php echo esc_attr( $color_map_light[ $color_class_name ] ); ?>" stop-opacity="0"/>
                        </linearGradient>
                    </defs>
                </svg>
                <?php load_img( class: 'img img-cover z-20', high: $background_image_id ); ?>
            </div>
        <?php endif; ?>
      
    </div>
</div>
