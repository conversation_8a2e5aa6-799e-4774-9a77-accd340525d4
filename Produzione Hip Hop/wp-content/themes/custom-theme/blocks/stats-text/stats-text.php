<?php
// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $value = $mb_current_block_data['value'] ?? '';
    $unit = $mb_current_block_data['unit'] ?? '';
    $label = $mb_current_block_data['label'] ?? '';
    $side_text = $mb_current_block_data['side_text'] ?? '';
} else {
    // Siamo nell'editor
    $value = mb_get_block_field( 'value' );
    $unit = mb_get_block_field( 'unit' );
    $label = mb_get_block_field( 'label' );
    $side_text = mb_get_block_field( 'side_text' );
}

// Se mancano i dati principali, non mostrare nulla.
if ( empty( $value ) && empty( $label ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block Stats Text</div><div class="components-placeholder__instructions">Aggiungi un valore o un\'etichetta.</div></div>';
    }
    return;
}

$color_class_name = get_current_corso_color_class_name();
$wrapper_attributes = get_safe_block_wrapper_attributes( 
    [ 'class' => 'block-stats-text mb-60 mt-60' ], 
    'stats-text' 
);

?>

<div <?php echo $wrapper_attributes; ?>>
	<div class="container">
		<div class="box flex gap-80 items-center border-2 border-gray border-solid rounded-24 p-60">
			
			<div class="main shrink-0">
				<div class="number mb-22 flex gap-14 items-center">
					<?php if ( ! empty( $value ) ) : ?>
						<div class="value font-ave-r text-60">
							<?php echo esc_html( $value ); ?>
						</div>
					<?php endif; ?>
					<?php if ( ! empty( $unit ) ) : ?>
						<div class="unit font-ave-r text-48">
							<?php echo esc_html( $unit ); ?>
						</div>
					<?php endif; ?>
				</div>
				<?php if ( ! empty( $label ) ) : ?>
					<div class="text uppercase font-tt-eb text-16 spacing-12 text-brand-<?php echo esc_attr( $color_class_name ); ?>">
						<?php echo esc_html( $label ); ?>
					</div>
				<?php endif; ?>
			</div>

			<?php if ( ! empty( $side_text ) ) : ?>
				<div class="side">
					<div class="text font-ave-m text-20 leading-15 text-gray">
						<?php echo wp_kses_post( $side_text ); ?>
					</div>
				</div>
			<?php endif; ?>

		</div>
	</div>
</div> 