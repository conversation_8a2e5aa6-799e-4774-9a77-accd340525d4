/* Button Block Styles */

.block-button .btn {
    transition: all 0.3s ease;
    text-decoration: none;
    display: inline-flex;
}

.block-button .btn:hover {
    text-decoration: none;
}

.block-button .text {
    /* Ensure text is properly styled */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .block-button .btn {
        width: 100%;
        max-width: none;
    }
}

@media (max-width: 480px) {
    .block-button .btn {
        height: 60px;
        font-size: 18px;
    }
    
    .block-button .text {
        font-size: 18px;
    }
} 