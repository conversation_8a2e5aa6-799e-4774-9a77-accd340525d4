<?php
/**
 * Block Button Template
 * 
 * @package Custom_Theme
 */

// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $text = $mb_current_block_data['text'] ?? '';
    $url = $mb_current_block_data['url'] ?? '';
    $target_blank = $mb_current_block_data['target_blank'] ?? false;
    $is_editor = false;
} else {
    // Siamo nell'editor, usa mb_get_block_field()
    $text = mb_get_block_field( 'text' );
    $url = mb_get_block_field( 'url' );
    $target_blank = mb_get_block_field( 'target_blank' );
    $is_editor = true;
}

// Se mancano i dati essenziali, non mostrare nulla.
if ( empty( $text ) && empty( $url ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block Button</div><div class="components-placeholder__instructions">Aggiungi testo e URL per visualizzare il bottone.</div></div>';
    }
    return;
}

// Recupera il colore dinamico dal CPT "Corsi"
$color_class_name = get_current_corso_color_class_name();

// Usa la funzione helper per gli attributi del wrapper
$wrapper_attributes = get_safe_block_wrapper_attributes(
    [ 'class' => 'block-button mt-60 mb-60' ],
    'button'
);

// Determina se aprire in nuova finestra
$target_attr = $target_blank ? ' target="_blank" rel="noopener noreferrer"' : '';

// Classi comuni per il bottone
$button_classes = 'btn h-72 max-w-440 rounded-16 text-center border-brand-' . esc_attr( $color_class_name ) . ' border-4 border-solid rounded-12 flex place-center z-20 relative w-100-100 font-ave-b text-20 hover-shadow-' . esc_attr( $color_class_name ) . ' hover-up-2';

?>

<div <?php echo $wrapper_attributes; ?>>
    <?php if ( ! empty( $text ) ) : ?>
        <?php if ( $is_editor ) : ?>
            <div class="<?php echo esc_attr( $button_classes ); ?>" style="cursor: default;">
                <?php echo esc_html( $text ); ?>
            </div>
        <?php elseif ( ! empty( $url ) ) : ?>
            <a href="<?php echo esc_url( $url ); ?>"<?php echo $target_attr; ?> class="<?php echo esc_attr( $button_classes ); ?>">
                <?php echo esc_html( $text ); ?>
            </a>
        <?php else : ?>
            <div class="<?php echo esc_attr( $button_classes ); ?>" style="cursor: default;">
                <?php echo esc_html( $text ); ?>
            </div>
        <?php endif; ?>
    <?php endif; ?>
</div> 