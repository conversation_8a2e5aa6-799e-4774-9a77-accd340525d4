/**
 * Gallery Block JavaScript
 * 
 * @package Custom_Theme
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all gallery blocks
    initializeGalleryBlocks();
});

/**
 * Initialize all gallery blocks on the page
 */
function initializeGalleryBlocks() {
    const galleryBlocks = document.querySelectorAll('.block-gallery[data-lightbox-enabled="true"]');
    
    galleryBlocks.forEach(function(block) {
        // Skip if already initialized
        if (block.dataset.initialized) {
            return;
        }
        
        initializeGalleryLightbox(block);
        block.dataset.initialized = 'true';
    });
}

/**
 * Initialize lightbox for a specific gallery block
 */
function initializeGalleryLightbox(block) {
    const triggers = block.querySelectorAll('.lightbox-trigger');
    const lightbox = block.querySelector('.lightbox');
    
    if (!lightbox) {
        return;
    }
    
    const lightboxImage = lightbox.querySelector('.lightbox-image');
    const closeButton = lightbox.querySelector('.lightbox-close');
    const prevButton = lightbox.querySelector('.lightbox-prev');
    const nextButton = lightbox.querySelector('.lightbox-next');
    const currentCounter = lightbox.querySelector('.lightbox-counter .current');
    
    let currentIndex = 0;
    let images = [];
    
    // Build images array from triggers
    triggers.forEach(function(trigger, index) {
        images.push({
            url: trigger.dataset.image,
            index: index
        });
    });
    
    // Function to update lightbox image
    function updateLightboxImage(index) {
        if (images[index]) {
            lightboxImage.src = images[index].url;
            currentIndex = index;
            if (currentCounter) {
                currentCounter.textContent = index + 1;
            }
            
            // Update navigation button states
            if (prevButton) {
                prevButton.style.opacity = index === 0 ? '0.5' : '1';
                prevButton.style.pointerEvents = index === 0 ? 'none' : 'auto';
            }
            if (nextButton) {
                nextButton.style.opacity = index === images.length - 1 ? '0.5' : '1';
                nextButton.style.pointerEvents = index === images.length - 1 ? 'none' : 'auto';
            }
        }
    }
    
    // Add click handlers to all image triggers
    triggers.forEach(function(trigger, index) {
        trigger.addEventListener('click', function(e) {
            e.preventDefault();
            
            const imageUrl = this.dataset.image;
            if (imageUrl && lightboxImage) {
                currentIndex = index;
                updateLightboxImage(currentIndex);
                
                // Show the lightbox
                lightbox.classList.remove('hidden');
                lightbox.classList.add('active');
                
                // Prevent body scroll
                document.body.style.overflow = 'hidden';
            }
        });
    });
    
    // Navigation handlers
    if (prevButton) {
        prevButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            if (currentIndex > 0) {
                updateLightboxImage(currentIndex - 1);
            }
        });
    }
    
    if (nextButton) {
        nextButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            if (currentIndex < images.length - 1) {
                updateLightboxImage(currentIndex + 1);
            }
        });
    }
    
    // Close lightbox handlers
    function closeLightbox() {
        lightbox.classList.remove('active');
        
        // Small delay before hiding to allow animation
        setTimeout(function() {
            lightbox.classList.add('hidden');
            // Reset body scroll
            document.body.style.overflow = '';
        }, 300);
    }
    
    // Close button handler
    if (closeButton) {
        closeButton.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            closeLightbox();
        });
    }
    
    // Close on overlay click (clicking outside the image)
    lightbox.addEventListener('click', function(e) {
        // If the click is on the lightbox background (not on the image or close button)
        if (e.target === lightbox || e.target.classList.contains('lightbox-content')) {
            closeLightbox();
        }
    });
    
    // Keyboard navigation
    function handleKeydown(e) {
        if (!lightbox.classList.contains('active')) {
            return;
        }
        
        switch(e.key) {
            case 'Escape':
                closeLightbox();
                break;
            case 'ArrowLeft':
                e.preventDefault();
                if (currentIndex > 0) {
                    updateLightboxImage(currentIndex - 1);
                }
                break;
            case 'ArrowRight':
                e.preventDefault();
                if (currentIndex < images.length - 1) {
                    updateLightboxImage(currentIndex + 1);
                }
                break;
        }
    }
    
    // Add keyboard event listener
    document.addEventListener('keydown', handleKeydown);
    
    // Store reference for cleanup if needed
    block.lightboxKeydownHandler = handleKeydown;
}

/**
 * Cleanup function for a gallery block
 */
function cleanupGalleryBlock(block) {
    if (block.lightboxKeydownHandler) {
        document.removeEventListener('keydown', block.lightboxKeydownHandler);
        delete block.lightboxKeydownHandler;
    }
}

/**
 * Reinitialize galleries when new content is added (for dynamic content)
 */
window.reinitializeGalleryBlocks = function() {
    initializeGalleryBlocks();
};

/**
 * Cleanup galleries (useful for editor)
 */
window.cleanupGalleryBlocks = function() {
    const galleryBlocks = document.querySelectorAll('.block-gallery[data-lightbox-enabled="true"]');
    galleryBlocks.forEach(cleanupGalleryBlock);
}; 