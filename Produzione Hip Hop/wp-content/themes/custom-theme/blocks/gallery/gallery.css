/* Gallery Block Styles */

.block-gallery .item {
    transition: transform 0.3s ease;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.block-gallery .item:hover {
    transform: translateY(-4px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.15);
}

.block-gallery .item.cursor-pointer {
    cursor: pointer;
}

.block-gallery .item img {
    transition: transform 0.3s ease;
}

.block-gallery .item:hover img {
    transform: scale(1.05);
}

/* Lightbox Styles */
.lightbox {
    backdrop-filter: blur(8px);
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.lightbox.active {
    opacity: 1;
    visibility: visible;
}

.lightbox-content {
    /* Content positioning */
}

.lightbox-image {
    max-width: 90vw;
    max-height: 90vh;
    transition: transform 0.3s ease;
}

.lightbox.active .lightbox-image {
    transform: scale(1);
}

.lightbox-close {
    transition: all 0.3s ease;
    font-size: 24px;
    line-height: 1;
    font-weight: bold;
}

.lightbox-close:hover {
    transform: scale(1.1);
    background-color: rgba(255, 255, 255, 0.4);
}

/* Lightbox Navigation */
.lightbox-prev,
.lightbox-next {
    transition: all 0.3s ease;
    cursor: pointer;
}

.lightbox-prev:hover,
.lightbox-next:hover {
    transform: translateY(-50%) scale(1.1);
    background-color: rgba(255, 255, 255, 0.4);
}

.lightbox-prev[style*="pointer-events: none"],
.lightbox-next[style*="pointer-events: none"] {
    cursor: not-allowed;
}

.lightbox-counter {
    font-family: system-ui, sans-serif;
    font-weight: 500;
    backdrop-filter: blur(4px);
}

/* Responsive Grid */
@media (max-width: 768px) {
    .block-gallery .container {
        gap: 8px;
    }
    
    .block-gallery .item {
        /* Su mobile forziaamo una colonna */
        grid-column: span 12 !important;
    }
    
    .lightbox-content {
        padding: 16px;
    }
    
    .lightbox-close {
        top: 8px;
        right: 8px;
        width: 32px;
        height: 32px;
        font-size: 20px;
    }
    
    .lightbox-prev,
    .lightbox-next {
        width: 48px;
        height: 48px;
    }
    
    .lightbox-prev {
        left: 8px;
    }
    
    .lightbox-next {
        right: 8px;
    }
    
    .lightbox-counter {
        bottom: 8px;
        font-size: 12px;
        padding: 6px 12px;
    }
}

@media (max-width: 480px) {
    .block-gallery .container {
        gap: 6px;
    }
    
    .lightbox-content {
        padding: 12px;
    }
    
    .lightbox-image {
        max-width: 95vw;
        max-height: 95vh;
    }
    
    .lightbox-prev,
    .lightbox-next {
        width: 40px;
        height: 40px;
    }
    
    .lightbox-prev svg,
    .lightbox-next svg {
        height: 16px;
    }
    
    .lightbox-counter {
        bottom: 6px;
        font-size: 11px;
        padding: 4px 10px;
    }
} 







.block-editor__container img {
    height:100%;
  }
  