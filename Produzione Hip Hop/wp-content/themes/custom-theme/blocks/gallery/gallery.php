<?php
/**
 * Block Gallery Template
 * 
 * @package Custom_Theme
 */

// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $images_data = $mb_current_block_data['images'] ?? [];
    $aspect_ratio = $mb_current_block_data['aspect_ratio'] ?? 'ratio-3-4';
    $columns = $mb_current_block_data['columns'] ?? '3';
    $enable_lightbox = $mb_current_block_data['enable_lightbox'] ?? false;
    
    // Normalizza i dati: potrebbero essere array di ID o array di oggetti
    $images = [];
    foreach ( $images_data as $image ) {
        if ( is_numeric( $image ) ) {
            // È solo un ID
            $images[] = [ 'ID' => $image ];
        } elseif ( is_array( $image ) && isset( $image['ID'] ) ) {
            // È già un array con ID
            $images[] = $image;
        } elseif ( is_array( $image ) && ! empty( $image ) ) {
            // Potrebbe essere un array con chiave numerica
            $first_key = array_key_first( $image );
            if ( is_numeric( $first_key ) ) {
                $images[] = [ 'ID' => $image[$first_key] ];
            }
        }
    }
} else {
    // Siamo nell'editor
    $images = [];
    $aspect_ratio = 'ratio-3-4';
    $columns = '3';
    $enable_lightbox = false;
    
    if ( function_exists( 'mb_get_block_field' ) ) {
        $images = mb_get_block_field( 'images' ) ?: [];
        $aspect_ratio = mb_get_block_field( 'aspect_ratio' ) ?: 'ratio-3-4';
        $columns = mb_get_block_field( 'columns' ) ?: '3';
        $enable_lightbox = mb_get_block_field( 'enable_lightbox' ) ?: false;
    }
}

// Se non ci sono immagini, mostra placeholder nell'editor
if ( empty( $images ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block Gallery</div><div class="components-placeholder__instructions">Carica una o più immagini per la galleria.</div></div>';
    }
    return;
}

// Converte il numero di colonne in classi grid
$grid_class = match( $columns ) {
    '1' => 'grid-cols-1',
    '2' => 'grid-cols-2', 
    '3' => 'grid-cols-3',
    default => 'grid-cols-3'
};

// Calcola la span per ogni item
$col_span = match( $columns ) {
    '1' => 'col-span-1',
    '2' => 'col-span-1',
    '3' => 'col-span-1',
    default => 'col-span-1'
};

$wrapper_attrs = get_safe_block_wrapper_attributes( 
    [ 'class' => 'block-gallery mb-60 mt-60' ], 
    'gallery' 
);

// Generate unique IDs for lightbox if enabled
$lightbox_id = 'lightbox-' . uniqid();

?>

<div <?php echo $wrapper_attrs; ?> <?php if ( $enable_lightbox ) : ?>data-lightbox-enabled="true"<?php endif; ?>>
    <div class="container grid grid-cols-12 gap-10">
        <?php foreach ( $images as $image ) : ?>
            <?php 
            $image_id = 0;
            if ( is_array( $image ) && isset( $image['ID'] ) ) {
                $image_id = (int) $image['ID'];
            } elseif ( is_numeric( $image ) ) {
                $image_id = (int) $image;
            }
            
            if ( $image_id > 0 ) : 
                // Per grid responsive: 
                // - 1 col: sempre col-span-12
                // - 2 col: col-span-6 
                // - 3 col: col-span-4
                $responsive_span = match( $columns ) {
                    '1' => 'col-span-12',
                    '2' => 'col-span-6',
                    '3' => 'col-span-4',
                    default => 'col-span-4'
                };
                
                $image_url_full = $enable_lightbox ? wp_get_attachment_image_url( $image_id, 'full' ) : '';
            ?>
                <div class="item relative <?php echo esc_attr( $aspect_ratio ); ?> <?php echo esc_attr( $responsive_span ); ?> overflow-hidden rounded-8 <?php if ( $enable_lightbox ) : ?>cursor-pointer lightbox-trigger<?php endif; ?>" <?php if ( $enable_lightbox ) : ?>data-image="<?php echo esc_url( $image_url_full ); ?>" data-index="<?php echo esc_attr( array_search( $image, $images ) ); ?>"<?php endif; ?>>
                    <?php load_img( class: 'img img-cover transition-transform duration-300 hover:scale-105', high: $image_id ); ?>
                </div>
            <?php endif; ?>
        <?php endforeach; ?>
    </div>
    
    <?php if ( $enable_lightbox ) : ?>
        <div class="lightbox fixed inset-0 bg-black bg-opacity-90 z-50 hidden backdrop-filter backdrop-blur-sm" id="<?php echo esc_attr( $lightbox_id ); ?>">
            <div class="lightbox-content relative w-full h-full flex items-center justify-center p-20">
                <button class="lightbox-close absolute top-4 right-4 text-white text-3xl w-40 h-40 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 flex items-center justify-center transition-all z-10">&times;</button>
                
                <!-- Navigation buttons -->
                <button class="lightbox-prev absolute left-4 top-1/2 transform -translate-y-1/2 text-white w-60 h-60 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 flex items-center justify-center transition-all z-10">
                    <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M15 18L9 12L15 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
                <button class="lightbox-next absolute right-4 top-1/2 transform -translate-y-1/2 text-white w-60 h-60 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 flex items-center justify-center transition-all z-10">
                    <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M9 18L15 12L9 6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
                
                <img src="" alt="" class="lightbox-image max-w-90 max-h-90 object-contain rounded-lg shadow-2xl">
                
                <!-- Image counter -->
                <div class="lightbox-counter absolute bottom-4 left-1/2 transform -translate-x-1/2 text-white bg-black bg-opacity-50 px-16 py-8 rounded-full text-14">
                    <span class="current">1</span> / <span class="total"><?php echo count( $images ); ?></span>
                </div>
            </div>
        </div>
    <?php endif; ?>
</div> 