<?php
/**
 * Block Roadmap Template
 * 
 * @package Custom_Theme
 */

// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $items = $mb_current_block_data['items'] ?? [];
} else {
    // Siamo nell'editor, usa mb_get_block_field()
    $items = [];
    if ( function_exists( 'mb_get_block_field' ) ) {
        $items = mb_get_block_field( 'items' );
    }
}

// Se non ci sono items, non mostrare nulla.
if ( empty( $items ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block Roadmap</div><div class="components-placeholder__instructions">Aggiungi step per creare la roadmap.</div></div>';
    }
    return;
}

// Recupera il colore dinamico dal CPT "Corsi"
$color_class_name = get_current_corso_color_class_name();

// Mappa dei colori (come nel single-corsi.php)
$color_map = [
    'blue' => '#1289ED',
    'sky' => '#56CCF2', 
    'pink' => '#E04E9D',
    'violet' => '#A973FF',
    'brown' => '#9D8271',
    'green' => '#25D366',
    'yellow' => '#FFBB44',
    'red' => '#FF4444',
    'ocean' => '#194DD4'
];

// Usa la funzione helper per gli attributi del wrapper
$wrapper_attributes = get_safe_block_wrapper_attributes(
    [ 'class' => 'block-roadmap mt-60 mb-60' ],
    'roadmap'
);

?>

<div <?php echo $wrapper_attributes; ?>>
    <div class="container flex gap-28 relative">
        
        <div class="line w-40 relative overflow-hidden shrink-0">
            <svg class="absolute-center-x top-0 w-4 h-auto" width="4" height="726" viewBox="0 0 4 726" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M2 2L2.00003 724" stroke="<?php echo esc_attr( $color_map[ $color_class_name ] ); ?>" stroke-width="4" stroke-linecap="round" stroke-dasharray="0.1 10"></path>
            </svg> 
        </div>

        <div class="items flex flex-col gap-48">
            <?php 
            $number_counter = 1;
            foreach ( $items as $item ) : 
                $text = $item['text'] ?? '';
                
                if ( empty( $text ) ) {
                    continue;
                }
            ?>
                <div class="item flex flex-col gap-20">
                    <div class="circle border-2 border-solid bg-brand-<?php echo esc_attr( $color_class_name ); ?>-light border-brand-<?php echo esc_attr( $color_class_name ); ?> rounded-full h-40 w-40 absolute left-0 font-ave-b text-20 place-center flex text-brand-<?php echo esc_attr( $color_class_name ); ?>">
                        <?php echo esc_html( $number_counter ); ?>
                    </div>
                    <div class="text font-ave-m leading-15 text-20 text-gray">
                        <?php echo wp_kses_post( $text ); ?>
                    </div>
                </div>
                
                <?php $number_counter++; ?>
                
            <?php endforeach; ?>
        </div>
    </div>
</div> 