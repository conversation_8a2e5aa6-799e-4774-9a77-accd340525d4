/* Block Roadmap Styles */

.block-roadmap .container {
    /* Container styles inherited */
}

.block-roadmap .line {
    /* Roadmap line styles */
}

.block-roadmap .items {
    /* Items container styles */
}

.block-roadmap .item {
    /* Individual item styles */
}

.block-roadmap .circle {
    /* Circle marker for roadmap */
    transition: all 0.3s ease;
}

.block-roadmap .text {
    /* Text content styles */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .block-roadmap .container {
        flex-direction: column;
        gap: 40px;
    }
    
    .block-roadmap .line {
        width: 100%;
        height: 4px;
        order: -1;
    }
    
    .block-roadmap .line svg {
        width: 100%;
        height: 4px;
        transform: rotate(90deg);
    }
    
    .block-roadmap .items {
        gap: 32px !important;
    }
    
    .block-roadmap .item {
        gap: 24px;
    }
    
    .block-roadmap .circle {
        position: relative !important;
        left: 0 !important;
        margin-bottom: 16px;
    }
}

@media (max-width: 480px) {
    .block-roadmap .items {
        gap: 28px !important;
    }
    
    .block-roadmap .item {
        gap: 20px;
    }
    
    .block-roadmap .text {
        font-size: 18px;
        line-height: 1.4;
    }
    
    .block-roadmap .circle {
        width: 32px;
        height: 32px;
        font-size: 16px;
    }
} 