<?php
/**
 * Block Ticket Template
 * 
 * @package Custom_Theme
 */

// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $value = $mb_current_block_data['value'] ?? '';
    $unit = $mb_current_block_data['unit'] ?? '';
    $label = $mb_current_block_data['label'] ?? '';
    $side_text = $mb_current_block_data['side_text'] ?? '';
} else {
    // Siamo nell'editor, usa mb_get_block_field()
    $value = mb_get_block_field( 'value' );
    $unit = mb_get_block_field( 'unit' );
    $label = mb_get_block_field( 'label' );
    $side_text = mb_get_block_field( 'side_text' );
}

// Se tutti i campi sono vuoti, non mostrare nulla.
if ( empty( $value ) && empty( $label ) && empty( $side_text ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block Ticket</div><div class="components-placeholder__instructions">Aggiungi contenuto per visualizzare il ticket.</div></div>';
    }
    return;
}

// Recupera il colore dinamico dal CPT "Corsi"
$color_class_name = get_current_corso_color_class_name();

// Usa la funzione helper per gli attributi del wrapper
$wrapper_attributes = get_safe_block_wrapper_attributes(
    [ 'class' => 'block-ticket mt-60 mb-60' ],
    'ticket'
);

?>

<div <?php echo $wrapper_attributes; ?>>
    <div class="container flex rounded-24 border-2 border-solid border-gray overflow-hidden">
        
        <div class="main p-60 flex flex-col gap-20 justify-center bg-brand-<?php echo esc_attr( $color_class_name ); ?>-light max-w-300 shrink-0">
            <?php if ( ! empty( $value ) || ! empty( $unit ) ) : ?>
                <div class="number flex items-center gap-12 text-brand-<?php echo esc_attr( $color_class_name ); ?>">
                    <?php if ( ! empty( $value ) ) : ?>
                        <div class="value font-ave-r text-60">
                            <?php echo esc_html( $value ); ?>
                        </div>
                    <?php endif; ?>
                    
                    <?php if ( ! empty( $unit ) ) : ?>
                        <div class="unit font-ave-m text-48">
                            <?php echo esc_html( $unit ); ?>
                        </div>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
            
            <?php if ( ! empty( $label ) ) : ?>
                <div class="text uppercase font-tt-eb text-16 spacing-12 leading-12">
                    <?php echo esc_html( $label ); ?>
                </div>
            <?php endif; ?>
        </div>
        
        <?php if ( ! empty( $side_text ) ) : ?>
            <div class="side p-60 flex items-center">
                <div class="text font-ave-m text-20 leading-15 text-gray">
                    <?php echo wp_kses_post( $side_text ); ?>
                </div>
            </div>
        <?php endif; ?>
        
    </div>
</div> 