/* Block List Styles */

.block-list .container {
    /* Container styles inherited */
}

/* Line styles removed - roadmap is now a separate block */

.block-list .items {
    /* Items container styles */
}

.block-list .item {
    /* Individual item styles */
}

/* Circle styles removed - roadmap is now a separate block */

.block-list .icon {
    /* Icon container styles */
}

.block-list .icon img,
.block-list .icon svg {
    max-width: none; /* Override any global max-width constraints */
    flex-shrink: 0;
}

.block-list .text {
    /* Text content styles */
}

/* Marker-specific styles */

.block-list .item[data-marker="numbered"] {
    /* Numbered specific adjustments if needed */
}

.block-list .item[data-marker="numbered_bg"] {
    /* Numbered with background specific adjustments if needed */
}

.block-list .item[data-marker="icon"] {
    /* Icon specific adjustments if needed */
}

.block-list .item[data-marker="bullet"] {
    /* Bullet specific adjustments if needed */
}

.block-list .item[data-marker="check"] {
    /* Check specific adjustments if needed */
}

.block-list .item[data-marker="cross"] {
    /* Cross specific adjustments if needed */
}

.block-list .item[data-marker="arrow"] {
    /* Arrow specific adjustments if needed */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .block-list .items {
        gap: 32px !important;
    }
    
    .block-list .item {
        gap: 24px;
    }
}

@media (max-width: 480px) {
    .block-list .items {
        gap: 28px !important;
    }
    
    .block-list .item {
        gap: 20px;
    }
    
    .block-list .text {
        font-size: 18px;
        line-height: 1.4;
    }
    
    .block-list .icon svg,
    .block-list .icon img {
        height: 24px;
    }
} 