<?php
/**
 * Block List Template
 * 
 * @package Custom_Theme
 */

// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $items = $mb_current_block_data['items'] ?? [];
} else {
    // Siamo nell'editor, usa mb_get_block_field()
    $items = [];
    if ( function_exists( 'mb_get_block_field' ) ) {
        $items = mb_get_block_field( 'items' );
    }
}

// Se non ci sono items, non mostrare nulla.
if ( empty( $items ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block List</div><div class="components-placeholder__instructions">Aggiungi elementi per creare la lista.</div></div>';
    }
    return;
}

// Recupera il colore dinamico dal CPT "Corsi"
$color_class_name = get_current_corso_color_class_name();

// Mappa dei colori (come nel single-corsi.php)
$color_map = [
    'blue' => '#1289ED',
    'sky' => '#56CCF2', 
    'pink' => '#E04E9D',
    'violet' => '#A973FF',
    'brown' => '#9D8271',
    'green' => '#25D366',
    'yellow' => '#FFBB44',
    'red' => '#FF4444',
    'ocean' => '#194DD4'
];

// Usa la funzione helper per gli attributi del wrapper
$wrapper_attributes = get_safe_block_wrapper_attributes(
    [ 'class' => 'block-list mt-60 mb-60' ],
    'block-list'
);

// Block list non ha più la roadmap - usa sempre il layout semplice

?>

<div <?php echo $wrapper_attributes; ?>>
    <div class="container">
        <div class="items flex flex-col gap-28">
            <?php 
            $number_counter = 1;
            foreach ( $items as $item ) : 
                $marker_type = $item['marker_type'] ?? 'numbered';
                $text = $item['text'] ?? '';
                
                // Gestione corretta dell'ID immagine per icona personalizzata
                $custom_icon_id = 0;
                if ( $marker_type === 'icon' ) {
                    if ( isset( $mb_current_block_data ) ) {
                        // Frontend: l'ID potrebbe essere direttamente un numero
                        $custom_icon_id = ! empty( $item['custom_icon'] ) ? (int) $item['custom_icon'] : 0;
                    } else {
                        // Editor: l'ID è in un array
                        $custom_icon_id = ! empty( $item['custom_icon']['ID'] ) ? (int) $item['custom_icon']['ID'] : 0;
                    }
                }
                
                if ( empty( $text ) && $marker_type !== 'icon' ) {
                    continue;
                }
            ?>
                
                <?php if ( $marker_type === 'numbered' ) : ?>
                   
                    <div class="item flex items-start gap-28">
                        <div class="w-40 shrink-0 font-ave-b text-20 text-center place-center leading-15 flex text-brand-<?php echo esc_attr( $color_class_name ); ?>">
                            <?php echo esc_html( $number_counter ); ?>
                        </div>
                        <?php if ( ! empty( $text ) ) : ?>
                            <div class="text font-ave-m leading-15 text-20 text-gray">
                                <?php echo wp_kses_post( $text ); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                <?php elseif ( $marker_type === 'numbered_bg' ) : ?>
                    
                    <div class="item flex items-start gap-28">
                        <div class="w-40 shrink-0 font-ave-b text-20 text-center place-center flex text-brand-<?php echo esc_attr( $color_class_name ); ?> bg-brand-<?php echo esc_attr( $color_class_name ); ?>-light rounded-8 h-40 relative top--4">
                            <?php echo esc_html( $number_counter ); ?>
                        </div>
                        <?php if ( ! empty( $text ) ) : ?>
                            <div class="text font-ave-m leading-15 text-20 text-gray">
                                <?php echo wp_kses_post( $text ); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                <?php elseif ( $marker_type === 'icon' ) : ?>
                    <div class="item flex gap-28">
                        <div class="icon flex w-40 shrink-0 justify-center">
                            <?php if ( $custom_icon_id ) : ?>
                                <?php load_img( class: 'img h-28', high: $custom_icon_id ); ?>
                            <?php else : ?>
                                <svg class="h-28" width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <circle cx="14" cy="14" r="12" stroke="<?php echo esc_attr( $color_map[ $color_class_name ] ); ?>" stroke-width="4"/>
                                </svg>
                            <?php endif; ?>
                        </div>
                        <?php if ( ! empty( $text ) ) : ?>
                            <div class="text font-ave-m text-20 leading-15 text-gray">
                                <?php echo wp_kses_post( $text ); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                <?php elseif ( $marker_type === 'bullet' ) : ?>
                    <div class="item flex items-start gap-28">
                        <div class="w-40 relative top-12 shrink-0 place-center flex">
                            <svg class="h-8" width="8" height="8" viewBox="0 0 8 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <rect width="8" height="8" rx="4" fill="<?php echo esc_attr( $color_map[ $color_class_name ] ); ?>"/>
                            </svg>
                        </div>
                        <?php if ( ! empty( $text ) ) : ?>
                            <div class="text font-ave-m leading-15 text-20 text-gray">
                                <?php echo wp_kses_post( $text ); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                <?php elseif ( $marker_type === 'check' ) : ?>
                    <div class="item flex items-start gap-28">
                        <div class="w-40 relative top-4 shrink-0 place-center flex">
                            <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3 13L10 18.5C10 18.5 19.5052 7.04556 21 5" stroke="#20CF01" stroke-width="4" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                        </div>
                        <?php if ( ! empty( $text ) ) : ?>
                            <div class="text font-ave-m leading-15 text-20 text-gray">
                                <?php echo wp_kses_post( $text ); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                <?php elseif ( $marker_type === 'cross' ) : ?>
                    <div class="item flex items-start gap-28">
                        <div class="w-40 relative top-4 shrink-0 place-center flex">
                            <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M4 4L20 20" stroke="#FF0000" stroke-width="4" stroke-linecap="round"></path>
                                <path d="M20 4L4 20" stroke="#FF0000" stroke-width="4" stroke-linecap="round"></path>
                            </svg>
                        </div>
                        <?php if ( ! empty( $text ) ) : ?>
                            <div class="text font-ave-m leading-15 text-20 text-gray">
                                <?php echo wp_kses_post( $text ); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                <?php elseif ( $marker_type === 'arrow' ) : ?>
                    <div class="item flex items-start gap-28">
                        <div class="w-40 relative top-4 shrink-0 place-center flex">
                            <svg class="h-24" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M21.2129 12.1055L1.99969 12.1055M21.2129 12.1055L14.8489 4.74151M21.2129 12.1055L15.2025 19.1159" stroke="<?php echo esc_attr( $color_map[ $color_class_name ] ); ?>" stroke-width="4" stroke-linecap="round"/>
                            </svg>
                        </div>
                        <?php if ( ! empty( $text ) ) : ?>
                            <div class="text font-ave-m leading-15 text-20 text-gray">
                                <?php echo wp_kses_post( $text ); ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    
                <?php endif; ?>
                
                <?php 
                // Incrementa il counter solo per i tipi numerati
                if ( in_array( $marker_type, [ 'numbered', 'numbered_bg' ] ) ) {
                    $number_counter++;
                }
                ?>
                
            <?php endforeach; ?>
        </div>
    </div>
</div> 