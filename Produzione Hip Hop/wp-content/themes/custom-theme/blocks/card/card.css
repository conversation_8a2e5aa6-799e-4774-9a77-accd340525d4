/* Block Card Styles */

.block-card .container {
    /* Container styles inherited */
}

.block-card .cover {
    /* Cover image section styles inherited */
}

.block-card .cover img {
    /* Cover image styles inherited */
}

.block-card .content {
    /* Content section styles inherited */
}

.block-card .title {
    /* Title styles inherited */
}

.block-card .text {
    /* Text content styles inherited */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .block-card .content {
        padding: 32px;
        gap: 16px;
    }
    
    .block-card .title {
        font-size: 14px;
        line-height: 1.2;
    }
    
    .block-card .text {
        font-size: 18px;
        line-height: 1.4;
    }
}

@media (max-width: 480px) {
    .block-card .content {
        padding: 24px;
        gap: 12px;
    }
    
    .block-card .title {
        font-size: 13px;
        line-height: 1.2;
    }
    
    .block-card .text {
        font-size: 16px;
        line-height: 1.4;
    }
}

/* Hover effects */
.block-card .container {
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.block-card .container:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
}

.block-card .cover img {
    transition: transform 0.3s ease;
}

.block-card .container:hover .cover img {
    transform: scale(1.05);
}

/* Aspect ratio support */
.block-card .cover {
    overflow: hidden;
}

.block-card .cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

/* Content without image */
.block-card .container:not(:has(.cover)) .content {
    padding: 48px;
}

@media (max-width: 768px) {
    .block-card .container:not(:has(.cover)) .content {
        padding: 32px;
    }
}

@media (max-width: 480px) {
    .block-card .container:not(:has(.cover)) .content {
        padding: 24px;
    }
} 