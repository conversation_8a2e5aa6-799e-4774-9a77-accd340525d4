<?php
/**
 * Block Card Template
 * 
 * @package Custom_Theme
 */

// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $cover_image_data = $mb_current_block_data['cover_image'] ?? 0;
    $aspect_ratio = $mb_current_block_data['aspect_ratio'] ?? 'ratio-16-9';
    $title = $mb_current_block_data['title'] ?? '';
    $text = $mb_current_block_data['text'] ?? '';
    
    // Gestione dell'immagine per il frontend
    if ( is_array( $cover_image_data ) && isset( $cover_image_data['ID'] ) ) {
        $cover_image_id = (int) $cover_image_data['ID'];
    } else {
        $cover_image_id = (int) $cover_image_data;
    }
} else {
    // Siamo nell'editor, usa mb_get_block_field()
    $cover_image_data = mb_get_block_field( 'cover_image' );
    $aspect_ratio = mb_get_block_field( 'aspect_ratio' );
    $title = mb_get_block_field( 'title' );
    $text = mb_get_block_field( 'text' );
    
    // Gestione dell'immagine per l'editor
    if ( is_array( $cover_image_data ) && isset( $cover_image_data['ID'] ) ) {
        $cover_image_id = (int) $cover_image_data['ID'];
    } else {
        $cover_image_id = (int) $cover_image_data;
    }
}

// Se tutti i campi sono vuoti, non mostrare nulla.
if ( empty( $cover_image_id ) && empty( $title ) && empty( $text ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block Card</div><div class="components-placeholder__instructions">Aggiungi contenuto per visualizzare la card.</div></div>';
    }
    return;
}

// Assicurati che $aspect_ratio abbia un valore di default
if ( empty( $aspect_ratio ) ) {
    $aspect_ratio = 'ratio-16-9';
}

// Genera un ID unico per questo blocco
$block_id = 'block-card-' . wp_unique_id();

// Attributi wrapper per il blocco (include classi CSS e attributi HTML)
$wrapper_attributes = get_block_wrapper_attributes( [
    'class' => 'block-card',
    'id'    => $block_id,
] );

?>
<div <?php echo $wrapper_attributes; ?>>
    <div class="container rounded-24 overflow-hidden border-2 border-solid border-gray">
        
        <?php if ( $cover_image_id ) : ?>
            <div class="cover relative <?php echo esc_attr( $aspect_ratio ); ?>">
                <?php load_img( class: 'img img-cover', high: $cover_image_id ); ?>
            </div>
        <?php endif; ?>
        
        <?php if ( ! empty( $title ) || ! empty( $text ) ) : ?>
            <div class="content p-48 flex flex-col gap-20">
                
                <?php if ( ! empty( $title ) ) : ?>
                    <div class="title uppercase font-tt-eb text-16 spacing-12 leading-12">
                        <?php echo esc_html( $title ); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ( ! empty( $text ) ) : ?>
                    <div class="text font-ave-m text-20 leading-15 text-gray">
                        <?php echo wp_kses_post( $text ); ?>
                    </div>
                <?php endif; ?>
                
            </div>
        <?php endif; ?>
        
    </div>
</div> 