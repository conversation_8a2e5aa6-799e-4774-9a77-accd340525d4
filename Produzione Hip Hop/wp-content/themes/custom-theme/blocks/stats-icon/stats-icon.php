<?php
// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $boxes = $mb_current_block_data['boxes'] ?? [];
} else {
    // Siamo nell'editor
    $boxes = mb_get_block_field( 'boxes' );
}

if ( empty( $boxes ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block Stats Icon</div><div class="components-placeholder__instructions">Aggiungi uno o più box con statistiche.</div></div>';
    }
    return;
}

$color_class_name = get_current_corso_color_class_name();
$wrapper_attributes = get_safe_block_wrapper_attributes( 
    [ 'class' => 'block-stats-icon my-60' ], 
    'stats-icon' 
);

?>

<div <?php echo $wrapper_attributes; ?>>
	<div class="container flex gap-40">

		<?php foreach ( $boxes as $box ) : ?>
			<div class="box flex-1 grow-1 flex gap-80 justify-between items-center border-2 border-gray border-solid rounded-24 p-60">
				<div class="main">
					<div class="number mb-22 flex gap-14 items-center">
						<?php if ( ! empty( $box['value'] ) ) : ?>
							<div class="value font-ave-r text-60">
								<?php echo esc_html( $box['value'] ); ?>
							</div>
						<?php endif; ?>
						<?php if ( ! empty( $box['unit'] ) ) : ?>
							<div class="unit font-ave-r text-48">
								<?php echo esc_html( $box['unit'] ); ?>
							</div>
						<?php endif; ?>
					</div>
					<?php if ( ! empty( $box['label'] ) ) : ?>
						<div class="text uppercase font-tt-eb text-16 spacing-12 text-brand-<?php echo esc_attr( $color_class_name ); ?>">
							<?php echo esc_html( $box['label'] ); ?>
						</div>
					<?php endif; ?>
				</div>

				<?php
				// Grazie al debug, ora sappiamo che l'ID è una stringa diretta.
				$icon_id = ! empty( $box['icon_image'] ) ? (int) $box['icon_image'] : 0;
				if ( $icon_id ) :
					?>
					<div class="side">
						<div class="icon flex">
							<?php load_img( class: 'img h-60', high: $icon_id ); ?>
						</div>
					</div>
				<?php endif; ?>
			</div>
		<?php endforeach; ?>

	</div>
</div> 