/* Block Comparison Styles */

.block-comparison .container {
    /* Container styles inherited */
}

.block-comparison .header {
    /* Header styles inherited */
}

.block-comparison .col {
    /* Column styles inherited */
}

.block-comparison .text {
    /* Text styles inherited */
}

.block-comparison .row {
    /* Row styles inherited */
}

.block-comparison .row .text {
    /* Row text specific adjustments */
}

.block-comparison .row .text svg {
    max-width: none; /* Override any global max-width constraints */
    flex-shrink: 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .block-comparison .header {
        grid-template-columns: 1fr;
        gap: 0;
    }
    
    .block-comparison .header .col:first-child {
        border-bottom-color: transparent;
        padding-bottom: 12px;
    }
    
    .block-comparison .header .col:last-child {
        padding-top: 12px;
        margin-bottom: 20px;
    }
    
    .block-comparison .row {
        grid-template-columns: 1fr;
        gap: 0;
        margin-bottom: 32px;
    }
    
    .block-comparison .row .col:first-child {
        border-bottom-color: transparent;
        padding-bottom: 12px;
        border-radius: 12px 12px 0 0;
        background-color: rgba(156, 163, 175, 0.1);
    }
    
    .block-comparison .row .col:last-child {
        padding-top: 12px;
        border-radius: 0 0 12px 12px;
    }
    
    .block-comparison .header .text {
        font-size: 24px;
    }
    
    .block-comparison .row .text {
        font-size: 18px;
        line-height: 1.4;
    }
}

@media (max-width: 480px) {
    .block-comparison .header {
        gap: 0;
    }
    
    .block-comparison .row {
        gap: 0;
        margin-bottom: 24px;
    }
    
    .block-comparison .col {
        padding: 20px 16px;
    }
    
    .block-comparison .header .text {
        font-size: 20px;
    }
    
    .block-comparison .row .text {
        font-size: 16px;
        line-height: 1.4;
    }
    
    .block-comparison .row .text svg {
        height: 20px;
        margin-right: 8px;
        top: 1px;
    }
} 