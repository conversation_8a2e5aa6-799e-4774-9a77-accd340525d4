<?php
/**
 * Block Comparison Template
 * 
 * @package Custom_Theme
 */

// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $column1_title = $mb_current_block_data['column1_title'] ?? 'Problema';
    $column2_title = $mb_current_block_data['column2_title'] ?? 'Soluzione';
    $rows = $mb_current_block_data['rows'] ?? [];
} else {
    // Siamo nell'editor, usa mb_get_block_field()
    $column1_title = 'Problema';
    $column2_title = 'Soluzione';
    $rows = [];
    if ( function_exists( 'mb_get_block_field' ) ) {
        $column1_title = mb_get_block_field( 'column1_title' ) ?: 'Problema';
        $column2_title = mb_get_block_field( 'column2_title' ) ?: 'Soluzione';
        $rows = mb_get_block_field( 'rows' );
    }
}

// Se non ci sono righe, non mostrare nulla.
if ( empty( $rows ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block Comparison</div><div class="components-placeholder__instructions">Aggiungi righe di comparazione per visualizzare la tabella.</div></div>';
    }
    return;
}

// Recupera il colore dinamico dal CPT "Corsi"
$color_class_name = get_current_corso_color_class_name();

// Mappa dei colori (come nel single-corsi.php)
$color_map = [
    'blue' => '#1289ED',
    'sky' => '#56CCF2', 
    'pink' => '#E04E9D',
    'violet' => '#A973FF',
    'brown' => '#9D8271',
    'green' => '#25D366',
    'yellow' => '#FFBB44',
    'red' => '#FF4444',
    'ocean' => '#194DD4'
];

// Usa la funzione helper per gli attributi del wrapper
$wrapper_attributes = get_safe_block_wrapper_attributes(
    [ 'class' => 'block-comparison mt-60 mb-60' ],
    'comparison'
);

?>

<div <?php echo $wrapper_attributes; ?>>
    <div class="container">
    
        <div class="header grid grid-cols-2 gap-40">
            <div class="col py-24 border-bottom-4 border-dotted border-gray">
                <div class="text font-ave-m text-28 leading-15">
                    <?php echo esc_html( $column1_title ); ?>
                </div>
            </div>
            <div class="col py-24 border-bottom-4 border-dotted border-brand-<?php echo esc_attr( $color_class_name ); ?>">
                <div class="text font-ave-m text-28 leading-15 text-brand-<?php echo esc_attr( $color_class_name ); ?>">
                    <?php echo esc_html( $column2_title ); ?>
                </div>
            </div>
        </div>

        <div class="rows">
            <?php foreach ( $rows as $row ) : ?>
                <?php 
                $column1 = $row['column1'] ?? '';
                $column2 = $row['column2'] ?? '';
                
                if ( empty( $column1 ) && empty( $column2 ) ) {
                    continue;
                }
                ?>
                <div class="row grid grid-cols-2 gap-40">
                    <div class="col py-24 border-bottom-4 border-dotted border-gray">
                        <?php if ( ! empty( $column1 ) ) : ?>
                            <div class="text font-ave-m text-20 leading-15 text-gray">
                                <?php 
                                remove_filter( 'the_content', 'wpautop' );
                                echo apply_filters( 'the_content', $column1 );
                                add_filter( 'the_content', 'wpautop' );
                                ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="col py-24 border-bottom-4 border-dotted border-brand-<?php echo esc_attr( $color_class_name ); ?>">
                        <?php if ( ! empty( $column2 ) ) : ?>
                            <div class="text font-ave-m text-20 leading-15 text-gray">
                                <svg class="h-24 mr-12 inline-block top-2 relative" width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M21.2129 12.1055L1.99969 12.1055M21.2129 12.1055L14.8489 4.74151M21.2129 12.1055L15.2025 19.1159" stroke="<?php echo esc_attr( $color_map[ $color_class_name ] ); ?>" stroke-width="4" stroke-linecap="round"></path>
                                </svg>
                                <?php 
                                remove_filter( 'the_content', 'wpautop' );
                                echo apply_filters( 'the_content', $column2 );
                                add_filter( 'the_content', 'wpautop' );
                                ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>

    </div>
</div> 