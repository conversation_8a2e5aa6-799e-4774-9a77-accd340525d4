<?php
/**
 * Block Line Text Template
 * 
 * @package Custom_Theme
 */

// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $title = $mb_current_block_data['title'] ?? '';
    $text = $mb_current_block_data['text'] ?? '';
} else {
    // Siamo nell'editor, usa mb_get_block_field()
    $title = mb_get_block_field( 'title' );
    $text = mb_get_block_field( 'text' );
}

// Se tutti i campi sono vuoti, non mostrare nulla.
if ( empty( $title ) && empty( $text ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block Line Text</div><div class="components-placeholder__instructions">Aggiungi un titolo e/o testo per visualizzare il blocco.</div></div>';
    }
    return;
}

// Recupera il colore dinamico dal CPT "Corsi"
$color_class_name = get_current_corso_color_class_name();

// Usa la funzione helper per gli attributi del wrapper
$wrapper_attributes = get_safe_block_wrapper_attributes(
    [ 'class' => 'block-line-text mt-60 mb-60' ],
    'line-text'
);

?>

<div <?php echo $wrapper_attributes; ?>>
    <div class="container border-left-4 border-brand-<?php echo esc_attr( $color_class_name ); ?> border-solid pl-48">
        <div class="content flex flex-col gap-20">
            <?php if ( ! empty( $title ) ) : ?>
                <div class="title font-tt-eb text-16 uppercase spacing-12">
                    <?php echo esc_html( $title ); ?>
                </div>
            <?php endif; ?>
            
            <?php if ( ! empty( $text ) ) : ?>
                <div class="text font-ave-m text-20 leading-15 text-gray">
                    <?php echo wp_kses_post( $text ); ?>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div> 