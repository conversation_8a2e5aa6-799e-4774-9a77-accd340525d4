/* FAQ Block Styles */

.block-faq .head .icon svg {
    transition: transform 0.3s ease;
}

.block-faq .row.active .head .icon svg {
    transform: rotate(45deg);
}

.wp-block-meta-box-faq .rows,
.wp-block-meta-box-faq .row {
    border-right-width: 0;
    border-left-width: 0;
} 

/* Responsive adjustments */
@media (max-width: 480px) {
    .block-faq .head {
        gap: 20px;
        flex-direction: row;
        align-items: flex-start;
    }
    
    .block-faq .head .text {
        flex: 1;
    }
} 