<?php
/**
 * Block FAQ Template
 * 
 * @package Custom_Theme
 */

// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $rows = $mb_current_block_data['rows'] ?? [];
} else {
    // Siamo nell'editor, usa mb_get_block_field()
    $rows = mb_get_block_field( 'rows' );
}

// Se non ci sono righe, non mostrare nulla.
if ( empty( $rows ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block FAQ</div><div class="components-placeholder__instructions">Aggiungi domande per creare la sezione FAQ</div></div>';
    }
    return;
}

// Usa la funzione helper per gli attributi del wrapper
$wrapper_attributes = get_safe_block_wrapper_attributes( 
    [ 'class' => 'block-faq mt-60 mb-60' ], 
    'faq' 
);
?>

<div <?php echo $wrapper_attributes; ?>>
    <div class="container">
        <div class="rows divide-y-2 divide-gray divide-solid border-y-2 border-gray border-solid">
            <?php foreach ( $rows as $index => $row ) :
                $question = $row['question'] ?? '';
                $answer = $row['answer'] ?? '';
                
                if ( empty( $question ) && empty( $answer ) ) {
                    continue;
                }
            ?>
                <div class="row">
                    <div class="head flex justify-between items-center gap-80 py-40 cursor-pointer">
                        <div class="text font-ave-d text-24 leading-15">
                            <?php echo esc_html( $question ); ?>
                        </div>
                        <div class="icon flex shrink-0">
                            <svg class="h-28" width="30" height="30" viewBox="0 0 30 30" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <g clip-path="url(#clip0_555_35215_<?php echo esc_attr( $index ); ?>)">
                                    <path d="M16.364 0H13.6367V30H16.364V0Z" fill="#074D75"/>
                                    <path d="M30 16.364V13.6367L0 13.6367V16.364H30Z" fill="#353F4F"/>
                                </g>
                                <defs>
                                    <clipPath id="clip0_555_35215_<?php echo esc_attr( $index ); ?>">
                                        <rect width="30" height="30" fill="white"/>
                                    </clipPath>
                                </defs>
                            </svg>
                        </div>
                    </div>
                    <div class="body pb-40 hidden">
                        <div class="text font-ave-m leading-15 text-20 text-gray">
                            <?php echo wp_kses_post( $answer ); ?>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        </div>
    </div>
</div> 