( function ($) {
    console.log( 'faq.js loaded' );
    
    /**
     * Inizializza la funzionalità FAQ per un singolo blocco.
     * @param {HTMLElement} blockContainer - L'elemento contenitore del blocco.
     */
    function initializeFAQ( blockContainer ) {
        if ( blockContainer.classList.contains( 'faq-initialized' ) ) {
            return;
        }
        
        const $container = $(blockContainer);
        const $rows = $container.find( '.row' );
        
        $rows.each( function() {
            const $row = $(this);
            const $head = $row.find( '.head' );
            const $body = $row.find( '.body' );
            
            if ( ! $head.length || ! $body.length ) {
                return;
            }
            
            $head.on( 'click', function() {
                // Toggle solo la FAQ corrente
                $row.toggleClass( 'active' );
                $body.slideToggle();
            });
        });
        
        blockContainer.classList.add( 'faq-initialized' );
    }
    
    /**
     * Inizializza tutti i blocchi FAQ nella pagina.
     */
    function initializeAllFAQBlocks() {
        $('.block-faq').each( function() {
            initializeFAQ( this );
        });
    }
    
    // Inizializza al caricamento della pagina
    $(document).ready( function() {
        initializeAllFAQBlocks();
    });
    
    // Inizializza anche quando vengono aggiunti nuovi blocchi (per l'editor)
    const observer = new MutationObserver( function( mutations ) {
        mutations.forEach( function( mutation ) {
            mutation.addedNodes.forEach( function( node ) {
                if ( node.nodeType === 1 ) { // Element node
                    // Check if the added node is a FAQ block
                    if ( node.classList && node.classList.contains( 'block-faq' ) ) {
                        initializeFAQ( node );
                    }
                    
                    // Check if the added node contains FAQ blocks
                    const faqBlocks = node.querySelectorAll ? node.querySelectorAll( '.block-faq' ) : [];
                    faqBlocks.forEach( initializeFAQ );
                }
            });
        });
    });
    
    observer.observe( document.body, {
        childList: true,
        subtree: true
    });
    
})( jQuery ); 