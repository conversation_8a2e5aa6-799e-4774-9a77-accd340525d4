jQuery(document).ready(function($) {
    // Utilizza la "event delegation" di jQuery.
    // Questo aggancia l'evento 'click' al 'document', ma lo esegue solo
    // quando l'elemento cliccato corrisponde a '.block-outline .row .head'.
    // Funziona perfettamente anche per i blocchi aggiunti dinamicamente nell'editor.
    $(document).on('click', '.block-outline .row .head', function() {
        console.log('click');
        // Usa 'this' per riferirsi all'elemento '.head' cliccato.
        // Cerca il '.toggle' al suo interno e aggiunge/rimuove la classe 'active'.
        $(this).find(".toggle").toggleClass('active');

        // Trova l'elemento '.row' più vicino e, al suo interno, 
        // anima l'apertura/chiusura del '.body' con slideToggle.
        $(this).closest(".row").find(".body").slideToggle();
    });
});
