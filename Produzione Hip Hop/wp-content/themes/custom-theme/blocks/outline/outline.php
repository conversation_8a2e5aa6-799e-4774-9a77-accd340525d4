<?php
// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $rows = $mb_current_block_data['rows'] ?? [];
} else {
    // Siamo nell'editor
    $rows = mb_get_block_field( 'rows' );
}

if ( empty( $rows ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block Outline</div><div class="components-placeholder__instructions">Aggiungi una o più righe.</div></div>';
    }
    return;
}

// Imposta gli attributi del wrapper del blocco
$wrapper_attributes = get_safe_block_wrapper_attributes(
    [ 'class' => 'block-outline mt-60 mb-80' ],
    'outline'
);

?>

<div <?php echo $wrapper_attributes; ?>>
	<div class="container border-2 border-gray border-solid rounded-24">
		<div class="rows divide-y-2 divide-gray divide-solid">

			<?php foreach ( $rows as $row ) : ?>
				<?php
				$title    = $row['title'] ?? '';
				$body     = $row['body'] ?? '';
				$has_body = ! empty( trim( $body ) );
				?>
				<div class="row">
					<div class="head flex gap-60 items-center pt-40 pb-40 px-40 justify-between <?php echo $has_body ? 'cursor-pointer' : ''; ?>">
						<div class="text font-din-bc text-28 relative top-2">
							<?php echo esc_html( $title ); ?>
						</div>
						<?php if ( $has_body ) : ?>
							<div class="toggle shrink-0 flex">
								<svg class="h-28" width="29" height="29" viewBox="0 0 29 29" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M14.5 2V27" stroke="#192329" stroke-width="3" stroke-linecap="round"/>
									<path d="M27 14.5L2 14.5" stroke="#192329" stroke-width="3" stroke-linecap="round"/>
								</svg>
							</div>
						<?php endif; ?>
					</div>
					<?php if ( $has_body ) : ?>
						<div class="body hidden px-40 pb-40">
							<div class="text font-ave-m text-20 leading-15 text-gray">
								<?php echo wp_kses_post( $body ); ?>
							</div>
						</div>
					<?php endif; ?>
				</div>
			<?php endforeach; ?>

		</div>
	</div>
</div> 