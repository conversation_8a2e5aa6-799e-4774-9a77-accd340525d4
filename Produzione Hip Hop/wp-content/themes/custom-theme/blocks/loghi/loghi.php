<?php
// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $logos_data = $mb_current_block_data['logos'] ?? [];
    
    // Normalizza i dati: potrebbero essere array di ID o array di oggetti
    $logos = [];
    foreach ( $logos_data as $logo ) {
        if ( is_numeric( $logo ) ) {
            // È solo un ID
            $logos[] = [ 'ID' => $logo ];
        } elseif ( is_array( $logo ) && isset( $logo['ID'] ) ) {
            // È già un array con ID
            $logos[] = $logo;
        } elseif ( is_array( $logo ) && ! empty( $logo ) ) {
            // Potrebbe essere un array con chiave numerica
            $first_key = array_key_first( $logo );
            if ( is_numeric( $first_key ) ) {
                $logos[] = [ 'ID' => $logo[$first_key] ];
            }
        }
    }
} else {
    // Siamo nell'editor
    $logos = mb_get_block_field( 'logos' ) ?: [];
}

// Se non ci sono loghi, mostra placeholder nell'editor
if ( empty( $logos ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block Loghi</div><div class="components-placeholder__instructions">Carica uno o più loghi.</div></div>';
    }
    return;
}

$wrapper_attrs = get_safe_block_wrapper_attributes( 
    [ 'class' => 'block-loghi mt-80 mb-80' ], 
    'loghi' 
);

// Generate a unique ID for this swiper instance
$swiper_id = 'swiper-' . uniqid();

?>

<div <?php echo $wrapper_attrs; ?>>
    <div class="container">
        <div class="items text-center items-center">
            <div class="swiper <?php echo esc_attr( $swiper_id ); ?>">
                <div class="swiper-wrapper items-center">
                    <?php foreach ( $logos as $logo ) : ?>
                        <?php 
                        $logo_id = 0;
                        if ( is_array( $logo ) && isset( $logo['ID'] ) ) {
                            $logo_id = (int) $logo['ID'];
                        } elseif ( is_numeric( $logo ) ) {
                            $logo_id = (int) $logo;
                        }
                        
                        if ( $logo_id > 0 ) : 
                        ?>
                            <div class="swiper-slide h-auto">
                                <div class="item flex place-center h-100-100">
                                    <?php load_img( class: 'img max-w-80-100', high: $logo_id ); ?>
                                </div>
                            </div>
                        <?php endif; ?>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
</div>