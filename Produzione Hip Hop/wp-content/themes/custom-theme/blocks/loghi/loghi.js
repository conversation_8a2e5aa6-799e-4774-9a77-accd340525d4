( function () {
    console.log( 'loghi.js loaded' );
	/**
	 * Inizializza un singolo slider Swiper.
	 * @param {HTMLElement} swiperContainer - L'elemento contenitore dello Swiper.
	 */
	function initializeSwiper( swiperContainer ) {
		if ( ! swiperContainer || swiperContainer.classList.contains( 'swiper-initialized' ) ) {
			return;
		}

		new Swiper( swiperContainer, {
			loop: true,
			slidesPerView: 4,
			spaceBetween: 40,
			autoplay: {
				delay: 3000,
				disableOnInteraction: false,
			},
		} );
	}

	/**
	 * Inizializza tutti gli Swiper trovati in un dato selettore, per il frontend.
	 */
	function initializeAllSwipers() {
		document.querySelectorAll( '.block-loghi .swiper' ).forEach( initializeSwiper );
	}

	// Inizializzazione per il frontend.
	if ( document.readyState === 'loading' ) {
		document.addEventListener( 'DOMContentLoaded', initializeAllSwipers );
	} else {
		initializeAllSwipers();
	}

	// Inizializzazione e reinizializzazione per l'editor di Gutenberg.
	if ( window.wp && window.wp.data && window.wp.data.subscribe ) {
		let initializedBlocks = new Set();

		const initializeBlock = ( block ) => {
			if ( block.name === 'meta-box/loghi' ) {
				const swiperContainer = document.querySelector( `#block-${ block.clientId } .swiper` );
				if ( swiperContainer && ! initializedBlocks.has( block.clientId ) ) {
					initializeSwiper( swiperContainer );
					initializedBlocks.add( block.clientId );
				}
			}
		};

		window.wp.data.subscribe( () => {
			const editor = wp.data.select( 'core/block-editor' );
			if ( ! editor ) return;

			const blocks = editor.getBlocks();
			blocks.forEach( initializeBlock );

			// Pulisce i blocchi rimossi per permettere la reinizializzazione se vengono ri-aggiunti
			const currentBlockIds = new Set( blocks.map( b => b.clientId ) );
			initializedBlocks.forEach( blockId => {
				if ( ! currentBlockIds.has( blockId ) ) {
					initializedBlocks.delete( blockId );
				}
			} );
		} );
	}
} )(); 