<?php
// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $photo_id = $mb_current_block_data['photo'] ?? 0;
    $pretitle = $mb_current_block_data['pretitle'] ?? '';
    $name = $mb_current_block_data['name'] ?? '';
    $role = $mb_current_block_data['role'] ?? '';
    $bio = $mb_current_block_data['bio'] ?? '';
    $social_instagram = $mb_current_block_data['social_instagram'] ?? '';
    $social_tiktok = $mb_current_block_data['social_tiktok'] ?? '';
    $social_facebook = $mb_current_block_data['social_facebook'] ?? '';
    $social_youtube = $mb_current_block_data['social_youtube'] ?? '';
} else {
    // Siamo nell'editor, usa mb_get_block_field()
    $photo_data = mb_get_block_field( 'photo' );
    $photo_id = ! empty( $photo_data['ID'] ) ? (int) $photo_data['ID'] : 0;
    $pretitle = mb_get_block_field( 'pretitle' );
    $name = mb_get_block_field( 'name' );
    $role = mb_get_block_field( 'role' );
    $bio = mb_get_block_field( 'bio' );
    $social_instagram = mb_get_block_field( 'social_instagram' );
    $social_tiktok = mb_get_block_field( 'social_tiktok' );
    $social_facebook = mb_get_block_field( 'social_facebook' );
    $social_youtube = mb_get_block_field( 'social_youtube' );
}

// Se tutti i campi sono vuoti, non mostrare il blocco.
if ( empty( $photo_id ) && empty( $pretitle ) && empty( $name ) && empty( $role ) && empty( $bio ) && empty( $social_instagram ) && empty( $social_tiktok ) && empty( $social_facebook ) && empty( $social_youtube ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Blocco Autore</div><div class="components-placeholder__instructions">Compila i campi per visualizzare il blocco.</div></div>';
    }
    return;
}

$color_class_name = get_current_corso_color_class_name();
$wrapper_attrs = get_safe_block_wrapper_attributes( 
    [ 'class' => 'block-author mb-80 mt-80' ], 
    'author' 
);

$socials = [
    'instagram' => $social_instagram,
    'tiktok'    => $social_tiktok,
    'facebook'  => $social_facebook,
    'youtube'   => $social_youtube,
];
$socials_filled = array_filter( $socials );
?>

<div <?php echo $wrapper_attrs; ?>>
	<div class="container">
		<div class="box border-2 border-gray rounded-24 border-solid p-60 flex flex-col gap-40">
			<div class="head flex justify-between gap-80 items-center">
				<div class="main flex gap-40 items-center">
					<?php if ( $photo_id ) : ?>
						<div class="photo rounded-full overflow-hidden h-100 w-100 relative">
							<?php load_img( class: 'img img-cover', high: $photo_id ); ?>
						</div>
					<?php endif; ?>

					<div class="info">
						<?php if ( ! empty( $pretitle ) ) : ?>
							<div class="pretitle uppercase font-tt-eb text-16 mb-24 spacing-12 text-brand-<?php echo esc_attr( $color_class_name ); ?>">
								<?php echo esc_html( $pretitle ); ?>
							</div>
						<?php endif; ?>

						<?php if ( ! empty( $name ) ) : ?>
							<div class="name font-din-bc text-48 mb-12">
								<?php echo esc_html( $name ); ?>
							</div>
						<?php endif; ?>

						<?php if ( ! empty( $role ) ) : ?>
							<div class="role font-ave-d text-18 text-gray">
								<?php echo esc_html( $role ); ?>
							</div>
						<?php endif; ?>
					</div>
				</div>

				<?php if ( ! empty( $socials_filled ) ) : ?>
					<div class="side">
						<div class="social flex gap-20 items-center">
							<?php foreach ( $socials_filled as $key => $url ) : ?>
								<a href="<?php echo esc_url( $url ); ?>" target="_blank" rel="noopener noreferrer" class="item flex">
									<?php if ( $key === 'instagram' ) : ?>
										<svg class="h-22" width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M15.9287 0H6.07108C2.72349 0 0 2.72362 0 6.0712V15.9288C0 19.2766 2.72349 22 6.07108 22H15.9287C19.2766 22 22 19.2764 22 15.9288V6.0712C22.0001 2.72362 19.2766 0 15.9287 0ZM20.0482 15.9288C20.0482 18.2002 18.2002 20.048 15.9288 20.048H6.07108C3.79979 20.0482 1.95195 18.2002 1.95195 15.9288V6.0712C1.95195 3.79992 3.79979 1.95195 6.07108 1.95195H15.9287C18.2001 1.95195 20.048 3.79992 20.048 6.0712L20.0482 15.9288Z" fill="#636C74"></path><path d="M10.997 5.33203C7.87112 5.33203 5.32812 7.87503 5.32812 11.0009C5.32812 14.1266 7.87112 16.6695 10.997 16.6695C14.1228 16.6695 16.6658 14.1266 16.6658 11.0009C16.6658 7.87503 14.1228 5.33203 10.997 5.33203ZM10.997 14.7174C8.94755 14.7174 7.28007 13.0502 7.28007 11.0008C7.28007 8.9512 8.94742 7.28385 10.997 7.28385C13.0465 7.28385 14.7138 8.9512 14.7138 11.0008C14.7138 13.0502 13.0464 14.7174 10.997 14.7174Z" fill="#636C74"></path><path d="M16.9119 3.67578C16.5358 3.67578 16.1664 3.82804 15.9008 4.0948C15.634 4.36026 15.4805 4.72983 15.4805 5.10721C15.4805 5.48341 15.6341 5.85286 15.9008 6.11962C16.1663 6.38508 16.5358 6.53863 16.9119 6.53863C17.2893 6.53863 17.6576 6.38508 17.9243 6.11962C18.1911 5.85286 18.3433 5.48329 18.3433 5.10721C18.3433 4.72983 18.1911 4.36026 17.9243 4.0948C17.6588 3.82804 17.2893 3.67578 16.9119 3.67578Z" fill="#636C74"></path></svg>
									<?php elseif ( $key === 'tiktok' ) : ?>
										<svg class="h-22" width="17" height="22" viewBox="0 0 17 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M17 9.20169C15.3289 9.2058 13.6988 8.66934 12.3399 7.66809V14.6511C12.3395 15.9444 11.9555 17.2068 11.2392 18.2694C10.523 19.332 9.50867 20.1442 8.33186 20.5974C7.15509 21.0505 5.87197 21.1231 4.65406 20.8053C3.43615 20.4876 2.3415 19.7946 1.5165 18.8192C0.691494 17.8437 0.175453 16.6323 0.0373795 15.3468C-0.100703 14.0613 0.145765 12.763 0.743827 11.6256C1.34188 10.4882 2.26302 9.56583 3.38408 8.98185C4.50512 8.39788 5.77266 8.18013 7.01718 8.35772V11.8699C6.44768 11.6855 5.83614 11.6911 5.2699 11.8858C4.70365 12.0805 4.21165 12.4544 3.86417 12.9541C3.51668 13.4539 3.33147 14.0538 3.33499 14.6684C3.3385 15.2829 3.53056 15.8806 3.88375 16.376C4.23693 16.8715 4.73317 17.2394 5.30161 17.4272C5.87005 17.6151 6.4816 17.6132 7.04895 17.4219C7.61629 17.2306 8.11041 16.8597 8.46074 16.3621C8.81106 15.8645 8.99972 15.2657 8.99972 14.6511V1H12.3399C12.3377 1.29036 12.3613 1.58033 12.4105 1.86624C12.5266 2.50447 12.7679 3.11163 13.1198 3.65057C13.4716 4.1895 13.9266 4.64889 14.4568 5.00062C15.2111 5.51407 16.0956 5.78774 17 5.78754V9.20169Z" fill="#636C74"></path></svg>
									<?php elseif ( $key === 'facebook' ) : ?>
										<svg class="h-22" width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M22.0378 11.0404C22.0378 4.94301 17.0948 0 10.9974 0C4.90004 0 -0.0429688 4.94301 -0.0429688 11.0404C-0.0429688 16.2179 3.52176 20.5625 8.33052 21.7558V14.4144H6.05399V11.0404H8.33052V9.58661C8.33052 5.82889 10.0312 4.08716 13.7204 4.08716C14.42 4.08716 15.6269 4.2245 16.1206 4.3614V7.4196C15.8601 7.39221 15.4074 7.37853 14.8452 7.37853C13.0351 7.37853 12.3355 8.06435 12.3355 9.84716V11.0404H15.9418L15.3222 14.4144H12.3355V22C17.8023 21.3398 22.0383 16.6851 22.0383 11.0404H22.0378Z" fill="#636C74"></path></svg>
									<?php elseif ( $key === 'youtube' ) : ?>
										<svg class="h-22" width="26" height="22" viewBox="0 0 26 22" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M25.8548 5.24706C25.8548 5.02354 25.5147 3.2353 24.7209 2.45294C23.7003 1.3353 22.5663 1.22353 21.9992 1.22353H21.8859C18.3706 1 13.1542 1 13.0408 1C13.0408 1 7.71109 1 4.19574 1.22353H4.08235C3.51535 1.22353 2.38137 1.3353 1.36079 2.45294C0.566992 3.34706 0.226797 5.1353 0.226797 5.35883C0.226797 5.47059 0 7.48237 0 9.6059V11.5059C0 13.6294 0.226797 15.6412 0.226797 15.753C0.226797 15.9765 0.566992 17.7647 1.36079 18.547C2.26797 19.553 3.40196 19.6647 4.08235 19.7765C4.19574 19.7765 4.30913 19.7765 4.42253 19.7765C6.46371 20 12.7006 20 12.9274 20C12.9274 20 18.2572 20 21.7725 19.7765H21.8859C22.4529 19.6647 23.5869 19.553 24.6074 18.547C25.4012 17.653 25.7414 15.8647 25.7414 15.6412C25.7414 15.5294 25.9683 13.5177 25.9683 11.3942V9.49414C26.0816 7.48237 25.8548 5.35883 25.8548 5.24706ZM17.4634 10.7236L10.6595 14.3C10.5461 14.3 10.5461 14.4119 10.4327 14.4119C10.3193 14.4119 10.2059 14.4119 10.2059 14.3C10.0925 14.1883 9.97906 14.0765 9.97906 13.853V6.58825C9.97906 6.36472 10.0925 6.25295 10.2059 6.14119C10.3193 6.02942 10.5461 6.02942 10.7728 6.14119L17.5768 9.71767C17.8035 9.82943 17.917 9.9412 17.917 10.1647C17.917 10.3883 17.6901 10.6117 17.4634 10.7236Z" fill="#636C74"></path></svg>
									<?php endif; ?>
								</a>
							<?php endforeach; ?>
						</div>
					</div>
				<?php endif; ?>
			</div>

			<?php if ( ! empty( $bio ) ) : ?>
				<div class="body">
					<div class="bio font-ave-m text-20 leading-15 text-gray">
						<?php echo wp_kses_post( $bio ); ?>
					</div>
				</div>
			<?php endif; ?>

		</div>
	</div>
</div> 