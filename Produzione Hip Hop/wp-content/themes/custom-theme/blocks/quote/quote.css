/* Quote Block Styles */



/* Responsive adjustments */
@media (max-width: 768px) {
    .block-quote .footer {
        flex-direction: column;
        align-items: center;
        gap: 40px;
        text-align: center;
    }
    
    .block-quote .text {
        font-size: 28px;
        line-height: 1.3;
    }
}

@media (max-width: 480px) {
    .block-quote .container {
        gap: 32px;
    }
    
    .block-quote .text {
        font-size: 24px;
        line-height: 1.4;
    }
    
    .block-quote .footer {
        gap: 32px;
    }
    
    .block-quote .icon svg {
        height: 36px;
    }
} 