<?php
/**
 * Block Quote Template
 * 
 * @package Custom_Theme
 */

// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $text = $mb_current_block_data['text'] ?? '';
    $author_photo_id = $mb_current_block_data['author_photo'] ?? 0;
    $author_name = $mb_current_block_data['author_name'] ?? '';
} else {
    // Siamo nell'editor, usa mb_get_block_field()
    $text = mb_get_block_field( 'text' );
    $author_photo_data = mb_get_block_field( 'author_photo' );
    $author_photo_id = ! empty( $author_photo_data['ID'] ) ? (int) $author_photo_data['ID'] : 0;
    $author_name = mb_get_block_field( 'author_name' );
}

// Se tutti i campi sono vuoti, non mostrare nulla.
if ( empty( $text ) && empty( $author_photo_id ) && empty( $author_name ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block Quote</div><div class="components-placeholder__instructions">Aggiungi una citazione per visualizzare il blocco.</div></div>';
    }
    return;
}

// Recupera il colore dinamico dal CPT "Corsi"
$color_class_name = get_current_corso_color_class_name();
$color_map = [
    'blue' => '#1289ED',
    'sky' => '#56CCF2', 
    'pink' => '#E04E9D',
    'violet' => '#A973FF',
    'brown' => '#9D8271',
    'green' => '#25D366',
    'yellow' => '#FFBB44',
    'red' => '#FF4444',
    'ocean' => '#194DD4'
];

// Usa la funzione helper per gli attributi del wrapper
$wrapper_attributes = get_safe_block_wrapper_attributes(
    [ 'class' => 'block-quote mb-60 mt-60' ],
    'quote'
);

?>

<div <?php echo $wrapper_attributes; ?>>
    <div class="container flex flex-col gap-40">
        <?php if ( ! empty( $text ) ) : ?>
            <div class="main">
                <div class="text font-ave-r text-36 leading-14 italic">
                    <?php echo wp_kses_post( $text ); ?>
                </div>
            </div>
        <?php endif; ?>
        
        <div class="footer flex justify-between gap-60 items-center">
            <div class="author flex items-center gap-36">  
                <?php if ( $author_photo_id ) : ?>
                    <div class="photo h-60 w-60 rounded-full overflow-hidden relative">
                        <?php load_img( class: 'img img-cover', high: $author_photo_id ); ?>
                    </div>
                <?php endif; ?>
                
                <?php if ( ! empty( $author_name ) ) : ?>
                    <div class="text uppercase font-tt-eb text-16 spacing-12">
                        <?php echo esc_html( $author_name ); ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="icon">
                <svg class="h-48" width="68" height="53" viewBox="0 0 68 53" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path d="M45.1603 53L41.6997 48.8158C50.0051 42.7719 54.1578 36.7281 54.1578 30.6842C54.1578 28.2434 53.6387 26.3838 52.6005 25.1053C51.5623 23.943 50.0628 22.7226 48.1018 21.4441C45.9101 20.1656 44.2952 18.8289 43.257 17.4342C42.2188 16.1557 41.6997 14.2961 41.6997 11.8553C41.6997 8.48465 42.8533 5.63706 45.1603 3.3125C47.352 1.10417 50.0628 0 53.2926 0C57.676 0 61.2519 1.6853 64.0204 5.05592C66.6735 8.54276 68 13.0757 68 18.6546C68 25.6283 66.0967 31.9627 62.2901 37.6579C58.4835 43.4693 52.7735 48.5833 45.1603 53ZM3.46056 53L0 48.8158C8.30534 42.7719 12.458 36.7281 12.458 30.6842C12.458 28.2434 11.9389 26.3838 10.9008 25.1053C9.86259 23.943 8.36301 22.7226 6.40203 21.4441C4.21034 20.1656 2.59542 18.8289 1.55725 17.4342C0.519081 16.1557 0 14.2961 0 11.8553C0 8.48465 1.15352 5.63706 3.46056 3.3125C5.65224 1.10417 8.36302 0 11.5929 0C15.9762 0 19.5522 1.6853 22.3206 5.05592C24.9737 8.54276 26.3003 13.0757 26.3003 18.6546C26.3003 25.6283 24.3969 32.0208 20.5903 37.8322C16.6684 43.6436 10.9584 48.6996 3.46056 53Z" fill="<?php echo esc_attr( $color_map[ $color_class_name ] ?? '#A973FF' ); ?>"/>
                </svg>
            </div>
        </div>
    </div>
</div> 