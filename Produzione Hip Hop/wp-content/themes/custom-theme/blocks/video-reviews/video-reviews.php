<?php
/**
 * Block Video Reviews Template
 * 
 * @package Custom_Theme
 */

// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $videos = $mb_current_block_data['videos'] ?? [];
} else {
    // Siamo nell'editor, usa mb_get_block_field()
    $videos = [];
    if ( function_exists( 'mb_get_block_field' ) ) {
        $videos = mb_get_block_field( 'videos' );
    }
}

// Se non ci sono video, non mostrare nulla.
if ( empty( $videos ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block Video Reviews</div><div class="components-placeholder__instructions">Aggiungi video testimonial.</div></div>';
    }
    return;
}

// Ottieni l'ID del blocco per garantire l'unicità
$block_id = 'video-reviews-' . uniqid();
$lightbox_id = 'lightbox-' . uniqid();

// Ottieni i dati del colore dal corso
$color_class_name = get_current_corso_color_class_name();

// Ottieni gli attributi del wrapper
$wrapper_attributes = get_safe_block_wrapper_attributes( [ 'class' => 'block-video-reviews' ], 'video-reviews' );
?>

<div <?php echo $wrapper_attributes; ?>>
    <div class="container relative m--40">
        <div class="swiper p-40" id="<?php echo esc_attr( $block_id ); ?>">
            <div class="swiper-wrapper">
                <?php foreach ( $videos as $video ) : ?>
                    <?php
                    // Gestione corretta dell'ID immagine autore come nel blocco quote
                    if ( isset( $mb_current_block_data ) ) {
                        // Frontend: l'ID potrebbe essere direttamente un numero
                        $author_photo_id = ! empty( $video['author_photo'] ) ? (int) $video['author_photo'] : 0;
                    } else {
                        // Editor: l'ID è in un array
                        $author_photo_id = ! empty( $video['author_photo']['ID'] ) ? (int) $video['author_photo']['ID'] : 0;
                    }
                    
                    $author_name = $video['author_name'] ?? '';
                    $review_text = $video['review_text'] ?? '';
                    $video_url = $video['video_url'] ?? '';
                    $aspect_ratio = $video['aspect_ratio'] ?? 'ratio-16-9';
                    
                    // Ottieni i dati del video usando la funzione helper
                    $video_data = custom_theme_get_video_data( $video_url );
                    ?>
                    <div class="swiper-slide">
                        <div class="item px-28 pt-28 pb-40 rounded-24 bg-white shadow-48 shadow-opacity-10">
                            <div class="head">
                                <div class="clip relative rounded-12 overflow-hidden">
                                    <?php if ( ! empty( $video_url ) && ! empty( $video_data['source'] ) ) : ?>
                                        <div class="play h-120 absolute-center flex place-center z-40 rounded-full w-120 cursor-pointer" 
                                             data-video="<?php echo esc_attr( $video_url ); ?>"
                                             data-video-id="<?php echo esc_attr( $video_data['video_id'] ); ?>"
                                             data-video-hash="<?php echo esc_attr( $video_data['hash'] ); ?>"
                                             data-lightbox="<?php echo esc_attr( $lightbox_id ); ?>">
                                            <div class="icon">
                                                <svg class="h-50-100" width="51" height="60" viewBox="0 0 51 60" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M48.0174 27.6454L17.8589 3.836C15.8916 2.28285 13 3.68412 13 6.19065V53.8094C13 56.3159 15.8916 57.7172 17.8589 56.164L48.0174 32.3546C49.5389 31.1535 49.5389 28.8465 48.0174 27.6454Z" fill="white"/>
                                                </svg>
                                            </div>
                                        </div>
                                    <?php endif; ?>
                                    <div class="video <?php echo esc_attr( $aspect_ratio ); ?> relative rounded-12 overflow-hidden">
                                        <?php if ( ! empty( $video_data['thumbnail_url'] ) ) : ?>
                                            <img class="img img-cover" src="<?php echo esc_url( $video_data['thumbnail_url'] ); ?>" alt="Video thumbnail">
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                            <div class="body relative z-40 pt-40">
                                <?php if ( $author_photo_id ) : ?>
                                    <div class="photo w-80 h-80 rounded-full overflow-hidden relative absolute top--60 right-28 z-40">
                                        <?php load_img( class: 'img img-cover', high: $author_photo_id ); ?>
                                    </div>
                                <?php endif; ?>
                                <div class="author">
                                    <?php if ( ! empty( $author_name ) ) : ?>
                                        <div class="name font-tt-eb text-16 uppercase spacing-12 mb-16">
                                            <?php echo esc_html( $author_name ); ?>
                                        </div>
                                    <?php endif; ?>
                                    <?php if ( ! empty( $review_text ) ) : ?>
                                        <div class="text font-ave-m text-gray leading-14 text-18">
                                            <?php echo wp_kses_post( $review_text ); ?>
                                        </div>
                                    <?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endforeach; ?>
            </div>
        </div>
        
        <div class="prev absolute-center-y left--40 z-50 cursor-pointer">
            <svg class="h-80" width="78" height="78" viewBox="0 0 78 78" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g filter="url(#filter0_d_555_17971)">
                    <circle cx="30" cy="30" r="30" transform="matrix(-1 0 0 1 69 6)" fill="white"/>
                </g>
                <path d="M43 27L34 36L43 45" stroke="#636C74" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                <defs>
                    <filter id="filter0_d_555_17971" x="0.172311" y="0.114874" width="77.6554" height="77.6554" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="2.94256"/>
                        <feGaussianBlur stdDeviation="4.41384"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0.156863 0 0 0 0 0.235294 0 0 0 0 0.313726 0 0 0 0.15 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_555_17971"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_555_17971" result="shape"/>
                    </filter>
                </defs>
            </svg>
        </div>
        
        <div class="next absolute-center-y right--40 z-50 cursor-pointer">
            <svg class="h-80" width="78" height="78" viewBox="0 0 78 78" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g filter="url(#filter0_d_555_17968)">
                    <circle cx="39" cy="36" r="30" fill="white"/>
                </g>
                <path d="M36 27L45 36L36 45" stroke="#636C74" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                <defs>
                    <filter id="filter0_d_555_17968" x="0.172311" y="0.114874" width="77.6554" height="77.6554" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="2.94256"/>
                        <feGaussianBlur stdDeviation="4.41384"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0.156863 0 0 0 0 0.235294 0 0 0 0 0.313726 0 0 0 0.15 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_555_17968"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_555_17968" result="shape"/>
                    </filter>
                </defs>
            </svg>
        </div>
    </div>
    <div class="lightbox fixed inset-0 bg-black bg-opacity-90 z-50 hidden backdrop-filter backdrop-blur-sm" id="<?php echo esc_attr( $lightbox_id ); ?>">
        <div class="lightbox-content relative w-full h-full flex items-center justify-center p-20">
            <button class="lightbox-close absolute top-4 right-4 text-white text-3xl w-40 h-40 rounded-full bg-white bg-opacity-20 hover:bg-opacity-30 flex items-center justify-center transition-all z-10">&times;</button>
            <div class="video-container w-full max-w-4xl aspect-video">
                <div class="player w-full h-full bg-black rounded-lg overflow-hidden">
                   
                </div>
            </div>
        </div>
    </div>
</div> 