( function ( $ ) {
    console.log( 'video-reviews.js loaded' );
    
    /**
     * Inizializza la funzionalità Video Reviews per un singolo blocco.
     * @param {HTMLElement} blockContainer - L'elemento contenitore del blocco.
     */
    function initializeVideoReviews( blockContainer ) {
        if ( blockContainer.classList.contains( 'video-reviews-initialized' ) ) {
            return;
        }
        
        const $container = $(blockContainer);
        const $swiper = $container.find( '.swiper' );
        const $prev = $container.find( '.prev' );
        const $next = $container.find( '.next' );
        const $playButtons = $container.find( '.play' );
        const $lightbox = $container.find( '.lightbox' );
        const $lightboxPlayer = $lightbox.find( '.player' );
        const $lightboxClose = $lightbox.find( '.lightbox-close' );
        
        if ( ! $swiper.length ) {
            return;
        }
        
        // Inizializza Swiper
        const swiper = new Swiper( $swiper[0], {
            slidesPerView: 2,
            spaceBetween: 20,
            navigation: {
                nextEl: $next[0],
                prevEl: $prev[0],
            },
            breakpoints: {
                // Quando la larghezza della finestra è >= 320px
                320: {
                    slidesPerView: 1,
                    spaceBetween: 10
                },
                // Quando la larghezza della finestra è >= 768px
                768: {
                    slidesPerView: 2,
                    spaceBetween: 20
                }
            }
        });
        
        // Funzione per chiudere il lightbox
        const closeLightbox = () => {
            $lightbox.addClass( 'hidden' );
            $lightboxPlayer.empty(); // Rimuove il video per fermarlo
        };
        
        // Funzione per aprire il lightbox con il video
        const openLightbox = ( videoId, videoHash ) => {
            // Costruisci l'URL dell'embed Vimeo
            let videoSrc = `https://player.vimeo.com/video/${videoId}?autoplay=1&autopause=0&dnt=1`;
            if ( videoHash ) {
                videoSrc += `&h=${videoHash}`;
            }
            
            // Crea l'iframe
            const $iframe = $('<iframe>', {
                src: videoSrc,
                width: '100%',
                height: '100%',
                frameborder: 0,
                allow: 'autoplay; fullscreen; picture-in-picture',
                allowfullscreen: '',
                class: 'w-full h-full'
            });
            
            // Inserisci l'iframe nel player e mostra il lightbox
            $lightboxPlayer.html( $iframe );
            $lightbox.removeClass( 'hidden' );
        };
        
        // Gestione click sui pulsanti play
        $playButtons.on( 'click', function() {
            const videoId = $(this).data( 'video-id' );
            const videoHash = $(this).data( 'video-hash' );
            
            if ( videoId ) {
                openLightbox( videoId, videoHash );
            }
        });
        
        // Gestione chiusura lightbox
        $lightboxClose.on( 'click', closeLightbox );
        
        // Chiudi lightbox cliccando fuori dal contenuto
        $lightbox.on( 'click', function( e ) {
            if ( e.target === this ) {
                closeLightbox();
            }
        });
        
        // Chiudi lightbox con il tasto Escape
        $(document).on( 'keydown', function( e ) {
            if ( e.key === 'Escape' && ! $lightbox.hasClass( 'hidden' ) ) {
                closeLightbox();
            }
        });
        
        // Segna come inizializzato
        blockContainer.classList.add( 'video-reviews-initialized' );
    }
    
    /**
     * Inizializza tutti i blocchi Video Reviews nella pagina.
     */
    function initializeAllVideoReviews() {
        const blocks = document.querySelectorAll( '.block-video-reviews' );
        
        blocks.forEach( function( block ) {
            initializeVideoReviews( block );
        });
    }
    
    // Inizializza quando il DOM è pronto
    document.addEventListener( 'DOMContentLoaded', function() {
        initializeAllVideoReviews();
    });
    
    // Inizializza anche quando vengono inseriti nuovi blocchi nell'editor
    if ( typeof wp !== 'undefined' && wp.data && wp.data.subscribe ) {
        wp.data.subscribe( function() {
            // Ritarda l'inizializzazione per permettere al DOM di aggiornarsi
            setTimeout( function() {
                initializeAllVideoReviews();
            }, 100 );
        });
    }
    
    // Osserva le modifiche al DOM per l'editor
    if ( typeof MutationObserver !== 'undefined' ) {
        const observer = new MutationObserver( function( mutations ) {
            mutations.forEach( function( mutation ) {
                if ( mutation.type === 'childList' ) {
                    mutation.addedNodes.forEach( function( node ) {
                        if ( node.nodeType === 1 ) { // Element node
                            // Verifica se il nodo aggiunto è un blocco video-reviews
                            if ( node.classList && node.classList.contains( 'block-video-reviews' ) ) {
                                initializeVideoReviews( node );
                            }
                            
                            // Verifica se contiene blocchi video-reviews
                            const childBlocks = node.querySelectorAll && node.querySelectorAll( '.block-video-reviews' );
                            if ( childBlocks ) {
                                childBlocks.forEach( function( block ) {
                                    initializeVideoReviews( block );
                                });
                            }
                        }
                    });
                }
            });
        });
        
        observer.observe( document.body, {
            childList: true,
            subtree: true
        });
    }
    
} )( jQuery ); 