/* Video Reviews Block Styles */

.block-video-reviews .item .play {
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(4px);
    transition: all 0.3s ease;
}

.block-video-reviews .item .play:hover {
    background: rgba(255, 255, 255, 0.4);
    transform: scale(1.05);
}

.block-video-reviews .item .clip:before {
    content: "";
    inset: 0;
    position: absolute;
    z-index: 40;
    background: rgba(40, 60, 80, 0.2);
    pointer-events: none;
}

.block-video-reviews .swiper-slide {
    height: auto;
}

.block-video-reviews .item {
    /* Ensure proper structure */
}

.block-video-reviews .head {
    /* Header styles */
}

.block-video-reviews .clip {
    /* Clip container styles */
}

.block-video-reviews .body {
    /* Body styles */
}

.block-video-reviews .photo {
    /* Photo styles */
}

.block-video-reviews .author {
    /* Author styles */
}

.block-video-reviews .name {
    /* Name styles */
}

.block-video-reviews .text {
    /* Text styles */
}

/* Navigation buttons */
.block-video-reviews .prev,
.block-video-reviews .next {
    transition: all 0.3s ease;
}

.block-video-reviews .prev:hover,
.block-video-reviews .next:hover {
    transform: scale(1.1);
}

/* Lightbox Styles */
.block-video-reviews .lightbox {
    backdrop-filter: blur(4px);
    transition: all 0.3s ease;
}

.block-video-reviews .lightbox.hidden {
    opacity: 0;
    pointer-events: none;
}

.block-video-reviews .lightbox-close {
    transition: all 0.3s ease;
}

.block-video-reviews .lightbox-close:hover {
    background: rgba(255, 255, 255, 0.4) !important;
    transform: scale(1.1);
}

.block-video-reviews .video-container {
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
}

.block-video-reviews .lightbox iframe {
    border-radius: 8px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .block-video-reviews .container {
        margin-left: -20px;
        margin-right: -20px;
    }
    
    .block-video-reviews .swiper {
        padding: 20px;
    }
    
    .block-video-reviews .prev,
    .block-video-reviews .next {
        display: none;
    }
    
    .block-video-reviews .lightbox-content {
        padding: 10px;
    }
    
    .block-video-reviews .video-container {
        max-width: 95%;
    }
}

@media (max-width: 480px) {
    .block-video-reviews .item {
        padding: 20px 20px 30px 20px;
    }
    
    .block-video-reviews .play {
        width: 100px;
        height: 100px;
    }
    
    .block-video-reviews .photo {
        width: 60px;
        height: 60px;
        top: -30px;
        right: 20px;
    }
    
    .block-video-reviews .body {
        padding-top: 30px;
    }
    
    .block-video-reviews .lightbox-close {
        top: 10px;
        right: 10px;
        width: 36px;
        height: 36px;
        font-size: 24px;
    }
} 