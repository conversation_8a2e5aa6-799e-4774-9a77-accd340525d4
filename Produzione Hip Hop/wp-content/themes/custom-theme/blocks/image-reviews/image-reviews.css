/* Image Reviews Block Styles */

.block-image-reviews .container {
    /* Container styles inherited */
}

.block-image-reviews .swiper-slide {
    width: auto;
    height: auto;
}

.block-image-reviews .item {
    transition: transform 0.3s ease;
}

.block-image-reviews .item:hover {
    transform: scale(1.02);
}

.block-image-reviews .item img {
    transition: transform 0.3s ease;
}

.block-image-reviews .swiper-button-disabled {
    opacity: 0.3;
    pointer-events: none;
}

/* Navigation buttons */
.block-image-reviews .prev,
.block-image-reviews .next {
    transition: all 0.3s ease;
}

.block-image-reviews .prev:hover,
.block-image-reviews .next:hover {
    transform: scale(1.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .block-image-reviews .container {
        margin-left: -20px;
        margin-right: -20px;
    }
    
    .block-image-reviews .swiper {
        padding: 20px;
    }
    
    .block-image-reviews .prev,
    .block-image-reviews .next {
        transform: scale(0.8);
    }
    
    .block-image-reviews .prev {
        left: -20px;
    }
    
    .block-image-reviews .next {
        right: -20px;
    }
}

@media (max-width: 480px) {
    .block-image-reviews .prev,
    .block-image-reviews .next {
        transform: scale(0.7);
        display: none; /* Hide navigation on very small screens */
    }
    
    .block-image-reviews .prev {
        left: -10px;
    }
    
    .block-image-reviews .next {
        right: -10px;
    }
    
    .block-image-reviews .container {
        margin-left: -10px;
        margin-right: -10px;
    }
    
    .block-image-reviews .swiper {
        padding: 10px;
    }
} 