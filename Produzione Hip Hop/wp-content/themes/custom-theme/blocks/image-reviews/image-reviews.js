( function () {
    console.log( 'image-reviews.js loaded' );
    
    /**
     * Inizializza un singolo slider Swiper.
     * @param {HTMLElement} blockContainer - L'elemento contenitore del blocco.
     */
    function initializeSwiper( blockContainer ) {
        const swiperContainer = blockContainer.querySelector( '.swiper' );
        if ( ! swiperContainer || swiperContainer.classList.contains( 'swiper-initialized' ) ) {
            return;
        }

        const prevButton = blockContainer.querySelector( '.prev' );
        const nextButton = blockContainer.querySelector( '.next' );

        new Swiper( swiperContainer, {
            slidesPerView: 2,
            spaceBetween: 20,
            loop: true,
            navigation: {
                nextEl: nextButton,
                prevEl: prevButton,
            },
            autoplay: {
                delay: 4000,
                disableOnInteraction: false,
            },
            breakpoints: {
                // Quando la larghezza della finestra è >= 320px
                320: {
                    slidesPerView: 1,
                    spaceBetween: 10
                },
                // Quando la larghezza della finestra è >= 768px
                768: {
                    slidesPerView: 2,
                    spaceBetween: 20
                }
            }
        } );
    }

    /**
     * Inizializza un singolo blocco image-reviews.
     * @param {HTMLElement} blockContainer - L'elemento contenitore del blocco.
     */
    function initializeBlock( blockContainer ) {
        if ( ! blockContainer || blockContainer.classList.contains( 'image-reviews-initialized' ) ) {
            return;
        }

        initializeSwiper( blockContainer );
        
        blockContainer.classList.add( 'image-reviews-initialized' );
    }

    /**
     * Inizializza tutti i blocchi image-reviews trovati, per il frontend.
     */
    function initializeAllBlocks() {
        document.querySelectorAll( '.block-image-reviews' ).forEach( initializeBlock );
    }

    // Inizializzazione per il frontend.
    if ( document.readyState === 'loading' ) {
        document.addEventListener( 'DOMContentLoaded', initializeAllBlocks );
    } else {
        initializeAllBlocks();
    }

    // Inizializzazione e reinizializzazione per l'editor di Gutenberg.
    if ( window.wp && window.wp.data && window.wp.data.subscribe ) {
        let initializedBlocks = new Set();

        const initializeEditorBlock = ( block ) => {
            if ( block.name === 'meta-box/image-reviews' ) {
                // Usa un timeout per assicurarsi che il DOM sia aggiornato
                setTimeout( () => {
                    const blockElement = document.querySelector( `#block-${ block.clientId }` );
                    if ( blockElement && ! initializedBlocks.has( block.clientId ) ) {
                        initializeBlock( blockElement );
                        initializedBlocks.add( block.clientId );
                    }
                }, 100 );
            }
        };

        window.wp.data.subscribe( () => {
            const editor = wp.data.select( 'core/block-editor' );
            if ( ! editor ) return;

            const blocks = editor.getBlocks();
            blocks.forEach( initializeEditorBlock );

            // Pulisce i blocchi rimossi per permettere la reinizializzazione se vengono ri-aggiunti
            const currentBlockIds = new Set( blocks.map( b => b.clientId ) );
            initializedBlocks.forEach( blockId => {
                if ( ! currentBlockIds.has( blockId ) ) {
                    initializedBlocks.delete( blockId );
                }
            } );
        } );
    }

    // Osserva le modifiche al DOM per l'editor
    if ( typeof MutationObserver !== 'undefined' ) {
        const observer = new MutationObserver( function( mutations ) {
            mutations.forEach( function( mutation ) {
                if ( mutation.type === 'childList' ) {
                    mutation.addedNodes.forEach( function( node ) {
                        if ( node.nodeType === 1 ) { // Element node
                            // Verifica se il nodo aggiunto è un blocco image-reviews
                            if ( node.classList && node.classList.contains( 'block-image-reviews' ) ) {
                                initializeBlock( node );
                            }
                            
                            // Verifica se contiene blocchi image-reviews
                            const childBlocks = node.querySelectorAll && node.querySelectorAll( '.block-image-reviews' );
                            if ( childBlocks ) {
                                childBlocks.forEach( function( block ) {
                                    initializeBlock( block );
                                });
                            }
                        }
                    });
                }
            });
        });
        
        observer.observe( document.body, {
            childList: true,
            subtree: true
        });
    }
} )(); 