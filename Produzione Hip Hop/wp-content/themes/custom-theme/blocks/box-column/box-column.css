/* Box Column Block Styles */

.block-box-column .container {
    /* Container styles are inherited from the HTML structure */
}

.block-box-column .main {
    /* Content area specific styles if needed */
}

.block-box-column .icon {
    /* Ensure proper icon sizing and positioning */
}

.block-box-column .icon img,
.block-box-column .icon svg {
    max-width: none; /* Override any global max-width constraints */
    flex-shrink: 0;
}

.block-box-column .title {
    /* Title specific styles if needed */
}

.block-box-column .text {
    /* Text specific styles if needed */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .block-box-column .container {
        padding: 40px;
    }
    
    .block-box-column .icon {
        margin-bottom: 32px;
    }
    
    .block-box-column .title {
        margin-bottom: 16px;
    }
}

@media (max-width: 480px) {
    .block-box-column .container {
        padding: 32px;
    }
    
    .block-box-column .icon {
        margin-bottom: 24px;
    }
    
    .block-box-column .icon img,
    .block-box-column .icon svg {
        height: 40px;
    }
} 