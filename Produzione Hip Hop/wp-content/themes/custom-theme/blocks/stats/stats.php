<?php
// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $stats = $mb_current_block_data['stats'] ?? [];
} else {
    // Siamo nell'editor
    $stats = mb_get_block_field( 'stats' ) ?: [];
}

// Se non ci sono statistiche, non mostrare nulla
if ( empty( $stats ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block Stats</div><div class="components-placeholder__instructions">Aggiungi una o più statistiche.</div></div>';
    }
    return;
}

// Recupera la classe del colore corrente del corso
$color_class_name = get_current_corso_color_class_name();

// Determina il numero di colonne per la griglia
$count = count( $stats );
$grid_cols_class = 'grid-cols-' . $count;

$wrapper_attributes = get_safe_block_wrapper_attributes(
    [ 'class' => 'block-stats mb-60 mt-60' ],
    'stats'
);
?>

<div <?php echo $wrapper_attributes; ?>>
	<div class="container">
		<div class="cols grid <?php echo esc_attr( $grid_cols_class ); ?> gap-60">

			<?php foreach ( $stats as $stat ) : ?>
				<div class="col grow-1 flex flex-col justify-center pl-60 border-left-2 border-gray border-solid py-60">
					<div class="number mb-22 flex gap-14 items-center">
						<?php if ( ! empty( $stat['value'] ) ) : ?>
							<div class="value font-ave-r text-60">
								<?php echo esc_html( $stat['value'] ); ?>
							</div>
						<?php endif; ?>

						<?php if ( ! empty( $stat['unit'] ) ) : ?>
							<div class="unit font-ave-r text-48">
								<?php echo esc_html( $stat['unit'] ); ?>
							</div>
						<?php endif; ?>
					</div>

					<?php if ( ! empty( $stat['label'] ) ) : ?>
						<div class="text uppercase font-tt-eb text-16 spacing-12 text-brand-<?php echo esc_attr( $color_class_name ); ?>">
							<?php echo esc_html( $stat['label'] ); ?>
						</div>
					<?php endif; ?>
				</div>
			<?php endforeach; ?>

		</div>
	</div>
</div> 