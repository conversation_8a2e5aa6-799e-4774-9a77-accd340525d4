<?php
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    $icon_id = $mb_current_block_data['icon'] ?? 0;
    $title = $mb_current_block_data['title'] ?? '';
    $text = $mb_current_block_data['text'] ?? '';
    $side_image_id = $mb_current_block_data['side_image'] ?? 0;
} else {
    // Fallback
    $icon = mb_get_block_field( 'icon' );
    $icon_id = is_array($icon) ? ($icon['ID'] ?? 0) : 0;
    $title = mb_get_block_field( 'title' );
    $text = mb_get_block_field( 'text' );
    $side_image = mb_get_block_field( 'side_image' );
    $side_image_id = is_array($side_image) ? ($side_image['ID'] ?? 0) : 0;
}

$color_class_name = get_current_corso_color_class_name();

// Usa la funzione helper
$wrapper_attrs = get_safe_block_wrapper_attributes(
    [ 'class' => 'block-box-image mt-20 mb-20' ],
    'box-image'
);
?>

<div <?php echo $wrapper_attrs; ?>>
	<div class="container grid grid-cols-12 gap-80 items-center border-2 border-solid border-gray rounded-24 p-60">
		<div class="main col-span-7">

			<?php if ( $icon_id ) : ?>
				<div class="icon flex mb-40">
					<?php load_img( class: 'h-48', high: $icon_id ); ?>
				</div>
			<?php endif; ?>
			
			<?php if ( ! empty( $title ) ) : ?>
				<div class="title uppercase font-tt-eb text-16 mb-20 spacing-12 text-brand-<?php echo esc_attr( $color_class_name ); ?>">
					<?php echo esc_html( $title ); ?>
				</div>
			<?php endif; ?>

			<?php if ( ! empty( $text ) ) : ?>
				<div class="text font-ave-m text-20 leading-15 text-gray">
					<?php echo wp_kses_post( $text ); ?>
				</div>
			<?php endif; ?>

		</div>

		<?php if ( $side_image_id ) : ?>
			<div class="side mr--100 col-span-5">
				<div class="visual w-100-100">
					<?php load_img( class: 'img w-100-100', high: $side_image_id ); ?>
				</div>
			</div>
		<?php endif; ?>
		
	</div>
</div> 