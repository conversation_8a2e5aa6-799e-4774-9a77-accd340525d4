/* Gallery Slider Block Styles */

.block-gallery-slider .container {
    /* Container styles inherited */
}

.block-gallery-slider .swiper-slide {
    width: auto;
    height: auto;
}

.block-gallery-slider .swiper-slide img {
    max-width: none;
    flex-shrink: 0;
    border-radius: 12px;
    overflow: hidden;
}

.block-gallery-slider .lightbox-trigger {
    display: block;
    cursor: pointer;
    transition: transform 0.3s ease;
}

.block-gallery-slider .lightbox-trigger:hover {
    transform: scale(1.02);
}

.block-gallery-slider .swiper-button-disabled {
    opacity: 0.3;
    pointer-events: none;
}

/* Lightbox Styles */
.block-gallery-slider .lightbox {
    backdrop-filter: blur(4px);
    transition: all 0.3s ease;
}

.block-gallery-slider .lightbox.hidden {
    opacity: 0;
    pointer-events: none;
}

.block-gallery-slider .lightbox-close {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    cursor: pointer;
    transition: background 0.3s ease;
}

.block-gallery-slider .lightbox-close:hover {
    background: rgba(255, 255, 255, 0.3);
}

.block-gallery-slider .lightbox img {
    border-radius: 8px;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .block-gallery-slider .prev,
    .block-gallery-slider .next {
        transform: scale(0.8);
    }
    
    .block-gallery-slider .prev {
        left: -20px;
    }
    
    .block-gallery-slider .next {
        right: -20px;
    }
}

@media (max-width: 480px) {
    .block-gallery-slider .prev,
    .block-gallery-slider .next {
        transform: scale(0.7);
    }
    
    .block-gallery-slider .prev {
        left: -10px;
    }
    
    .block-gallery-slider .next {
        right: -10px;
    }
    
    .block-gallery-slider .lightbox-close {
        top: 20px;
        right: 20px;
    }
} 