( function () {
    console.log( 'gallery-slider.js loaded' );
    
    /**
     * Inizializza un singolo slider Swiper.
     * @param {HTMLElement} blockContainer - L'elemento contenitore del blocco.
     */
    function initializeSwiper( blockContainer ) {
        const swiperContainer = blockContainer.querySelector( '.swiper' );
        if ( ! swiperContainer || swiperContainer.classList.contains( 'swiper-initialized' ) ) {
            return;
        }

        const prevButton = blockContainer.querySelector( '.prev' );
        const nextButton = blockContainer.querySelector( '.next' );

        new Swiper( swiperContainer, {
            slidesPerView: "auto",
            spaceBetween: 10,
            loop: true,
            centeredSlides: true,
            navigation: {
                nextEl: nextButton,
                prevEl: prevButton,
            },
            autoplay: {
                delay: 4000,
            },
        } );
    }

    /**
     * Inizializza la funzionalità lightbox per un blocco.
     * @param {HTMLElement} blockContainer - L'elemento contenitore del blocco.
     */
    function initializeLightbox( blockContainer ) {
        const lightbox = blockContainer.querySelector( '.lightbox' );
        const lightboxImg = lightbox.querySelector( 'img' );
        const closeBtn = lightbox.querySelector( '.lightbox-close' );
        const triggers = blockContainer.querySelectorAll( '.lightbox-trigger' );

        if ( ! lightbox || ! lightboxImg || ! closeBtn || triggers.length === 0 ) {
            return;
        }

        // Function to close lightbox
        const closeLightbox = () => {
            lightbox.classList.add( 'hidden' );
        };

        // Function to open lightbox
        const openLightbox = ( imageSrc ) => {
            lightboxImg.src = imageSrc;
            lightbox.classList.remove( 'hidden' );
        };

        // Add click events to triggers
        triggers.forEach( trigger => {
            trigger.addEventListener( 'click', ( e ) => {
                e.preventDefault();
                const imageSrc = trigger.getAttribute( 'data-image' );
                if ( imageSrc ) {
                    openLightbox( imageSrc );
                }
            } );
        } );

        // Close button event
        closeBtn.addEventListener( 'click', closeLightbox );

        // Click outside to close
        lightbox.addEventListener( 'click', ( e ) => {
            if ( e.target === lightbox ) {
                closeLightbox();
            }
        } );

        // ESC key to close
        document.addEventListener( 'keydown', ( e ) => {
            if ( e.key === 'Escape' && ! lightbox.classList.contains( 'hidden' ) ) {
                closeLightbox();
            }
        } );
    }

    /**
     * Inizializza un singolo blocco gallery-slider.
     * @param {HTMLElement} blockContainer - L'elemento contenitore del blocco.
     */
    function initializeBlock( blockContainer ) {
        if ( ! blockContainer || blockContainer.classList.contains( 'gallery-slider-initialized' ) ) {
            return;
        }

        initializeSwiper( blockContainer );
        initializeLightbox( blockContainer );
        
        blockContainer.classList.add( 'gallery-slider-initialized' );
    }

    /**
     * Inizializza tutti i blocchi gallery-slider trovati, per il frontend.
     */
    function initializeAllBlocks() {
        document.querySelectorAll( '.block-gallery-slider' ).forEach( initializeBlock );
    }

    // Inizializzazione per il frontend.
    if ( document.readyState === 'loading' ) {
        document.addEventListener( 'DOMContentLoaded', initializeAllBlocks );
    } else {
        initializeAllBlocks();
    }

    // Inizializzazione e reinizializzazione per l'editor di Gutenberg.
    if ( window.wp && window.wp.data && window.wp.data.subscribe ) {
        let initializedBlocks = new Set();

        const initializeEditorBlock = ( block ) => {
            if ( block.name === 'meta-box/gallery-slider' ) {
                // Usa un timeout per assicurarsi che il DOM sia aggiornato
                setTimeout( () => {
                    const blockElement = document.querySelector( `#block-${ block.clientId }` );
                    if ( blockElement && ! initializedBlocks.has( block.clientId ) ) {
                        initializeBlock( blockElement );
                        initializedBlocks.add( block.clientId );
                    }
                }, 100 );
            }
        };

        window.wp.data.subscribe( () => {
            const editor = wp.data.select( 'core/block-editor' );
            if ( ! editor ) return;

            const blocks = editor.getBlocks();
            blocks.forEach( initializeEditorBlock );

            // Pulisce i blocchi rimossi per permettere la reinizializzazione se vengono ri-aggiunti
            const currentBlockIds = new Set( blocks.map( b => b.clientId ) );
            initializedBlocks.forEach( blockId => {
                if ( ! currentBlockIds.has( blockId ) ) {
                    initializedBlocks.delete( blockId );
                }
            } );
        } );
    }
} )(); 