<?php
/**
 * Block Gallery Slider Template
 * 
 * @package Custom_Theme
 */

// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $images_data = $mb_current_block_data['images'] ?? [];
    
    // Normalizza i dati: potrebbero essere array di ID o array di oggetti
    $images = [];
    foreach ( $images_data as $image ) {
        if ( is_numeric( $image ) ) {
            // È solo un ID
            $images[] = [ 'ID' => $image ];
        } elseif ( is_array( $image ) && isset( $image['ID'] ) ) {
            // È già un array con ID
            $images[] = $image;
        } elseif ( is_array( $image ) && ! empty( $image ) ) {
            // Potrebbe essere un array con chiave numerica
            $first_key = array_key_first( $image );
            if ( is_numeric( $first_key ) ) {
                $images[] = [ 'ID' => $image[$first_key] ];
            }
        }
    }
} else {
    // Siamo nell'editor
    $images = mb_get_block_field( 'images' ) ?: [];
}

// Se non ci sono immagini, mostra placeholder nell'editor
if ( empty( $images ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block Gallery Slider</div><div class="components-placeholder__instructions">Carica una o più immagini per la galleria.</div></div>';
    }
    return;
}

$wrapper_attrs = get_safe_block_wrapper_attributes( 
    [ 'class' => 'block-gallery-slider mb-60 mt-60' ], 
    'gallery-slider' 
);

// Generate a unique ID for this swiper instance
$swiper_id = 'swiper-' . uniqid();
$lightbox_id = 'lightbox-' . uniqid();

?>

<div <?php echo $wrapper_attrs; ?>>
    <div class="container relative">
        <div class="swiper <?php echo esc_attr( $swiper_id ); ?>">
            <div class="swiper-wrapper">
                <?php foreach ( $images as $image ) : ?>
                    <?php 
                    $image_id = 0;
                    if ( is_array( $image ) && isset( $image['ID'] ) ) {
                        $image_id = (int) $image['ID'];
                    } elseif ( is_numeric( $image ) ) {
                        $image_id = (int) $image;
                    }
                    
                    if ( $image_id > 0 ) : 
                        $image_url = wp_get_attachment_image_url( $image_id, 'full' );
                    ?>
                        <div class="swiper-slide">
                            <a href="#" class="lightbox-trigger" data-image="<?php echo esc_url( $image_url ); ?>">
                                <?php load_img( class: 'img h-400', high: $image_id ); ?>
                            </a>
                        </div>
                    <?php endif; ?>
                <?php endforeach; ?>

            </div>
        </div>

        <div class="prev absolute-center-y left--40 z-50 cursor-pointer">
            <svg class="h-80" width="78" height="78" viewBox="0 0 78 78" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g filter="url(#filter0_d_555_17971)">
                    <circle cx="30" cy="30" r="30" transform="matrix(-1 0 0 1 69 6)" fill="white"/>
                </g>
                <path d="M43 27L34 36L43 45" stroke="#636C74" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                <defs>
                    <filter id="filter0_d_555_17971" x="0.172311" y="0.114874" width="77.6554" height="77.6554" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="2.94256"/>
                        <feGaussianBlur stdDeviation="4.41384"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0.156863 0 0 0 0 0.235294 0 0 0 0 0.313726 0 0 0 0.15 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_555_17971"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_555_17971" result="shape"/>
                    </filter>
                </defs>
            </svg>
        </div>
        <div class="next absolute-center-y right--40 z-50 cursor-pointer">
            <svg class="h-80" width="78" height="78" viewBox="0 0 78 78" fill="none" xmlns="http://www.w3.org/2000/svg">
                <g filter="url(#filter0_d_555_17968)">
                    <circle cx="39" cy="36" r="30" fill="white"/>
                </g>
                <path d="M36 27L45 36L36 45" stroke="#636C74" stroke-width="3" stroke-linecap="round" stroke-linejoin="round"/>
                <defs>
                    <filter id="filter0_d_555_17968" x="0.172311" y="0.114874" width="77.6554" height="77.6554" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
                        <feFlood flood-opacity="0" result="BackgroundImageFix"/>
                        <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha"/>
                        <feOffset dy="2.94256"/>
                        <feGaussianBlur stdDeviation="4.41384"/>
                        <feComposite in2="hardAlpha" operator="out"/>
                        <feColorMatrix type="matrix" values="0 0 0 0 0.156863 0 0 0 0 0.235294 0 0 0 0 0.313726 0 0 0 0.15 0"/>
                        <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_555_17968"/>
                        <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_555_17968" result="shape"/>
                    </filter>
                </defs>
            </svg>
        </div>
    </div>

    <div class="lightbox fixed inset-0 bg-black bg-opacity-90 z-50 hidden" id="<?php echo esc_attr( $lightbox_id ); ?>">
        <div class="lightbox-content relative w-full h-full flex items-center justify-center">
            <button class="lightbox-close absolute top-4 right-4 text-white text-2xl">&times;</button>
            <img src="" alt="" class="max-w-90 max-h-90">
        </div>
    </div>
</div> 