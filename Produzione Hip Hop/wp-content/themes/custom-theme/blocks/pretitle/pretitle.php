<?php
// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $text = $mb_current_block_data['text'] ?? 'Pretitle';
} else {
    // Siamo nell'editor
    $text = mb_get_block_field( 'text' ) ?: 'Pretitle';
}

$color_class_name = get_current_corso_color_class_name();

// Prepariamo la stringa completa di classi
$wrapper_classes = 'pretitle uppercase font-tt-eb text-16 mb-36 spacing-12 text-brand-' . esc_attr( $color_class_name );

$wrapper_attrs = get_safe_block_wrapper_attributes( 
    [ 'class' => $wrapper_classes ], 
    'pretitle' 
);

?>

<div <?php echo $wrapper_attrs; ?>>
    <?php echo esc_html( $text ); ?>
</div>