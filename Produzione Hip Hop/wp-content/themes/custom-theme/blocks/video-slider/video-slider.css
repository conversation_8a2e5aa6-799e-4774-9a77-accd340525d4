/* Video Slider Block Styles */

.block-video-slider .container {
    /* Container styles inherited */
}

/* Vimeo Player Styles */
.block-video-slider .vimeo-player-container .play {
    background: rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(4px);
    transition: all 0.3s ease-in-out;
}

.block-video-slider .cover:before {
    content: "";
    inset: 0;
    position: absolute;
    z-index: 50;
    background: rgba(40, 60, 80, 0.2);
    transition: all 0.3s ease-in-out;
}

.block-video-slider .cover:hover:before {
    background: rgba(40, 60, 80, 0.4);
}

.block-video-slider .swiper-slide {
    width: auto;
    height: auto;
}

.block-video-slider .item {
    transition: transform 0.3s ease;
}

.block-video-slider .item:hover {
    transform: translateY(-2px);
}

.block-video-slider .visual {
    transition: transform 0.3s ease;
    position: relative;
    overflow: hidden;
}

.block-video-slider .visual:hover {
    transform: scale(1.02);
}

.block-video-slider .visual svg {
    transition: all 0.3s ease;
}

.block-video-slider .visual:hover svg {
    transform: scale(1.1);
}

.block-video-slider .info {
    /* Info styling */
}

.block-video-slider .title {
    /* Title styling */
}

.block-video-slider .author {
    /* Author styling */
}

.block-video-slider .stats {
    /* Stats styling */
}

.block-video-slider .swiper-button-disabled {
    opacity: 0.3;
    pointer-events: none;
}

/* Navigation buttons */
.block-video-slider .prev,
.block-video-slider .next {
    transition: all 0.3s ease;
}

.block-video-slider .prev:hover,
.block-video-slider .next:hover {
    transform: scale(1.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .block-video-slider .container {
        margin-left: -20px;
        margin-right: -20px;
    }
    
    .block-video-slider .swiper {
        padding: 20px;
    }
    
    .block-video-slider .prev,
    .block-video-slider .next {
        transform: scale(0.8);
    }
    
    .block-video-slider .prev {
        left: -20px;
    }
    
    .block-video-slider .next {
        right: -20px;
    }
    
    .block-video-slider .item {
        gap: 32px;
    }
    
    .block-video-slider .info {
        gap: 16px;
    }
}

@media (max-width: 480px) {
    .block-video-slider .prev,
    .block-video-slider .next {
        transform: scale(0.7);
        display: none; /* Hide navigation on very small screens */
    }
    
    .block-video-slider .prev {
        left: -10px;
    }
    
    .block-video-slider .next {
        right: -10px;
    }
    
    .block-video-slider .container {
        margin-left: -10px;
        margin-right: -10px;
    }
    
    .block-video-slider .swiper {
        padding: 10px;
    }
    
    .block-video-slider .item {
        gap: 24px;
    }
    
    .block-video-slider .info {
        gap: 12px;
    }
    
    .block-video-slider .title {
        font-size: 14px;
    }
    
    .block-video-slider .author {
        font-size: 18px;
    }
    
    .block-video-slider .stats {
        gap: 8px;
    }
} 