/**
 * Video Slider Block JavaScript
 * 
 * @package Custom_Theme
 */

document.addEventListener('DOMContentLoaded', function() {
    // Initialize all video sliders
    initializeVideoSliders();
    
    // Initialize Vimeo players in sliders
    initializeVimeoPlayersInSliders();
});

/**
 * Initialize all video sliders on the page
 */
function initializeVideoSliders() {
    const videoSliders = document.querySelectorAll('.block-video-slider .swiper');
    
    videoSliders.forEach(function(slider) {
        // Skip if already initialized
        if (slider.swiper) {
            return;
        }
        
        // Get the unique ID class
        const classes = slider.classList;
        let swiperClass = '';
        
        for (let i = 0; i < classes.length; i++) {
            if (classes[i].startsWith('swiper-')) {
                swiperClass = '.' + classes[i];
                break;
            }
        }
        
        if (!swiperClass) {
            return;
        }
        
        // Find corresponding navigation buttons
        const container = slider.closest('.block-video-slider');
        const prevButton = container.querySelector('.prev');
        const nextButton = container.querySelector('.next');
        
        // Initialize Swiper
        const swiper = new Swiper(swiperClass, {
            // Basic settings
            spaceBetween: 20,
            
            // Responsive breakpoints
            breakpoints: {
                320: {
                    slidesPerView: 1,
                    spaceBetween: 10
                },
                480: {
                    slidesPerView: 1,
                    spaceBetween: 15
                },
                768: {
                    slidesPerView: 2,
                    spaceBetween: 20
                },
                1024: {
                    slidesPerView: 2,
                    spaceBetween: 25
                },
            },
            
            // Navigation
            navigation: {
                nextEl: nextButton,
                prevEl: prevButton
            },
            
            // Additional options
            grabCursor: true,
            keyboard: {
                enabled: true,
            },
            
            // Accessibility
            a11y: {
                prevSlideMessage: 'Previous video',
                nextSlideMessage: 'Next video'
            },
            
            // Performance
            watchSlidesProgress: true,
            watchSlidesVisibility: true,
            
            // Events
            on: {
                init: function() {
                    console.log('Video slider initialized');
                },
                slideChange: function() {
                    // Optional: Analytics or custom events
                }
            }
        });
        
        // Handle navigation button states
        swiper.on('slideChange', function() {
            updateNavigationButtons(swiper, prevButton, nextButton);
        });
        
        // Initial navigation state
        updateNavigationButtons(swiper, prevButton, nextButton);
    });
}

/**
 * Update navigation button states
 */
function updateNavigationButtons(swiper, prevButton, nextButton) {
    if (!prevButton || !nextButton) return;
    
    // Remove disabled class
    prevButton.classList.remove('swiper-button-disabled');
    nextButton.classList.remove('swiper-button-disabled');
    
    // Add disabled class if needed (for non-loop mode)
    if (!swiper.params.loop) {
        if (swiper.isBeginning) {
            prevButton.classList.add('swiper-button-disabled');
        }
        if (swiper.isEnd) {
            nextButton.classList.add('swiper-button-disabled');
        }
    }
}

/**
 * Reinitialize sliders when new content is added (for dynamic content)
 */
window.reinitializeVideoSliders = function() {
    initializeVideoSliders();
    initializeVimeoPlayersInSliders();
};

/**
 * Initialize Vimeo players in video sliders (same approach as single video block)
 */
function initializeVimeoPlayersInSliders() {
    const vimeoContainers = document.querySelectorAll('.block-video-slider .vimeo-player-container');
    
    vimeoContainers.forEach(function(container) {
        if (container.dataset.initialized) {
            return; // Already initialized
        }
        
        const videoId = container.dataset.videoId;
        const videoHash = container.dataset.videoHash;
        const playerElement = container.querySelector('.player');
        const coverElement = container.querySelector('.cover');
        
        if (!videoId || !playerElement || !coverElement) {
            return;
        }
        
        // Handle cover click to start video (same as single video block)
        coverElement.addEventListener('click', function() {
            // Costruiamo l'URL dell'embed di Vimeo
            let videoSrc = `https://player.vimeo.com/video/${videoId}?autoplay=1&autopause=0&dnt=1`;
            if (videoHash) {
                videoSrc += `&h=${videoHash}`;
            }

            // Creiamo l'iframe
            const iframe = document.createElement('iframe');
            iframe.src = videoSrc;
            iframe.width = '100%';
            iframe.height = '100%';
            iframe.frameBorder = 0;
            iframe.allow = 'autoplay; fullscreen; picture-in-picture';
            iframe.allowFullscreen = true;

            // Sostituiamo il placeholder con l'iframe e nascondiamo la copertina
            playerElement.innerHTML = '';
            playerElement.appendChild(iframe);
            coverElement.style.display = 'none';
        });
        
        // Mark as initialized
        container.dataset.initialized = 'true';
    });
}

/**
 * Destroy all video sliders (for cleanup)
 */
window.destroyVideoSliders = function() {
    const videoSliders = document.querySelectorAll('.block-video-slider .swiper');
    
    videoSliders.forEach(function(slider) {
        if (slider.swiper) {
            slider.swiper.destroy(true, true);
        }
    });
}; 