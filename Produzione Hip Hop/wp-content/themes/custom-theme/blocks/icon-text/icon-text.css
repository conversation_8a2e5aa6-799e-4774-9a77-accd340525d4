/* Icon Text Block Styles */

.block-icon-text .container {
    /* Container styles are inherited from the HTML structure */
}

.block-icon-text .side {
    /* Icon container specific styles if needed */
}

.block-icon-text .icon {
    /* Ensure proper icon sizing and positioning */
}

.block-icon-text .icon img,
.block-icon-text .icon svg {
    max-width: none; /* Override any global max-width constraints */
    flex-shrink: 0;
}

.block-icon-text .main {
    /* Content area specific styles if needed */
    flex: 1;
}

.block-icon-text .text {
    /* Text specific styles if needed */
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .block-icon-text .container {
        flex-direction: column;
        text-align: center;
        gap: 40px;
    }
    
    .block-icon-text .side {
        flex-shrink: initial;
    }
}

@media (max-width: 480px) {
    .block-icon-text .container {
        gap: 32px;
    }
} 