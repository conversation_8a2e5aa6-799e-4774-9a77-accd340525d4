<?php
/**
 * Block Icon Text Template
 * 
 * @package Custom_Theme
 */

// Evita l'accesso diretto al file
if ( ! defined( 'ABSPATH' ) ) {
    exit;
}

// Usa i dati passati globalmente se disponibili
global $mb_current_block_data;

if ( isset( $mb_current_block_data ) ) {
    // Siamo nel frontend con rendering manuale
    $icon_id = $mb_current_block_data['icon'] ?? 0;
    $text = $mb_current_block_data['text'] ?? '';
} else {
    // Siamo nell'editor, usa mb_get_block_field()
    $icon_data = mb_get_block_field( 'icon' );
    
    $icon_id = ! empty( $icon_data['ID'] ) ? (int) $icon_data['ID'] : 0;
    $text = mb_get_block_field( 'text' );
}

// Se tutti i campi sono vuoti, non mostrare nulla.
if ( empty( $icon_id ) && empty( $text ) ) {
    if ( is_admin() && ! isset( $mb_current_block_data ) ) {
        echo '<div class="components-placeholder"><div class="components-placeholder__label">Block Icon Text</div><div class="components-placeholder__instructions">Aggiungi un\'icona e del testo per visualizzare il blocco.</div></div>';
    }
    return;
}

// Usa la funzione helper per gli attributi del wrapper
$wrapper_attributes = get_safe_block_wrapper_attributes(
    [ 'class' => 'block-icon-text mt-60 mb-60' ],
    'icon-text'
);

?>

<div <?php echo $wrapper_attributes; ?>>
    <div class="container flex gap-80 items-center">
        
        <?php if ( $icon_id ) : ?>
            <div class="side shrink-0">
                <div class="icon flex">
                    <?php load_img( class: 'img h-48', high: $icon_id ); ?>
                </div>
            </div>
        <?php endif; ?>

        <?php if ( ! empty( $text ) ) : ?>
            <div class="main">
                <div class="text font-ave-m text-20 leading-15 text-gray">
                    <?php echo wp_kses_post( $text ); ?>
                </div>
            </div>
        <?php endif; ?>
        
    </div>
</div> 