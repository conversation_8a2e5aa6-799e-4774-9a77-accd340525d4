<?php
/**
 * Custom theme functions and definitions
 *
 * @link https://developer.wordpress.org/themes/basics/theme-functions/
 *
 * @package Custom_theme
 */

if ( ! defined( '_S_VERSION' ) ) {
	// Replace the version number of the theme on each release.
	define( '_S_VERSION', '1.0.0' );
}

/**
 * Sets up theme defaults and registers support for various WordPress features.
 *
 * Note that this function is hooked into the after_setup_theme hook, which
 * runs before the init hook. The init hook is too late for some features, such
 * as indicating support for post thumbnails.
 */
function custom_theme_setup() {
	/*
		* Make theme available for translation.
		* Translations can be filed in the /languages/ directory.
		* If you're building a theme based on Custom theme, use a find and replace
		* to change 'custom-theme' to the name of your theme in all the template files.
		*/
	load_theme_textdomain( 'custom-theme', get_template_directory() . '/languages' );

	// Add default posts and comments RSS feed links to head.
	add_theme_support( 'automatic-feed-links' );

	/*
		* Let WordPress manage the document title.
		* By adding theme support, we declare that this theme does not use a
		* hard-coded <title> tag in the document head, and expect WordPress to
		* provide it for us.
		*/
	add_theme_support( 'title-tag' );

	/*
		* Enable support for Post Thumbnails on posts and pages.
		*
		* @link https://developer.wordpress.org/themes/functionality/featured-images-post-thumbnails/
		*/
	add_theme_support( 'post-thumbnails' );

	// This theme uses wp_nav_menu() in one location.
	register_nav_menus(
		array(
			'menu-1' => esc_html__( 'Primary', 'custom-theme' ),
		)
	);

	/*
		* Switch default core markup for search form, comment form, and comments
		* to output valid HTML5.
		*/
	add_theme_support(
		'html5',
		array(
			'search-form',
			'comment-form',
			'comment-list',
			'gallery',
			'caption',
			'style',
			'script',
		)
	);

	// Set up the WordPress core custom background feature.
	add_theme_support(
		'custom-background',
		apply_filters(
			'custom_theme_custom_background_args',
			array(
				'default-color' => 'ffffff',
				'default-image' => '',
			)
		)
	);

	// Add theme support for selective refresh for widgets.
	add_theme_support( 'customize-selective-refresh-widgets' );

	/**
	 * Add support for core custom logo.
	 *
	 * @link https://codex.wordpress.org/Theme_Logo
	 */
	add_theme_support(
		'custom-logo',
		array(
			'height'      => 250,
			'width'       => 250,
			'flex-width'  => true,
			'flex-height' => true,
		)
	);
}
add_action( 'after_setup_theme', 'custom_theme_setup' );

/**
 * Set the content width in pixels, based on the theme's design and stylesheet.
 *
 * Priority 0 to make it available to lower priority callbacks.
 *
 * @global int $content_width
 */
function custom_theme_content_width() {
	$GLOBALS['content_width'] = apply_filters( 'custom_theme_content_width', 640 );
}
add_action( 'after_setup_theme', 'custom_theme_content_width', 0 );

/**
 * Register widget area.
 *
 * @link https://developer.wordpress.org/themes/functionality/sidebars/#registering-a-sidebar
 */
function custom_theme_widgets_init() {
	register_sidebar(
		array(
			'name'          => esc_html__( 'Sidebar', 'custom-theme' ),
			'id'            => 'sidebar-1',
			'description'   => esc_html__( 'Add widgets here.', 'custom-theme' ),
			'before_widget' => '<section id="%1$s" class="widget %2$s">',
			'after_widget'  => '</section>',
			'before_title'  => '<h2 class="widget-title">',
			'after_title'   => '</h2>',
		)
	);
}
add_action( 'widgets_init', 'custom_theme_widgets_init' );

/**
 * Enqueue scripts and styles.
 */
function custom_theme_scripts() {
	wp_enqueue_style( 'custom-theme-style', get_stylesheet_uri(), array(), _S_VERSION );
	wp_style_add_data( 'custom-theme-style', 'rtl', 'replace' );



/*--------------------------------------------------------------
## Vendor
--------------------------------------------------------------*/
  
    /* CSS */


  
	wp_enqueue_style( 'custom-theme-swiper', get_template_directory_uri() . '/assets/css/vendor/swiper.css',false,filemtime(get_template_directory() .'/assets/css/vendor/swiper.css'),'all');

	/* JS */
	
	
	wp_enqueue_script( 'custom-theme-swiper-js', get_template_directory_uri() . '/assets/js/vendor/swiper.js', array ( 'jquery' ), filemtime(get_template_directory() .'/assets/js/vendor/swiper.js'), true);
  
	wp_enqueue_script( 'custom-theme-scrollmonitor-js', get_template_directory_uri() . '/assets/js/vendor/scrollmonitor.js', array ( 'jquery' ), filemtime(get_template_directory() .'/assets/js/vendor/scrollmonitor.js'), true);
  
	wp_enqueue_script( 'custom-theme-keyframes-js', get_template_directory_uri() . '/assets/js/vendor/keyframes.js', array ( 'jquery' ), filemtime(get_template_directory() .'/assets/js/vendor/keyframes.js'), true);
  

        // Enqueue CSS di intl-tel-input
        wp_enqueue_style(
            'intl-tel-input-css',
            get_template_directory_uri() . '/assets/css/vendor/intlTelInput.css',
            [],
            filemtime( get_template_directory() . '/assets/css/vendor/intlTelInput.css' ),
            'all'
        );
    
        // Enqueue JS di intl-tel-input (versione combinata con utils)
        wp_enqueue_script(
            'intl-tel-input-js',
            get_template_directory_uri() . '/assets/js/vendor/intlTelInputWithUtils.js',
            [ 'jquery' ],
            filemtime( get_template_directory() . '/assets/js/vendor/intlTelInputWithUtils.js' ),
            true
        );
  


/*--------------------------------------------------------------
## General
--------------------------------------------------------------*/
  
    /* CSS */
  
     wp_enqueue_style( 'custom-theme-framework', get_template_directory_uri() . '/assets/css/general/framework.css',false,filemtime(get_template_directory() .'/assets/css/general/framework.css'),'all');
     
     wp_enqueue_style( 'custom-theme-fonts-css', get_template_directory_uri() . '/assets/css/general/fonts.css',false,filemtime(get_template_directory() .'/assets/css/general/fonts.css'),'all');
  
     wp_enqueue_style( 'custom-theme-general-css', get_template_directory_uri() . '/assets/css/general/general.css',false,filemtime(get_template_directory() .'/assets/css/general/general.css'),'all');
  
    wp_enqueue_style( 'custom-theme-customisation-css', get_template_directory_uri() . '/assets/css/general/customisation.css',false,filemtime(get_template_directory() .'/assets/css/general/customisation.css'),'all');
  
  /* JS */
  
  
   wp_enqueue_script( 'custom-theme-general-js', get_template_directory_uri() . '/assets/js/general/general.js', array ( 'jquery' ), filemtime(get_template_directory() .'/assets/js/general/general.js'), true);
  
  
/*--------------------------------------------------------------
## Pages
--------------------------------------------------------------*/
  

if(is_front_page()){

    /* CSS */
  
     wp_enqueue_style( 'custom-theme-front-page', get_template_directory_uri() . '/assets/css/pages/front-page.css',false,filemtime(get_template_directory() .'/assets/css/pages/front-page.css'),'all');
  
  /* JS */
  
   wp_enqueue_script( 'custom-theme-front-page-js', get_template_directory_uri() . '/assets/js/pages/front-page.js', array ( 'jquery' ), filemtime(get_template_directory() .'/assets/js/pages/front-page.js'), true);

}

if(is_page('scuola')){
    /* CSS */
  
     wp_enqueue_style( 'custom-theme-scuola', get_template_directory_uri() . '/assets/css/pages/scuola.css',false,filemtime(get_template_directory() .'/assets/css/pages/scuola.css'),'all');
  
  /* JS */
  
   wp_enqueue_script( 'custom-theme-scuola-js', get_template_directory_uri() . '/assets/js/pages/scuola.js', array ( 'jquery' ), filemtime(get_template_directory() .'/assets/js/pages/scuola.js'), true);
}

if(is_archive('corsi')){
    /* CSS */
  
     wp_enqueue_style( 'custom-theme-corsi', get_template_directory_uri() . '/assets/css/pages/corsi.css',false,filemtime(get_template_directory() .'/assets/css/pages/corsi.css'),'all');
  
  /* JS */
  
   wp_enqueue_script( 'custom-theme-corsi-js', get_template_directory_uri() . '/assets/js/pages/corsi.js', array ( 'jquery' ), filemtime(get_template_directory() .'/assets/js/pages/corsi.js'), true);
}

if(is_page('contatti')){
    /* CSS */
  
     wp_enqueue_style( 'custom-theme-contatti', get_template_directory_uri() . '/assets/css/pages/contatti.css',false,filemtime(get_template_directory() .'/assets/css/pages/contatti.css'),'all');
  
  /* JS */
  
   wp_enqueue_script( 'custom-theme-contatti-js', get_template_directory_uri() . '/assets/js/pages/contatti.js', array ( 'jquery' ), filemtime(get_template_directory() .'/assets/js/pages/contatti.js'), true);
}

if(is_singular('corsi')){


    wp_enqueue_script( 'vimeo-player', 'https://player.vimeo.com/api/player.js', array(), null, true );
    /* CSS */
  
     wp_enqueue_style( 'custom-theme-corso', get_template_directory_uri() . '/assets/css/pages/corso.css',false,filemtime(get_template_directory() .'/assets/css/pages/corso.css'),'all');
     wp_enqueue_style( 'custom-theme-gutenberg', get_template_directory_uri() . '/assets/css/sections/gutenberg.css',false,filemtime(get_template_directory() .'/assets/css/sections/gutenberg.css'),'all');

     wp_enqueue_style( 'custom-theme-fluentform', get_template_directory_uri() . '/assets/css/vendor/fluentform.css',false,filemtime(get_template_directory() .'/assets/css/vendor/fluentform.css'),'all');
  
     wp_enqueue_script( 'custom-theme-fluentform-js', get_template_directory_uri() . '/assets/js/vendor/fluentform.js', array ( 'jquery' ), filemtime(get_template_directory() .'/assets/js/vendor/fluentform.js'), true);
  
  /* JS */
  
   wp_enqueue_script( 'custom-theme-corso-js', get_template_directory_uri() . '/assets/js/pages/corso.js', array ( 'jquery' ), filemtime(get_template_directory() .'/assets/js/pages/corso.js'), true);

   wp_enqueue_script( 'custom-theme-gutenberg-js', get_template_directory_uri() . '/assets/js/sections/gutenberg.js', array ( 'jquery' ), filemtime(get_template_directory() .'/assets/js/sections/gutenberg.js'), true);

}







  
  
/*--------------------------------------------------------------
## Sections
--------------------------------------------------------------*/
  

    /* CSS */

	wp_enqueue_style( 'custom-theme-nav', get_template_directory_uri() . '/assets/css/sections/nav.css',false,filemtime(get_template_directory() .'/assets/css/sections/nav.css'),'all');

	wp_enqueue_style( 'custom-theme-footer', get_template_directory_uri() . '/assets/css/sections/footer.css',false,filemtime(get_template_directory() .'/assets/css/sections/footer.css'),'all');
	wp_enqueue_style( 'custom-theme-header', get_template_directory_uri() . '/assets/css/sections/header.css',false,filemtime(get_template_directory() .'/assets/css/sections/header.css'),'all');
	   
  
     wp_enqueue_style( 'custom-theme-cookies', get_template_directory_uri() . '/assets/css/sections/cookies.css',false,filemtime(get_template_directory() .'/assets/css/sections/cookies.css'),'all');
  

  
	wp_enqueue_style( 'custom-theme-form', get_template_directory_uri() . '/assets/css/sections/form.css',false,filemtime(get_template_directory() .'/assets/css/sections/form.css'),'all');


	wp_enqueue_style( 'custom-theme-footer-cta', get_template_directory_uri() . '/assets/css/sections/footer-cta.css',false,filemtime(get_template_directory() .'/assets/css/sections/footer-cta.css'),'all');


	wp_enqueue_style( 'custom-theme-footer-classic', get_template_directory_uri() . '/assets/css/sections/footer-classic.css',false,filemtime(get_template_directory() .'/assets/css/sections/footer-classic.css'),'all');

  /* JS */
  
  wp_enqueue_script( 'custom-theme-nav-js', get_template_directory_uri() . '/assets/js/sections/nav.js', array ( 'jquery' ), filemtime(get_template_directory() .'/assets/js/sections/nav.js'), true);

wp_enqueue_script( 'custom-theme-header-js', get_template_directory_uri() . '/assets/js/sections/header.js', array ( 'jquery' ), filemtime(get_template_directory() .'/assets/js/sections/header.js'), true);
wp_enqueue_script( 'custom-theme-footer-js', get_template_directory_uri() . '/assets/js/sections/footer.js', array ( 'jquery' ), filemtime(get_template_directory() .'/assets/js/sections/footer.js'), true);

wp_enqueue_script( 'custom-theme-form-js', get_template_directory_uri() . '/assets/js/sections/form.js', array ( 'jquery' ), filemtime(get_template_directory() .'/assets/js/sections/form.js'), true);



        wp_enqueue_script(
            'custom-theme-footer-cta-js',
            get_template_directory_uri() . '/assets/js/sections/footer-cta.js',
            [ 'jquery', 'intl-tel-input-js' ],
            filemtime( get_template_directory() . '/assets/js/sections/footer-cta.js' ),
            true
        ); 

        wp_enqueue_script(
            'custom-theme-footer-classic-js',
            get_template_directory_uri() . '/assets/js/sections/footer-classic.js',
            [ 'jquery' ],
            filemtime( get_template_directory() . '/assets/js/sections/footer-classic.js' ),
            true
        ); 
    
        wp_localize_script(
            'custom-theme-footer-cta-js',
            'footer_form_ajax_object',
            [
              'ajaxurl' => admin_url('admin-ajax.php'),
              'security'   => wp_create_nonce('footer-contact-nonce')
            ]
        );
  
  

	if ( is_singular() && comments_open() && get_option( 'thread_comments' ) ) {
		wp_enqueue_script( 'comment-reply' );
	}
}
add_action( 'wp_enqueue_scripts', 'custom_theme_scripts' );



/*--------------------------------------------------------------

## Include

--------------------------------------------------------------*/

include_once(get_template_directory() . '/inc/general/custom-post-types.php');
include_once(get_template_directory() . '/inc/general/custom-post-fields.php');
include_once(get_template_directory() . '/inc/general/custom-blocks.php');

include_once(get_template_directory() . '/inc/pages/footer-form.php');


/**
 * Implement the Custom Header feature.
 */
require get_template_directory() . '/inc/custom-header.php';

/**
 * Custom template tags for this theme.
 */
require get_template_directory() . '/inc/template-tags.php';

/**
 * Functions which enhance the theme by hooking into WordPress.
 */
require get_template_directory() . '/inc/template-functions.php';

/**
 * Customizer additions.
 */
require get_template_directory() . '/inc/customizer.php';

/**
 * Load Jetpack compatibility file.
 */
if ( defined( 'JETPACK__VERSION' ) ) {
	require get_template_directory() . '/inc/jetpack.php';
}




/*--------------------------------------------------------------

## Disable lazy load

--------------------------------------------------------------*/


// Disable WordPress Lazy Load
add_filter( 'wp_lazy_loading_enabled', '__return_false' );



/*--------------------------------------------------------------

## Remove global styles (svg e variabili)

--------------------------------------------------------------*/


  add_action('after_setup_theme', function () {
    remove_action('wp_enqueue_scripts', 'wp_enqueue_global_styles');
    remove_action('wp_footer', 'wp_enqueue_global_styles', 1);
}, 10, 0);
  
function rankya_remove_global_styles() {
remove_action( 'wp_body_open', 'wp_global_styles_render_svg_filters' );
remove_action( 'in_admin_header', 'wp_global_styles_render_svg_filters' );
}
add_action('after_setup_theme', 'rankya_remove_global_styles', 10, 0);





/*--------------------------------------------------------------

## Generate img

Example:
load_img(class: 'img', high: 123, low: 456);
load_img(class: 'img', high: 123);

--------------------------------------------------------------*/

/**
 * Generate a responsive picture element with optional lazy loading.
 *
 * @param string   $class     Additional CSS classes for the image.
 * @param int      $high  Attachment ID of the high-resolution image.
 * @param int|null $low   Optional. Attachment ID of the low-resolution image.
 */

 function load_img($class, $high, $low = null) {
    // Get high-resolution image attributes
    $srcset_high = wp_get_attachment_image_srcset($high, 'full');
    $sizes_high = wp_get_attachment_image_sizes($high, 'full');
    $src_high = wp_get_attachment_image_url($high, 'full');

    // Set low-resolution image source (if available, else use high-res image)
    $src_low = $low ? wp_get_attachment_image_url($low, 'full') : $src_high;

    // Get alt text from high-res image
    $alt = get_post_meta($high, '_wp_attachment_image_alt', true);

    // Prepare AVIF and WEBP versions
    $srcset_avif = preg_replace('/\.(jpg|jpeg|png)$/i', '.avif', $srcset_high);
    $srcset_webp = preg_replace('/\.(jpg|jpeg|png)$/i', '.webp', $srcset_high);

    // Check if AVIF/WEBP files exist
    $upload_dir = wp_upload_dir();
    $base_dir = $upload_dir['basedir'];
    $src_high_file_path = str_replace($upload_dir['baseurl'], $base_dir, $src_high);

    $avif_file_path = preg_replace('/\.(jpg|jpeg|png)$/i', '.avif', $src_high_file_path);
    $webp_file_path = preg_replace('/\.(jpg|jpeg|png)$/i', '.webp', $src_high_file_path);

    $avif_exists = file_exists($avif_file_path);
    $webp_exists = file_exists($webp_file_path);

    // Prepare classes
    $classes = !empty($class) ? explode(' ', $class) : [];
    
    // If low-resolution image is provided, add 'lazy' class
    if ($low) {
        $classes[] = 'lazy';
    }
    
    // Remove duplicate classes
    $classes = array_unique($classes);

    // Build HTML output
    echo '<picture>';
    if ($avif_exists) {
        echo '<source srcset="' . esc_attr($srcset_avif) . '" sizes="' . esc_attr($sizes_high) . '" type="image/avif">';
    }
    if ($webp_exists) {
        echo '<source srcset="' . esc_attr($srcset_webp) . '" sizes="' . esc_attr($sizes_high) . '" type="image/webp">';
    }
    echo '<img src="' . esc_url($src_low) . '" data-src="' . esc_url($src_high) . '" srcset="' . esc_attr($srcset_high) . '" sizes="' . esc_attr($sizes_high) . '" alt="' . esc_attr($alt) . '" class="' . esc_attr(implode(' ', $classes)) . '">';
    echo '</picture>';
}

 





// Remove the admin bar for all users except administrators
function remove_admin_bar_for_non_admins() {
    if (!current_user_can('administrator') && !is_admin()) {
        show_admin_bar(false);
    }
}
add_action('after_setup_theme', 'remove_admin_bar_for_non_admins');



/*--------------------------------------------------------------

## ID Column - Media Library

--------------------------------------------------------------*/


// Add ID column to media library
function add_media_id_column($columns) {
    $new_columns = array();
    $new_columns['media_id'] = 'ID';
    return array_merge($new_columns, $columns);
}
add_filter('manage_media_columns', 'add_media_id_column');

// Display ID in the new column
function display_media_id_column($column_name, $attachment_id) {
    if ($column_name === 'media_id') {
        echo $attachment_id;
    }
}
add_action('manage_media_custom_column', 'display_media_id_column', 10, 2);

// Make ID column sortable
function make_media_id_column_sortable($columns) {
    $columns['media_id'] = 'ID';
    return $columns;
}
add_filter('manage_upload_sortable_columns', 'make_media_id_column_sortable');

// Add CSS to make ID column narrower
function media_id_column_css() {
    echo '<style>
        .wp-list-table .column-media_id {
            width: 60px !important;
            text-align: center;
        }
        .wp-list-table .column-media_id a {
            text-decoration: none;
        }
    </style>';
}
add_action('admin_head-upload.php', 'media_id_column_css');