{"$schema": "https://schemas.wp.org/wp/6.7/theme.json", "version": 3, "title": "Evening", "settings": {"color": {"palette": [{"color": "#1B1B1B", "name": "Base", "slug": "base"}, {"color": "#F0F0F0", "name": "Contrast", "slug": "contrast"}, {"color": "#786D0A", "name": "Accent 1", "slug": "accent-1"}, {"color": "#442369", "name": "Accent 2", "slug": "accent-2"}, {"color": "#D1D0EA", "name": "Accent 3", "slug": "accent-3"}, {"color": "#CBCBCB", "name": "Accent 4", "slug": "accent-4"}, {"color": "#353535", "name": "Accent 5", "slug": "accent-5"}, {"color": "#FFFFFF33", "name": "Accent 6", "slug": "accent-6"}]}}, "styles": {"color": {"text": "var:preset|color|accent-4"}, "elements": {"button": {"color": {"background": "var:preset|color|contrast", "text": "var:preset|color|base"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--contrast) 85%, transparent)", "text": "var:preset|color|base"}}}}, "variations": {"section-2": {"elements": {"button": {"color": {"background": "var:preset|color|base", "text": "var:preset|color|contrast"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--base) 85%, transparent)"}}}}}, "section-4": {"elements": {"button": {"color": {"background": "var:preset|color|accent-2", "text": "var:preset|color|contrast"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--accent-2) 85%, transparent)"}}}}}}}}