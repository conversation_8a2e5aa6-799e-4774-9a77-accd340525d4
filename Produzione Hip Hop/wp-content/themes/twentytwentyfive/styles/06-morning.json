{"$schema": "https://schemas.wp.org/wp/6.7/theme.json", "version": 3, "title": "Morning", "settings": {"color": {"palette": [{"color": "#DFDCD7", "name": "Base", "slug": "base"}, {"color": "#191919", "name": "Contrast", "slug": "contrast"}, {"color": "#7A9BDB", "name": "Accent 1", "slug": "accent-1"}, {"color": "#F7E6FF", "name": "Accent 2", "slug": "accent-2"}, {"color": "#182949", "name": "Accent 3", "slug": "accent-3"}, {"color": "#5F5F5F", "name": "Accent 4", "slug": "accent-4"}, {"color": "#D7D3CC", "name": "Accent 5", "slug": "accent-5"}, {"color": "#19191933", "name": "Accent 6", "slug": "accent-6"}]}, "typography": {"fontFamilies": [{"name": "Literata", "slug": "literata", "fontFamily": "Literata, serif", "fontFace": [{"src": ["file:./assets/fonts/literata/Literata72pt-ExtraLight.woff2"], "fontWeight": "200", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-ExtraLightItalic.woff2"], "fontWeight": "200", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-Light.woff2"], "fontWeight": "300", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-LightItalic.woff2"], "fontWeight": "300", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-Regular.woff2"], "fontWeight": "400", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-RegularItalic.woff2"], "fontWeight": "400", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-Medium.woff2"], "fontWeight": "500", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-MediumItalic.woff2"], "fontWeight": "500", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-SemiBold.woff2"], "fontWeight": "600", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-SemiBoldItalic.woff2"], "fontWeight": "600", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-Bold.woff2"], "fontWeight": "700", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-BoldItalic.woff2"], "fontWeight": "700", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-ExtraBold.woff2"], "fontWeight": "800", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-ExtraBoldItalic.woff2"], "fontWeight": "800", "fontStyle": "italic", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-Black.woff2"], "fontWeight": "900", "fontStyle": "normal", "fontFamily": "Literata"}, {"src": ["file:./assets/fonts/literata/Literata72pt-BlackItalic.woff2"], "fontWeight": "900", "fontStyle": "italic", "fontFamily": "Literata"}]}, {"name": "Ysabeau Office", "slug": "ysabeau-office", "fontFamily": "\"Ysabeau Office\", sans-serif", "fontFace": [{"src": ["file:./assets/fonts/ysabeau-office/YsabeauOffice-VariableFont_wght.woff2"], "fontWeight": "100 900", "fontStyle": "normal", "fontFamily": "\"Ysabeau Office\""}, {"src": ["file:./assets/fonts/ysabeau-office/YsabeauOffice-Italic-VariableFont_wght.woff2"], "fontWeight": "100 900", "fontStyle": "italic", "fontFamily": "\"Ysabeau Office\""}]}], "fontSizes": [{"fluid": false, "name": "Small", "size": "0.875rem", "slug": "small"}, {"fluid": {"max": "1.125rem", "min": "1rem"}, "name": "Medium", "size": "1rem", "slug": "medium"}, {"fluid": {"max": "1.375rem", "min": "1.125rem"}, "name": "Large", "size": "1.38rem", "slug": "large"}, {"fluid": {"max": "2rem", "min": "1.75rem"}, "name": "Extra Large", "size": "1.75rem", "slug": "x-large"}, {"fluid": {"max": "2.6rem", "min": "1.4rem"}, "name": "Extra Extra Large", "size": "2.6rem", "slug": "xx-large"}]}}, "styles": {"color": {"text": "var:preset|color|accent-4"}, "typography": {"fontFamily": "var:preset|font-family|ysabeau-office", "letterSpacing": "-0.24px"}, "blocks": {"core/code": {"color": {"text": "var:preset|color|contrast", "background": "var:preset|color|accent-5"}}, "core/navigation": {"typography": {"fontSize": "1.25rem"}}, "core/paragraph": {"elements": {"link": {"color": {"text": "var:preset|color|contrast"}}}}, "core/post-author-name": {"elements": {"link": {"color": {"text": "var:preset|color|contrast"}}}}, "core/post-title": {"typography": {"fontWeight": "900", "letterSpacing": "-0.96px"}, "elements": {"link": {"color": {"text": "var:preset|color|contrast"}}}}, "core/pullquote": {"color": {"text": "var:preset|color|contrast"}, "typography": {"fontSize": "var:preset|font-size|xx-large"}, "elements": {"cite": {"typography": {"fontSize": "var:preset|font-size|medium", "letterSpacing": "-0.14px"}, "color": {"text": "var:preset|color|accent-4"}}}}, "core/quote": {"elements": {"cite": {"typography": {"fontSize": "var:preset|font-size|medium", "letterSpacing": "-0.14px"}, "color": {"text": "var:preset|color|accent-4"}}}}, "core/query-title": {"typography": {"fontWeight": "900"}}, "core/site-title": {"typography": {"fontFamily": "var:preset|font-family|ysabeau-office", "textTransform": "uppercase", "letterSpacing": "1.6px"}}}, "elements": {"button": {"typography": {"fontFamily": "var:preset|font-family|literata", "fontSize": "var:preset|font-size|medium", "fontWeight": "900", "letterSpacing": "-0.36px"}, "color": {"text": "var:preset|color|contrast", "background": "var:preset|color|accent-1"}, "border": {"radius": "0px"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--accent-1) 85%, transparent)", "text": "var:preset|color|contrast"}}}, "heading": {"color": {"text": "var:preset|color|contrast"}, "typography": {"fontFamily": "var:preset|font-family|literata", "fontWeight": "900", "lineHeight": "1.2"}}, "h5": {"typography": {"letterSpacing": "0px"}}, "h6": {"typography": {"fontWeight": "900", "letterSpacing": "0px"}}, "link": {"color": {"text": "currentColor"}}}, "variations": {"post-terms-1": {"typography": {"fontSize": "var:preset|font-size|medium"}, "elements": {"link": {"color": {"background": "var:preset|color|accent-5"}, "border": {"radius": "100px", "color": "var:preset|color|accent-5"}}}}, "section-2": {"elements": {"button": {"color": {"background": "var:preset|color|accent-1", "text": "var:preset|color|contrast"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--accent-1) 85%, transparent)"}}}}}, "section-3": {"elements": {"button": {"color": {"background": "var:preset|color|contrast", "text": "var:preset|color|base"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--contrast) 85%, transparent)"}}}}}, "section-4": {"color": {"text": "var:preset|color|base"}, "elements": {"heading": {"color": {"text": "currentColor"}}, "button": {"color": {"background": "var:preset|color|accent-1", "text": "var:preset|color|contrast"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--accent-1) 85%, transparent)", "text": "var:preset|color|contrast"}}}, "link": {"color": {"text": "currentColor"}}}}, "section-5": {"elements": {"heading": {"color": {"text": "var:preset|color|base"}}, "button": {"color": {"background": "var:preset|color|accent-1", "text": "var:preset|color|contrast"}, ":hover": {"color": {"background": "color-mix(in srgb, var(--wp--preset--color--accent-1) 85%, transparent)"}}}, "link": {"color": {"text": "currentColor"}}}}}}}