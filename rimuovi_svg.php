<?php
// rimuovi_svg.php (Versione 3.1 - Finale Corretta)

$fileElenco = 'elenco_file.txt';
$backupDir = 'svg_backups'; // Nome della cartella per i backup

// 1. Assicura che la cartella di backup esista
if (!is_dir($backupDir)) {
    mkdir($backupDir, 0755, true);
}

if (!file_exists($fileElenco)) {
    echo "Errore: Il file di configurazione '$fileElenco' non è stato trovato.\n";
    echo "Crea questo file e aggiungi un percorso per ogni riga.\n";
    exit(1);
}

$percorsi = file($fileElenco, FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
if (empty($percorsi)) {
    echo "Il file '$fileElenco' è vuoto. Nessuna operazione.\n";
    exit(0);
}

$fileModificati = 0;
$svgTotali = 0;

foreach ($percorsi as $file) {
    if (!file_exists($file)) {
        echo "Attenzione: File non trovato '$file'. Saltato.\n";
        continue;
    }

    $contenutoOriginale = file_get_contents($file);
    $svgTrovatiInFile = 0;
    
    $regex = '/<svg[\s\S]*?<\/svg>/';

    $contenutoModificato = preg_replace_callback($regex, function ($matches) use ($backupDir, &$svgTrovatiInFile) {
        $svgTrovatiInFile++;
        $uniqueId = uniqid('svg_', true);
        
        // VERSIONE CORRETTA: Usa la concatenazione standard (con il punto) per la massima compatibilità.
        $segnaposto = 'SVG_PLACEHOLDER_' . $uniqueId;

        $backupFile = $backupDir . '/' . $uniqueId . '.svgbak';
        file_put_contents($backupFile, $matches[0]);
        
        return $segnaposto;
    }, $contenutoOriginale);

    if ($svgTrovatiInFile > 0) {
        file_put_contents($file, $contenutoModificato);
        echo "✓ Rimosso $svgTrovatiInFile SVG da '$file'.\n";
        $fileModificati++;
        $svgTotali += $svgTrovatiInFile;
    } else {
        echo "i Nessun SVG trovato in '$file'.\n";
    }
}

if ($fileModificati > 0) {
    echo "\nOperazione completata: $svgTotali SVG rimossi da $fileModificati file.\n";
    echo "I backup individuali sono stati salvati nella cartella '$backupDir'.\n";
} else {
    echo "\nNessuna operazione eseguita.\n";
}